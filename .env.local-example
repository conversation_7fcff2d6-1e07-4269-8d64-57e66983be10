# Create a .env.secret file to overide variables in .env.local and
# to automatically load varibles that will manually defined on the server
# THE .env.secret FILE MUST BE IN THE .gitignore FILE to avoid overwriting
# other developers settings.

# Overide local env values if you need to e.g
# ---------------------------------------
# PORT=5001
# ---------------------------------------

# These secrets need to added through GCP console for DEV, TEST, and PROD environments
# --------------------------------------------------------------------------
# DATABASE_URL= This needs to be added to GCP but it not required in this file

LOG_LEVEL=debug
TEST_LOG_MESSAGES=true
PORT=4567
SENTRY_SAMPLE_RATE=1.0

AWS_REGION=
AWS_ADMIN_ID=
AWS_ADMIN_SECRET=
AWS_COGNITO_USER_POOL_ID=
AWS_COGNITO_CLIENT_ID=
AWS_S3_BUCKET=
AWS_S3_BUCKET_CARD=

INFO_LOG_CLIENT_ID=
INFO_LOG_CLIENT_SECRET=
INFO_LOG_USERNAME=
INFO_LOG_BASE_URL=

# ---------------------------------------------------------------------------