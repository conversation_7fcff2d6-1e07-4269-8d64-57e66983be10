name: deploy-epay-PROD
env:
  ENVIRONMENT: prod
  # Convention to prefix environment with the appname for identifying all resources
  APP_NAME: epay-api
  AWS_ACCOUNT: ************
on:
  push:
    branches:
      - main

jobs:
  build:
    name: 'Build and Publish Image'
    runs-on: ubuntu-latest
    environment: prod
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@0e613a0980cbf65ed5b322eb7a1e075d28913a83
        with:
          aws-access-key-id: ${{secrets.EPAYNZ_AWS_ACCESS_KEY_PROD}}
          aws-secret-access-key: ${{secrets.EPAYNZ_AWS_SECRET_ACCESS_KEY_PROD}}
          aws-region: ap-southeast-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@62f4f872db3836360b72999f4b87f1ff13310f3a

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ vars.PROD_CONTAINER_REGISTRY }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker buildx create --use
          docker buildx build --build-arg RUNNER_TEMP=${{runner.temp}} --load -t ${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG} .
          docker push ${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG}
          sed -i "s|<ECR_REPOSITORY_URL>|${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG}|g" task-definition.json

      - name: Replace variables task definition
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          sed -i "s|<JSON_SECRETS>|PROD_JSON_SECRETS|g" task-definition.json
          sed -i "s|<ARN_SMTP_PRIVATE_KEY>|arn:aws:secretsmanager:ap-southeast-2:************:secret:PROD_SMTP_PRIVATE_KEY-iex6nT|g" task-definition.json
          sed -i "s|<ARN_EPAY_SMTP_PRIVATE_KEY>|arn:aws:secretsmanager:ap-southeast-2:************:secret:PROD_EPAY_SMTP_PRIVATE_KEY-5gqB51|g" task-definition.json
          sed -i "s|<ARN_JSON_SECRETS>|arn:aws:secretsmanager:ap-southeast-2:************:secret:PROD_JSON_SECRETS-9AuH4T|g" task-definition.json
          sed -i "s|<ARN_REN_DOMAIN_CERT>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:PROD_REN_DOMAIN_CERT-TzxrWD|g" task-definition.json
          sed -i "s|<ARN_REN_INTERMEDIATE_CERT>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:PROD_REN_INTERMEDIATE_CERT-KnCpBd|g" task-definition.json
          sed -i "s|<ARN_REN_CA_CERT>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:PROD_REN_CA_CERT-9Oz6kA|g" task-definition.json
          sed -i "s|<ENV>|prod|g" task-definition.json
          sed -i "s|<ENV_SECRET>|prod|g" task-definition.json
          sed -i "s|<ENV_UPPER>|PROD|g" task-definition.json
          sed -i "s|<AWS_ACCOUNT>|${AWS_ACCOUNT}|g" task-definition.json
          sed -i "s|<TASK_ROLE>|ecsTaskExecutionRole|g" task-definition.json
          sed -i "s|<EXECUTION_ROLE>|ecsTaskExecutionRole|g" task-definition.json
          sed -i "s|<TASK_DEFINITION_FAMILY>|${ENVIRONMENT}-${APP_NAME}-definition|g" task-definition.json
          sed -i "s|<CONTAINER_NAME>|${APP_NAME}|g" task-definition.json

      - name: Deploy to Amazon ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: task-definition.json
          service: epay-api-service
          cluster: epay-portal-cluster
          wait-for-service-stability: true

      # - name: Set image_name variable
      #   id: set_image_var
      #   env:
      #     IMAGE_NAME: ${{ steps.login-ecr.outputs.registry }}/${APP_NAME}:${{ github.sha }}
      #   run: |
      #     echo "image_test=$IMAGE_NAME" >> $GITHUB_OUTPUT

      # - name: Debug image_name variable
      #   run: |
      #     echo "Debug image_test: ${{ steps.set_image_var.outputs.image_test }}"

  # staging:
  #   name: 'Deploy to Staging'
  #   runs-on: ubuntu-latest
  #   needs: build
  #   environment: prod
  #   env:
  #     IMAGE_NAME: ${{ vars.PROD_CONTAINER_REGISTRY }}/epay-api:${{ github.sha }}
  #   steps:
  #     - name: 'Checkout'
  #       uses: actions/checkout@v3

  #     - name: Configure AWS credentials
  #       uses: aws-actions/configure-aws-credentials@v1
  #       with:
  #         aws-access-key-id: ${{secrets.EPAYNZ_AWS_ACCESS_KEY_PROD}}
  #         aws-secret-access-key: ${{secrets.EPAYNZ_AWS_SECRET_ACCESS_KEY_PROD}}
  #         aws-region: ap-southeast-2

  #     - name: Replace variables task definition
  #       run: |
  #         sed -i "s|<ECR_REPOSITORY_URL>|$IMAGE_NAME|g" task-definition.json
  #         sed -i "s|<JSON_SECRETS>|STAGING_JSON_SECRETS|g" task-definition.json
  #         sed -i "s|<ARN_SMTP_PRIVATE_KEY>|arn:aws:secretsmanager:ap-southeast-2:************:secret:STAGING_JSON_SECRETS-b6D53i|g" task-definition.json
  #         sed -i "s|<ARN_JSON_SECRETS>|arn:aws:secretsmanager:ap-southeast-2:************:secret:STAGING_JSON_SECRETS-b6D53i|g" task-definition.json
  #         sed -i "s|<ENV>|staging|g" task-definition.json
  #         sed -i "s|<ENV_UPPER>|STAGING|g" task-definition.json
  #         sed -i "s|<AWS_ACCOUNT>|************|g" task-definition.json
  #         sed -i "s|<TASK_ROLE>|ecsTaskExecutionRole|g" task-definition.json
  #         sed -i "s|<EXECUTION_ROLE>|ecsTaskExecutionRole|g" task-definition.json
  #         sed -i "s|<TASK_DEFINITION_FAMILY>|staging-epay-api-definition|g" task-definition.json
  #         sed -i "s|<CONTAINER_NAME>|epay-api|g" task-definition.json

  #     - name: Deploy to Amazon ECS
  #       uses: aws-actions/amazon-ecs-deploy-task-definition@v1
  #       with:
  #         task-definition: task-definition.json
  #         service: staging-epay-api-service4
  #         cluster: staging-epay-portal-cluster
  #         wait-for-service-stability: true

  # production:
  #   name: 'Deploy to Production'
  #   runs-on: ubuntu-latest
  #   environment: prod
  #   needs: build
  #   env:
  #     IMAGE_NAME: ${{ vars.PROD_CONTAINER_REGISTRY }}/${APP_NAME}:${{ github.sha }}
  #   steps:
  #     - name: 'Checkout'
  #       uses: actions/checkout@v3

  #     - name: Configure AWS credentials
  #       uses: aws-actions/configure-aws-credentials@v1
  #       with:
  #         aws-access-key-id: ${{secrets.EPAYNZ_AWS_ACCESS_KEY_PROD}}
  #         aws-secret-access-key: ${{secrets.EPAYNZ_AWS_SECRET_ACCESS_KEY_PROD}}
  #         aws-region: ap-southeast-2

  #     - name: Replace variables task definition
  #       run: |
  #         sed -i "s|<ECR_REPOSITORY_URL>|$IMAGE_NAME|g" task-definition.json
  #         sed -i "s|<JSON_SECRETS>|PROD_JSON_SECRETS|g" task-definition.json
  #         sed -i "s|<ARN_SMTP_PRIVATE_KEY>|arn:aws:secretsmanager:ap-southeast-2:************:secret:DEV_SMTP_PRIVATE_KEY-UOloH9|g" task-definition.json
  #         sed -i "s|<ARN_JSON_SECRETS>|arn:aws:secretsmanager:ap-southeast-2:************:secret:PROD_SMTP_PRIVATE_KEY-iex6nT|g" task-definition.json
  #         sed -i "s|<ENV>|prod|g" task-definition.json
  #         sed -i "s|<ENV_UPPER>|PROD|g" task-definition.json
  #         sed -i "s|<AWS_ACCOUNT>|************|g" task-definition.json
  #         sed -i "s|<TASK_ROLE>|ecsTaskExecutionRole|g" task-definition.json
  #         sed -i "s|<EXECUTION_ROLE>|ecsTaskExecutionRole|g" task-definition.json
  #         sed -i "s|<TASK_DEFINITION_FAMILY>|prod-epay-api-definition|g" task-definition.json
  #         sed -i "s|<CONTAINER_NAME>|epay-api|g" task-definition.json

  #     - name: Deploy to Amazon ECS
  #       uses: aws-actions/amazon-ecs-deploy-task-definition@v1
  #       with:
  #         task-definition: task-definition.json
  #         service: epay-api-service
  #         cluster: epay-portal-cluster
  #         wait-for-service-stability: true
