name: deploy-epay
env:
  ENVIRONMENT: dev
  APP_NAME: epay-api
  AWS_ACCOUNT: ************
on:
  push:
    branches:
      - dev

jobs:
  build-and-publish-image:
    name: 'Build and Publish Image'
    environment: dev
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@0e613a0980cbf65ed5b322eb7a1e075d28913a83
        with:
          aws-access-key-id: ${{secrets.AWS_ACCESS_KEY}}
          aws-secret-access-key: ${{secrets.AWS_SECRET_ACCESS_KEY}}
          aws-region: ap-southeast-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@62f4f872db3836360b72999f4b87f1ff13310f3a

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker buildx create --use
          docker buildx build --build-arg RUNNER_TEMP=${{runner.temp}} --load -t ${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG} .
          docker push ${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG}
          sed -i "s|<ECR_REPOSITORY_URL>|${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG}|g" task-definition.json

      - name: Replace variables task definition
        run: |
          sed -i "s|<JSON_SECRETS>|DEV_JSON_SECRETS|g" task-definition.json
          sed -i "s|<ARN_JSON_SECRETS>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:DEV_JSON_SECRETS-47e3KM|g" task-definition.json
          sed -i "s|<ARN_SMTP_PRIVATE_KEY>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:DEV_SMTP_PRIVATE_KEY-UOloH9|g" task-definition.json
          sed -i "s|<ARN_EPAY_SMTP_PRIVATE_KEY>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:DEV_EPAY_SFTP_PRIVATE_KEY-MadwYL|g" task-definition.json
          sed -i "s|<ARN_REN_DOMAIN_CERT>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:DEV_REN_DOMAIN_CERT-V64P3Y|g" task-definition.json
          sed -i "s|<ARN_REN_INTERMEDIATE_CERT>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:DEV_REN_INTERMEDIATE_CERT-udj9sD|g" task-definition.json
          sed -i "s|<ARN_REN_CA_CERT>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:DEV_REN_CA_CERT-rvt4qC|g" task-definition.json
          sed -i "s|<ENV>|dev|g" task-definition.json
          sed -i "s|<ENV_SECRET>|dev|g" task-definition.json
          sed -i "s|<ENV_UPPER>|DEV|g" task-definition.json
          sed -i "s|<AWS_ACCOUNT>|${AWS_ACCOUNT}|g" task-definition.json
          sed -i "s|<TASK_ROLE>|escTaskAdmin|g" task-definition.json
          sed -i "s|<EXECUTION_ROLE>|escTaskAdmin|g" task-definition.json
          sed -i "s|<TASK_DEFINITION_FAMILY>|dev-epay-api-definition|g" task-definition.json
          sed -i "s|<CONTAINER_NAME>|${ENVIRONMENT}-${APP_NAME}|g" task-definition.json

      - name: Deploy to Amazon ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: task-definition.json
          service: dev-epay-api-5
          cluster: dev-epay-cluster
          wait-for-service-stability: true
# test
