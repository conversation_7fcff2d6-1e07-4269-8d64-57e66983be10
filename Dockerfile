FROM node:18.12-alpine3.16 AS base

COPY . /app
WORKDIR /app

RUN npm install -g pnpm

FROM base AS prod-deps
RUN pnpm install --prod --frozen-lockfile --shamefully-hoist=true

FROM base AS build
# installs dev dependencies
RUN pnpm install --frozen-lockfile --shamefully-hoist=true
# compiles typescript (needs prisma generated schema)
RUN pnpm run build

FROM base
# only copy production dependencies for node_modules
COPY --from=prod-deps /app/node_modules /app/node_modules
COPY --from=build /app/dist/src /app/dist/src
COPY --from=build /app/package.json /app/package.json
COPY --from=build /app/prisma /app/prisma
COPY --from=build /app/.env.dev /app/.env.dev
COPY --from=build /app/.env.staging /app/.env.staging
COPY --from=build /app/.env.prod /app/.env.prod

RUN npx prisma generate

EXPOSE 8000
CMD [ "pnpm", "start:migrate:prod" ]
