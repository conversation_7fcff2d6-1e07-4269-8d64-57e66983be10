CLIENT_URL_BASE=https://epay-staging-corp-portal.nextchapter.live
CORPORATE_PORTAL_URL_BASE=https://epay-staging-corp-portal.nextchapter.live
ADMIN_PORTAL_URL_BASE=https://epay-staging-admin-portal.nextchapter.live
API_URL_BASE=https://epay-staging-corp-portal-api.nextchapter.live

CLIENT_DOMAIN=prezzycard.co.nz

LOG_LEVEL=debug
PORT=8000
TEST_LOG_MESSAGES=false
SENTRY_SAMPLE_RATE=1
FORCE_LOG_TO_CONSOLE=false
SENTRY_DSN=https://<EMAIL>/****************

AWS_REGION=ap-southeast-2
AWS_ADMIN_ID=AKIA4HYZMDCKZKVD5PVK
AWS_S3_BUCKET=prod-prezzy-corporate-application-identity
AWS_S3_BUCKET_CARD=public-epay-portal-assets
AWS_S3_BUCKET_REPORT=printer-report

AWS_COGNITO_USER_POOL_ID=ap-southeast-2_0XdXOpsTK
AWS_COGNITO_CLIENT_ID=6tac2qvm0v87tm3p6hndmt3eum

INFO_LOG_BASE_URL=https://api2.infolog.nz
INFO_LOG_USERNAME=ePayAPI
INFO_LOG_CLIENT_ID=e8ddca4f-0a0f-49a4-8f0a-dd87f8394899

I2C_URL=https://ws2.mycardplace.com:6443
I2C_ID=EpayTest
I2C_USER_ID=epayacqtest

SOAP_GIFT_CARD_URL='https://serv002.ezipay.net.nz:41443/GiftCard.asmx'
SOAP_GIFT_CARD_ACTION='http://compass.net.nz/WebServices/GiftCard/MessageExchange'
SOAP_AUTH_USERNAME='NextChapterLive0818'
SOAP_MESSAGE_EXCHANGE_MESSAGE_TYPE='PrezzyBatchDetails'
SOAP_MESSAGE_EXCHANGE_USER_CODE='145226'

SENDER_EMAIL=<EMAIL>

USE_MOCKING=false

WINDCAVE_URL=https://sec.windcave.com/api/v1/sessions
WINDCAVE_USER=MyCardPlace_Rest

SFTP_HOST=secure.placard.nz
SFTP_PORT=22
SFTP_USERNAME=epaynz_prod
SFTP_REMOTE_PATH=prod

EPAY_SFTP_HOST=**************
EPAY_SFTP_PORT=2200 
EPAY_SFTP_USERNAME=CorporatePortalProd
EPAY_SFTP_REMOTE_PATH=./

EPAY_ADMIN_EMAIL=<EMAIL>
EPAY_SUPPORT_EMAIL=<EMAIL>
EPAY_KYC_EMAIL=<EMAIL>
EPY_KYC_ALTERNATIVE_EMAIL=<EMAIL>

# update to prod urls when available
EPAY_INTEGRATION_URL_1=https://serv002.ezipay.net.nz:52572
#EPAY_INTEGRATION_URL_2=

AUTH0_ISSUER_BASE_URL=https://epaynz.au.auth0.com
AUTH0_AUDIENCE=https://epaynz-api.epayworldwide.co.nz
AUTH0_CLIENT_ID=jNDXxOFHHrgnWfFaFQu6cKXhKhfxkXgF
AUTH0_CONNECTION=Username-Password-Authentication
AUTH0_CONNECTION_ID=con_aDxyAh2d0ExSrbkM
AUTH0_ADMIN_CONNECTION=Admin-Authentication
AUTH0_ADMIN_CONNECTION_ID=con_dTFrJYn6ySJgFA1V

TEAM_NOTIFICATION_EMAIL=<EMAIL>

EPAY_REPORT_PROD_TEST=false
SEND_ORDER_TO_EPAY=true

REN_URL=https://prezzynzprepaid.eftapme.com
REN_URL2=https://dr-prezzynzprepaid.eftapme.com
REN_VCARD_BRANCH=000001
REN_PART_ID=4001

ORDER_LOCK_TIMEOUT_IN_MINUTES=60
DEFERRED_DELAY_BEFORE_RETRY_IN_MINUTES=5

TWILIO_ACCOUNT_SID=ACbca5cdd7bd39b20c7f01404483ad4a5d
TWILIO_VERIFY_SERVICE_SID=VA9ba16454a197d9b17378d7c859f3c33b