import {
  CardType,
  DeliveryMethod,
  PrismaClient,
  ProductStatus,
  ProductType,
  Prisma,
  DesignType,
  CardDirection,
  Resolution,
} from '@prisma/client'
import { STOCK_DETAILS } from 'constants/prezzy'
//const prisma = new PrismaClient()

const CREATE_CARD_PRODUCTS: Prisma.CustomProductOrderItemCreateInput[] = [
  {
    id: '1c2fe768-cd29-4e73-91da-5da2f71f97f9',
    productCode: 'LOGO',
    unitPrice: 5000,
    discount: 0,
  },
  {
    id: '6056695c-7312-4f9f-bab1-339939b9b203',
    productCode: 'STOCK_LOGO',
    unitPrice: 17250,
    discount: 0,
  },
]

const PHYSICAL_DETAILS =
  '<p>Prezzy card is the ultimate gift of choice for staff incentive or rewards. The perfect way to say "Thank you".The recipient can choose exactly what they want from over 32 million Visa outlets – in store and online.Card fee is not included in the balance available on the card</p>'

const CONDITIONS =
  '<p>Click <a style={{ color: "#ff7300", fontWeight: "300" }} href="https://www.prezzycard.co.nz/terms-and-conditions" target="_blank">HERE</a> for Prezzy Card Terms and Conditions</p>'

const VIRTUAL_DETAILS =
  "<p>Our new Prezzy Virtual card is the perfect way to show appreciation, share the love and give the gift of choice! Our virtual gift card is not only a convenient and hassle-free option, but it's also eco-friendly and contactless – making it easy to send and receive gifts in today's world. Whether you're celebrating a special occasion or just want to brighten someone's day, our virtual gift card is the perfect choice.</p><p><b>Note – Your virtual card can be used online and added to your digital wallet (Apple pay & Google pay) for in-store purchases.</b></p><p><b>Digital Delivery</b></p><p>Please note digital orders are not delivered immediately. Orders are checked and processed between 8.30am and 5pm, Monday to Saturday, NZ standard time.</p>"

const FIXED_VALUES = [2000, 5000, 7500, 10000, 15000, 20000, 50000]

const MIN_VALUE = 2500

const MAX_VALUE = 500000

function toDeliveryMethods(methods: string[]): DeliveryMethod[] {
  return methods.map(
    (method) => DeliveryMethod[method as keyof typeof DeliveryMethod]
  )
}

function toCardTypes(types: string[]): CardType[] {
  return types.map((type) => CardType[type as keyof typeof CardType])
}

const CARD_DESIGNS = [
  {
    cardDesign: {
      id: '363bbb3a-9dd4-46e5-8d77-17a59ad46894',
      externalCardDesignId: '6',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/6.png',
      isPrezzyDesign: true,
      externalCardType: 'CP1',
      externalBinNumber: '',
    },
    product: {
      id: 'f548b947-0ccc-4e86-80e4-307527057f22',
      name: 'Prezzy Red',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['PHYSICAL'],
      details: PHYSICAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['COURIER'],
    },
  },
  {
    cardDesign: {
      id: '796fa5e7-490b-4655-8f03-fac6ca17c7df',
      externalCardDesignId: '1063',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/1063.png',
      isPrezzyDesign: true,
      externalCardType: 'CP1',
      externalBinNumber: '',
    },
    product: {
      id: '626454ba-b64d-471f-b53f-1065a6f802b4',
      name: 'Prezzy Card - Black Glitter',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['PHYSICAL'],
      details: PHYSICAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['COURIER'],
    },
  },
  {
    cardDesign: {
      id: '0c2a9a69-9ffe-496c-bb56-b1f0eb6c1644',
      externalCardDesignId: '993',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/993.png',
      isPrezzyDesign: true,
      externalCardType: 'CP1',
      externalBinNumber: '',
    },
    product: {
      id: '660a97ad-d4af-49aa-b985-0e14733d818c',
      name: 'Prezzy Card - Kiwi Bach',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['PHYSICAL'],
      details: PHYSICAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['COURIER'],
    },
  },
  {
    cardDesign: {
      id: '8ade408d-8291-4cf0-b1f9-bc74bfc3a0cf',
      externalCardDesignId: '90405',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/90405.png',
      isPrezzyDesign: false,
      externalCardType: 'COV',
      externalBinNumber: '',
    },
    product: {
      id: 'b1750180-2dfb-43f8-af00-a0d45ccd8fd0',
      name: 'Prezzy Virtual',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      digitalFee: 150,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['VIRTUAL'],
      details: VIRTUAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['EMAIL'],
    },
  },
  // Stock cards
  {
    cardDesign: {
      id: 'stock-vertical-low-res',
      externalCardDesignId: '90003',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/prezzy-vertical-hig-res.png',
      isPrezzyDesign: true,
      externalCardType: 'STOCK',
      externalBinNumber: '',
      designType: 'STOCK',
      cardDirection: 'VERTICAL',
      resolution: 'LOW',
      available: true,
    },
  },
  {
    cardDesign: {
      id: 'stock-vertical-high-res',
      externalCardDesignId: '90002',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/prezzy-vertical-hig-res.png',
      isPrezzyDesign: true,
      externalCardType: 'STOCK',
      externalBinNumber: '',
      designType: 'STOCK',
      cardDirection: 'VERTICAL',
      resolution: 'HIGH',
      available: true,
    },
  },
]

export async function seedCards(prisma: PrismaClient) {
  for (const prezzyProducts of CARD_DESIGNS) {
    if (!prezzyProducts.cardDesign) {
      continue
    }
    const createdCardDesign = await prisma.cardDesign.upsert({
      where: {
        id: prezzyProducts.cardDesign.id,
      },
      create: {
        id: prezzyProducts.cardDesign.id,
        cardDesignUrl: prezzyProducts.cardDesign.cardDesignUrl,
        externalCardDesignId: prezzyProducts.cardDesign.externalCardDesignId,
        isPrezzyDesign: prezzyProducts.cardDesign.isPrezzyDesign,
        designType: prezzyProducts.cardDesign.designType
          ? DesignType[
              prezzyProducts.cardDesign.designType as keyof typeof DesignType
            ]
          : DesignType.PREZZY,
        cardDirection: prezzyProducts.cardDesign.cardDirection
          ? CardDirection[
              prezzyProducts.cardDesign
                .cardDirection as keyof typeof CardDirection
            ]
          : undefined,
        resolution: prezzyProducts.cardDesign.resolution
          ? Resolution[
              prezzyProducts.cardDesign.resolution as keyof typeof Resolution
            ]
          : undefined,
        externalCardType: prezzyProducts.cardDesign.externalCardType,
        externalBinNumber: prezzyProducts.cardDesign.externalBinNumber,
        available: prezzyProducts.cardDesign.available,
      },
      update: {},
    })

    if (!prezzyProducts.product) {
      continue
    }
    await prisma.product.upsert({
      where: {
        id: prezzyProducts.product.id,
      },

      create: {
        id: prezzyProducts.product.id,
        name: prezzyProducts.product.name,
        status: prezzyProducts.product.status,
        loadingFee: prezzyProducts.product.loadingFee,
        digitalFee: prezzyProducts.product.digitalFee,
        isArchived: prezzyProducts.product.isArchived,
        type: prezzyProducts.product.type,
        details: prezzyProducts.product.details,
        conditions: prezzyProducts.product.conditions,
        fixedValues: prezzyProducts.product.fixedValues,
        minValue: prezzyProducts.product.minValue,
        maxValue: prezzyProducts.product.maxValue,
        deliveryMethods: toDeliveryMethods(
          prezzyProducts.product.deliveryMethods
        ),
        cardTypes: toCardTypes(prezzyProducts.product.cardTypes),
        designId: createdCardDesign.id,
      },
      update: {},
    })
  }

  // Logos
  for (const product of CREATE_CARD_PRODUCTS) {
    await prisma.customProductOrderItem.upsert({
      where: {
        id: product.id,
      },

      create: {
        id: product.id,
        productCode: product.productCode,
        unitPrice: product.unitPrice,
        discount: product.discount,
      },
      update: {},
    })
  }
}
