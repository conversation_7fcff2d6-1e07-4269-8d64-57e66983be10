import {
  DeliveryMethod,
  PrismaClient,
  ProductStatus,
  ProductType,
} from '@prisma/client'

export const STOCK_ORDER_ID = '3785e936-4cf6-493f-848f-1c52c47371b1'
export const STOCK_ORDER_NUMBER = '13823'

export const STOCK_CARD_DESIGN_ID = 'a2867df8-f607-41fb-a64f-aad03bc06909'

export let STOCK_CARD_ORDER_ID = ''
export let STOCK_CARD_ORDER_NUMBER = ''

export const STOCK_PRODUCT_CODE = 99

export async function seedOrders(
  prisma: PrismaClient,
  orgId: string,
  userId: string
) {
  const higResCardDesign = await prisma.cardDesign.upsert({
    where: {
      id: 'a2867df8-f607-41fb-a64f-aad03bc06909',
    },
    create: {
      id: 'a2867df8-f607-41fb-a64f-aad03bc06909',
      cardDesignUrl:
        'https://prezzy-card.s3.ap-southeast-2.amazonaws.com/logo/new_card_woohoo_testing_order_flow_3bdcd079-404c-4727-bde8-97ff9db46c5a.png.jpg',
      externalCardDesignId: '123',
      isPrezzyDesign: true,
      resolution: 'HIGH',
    },
    update: {},
  })

  const lowResCrnDesign = await prisma.cardDesign.upsert({
    where: {
      id: '675b956c-ebe9-4df5-8845-26da90690cd8',
    },
    create: {
      id: '675b956c-ebe9-4df5-8845-26da90690cd8',
      cardDesignUrl:
        'https://prezzy-card.s3.ap-southeast-2.amazonaws.com/logo/new_card_woohoo_testing_order_flow_3bdcd079-404c-4727-bde8-97ff9db46c5a.png.jpg',
      externalCardDesignId: '123',
      isPrezzyDesign: true,
      resolution: 'LOW',
    },
    update: {},
  })

  const personalizedCardProduct = await prisma.product.upsert({
    where: {
      id: '88b6f3c4-2a03-428b-aac4-0c87821b5de3',
    },
    create: {
      id: '88b6f3c4-2a03-428b-aac4-0c87821b5de3',
      name: 'The Not Wohoo Stock Card',
      status: ProductStatus.APPROVED,
      type: ProductType.STOCK,
      loadingFee: 595,
      productCode: STOCK_PRODUCT_CODE,
      isArchived: false,
      minValue: 100,
      maxValue: 500000,
      option1Fee: 250,
      option2Fee: 750,
      deliveryMethods: [DeliveryMethod.COURIER],
      organizationId: orgId,
      designId: lowResCrnDesign.id,
      extraDesignId: higResCardDesign.id,
      logoId: null,
      cardTypes: ['PHYSICAL'],
    },
    update: {},
  })

  // const stockCardOrderItems: StockCardOrder = [
  //   {
  //     productCode: STOCK_PRODUCT_CODE, // Assuming this is the product code for 'Woohooo new card'
  //     quantity: 5,
  //     resolution: 'LOW',
  //     deliveryMethod: {
  //       type: 'COURIER',
  //       address: '123 Stock Card Street',
  //       suburb: 'Stock Card Suburb',
  //       city: 'Stock City',
  //       postcode: '1234',
  //     },
  //   },
  // ]

  // const orderResult = await tryCreateStockCardOrder({
  //   userId: userId,
  //   stockCardOrderItems,
  //   orgId: NEXT_CHAPTER_ORG_ID,
  // })

  // if (orderResult.isOk()) {
  //   STOCK_CARD_ORDER_NUMBER = orderResult.value.orderNumber
  //   console.log('seed order number', STOCK_CARD_ORDER_NUMBER)
  //   STOCK_CARD_ORDER_ID = orderResult.value.id
  //   console.log('seed order id', STOCK_CARD_ORDER_ID)
  // }

  // const submitResult = await submitProductOrder({
  //   orgId: NEXT_CHAPTER_ORG_ID,
  //   orderNumber: STOCK_CARD_ORDER_NUMBER,
  //   billingAddress: 'Stock Card Way',
  //   city: 'Auckland',
  //   country: 'New Zealand',
  //   postcode: '2014',
  //   purchaseOrderNumber: null,
  //   lockCode: 'no_lock_code',
  //   paymentMethod: 'BANK_TRANSFER',
  //   secureDelivery: false,
  // })

  // // submit order and create cards
  // const supplierResult = await orderCardsFromSupplier({
  //   orderId: STOCK_CARD_ORDER_ID,
  //   disableMocking: false,
  //   userId: userId,
  //   paymentDate: new Date(),
  // })
}
