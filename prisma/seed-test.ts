import {
  DeliveryMethod,
  PrismaClient,
  ProductStatus,
  ProductType,
  UserRole,
} from '@prisma/client'
import { seedCards } from './seed-cards'
import { seedOrders } from './seed-orders'
import { seedFloatFunds } from './seed-float-funds'
const prisma = new PrismaClient()

/*******************************************
 * Create Next Chapter Studio
 * And users:
 * - <EMAIL> (ORG_ADMIN)
 * - <EMAIL> (ORG_ADMIN)
 * - <EMAIL> (EPAY_COMPLIANCE)
 * - <EMAIL> (EPAY_ADMIN)
 */

/**
 * Default global discount values (in cents)
 * Exported for use in tests and other parts of the application
 */
export const TEST_GLOBAL_DISCOUNT = {
  id: 1,
  loadingFee: 300, // $3.00
  digitalFee: 90, // $0.90
  urbanShipping: 500, // $5.00
  ruralShipping: 800, // $8.00
  createCard: 1000, // $10.00
  stockCard: 500, // $5.00
  highResStockCard: 100, // $1.00
  lowResStockCard: 50, // $0.55
  lastUpdatedAt: new Date(),
  updatedByUserId: '66903ec7616c1c4790f0fe73', // Nathan Admin user
}

export const PRODUCT_ID_WOOHOO_NEW_CARD = '10990238091283091283001928309128390'
export const PRODUCT_WOOHOO_OPTION1_FEE = 150
export const PRODUCT_WOOHOO_OPTION2_FEE = 350
export const PRODUCT_WOOHOO_LOADING_FEE = 595

export const PRODUCT_CODE_WOOHOO_NEW_CARD = 5
export const CORP_APPLICATION_ID_456_EMPTY = '456_seed_application_id'
export const USER_ID_SEEDED_123 = '123_seed_user_id'

// ** Begin Product Orders
export const PENDING_PRODUCT_ORDER_ID = '8a03f8a0-3b38-4d4c-a21f-e29a55f00f77'
export const PENDING_PRODUCT_ORDER_NUMBER = '15000'
// ** End Product Orders

export const NEXT_CHAPTER_ORG_ID = '6c16a991-e43b-41bd-9e6a-a8120cb311e5'
export const TEST_ORG_1_ID = 'a18060d9-5918-4514-bfb3-ff377f6d42e5'
export const TEST_ORG_1_USER = '43e798a3-106b-4f80-8a1b-afc84a8f43d5'
export const TEST_ORG_1_NAME = 'Test org #1'
export const TEST_ORG_1_CUSTOMER_CODE = 'C-00040000'

export const CARD_DESIGN_URL = 'https://www.thenextchapter.studio'

async function main() {
  const nextChapter = await prisma.organization.upsert({
    where: {
      id: NEXT_CHAPTER_ORG_ID,
    },
    create: {
      id: NEXT_CHAPTER_ORG_ID,
      type: 'LIMITED_LIABILITY_COMPANY',
      name: 'Next Chapter Studio Limited',
      customerCode: '*********',
      tradingName: '',
      nzbn: '9429048680271',
      gstNumber: '',
      address: '',
      city: '',
      suburb: '',
      zip: '',
      country: '',
      principalPlace: '',
      natureAndPurpose: '',
      phone: '',
      cPartnerIds: ['122322'],
      createdAt: '2023-06-20T01:03:54.668Z',
      updatedAt: '2023-06-20T01:05:38.432Z',
    },
    update: {},
  })

  await prisma.organization.upsert({
    where: {
      id: TEST_ORG_1_ID,
    },
    create: {
      id: TEST_ORG_1_ID,
      customerCode: TEST_ORG_1_CUSTOMER_CODE,
      type: 'LIMITED_LIABILITY_COMPANY',
      name: TEST_ORG_1_NAME,
      tradingName: '',
      nzbn: '9429048680272',
      gstNumber: '',
      address: '',
      city: '',
      suburb: '',
      zip: '',
      country: '',
      principalPlace: '',
      natureAndPurpose: '',
      phone: '',
      cPartnerIds: [''],
      createdAt: '2025-01-20T01:03:54.668Z',
      updatedAt: '2025-01-20T01:05:38.432Z',
    },
    update: {},
  })

  const testAdmin = await prisma.user.upsert({
    where: {
      id: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
    },
    create: {
      id: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      email: '<EMAIL>',
      role: UserRole.ORG_ADMIN,
      firstName: 'Test',
      lastName: 'User',
      organizationId: nextChapter.id,
    },
    update: {},
  })

  const emmaOrgAdmin = await prisma.user.upsert({
    where: {
      id: '58000f62-8640-4301-aa40-756b60c6367c',
    },
    create: {
      id: '58000f62-8640-4301-aa40-756b60c6367c',
      email: '<EMAIL>',
      role: UserRole.ORG_ADMIN,
      firstName: 'Emma',
      lastName: 'Slight',
      organizationId: null,
    },
    update: {},
  })

  const ngOrgAdmin = await prisma.user.upsert({
    where: {
      id: '658e5ccb31ed5183faa803b2',
    },
    create: {
      id: '658e5ccb31ed5183faa803b2',
      email: '<EMAIL>',
      role: UserRole.ORG_ADMIN,
      firstName: 'Mr',
      lastName: 'Knob',
      organizationId: nextChapter.id,
    },
    update: {},
  })

  await prisma.user.upsert({
    where: {
      id: USER_ID_SEEDED_123,
    },
    create: {
      id: USER_ID_SEEDED_123,
      email: '<EMAIL>',
      role: UserRole.ORG_ADMIN,
      firstName: 'Mr',
      lastName: 'Not Proper',
    },
    update: {},
  })

  const approver = await prisma.user.upsert({
    where: {
      id: '6599e4e5193ea79c142c8b7e',
    },
    create: {
      id: '6599e4e5193ea79c142c8b7e',
      email: '<EMAIL>',
      role: UserRole.EPAY_COMPLIANCE,
      firstName: 'Mister',
      lastName: 'Approver',
    },
    update: {},
  })

  const admin = await prisma.user.upsert({
    where: {
      id: '6599e51f50c57b82204a4cc9',
    },
    create: {
      id: '6599e51f50c57b82204a4cc9',
      email: '<EMAIL>',
      role: UserRole.EPAY_ADMIN,
      firstName: 'Mister',
      lastName: 'Admin',
    },
    update: {},
  })

  await prisma.user.upsert({
    where: {
      id: TEST_ORG_1_USER,
    },
    create: {
      id: TEST_ORG_1_USER,
      email: '<EMAIL>',
      role: UserRole.ORG_ADMIN,
      firstName: 'Mr',
      lastName: 'TestOrgUser',
      organizationId: TEST_ORG_1_ID,
    },
    update: {},
  })

  await prisma.corporateApplication.upsert({
    where: {
      id: 'db681306-2f03-4492-aea6-93bac6f70a9e',
    },
    create: {
      id: 'db681306-2f03-4492-aea6-93bac6f70a9e',
      organizationId: nextChapter.id,
      organizationType: 'LIMITED_LIABILITY_COMPANY',
      name: 'Next Chapter Studio Limited',
      tradingName: '',
      nzbn: '9429048680271',
      gstNumber: '',
      address: '',
      city: '',
      suburb: '',
      zip: '',
      country: '',
      principalPlace: '',
      natureAndPurpose: '',
      phone: '',
      status: 'APPROVED',
      createdAt: '2023-06-19T23:06:37.141Z',
      updatedAt: '2023-06-20T01:03:54.668Z',
      submittedAt: '2023-06-19T23:17:13.147Z',
      userId: ngOrgAdmin.id,
    },
    update: {},
  })

  await prisma.corporateApplication.upsert({
    where: {
      id: TEST_ORG_1_ID,
    },
    create: {
      id: 'f2c97d60-a61d-4561-a912-6ded869d0111',
      organizationId: TEST_ORG_1_ID,
      organizationType: 'LIMITED_LIABILITY_COMPANY',
      name: TEST_ORG_1_NAME,
      tradingName: '',
      nzbn: '9429048680271',
      gstNumber: '',
      address: '',
      city: '',
      suburb: '',
      zip: '',
      country: '',
      principalPlace: '',
      natureAndPurpose: '',
      phone: '',
      status: 'APPROVED',
      createdAt: '2023-06-19T23:06:37.141Z',
      updatedAt: '2023-06-20T01:03:54.668Z',
      submittedAt: '2023-06-19T23:17:13.147Z',
      userId: TEST_ORG_1_USER,
    },
    update: {},
  })

  // generate a new EMPTY application

  await prisma.corporateApplication.upsert({
    where: {
      id: CORP_APPLICATION_ID_456_EMPTY,
    },
    create: {
      id: CORP_APPLICATION_ID_456_EMPTY,
      userId: USER_ID_SEEDED_123,
    },
    update: {},
  })

  await prisma.corporateApplication.upsert({
    where: {
      id: '09282fcd-95e7-4cb1-ab40-b8f83998a338',
    },
    create: {
      id: '09282fcd-95e7-4cb1-ab40-b8f83998a338',

      userId: emmaOrgAdmin.id,
    },
    update: {},
  })

  await seedCards(prisma)

  const prezzyRed = await prisma.product.findFirst({
    where: {
      name: 'Prezzy Red',
    },
  })
  if (!prezzyRed) {
    throw new Error('Prezzy Red not found')
  }

  const orderForCrnUpdateTests = await prisma.productOrder.upsert({
    where: {
      id: '3785e936-4cf6-493f-848f-1c52c47371b1',
    },
    create: {
      id: '3785e936-4cf6-493f-848f-1c52c47371b1',
      orderStatus: 'PENDING',
      orderNumber: '14899',
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      totalQuantity: 2,
      subTotal: 10000,
      secureDelivery: false,
      loadingFeeTotal: 1190,
      digitalFeeTotal: 0,
      shippingTotal: 652,
      discountTotal: 90,
      gstAmount: 98,
      orderTotal: 12046,
      creditCardFee: 294,
      lockCode: null,
      lockCodeEmailSent: false,
      createdAt: '2024-02-06T19:57:17.901Z',
      reportedToEpay: false,
      organizationId: nextChapter.id,
      userId: emmaOrgAdmin.id,
    },
    update: {},
  })

  const orderItemForCrnUpdateTests = await prisma.productOrderItem.upsert({
    where: {
      id: '70d7dcf9-212d-47c2-889d-5a55713686d1',
    },
    create: {
      id: '70d7dcf9-212d-47c2-889d-5a55713686d1',
      productOrderId: orderForCrnUpdateTests.id,
      productId: prezzyRed?.id!,
      productCode: prezzyRed?.productCode!,
      quantity: 2,
      deliveryMethod: 'COURIER',
      unitPrice: 5000,
      recipientName: 'Nathan Smith',
      recipientEmail: '<EMAIL>',
      recipientAddress: '5B Luna Way',
      recipientCity: 'Auckland',
      recipientCountry: 'New Zealand',
      recipientSuburb: 'Bucklands Beach',
      recipientPostCode: '2014',
      externalBatchId: 'mock-866347999',
      loadingFee: 1190,
      deliveryBatchId: '1',
      digitalFee: 0,
      discount: 90,
      lockCodeEmailSent: false,
    },
    update: {},
  })

  const productOrderForWindcave = await prisma.productOrder.upsert({
    where: {
      id: PENDING_PRODUCT_ORDER_ID,
    },
    create: {
      id: PENDING_PRODUCT_ORDER_ID,
      orderStatus: 'PENDING',
      orderNumber: PENDING_PRODUCT_ORDER_NUMBER,
      paymentMethod: 'CREDIT_CARD',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      orderTotal: 5000,
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: nextChapter.id,
      userId: testAdmin.id,
      windcaveSessionId: 'test-windcaveSessionId',
      windcaveSessionConfirmed: false,
    },
    update: {},
  })

  const customProductOrderForWindcave = await prisma.customProductOrder.upsert({
    where: {
      id: '8a03f8a0-3b38-4d4c-a21f-e29a55f00f80',
    },
    create: {
      id: '8a03f8a0-3b38-4d4c-a21f-e29a55f00f80',
      orderStatus: 'PENDING',
      orderNumber: 'LOGO098',
      paymentMethod: 'CREDIT_CARD',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      orderTotal: 5000,
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: nextChapter.id,
      userId: testAdmin.id,
      windcaveSessionId: 'test-windcaveSessionId',
      windcaveSessionConfirmed: false,
      productId: prezzyRed?.id!,
    },
    update: {},
  })

  const higResCardDesign = await prisma.cardDesign.upsert({
    where: {
      id: '129308-102938209-129380298',
    },
    create: {
      id: '129308-102938209-129380298',
      cardDesignUrl: CARD_DESIGN_URL,
      externalCardDesignId: '6789',
      isPrezzyDesign: true,
      resolution: 'HIGH',
    },
    update: {},
  })

  const lowResCrnDesign = await prisma.cardDesign.upsert({
    where: {
      id: '1293801238-102938098-1293801238',
    },
    create: {
      id: '1293801238-102938098-1293801238',
      cardDesignUrl: CARD_DESIGN_URL,
      externalCardDesignId: '123',
      isPrezzyDesign: true,
      resolution: 'LOW',
    },
    update: {},
  })

  const woohoooNewCard = await prisma.product.upsert({
    where: {
      id: PRODUCT_ID_WOOHOO_NEW_CARD,
    },
    create: {
      id: PRODUCT_ID_WOOHOO_NEW_CARD,
      name: 'Woohooo new card',
      status: ProductStatus.APPROVED,
      type: ProductType.STOCK,
      loadingFee: PRODUCT_WOOHOO_LOADING_FEE,
      isArchived: false,
      minValue: 100,
      maxValue: 500000,
      option1Fee: PRODUCT_WOOHOO_OPTION1_FEE,
      option2Fee: PRODUCT_WOOHOO_OPTION2_FEE,
      deliveryMethods: [DeliveryMethod.COURIER],
      organizationId: nextChapter.id,
      designId: lowResCrnDesign.id,
      extraDesignId: higResCardDesign.id,
      logoId: null,
    },
    update: {},
  })

  console.log('seeding orders')
  await seedOrders(prisma, nextChapter.id, ngOrgAdmin.id)

  // Seed GlobalDiscount table
  await prisma.globalDiscount.upsert({
    where: { id: TEST_GLOBAL_DISCOUNT.id },
    create: {
      ...TEST_GLOBAL_DISCOUNT,
      // Exclude the lastUpdatedAt as it's managed by Prisma
      lastUpdatedAt: undefined,
    },
    update: {},
  })

  console.log('GlobalDiscount table seeded')

  await seedFloatFunds(
    prisma,
    TEST_ORG_1_ID,
    TEST_ORG_1_CUSTOMER_CODE,
    TEST_ORG_1_USER
  )
}

main()
  .then(async () => {
    console.log('Seed success')
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.log(e, 'Seed failed')
    await prisma.$disconnect()
    // process.exit(1)
  })
