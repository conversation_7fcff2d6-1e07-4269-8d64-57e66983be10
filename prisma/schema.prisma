datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch"]
  binaryTargets   = ["native", "linux-musl"]
  engineType      = "binary"
}

// disable for prod
// generator dbml {
//   provider = "prisma-dbml-generator"
// }

// ***
// DEFINING MODELS:
// https://www.prisma.io/docs/concepts/components/prisma-schema/data-model
//
// RELATIONS
// https://www.prisma.io/docs/concepts/components/prisma-schema/relations
//
// NAMING CONVENTIONS
//https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#naming-conventions
//
// Model Names: 
//  Do use PascalCase
//  Do use singlular names
//
// Field Names:
//  Do use camelCase
//  Do use plural when defining a 1-to-many relationship
//
// MULTIPLE FOREIGN KEYS REFERENCING THE SAME TABLE
// https://www.prisma.io/docs/concepts/components/introspection#disambiguating-relations
//*** 

enum UserRole {
  ORG_ADMIN
  ORG_MANAGER
  ORG_BASIC

  EPAY_ADMIN
  EPAY_COSTUMER_SERVICE
  EPAY_REPORTS
  EPAY_COMPLIANCE
  EPAY_ACCOUNTS
}

enum UserLoginNotification {
  APPLICATION_CELEBRATION
}

model CPartnerOrg {
  id String @id @default(uuid())

  stakeholderId String @unique

  cPartnerLogos   CPartnerLogo[]
  CPartnerAccount CPartnerAccount[]
}

model CPartnerAccount {
  id String @id @default(uuid())

  email  String
  userId String

  cPartnerOrg                   CPartnerOrg                     @relation(fields: [cPartnerOrgId], references: [id])
  cPartnerOrgId                 String
  organizationCPartnerJoinTable OrganizationCPartnerJoinTable[]
}

model CPartnerLogo {
  id String @id @default(uuid())

  logoUrl            String
  externalCardLogoId String

  cPartnerOrg                       CPartnerOrg                         @relation(fields: [cPartnerOrgId], references: [id])
  cPartnerOrgId                     String
  organizationCPartnerLogoJoinTable OrganizationCPartnerLogoJoinTable[]
}

model OrganizationCPartnerJoinTable {
  id String @id @default(uuid())

  cPartnerAccount   CPartnerAccount @relation(fields: [cPartnerAccountId], references: [id])
  organization      Organization    @relation(fields: [organizationId], references: [id])
  cPartnerAccountId String
  organizationId    String

  @@unique([cPartnerAccountId, organizationId])
}

model OrganizationCPartnerLogoJoinTable {
  id String @id @default(uuid())

  created Boolean @default(false)

  cPartnerLogo   CPartnerLogo @relation(fields: [cPartnerLogoId], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id])
  cPartnerLogoId String
  organizationId String

  @@unique([cPartnerLogoId, organizationId])
}

model User {
  id    String @id // from auth0
  email String

  role UserRole?

  firstName String?
  lastName  String?
  branch    String?
  phoneNbr  String?

  loginNotification UserLoginNotification?

  createdAt     DateTime @default(now())
  lastUpdatedAt DateTime @updatedAt

  //relations
  corporateApplications     CorporateApplication?
  corporateApplicationNotes CorporateApplicationNote[]

  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?

  productOrders          ProductOrder[] @relation("user")
  cancelledProductOrders ProductOrder[] @relation("cancelledByUser")
  AuditTrail             AuditTrail[]

  customProductOrder             CustomProductOrder[] @relation("user")
  logoTermsAndConditionsAccepted CustomProductOrder[] @relation("termsAndConditionsAcceptedByUser")
  productOrderNotes              ProductOrderNote[]
  addresses                      Address[]

  globalDiscountsUpdated    GlobalDiscount[]
  ReissueTransactionHistory ReissueTransactionHistory[]
  floatFundsTransaction     FloatFundsTransaction[]
}

model skipKyc {
  email String @unique
}

enum OrganizationType {
  LIMITED_LIABILITY_COMPANY
  SOLE_TRADER
  OTHER
}

enum CorporateApplicationStatus {
  DRAFT
  PENDING
  REVIEWING
  REQUIRES_INFO
  DECLINED
  APPROVED
  PRE_APPROVED
}

enum KycStatus {
  DD //due diligence
  EDD //enhanced due diligence
  FKYC //failed KYC
  NKYC // needs KYC
}

model CorporateApplication {
  id String @id @default(uuid())

  status    CorporateApplicationStatus @default(DRAFT)
  kycStatus KycStatus?

  name String?
  nzbn String?

  infologSessionId String?

  organizationType OrganizationType @default(LIMITED_LIABILITY_COMPANY)

  tradingName String?

  address                        String?
  city                           String?
  suburb                         String?
  zip                            String?
  country                        String?
  hasNoIndividualBeneficialOwner Boolean? @default(false)

  principalPlace   String?
  natureAndPurpose String?
  phone            String?

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  submittedAt DateTime?

  mcpStakeholderId String? //ignore no longer used
  cPartnerUserId   String?
  gstNumber        String? //this is no longer needed (EPY-452)

  // relations
  user   User   @relation(fields: [userId], references: [id])
  userId String @unique

  directors Director[]

  beneficialOwners BeneficialOwner[]

  notes CorporateApplicationNote[]

  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?       @unique
}

model Organization {
  id String @id @default(uuid())

  type OrganizationType

  name         String
  customerCode String?
  increment    Int     @default(autoincrement())

  tradingName String?
  nzbn        String
  gstNumber   String? //this is no longer needed (EPY-452)

  address String?
  city    String?
  suburb  String?
  zip     String?
  country String?

  principalPlace   String?
  natureAndPurpose String?
  phone            String?

  cPartnerIds String[] // this is the c-partner user id

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // relations
  users User[]

  corporateApplications CorporateApplication?

  organizationCPartnerJoinTable OrganizationCPartnerJoinTable[]

  enableCustomerOrderReference     Boolean @default(false)
  enableBrandedStockCards          Boolean @default(false)
  enableOrderVisibilityRestriction Boolean @default(false)

  productOrders                     ProductOrder[]
  cardLogos                         CardLogo[]
  products                          Product[]
  OrganizationCPartnerLogoJoinTable OrganizationCPartnerLogoJoinTable[]

  customProductOrder CustomProductOrder[]
  discounts          Discount?
}

enum IdentityType {
  PASSPORT
  DRIVER_LICENSE
}

model Director {
  id String @id @default(uuid())

  firstName   String?
  givenName   String?
  lastName    String?
  dateOfBirth DateTime?

  address String?
  city    String?
  country String?
  zip     String?

  identificationType     IdentityType?
  identificationNbr      String?
  passportExpiryDate     DateTime?
  driverLicenseVersion   String?
  identificationPhotoUrl String?

  // relations
  corporateApplication   CorporateApplication? @relation(fields: [corporateApplicationId], references: [id])
  corporateApplicationId String?
}

enum BeneficialOwnerType {
  INDIVIDUAL
  ENTITY
}

model BeneficialOwner {
  id                  String              @id @default(uuid())
  beneficialOwnerType BeneficialOwnerType @default(INDIVIDUAL)

  nzbn       String?
  entityName String?

  firstName   String?
  givenName   String?
  lastName    String?
  dateOfBirth DateTime?

  address String?
  city    String?
  country String?
  zip     String?

  identificationType     IdentityType?
  identificationNbr      String?
  passportExpiryDate     DateTime?
  driverLicenseVersion   String?
  identificationPhotoUrl String?

  // relations
  corporateApplication   CorporateApplication? @relation(fields: [corporateApplicationId], references: [id])
  corporateApplicationId String?
}

model CorporateApplicationNote {
  id String @id @default(uuid())

  title   String?
  message String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  //relations
  user   User?   @relation(fields: [userId], references: [id])
  userId String?

  corporateApplication   CorporateApplication? @relation(fields: [corporateApplicationId], references: [id])
  corporateApplicationId String?
}

model CardLogo {
  id                 String   @id @default(uuid())
  name               String   @unique
  logoUrl            String
  externalCardLogoId String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  //relations
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  product Product?
}

enum DesignType {
  PREZZY
  STOCK
}

enum CardDirection {
  VERTICAL
  HORIZONTAL
}

enum Resolution {
  LOW
  HIGH
}

enum RibbonColor {
  WHITE
  BLACK
}

model CardDesign {
  id String @id @default(uuid())

  designType    DesignType    @default(PREZZY)
  cardDirection CardDirection @default(HORIZONTAL)
  resolution    Resolution?

  cardDesignUrl        String
  externalCardDesignId String? // REN system maps to cardSubType
  externalCardType     String? // REN system maps to cardType (shoudl be same as prgramId)
  externalBinNumber    String? // REN Cards require bin numbers 
  externalProductCode  String? // REN Virtual Cards require product code

  available Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  isPrezzyDesign Boolean? @default(false)

  ribbonColor RibbonColor @default(WHITE)

  //relations

  product        Product[] @relation("product")
  extraProductId Product[] @relation("extraDesignId")
}

enum ProductStatus {
  PENDING //sitting with epay to evaluate
  DECLINED //epay has declined logo
  SENT //epay has approved and is sent to placard
  APPROVED //placard has notified us logo is ready to use
}

enum CardType {
  PHYSICAL
  VIRTUAL
}

enum DeliveryMethod {
  COURIER
  EMAIL
  PICKUP
  SMS
}

enum ProductType {
  PREZZY
  GIFT_STATION
  CUSTOM
  STOCK
}

// A Product is a thing some can purchase. In this case, a card logo and design combo
model Product {
  id String @id @default(uuid())

  name        String
  status      ProductStatus
  productCode Int           @default(autoincrement())

  type ProductType @default(GIFT_STATION)

  loadingFee Int
  digitalFee Int?

  isArchived Boolean @default(false)

  fixedValues Int[] // mutable if min/max
  minValue    Int?
  maxValue    Int?

  option1Fee Int? // low res fee 
  option2Fee Int? // high res fee

  details    String? // mutable
  conditions String? // mutable
  redeem     String? // mutable

  categories String[]

  cardTypes       CardType[]
  deliveryMethods DeliveryMethod[] // mutable

  //relations
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  designId String
  design   CardDesign @relation("product", fields: [designId], references: [id])

  extraDesignId String?
  extraDesign   CardDesign? @relation("extraDesignId", fields: [extraDesignId], references: [id])

  logoId String?   @unique
  logo   CardLogo? @relation(fields: [logoId], references: [id])

  displayLogoUrl String?

  productOrderItems       ProductOrderItem[]
  customProductOrder      CustomProductOrder[]
  FundingProductOrderItem FundingProductOrderItem[]
}

enum OrderStatus {
  PENDING // PENDING PAYMENT - first created
  SUBMITTED // BY customer - invoice generated
  VALIDATING // Payed by credit, need accountent approval
  PROCESSING // APPROVED BY EPAY *or* CREDIT CARD PAYMENT MADE => I2C addCard request
  CONFIRMED // I2C has confirmed with the daily card reference number report
  ACTIVATED // After activating order or activating all cards in the order
  CANCELLED // order was cancelled by the user 
  ON_HOLD // set by admin while awaiting payment - new use is when there is an error releasing the order
  RELEASING // put into this status when first releasing the order so calling processes don't have to wait
  SCHEDULED // set when order is released when it has a scheduled date

  // These ones will be implented later
  COMPLETED // order is complete - maybe a 2 week delay?
  DELIVERED // HAVE TO CALL COURIER COMPANY API TO FIND
  SHIPPED // Placard (card printer) has shipped cards. They have supplied tracking number
}

enum PaymentMethod {
  CREDIT_CARD
  BANK_TRANSFER
  FLOAT_FUNDS
}

enum OrderType {
  PRODUCT
  STOCK
  LOAD_FUNDS // deprecated
  SINGLE_FUNDS_LOAD // only allow a single funds load on CRN
  FLOAT_FUNDS
}

model ProductOrder {
  id String @id @default(uuid())

  orderType OrderType @default(PRODUCT)

  orderStatus          OrderStatus @default(PENDING)
  orderNumber          String?     @unique
  orderIncrementNumber Int         @default(autoincrement())

  paymentMethod            PaymentMethod?
  windcaveSessionId        String?
  windcaveSessionConfirmed Boolean        @default(false)
  purchaseOrderNumber      String?
  billingAddress           String?
  city                     String?
  country                  String?
  postCode                 String?
  gstNumber                String?

  totalQuantity Int? //total quantity for order
  subTotal      Int? //total face value for order

  secureDelivery Boolean @default(false)

  loadingFeeTotal Int?
  digitalFeeTotal Int?
  shippingTotal   Int?

  discountTotal            Int? // loadingFee Discount Total + digitalFee Discount Total + shippingFeeDiscount
  digitalFeeDiscountTotal  Int?
  loadingFeeDiscountTotal  Int?
  shippingFeeDiscountTotal Int?

  creditCardFee Int?

  gstAmount  Int?
  orderTotal Int?

  lockCode          String?
  lockCodeEmailSent Boolean @default(false)

  createdAt       DateTime  @default(now())
  submittedAt     DateTime? // This is actually the released date
  paymentDate     DateTime?
  cancelledByDate DateTime?
  scheduledDate   DateTime? // Date to send virtual cards

  reportedToEpay Boolean @default(false)
  releasedBy     String?

  createdByLiveAgent Boolean @default(false)
  authorizedByName   String?
  authorizedByEmail  String?

  //relations
  productOrderItems ProductOrderItem[]
  productOrderNotes ProductOrderNote[]

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])

  userId String
  user   User   @relation("user", fields: [userId], references: [id])

  cancelledById           String?
  cancelledByUser         User?                     @relation("cancelledByUser", fields: [cancelledById], references: [id])
  FundingProductOrderItem FundingProductOrderItem[]
  OrderProcessingQueue    OrderProcessingQueue?
  floatFundsTransaction   FloatFundsTransaction?
}

model ProductOrderItem {
  id String @id @default(uuid())

  productCode    Int // What the customer to reference the product in CSV
  deliveryMethod DeliveryMethod

  unitPrice Int //faceValue  
  quantity  Int

  recipientName  String
  recipientEmail String?

  itemFee Int? // 
  options Json? // an value for high or low res printing or an array of crns for loading cards

  recipientAddress      String?
  recipientAddressLine2 String?
  recipientSuburb       String?
  recipientCity         String?
  recipientPostCode     String?
  recipientCountry      String?

  externalBatchId String? // I2C
  deliveryBatchId String? @db.VarChar(40)

  deliveryRecipient Boolean @default(false) //who the parcel is addresssed to

  loadingFee         Int?
  digitalFee         Int?
  discount           Int? // loadingFeeDiscount + digitalFeeDiscount
  loadingFeeDiscount Int?
  digitalFeeDiscount Int?

  lockCodeEmailSent Boolean @default(false)

  extraEmbossingLine String? @db.VarChar(25) // I2C
  giftStationMessage String? @db.VarChar(500)

  lineNumber Int? // epay purposes only for epay report

  customerReference String? //for epay client Above and Beyond

  //relations
  productOrderId String
  productOrder   ProductOrder @relation(fields: [productOrderId], references: [id], onDelete: Cascade)

  product   Product @relation(fields: [productId], references: [id])
  productId String

  cardItems ProductOrderItemCard[]
  error     String?
}

model ProductOrderItemCard {
  id                          String @id @default(uuid())
  externalCardReferenceNumber String // I2C

  lockCode String?

  createdAt          DateTime  @default(now())
  activated          Boolean   @default(false)
  activatedAt        DateTime?
  activateError      String?
  blocked            Boolean   @default(false)
  blockedAt          DateTime?
  blockError         String?
  lockCodeSet        Boolean   @default(false)
  lockCodeSetAt      DateTime?
  lockCodeError      String?
  fundsLoaded        Boolean   @default(false)
  unitPriceInCents   Int?
  usedInFundingOrder Boolean   @default(false)
  error              String?

  virtualCardSendCount Int              @default(0)
  virtualCardUpdatedAt DateTime?
  //relations
  productOrderItemId   String
  productOrderItem     ProductOrderItem @relation(fields: [productOrderItemId], references: [id], onDelete: Cascade)

  fundingProductOrderItem   FundingProductOrderItem[]
  ReissueTransactionHistory ReissueTransactionHistory[]
}

model ReissueTransactionHistory {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())

  currentProxyNumber String // externalCardReferenceNumber
  newProxyNumber     String // externalCardReferenceNumber

  sequenceNumber Int

  currentRecipientEmail String
  newRecipientEmail     String

  //relations
  createdByUserId        String
  createdByUser          User   @relation(fields: [createdByUserId], references: [id])
  productOrderItemCardId String

  productOrderItemCard ProductOrderItemCard @relation(fields: [productOrderItemCardId], references: [id])
}

model FundingProductOrderItem {
  id                          String @id @default(uuid())
  externalCardReferenceNumber String

  lockCode String

  createdAt DateTime @default(now())

  activated   Boolean   @default(false)
  activatedAt DateTime?

  blocked   Boolean   @default(false)
  blockedAt DateTime?

  lockCodeSet   Boolean   @default(false)
  lockCodeSetAt DateTime?
  lockCodeError String?

  fundsLoaded   Boolean   @default(false)
  fundsLoadedAt DateTime?

  externalBatchId String? // I2C

  unitPriceInCents   Int // face value
  itemFee            Int? // 
  loadingFee         Int?
  digitalFee         Int?
  discount           Int? // loadingFeeDiscount + digitalFeeDiscount
  loadingFeeDiscount Int?
  digitalFeeDiscount Int?

  recipientEmail String?

  lockCodeEmailSent Boolean @default(false)

  lineNumber     Int? // epay purposes only for epay report
  //relations
  productOrderId String
  productOrder   ProductOrder @relation(fields: [productOrderId], references: [id], onDelete: Cascade)
  productId      String
  product        Product      @relation(fields: [productId], references: [id])
  error          String?

  productOrderItemCardId String?
  productOrderItemCard   ProductOrderItemCard? @relation(fields: [productOrderItemCardId], references: [id])
}

model AuditTrail {
  id           String   @id @default(uuid())
  timestamp    DateTime @default(now())
  schema       String
  tableName    String
  primaryKey   String
  secondaryKey String?
  tertiaryKey  String?
  action       Json

  //relations
  user   User?   @relation(fields: [userId], references: [id])
  userId String?
}

model CustomProductOrder {
  id                   String      @id @default(uuid())
  orderStatus          OrderStatus @default(PENDING)
  orderNumber          String?     @unique
  orderIncrementNumber Int         @default(autoincrement())

  paymentMethod            PaymentMethod?
  windcaveSessionId        String?
  windcaveSessionConfirmed Boolean        @default(false)
  purchaseOrderNumber      String?
  billingAddress           String?
  city                     String?
  country                  String?
  postCode                 String?
  gstNumber                String?

  discountTotal Int?
  logoUploadFee Int?
  creditCardFee Int?

  gstAmount  Int?
  orderTotal Int?

  createdAt   DateTime  @default(now())
  submittedAt DateTime?
  paymentDate DateTime?

  releasedBy String?

  createdByLiveAgent Boolean @default(false)
  authorizedByName   String?
  authorizedByEmail  String?

  // delivery address for sample cards
  deliveryRecipientName String?
  deliveryAddressLine1  String?
  deliveryAddressLine2  String?
  deliverySuburb        String?
  deliveryCity          String?
  deliveryPostcode      String?

  //relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])

  userId String
  user   User   @relation("user", fields: [userId], references: [id])

  termsAndConditionsVersion        String?
  termsAndConditionsAcceptedBy     String?
  termsAndConditionsAcceptedByUser User?   @relation("termsAndConditionsAcceptedByUser", fields: [termsAndConditionsAcceptedBy], references: [id])

  error            String?
  externalBatchId1 String?
  externalBatchId2 String?

  floatFundsTransaction FloatFundsTransaction?
  productId             String
  product               Product                @relation(fields: [productId], references: [id])
}

model CustomProductOrderItem {
  id         String     @id @default(uuid())
  designType DesignType @default(PREZZY) // to differentiate between stock and custom product set up prices. 

  productCode String

  unitPrice Int
  discount  Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model OneTimePassword {
  userId         String   @id
  otp            String
  phoneNumber    String
  verified       Boolean  @default(false)
  createdAt      DateTime
  failedAttempts Int      @default(0)
}

model Discount {
  id                       String @id @default(uuid())
  loadingFee               Int?   @default(0)
  digitalFee               Int?   @default(0)
  shippingFeeDiscountCents Int?   @default(0)

  expiryDate DateTime? //not used 

  //relations
  organizationId String       @unique
  organization   Organization @relation(fields: [organizationId], references: [id])
}

enum ProductOrderNoteStatus {
  GENERAL
  CANCELLED
  ON_HOLD
  ERROR
}

model ProductOrderNote {
  id String @id @default(uuid())

  title   String?
  message String
  status  ProductOrderNoteStatus? @default(GENERAL)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  //relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  productOrderId String
  productOrder   ProductOrder @relation(fields: [productOrderId], references: [id], onDelete: Cascade)
}

model Address {
  id           String  @id @default(uuid())
  address      String
  addressLine2 String?
  city         String
  suburb       String
  postCode     String
  country      String?
  isDefault    Boolean @default(false)

  //relations
  userId String
  user   User   @relation(fields: [userId], references: [id])
}

enum OrderProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  DEFERRED
}

model OrderProcessingQueue {
  queueId        Int                   @id @default(autoincrement())
  orderNumber    String                @unique
  productOrderId String                @unique
  dateCreated    DateTime              @default(now())
  dateStarted    DateTime?
  dateCompleted  DateTime?
  lastUpdated    DateTime              @updatedAt
  error          String?
  status         OrderProcessingStatus @default(PENDING)

  productOrder ProductOrder @relation(fields: [productOrderId], references: [id], onDelete: Cascade)

  @@index([status])
}

model ProcessingLock {
  id           String   @id
  isProcessing Boolean  @default(false)
  lastUpdated  DateTime @updatedAt
}

enum ReportType {
  ORDER_DETAIL
  CRN_ORDER
  PRODUCT_ORDER
  CUSTOMER_DETAIL
}

enum ReportJobStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

model ReportJob {
  id          String          @id @default(uuid())
  type        ReportType
  status      ReportJobStatus
  progress    Float           @default(0)
  filters     Json? // Store report-specific filters
  startDate   DateTime
  endDate     DateTime
  createdAt   DateTime        @default(now())
  completedAt DateTime?
  filename    String? // Final report location
  error       String?
  userId      String // Who requested the report
}

model GlobalDiscount {
  id Int @id @default(autoincrement())

  loadingFee       Int @default(0)
  digitalFee       Int @default(0)
  urbanShipping    Int @default(0)
  ruralShipping    Int @default(0)
  createCard       Int @default(0) // For custom card creation
  stockCard        Int @default(0) // For stock card creation
  highResStockCard Int @default(0)
  lowResStockCard  Int @default(0)

  lastUpdatedAt DateTime @updatedAt

  updatedByUserId String?
  updatedByUser   User?   @relation(fields: [updatedByUserId], references: [id])
}

enum FloatFundsTransactionType {
  DEBIT
  CREDIT
}

model FloatFundsTransaction {
  id                        Int                       @id @default(autoincrement())
  orgId                     String
  customerCode              String
  amount                    Int
  balanceAfterTransaction   Int
  floatFundsTransactionType FloatFundsTransactionType
  transactionType           String
  orderId                   String?                   @unique
  customProductOrderId      String?                   @unique
  orderNumber               String?
  description               String?
  note                      String?
  bankAccountName           String?
  bankAccountNumber         String?
  transactionDate           DateTime

  createdBy     String
  createdByUser User   @relation(fields: [createdBy], references: [id])

  productOrder       ProductOrder?       @relation(fields: [orderId], references: [id], onDelete: Cascade)
  customProductOrder CustomProductOrder? @relation(fields: [customProductOrderId], references: [id], onDelete: Cascade)
}

model OrgFloatFund {
  orgId           String   @unique
  customerCode    String   @unique
  currentBalance  Int
  lastUpdatedDate DateTime
  isActive        Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  updatedBy       String
}
