-- AlterEnum
ALTER TYPE "ProductOrderNoteStatus" ADD VALUE 'ERROR';

-- DropForeignKey
ALTER TABLE "ProductOrderNote" DROP CONSTRAINT "ProductOrderNote_userId_fkey";

-- AlterTable
ALTER TABLE "ProductOrderNote" ALTER COLUMN "userId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "ProductOrderNote" ADD CONSTRAINT "ProductOrderNote_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
