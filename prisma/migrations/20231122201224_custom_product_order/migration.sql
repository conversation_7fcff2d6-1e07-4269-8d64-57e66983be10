/*
  Warnings:

  - You are about to drop the `CreateCardOrder` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `CreateCardProduct` table. If the table is not empty, all the data it contains will be lost.

*/

-- CreateTable
CREATE TABLE "CustomProductOrder" (
    "id" TEXT NOT NULL,
    "orderStatus" "OrderStatus" NOT NULL DEFAULT 'PENDING',
    "orderNumber" TEXT,
    "orderIncrementNumber" SERIAL NOT NULL,
    "paymentMethod" "PaymentMethod",
    "windcaveSessionId" TEXT,
    "purchaseOrderNumber" TEXT,
    "billingAddress" TEXT,
    "city" TEXT,
    "country" TEXT,
    "postCode" TEXT,
    "gstNumber" TEXT,
    "discountTotal" INTEGER,
    "logoUploadFee" INTEGER,
    "gstAmount" INTEGER,
    "orderTotal" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "submittedAt" TIMESTAMP(3),
    "organizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,

    CONSTRAINT "CustomProductOrder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CustomProductOrderItem" (
    "id" TEXT NOT NULL,
    "productCode" TEXT NOT NULL,
    "unitPrice" INTEGER NOT NULL,
    "discount" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CustomProductOrderItem_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CustomProductOrder_orderNumber_key" ON "CustomProductOrder"("orderNumber");

-- AddForeignKey
ALTER TABLE "CustomProductOrder" ADD CONSTRAINT "CustomProductOrder_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomProductOrder" ADD CONSTRAINT "CustomProductOrder_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CustomProductOrder" ADD CONSTRAINT "CustomProductOrder_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
