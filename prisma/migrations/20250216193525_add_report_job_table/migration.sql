-- CreateEnum
CREATE TYPE "ReportType" AS ENUM ('OR<PERSON><PERSON>_DETAIL', 'CRN_ORDER', 'PRODUCT_ORDER');

-- CreateEnum
CREATE TYPE "ReportJobStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');

-- CreateTable
CREATE TABLE "ReportJob" (
    "id" TEXT NOT NULL,
    "type" "ReportType" NOT NULL,
    "status" "ReportJobStatus" NOT NULL,
    "progress" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "filters" JSONB,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "filename" TEXT,
    "error" TEXT,
    "userId" TEXT NOT NULL,

    CONSTRAINT "ReportJob_pkey" PRIMARY KEY ("id")
);
