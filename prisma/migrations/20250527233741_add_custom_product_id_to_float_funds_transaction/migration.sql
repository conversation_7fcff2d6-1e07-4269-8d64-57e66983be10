/*
  Warnings:

  - A unique constraint covering the columns `[customProductOrderId]` on the table `FloatFundsTransaction` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "FloatFundsTransaction" ADD COLUMN     "customProductOrderId" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "FloatFundsTransaction_customProductOrderId_key" ON "FloatFundsTransaction"("customProductOrderId");

-- AddForeignKey
ALTER TABLE "FloatFundsTransaction" ADD CONSTRAINT "FloatFundsTransaction_customProductOrderId_fkey" FOREIGN KEY ("customProductOrderId") REFERENCES "CustomProductOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;
