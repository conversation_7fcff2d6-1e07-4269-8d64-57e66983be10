/*
  Warnings:

  - The values [DEPOSIT,WIT<PERSON><PERSON><PERSON>] on the enum `FloatFundsTransactionType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "FloatFundsTransactionType_new" AS ENUM ('DEBIT', 'CREDIT');
ALTER TABLE "FloatFundsTransaction" ALTER COLUMN "floatFundsTransactionType" TYPE "FloatFundsTransactionType_new" USING ("floatFundsTransactionType"::text::"FloatFundsTransactionType_new");
ALTER TYPE "FloatFundsTransactionType" RENAME TO "FloatFundsTransactionType_old";
ALTER TYPE "FloatFundsTransactionType_new" RENAME TO "FloatFundsTransactionType";
DROP TYPE "FloatFundsTransactionType_old";
COMMIT;
