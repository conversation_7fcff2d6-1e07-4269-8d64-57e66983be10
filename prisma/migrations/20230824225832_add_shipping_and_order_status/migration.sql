-- CreateEnum
CREATE TYPE "OrderStatus" AS ENUM ('PENDING', 'PROCESSING', 'CONFIRMED', 'COMPLETED', 'DELIVERED', 'SHIPPED', 'CANCELLED');

-- AlterTable
ALTER TABLE "Order" ADD COLUMN     "discountTotal" DECIMAL(65,30),
ADD COLUMN     "orderStatus" "OrderStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "shippingTotal" DECIMAL(65,30);

-- AlterTable
ALTER TABLE "OrderItem" ADD COLUMN     "shippingCost" DECIMAL(65,30);
