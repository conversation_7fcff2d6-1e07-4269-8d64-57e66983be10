-- CreateTable
CREATE TABLE "CPartnerOrg" (
    "id" TEXT NOT NULL,
    "stakeholderId" TEXT NOT NULL,

    CONSTRAINT "CPartnerOrg_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CPartnerAccount" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "cPartnerOrgId" TEXT NOT NULL,

    CONSTRAINT "CPartnerAccount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CPartnerLogo" (
    "id" TEXT NOT NULL,
    "logoUrl" TEXT NOT NULL,
    "externalCardLogoId" TEXT NOT NULL,
    "cPartnerOrgId" TEXT NOT NULL,

    CONSTRAINT "CPartnerLogo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationCPartnerJoinTable" (
    "id" TEXT NOT NULL,
    "cPartnerAccountId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "OrganizationCPartnerJoinTable_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrganizationCPartnerLogoJoinTable" (
    "id" TEXT NOT NULL,
    "created" BOOLEAN NOT NULL DEFAULT false,
    "cPartnerLogoId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "OrganizationCPartnerLogoJoinTable_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CPartnerOrg_stakeholderId_key" ON "CPartnerOrg"("stakeholderId");

-- CreateIndex
CREATE UNIQUE INDEX "OrganizationCPartnerJoinTable_cPartnerAccountId_organizatio_key" ON "OrganizationCPartnerJoinTable"("cPartnerAccountId", "organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "OrganizationCPartnerLogoJoinTable_cPartnerLogoId_organizati_key" ON "OrganizationCPartnerLogoJoinTable"("cPartnerLogoId", "organizationId");

-- AddForeignKey
ALTER TABLE "CPartnerAccount" ADD CONSTRAINT "CPartnerAccount_cPartnerOrgId_fkey" FOREIGN KEY ("cPartnerOrgId") REFERENCES "CPartnerOrg"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CPartnerLogo" ADD CONSTRAINT "CPartnerLogo_cPartnerOrgId_fkey" FOREIGN KEY ("cPartnerOrgId") REFERENCES "CPartnerOrg"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationCPartnerJoinTable" ADD CONSTRAINT "OrganizationCPartnerJoinTable_cPartnerAccountId_fkey" FOREIGN KEY ("cPartnerAccountId") REFERENCES "CPartnerAccount"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationCPartnerJoinTable" ADD CONSTRAINT "OrganizationCPartnerJoinTable_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationCPartnerLogoJoinTable" ADD CONSTRAINT "OrganizationCPartnerLogoJoinTable_cPartnerLogoId_fkey" FOREIGN KEY ("cPartnerLogoId") REFERENCES "CPartnerLogo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrganizationCPartnerLogoJoinTable" ADD CONSTRAINT "OrganizationCPartnerLogoJoinTable_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
