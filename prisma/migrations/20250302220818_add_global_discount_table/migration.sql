-- CreateTable
CREATE TABLE "GlobalDiscount" (
    "id" SERIAL NOT NULL,
    "loadingFee" INTEGER NOT NULL DEFAULT 0,
    "digitalFee" INTEGER NOT NULL DEFAULT 0,
    "urbanShipping" INTEGER NOT NULL DEFAULT 0,
    "ruralShipping" INTEGER NOT NULL DEFAULT 0,
    "createCard" INTEGER NOT NULL DEFAULT 0,
    "highResStockCard" INTEGER NOT NULL DEFAULT 0,
    "lowResStockCard" INTEGER NOT NULL DEFAULT 0,
    "lastUpdatedAt" TIMESTAMP(3) NOT NULL,
    "updatedByUserId" TEXT,

    CONSTRAINT "GlobalDiscount_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "GlobalDiscount" ADD CONSTRAINT "GlobalDiscount_updatedByUserId_fkey" FOREIGN KEY ("updatedByUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Insert a new entry into the GlobalDiscount table
INSERT INTO "GlobalDiscount" (
  "loadingFee",
  "digitalFee",
  "urbanShipping",
  "ruralShipping",
  "createCard",
  "highResStockCard",
  "lowResStockCard",
  "lastUpdatedAt",
  "updatedByUserId"
)
VALUES (
  300,   -- loadingFee default value
  0,   -- digitalFee default value
  0,   -- urbanShipping default value
  0,   -- ruralShipping default value
  0,   -- createCard default value
  50,   -- highResStockCard default value
  50,   -- lowResStockCard default value
  CURRENT_TIMESTAMP,  -- lastUpdatedAt set to current time
  NULL  -- updatedByUserId is optional, set to NULL
);