/*
  Warnings:

  - You are about to alter the column `digitalFee` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `gstAmount` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `orderTotal` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `discountTotal` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `shippingTotal` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `loadingFeeTotal` on the `Order` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `unitPrice` on the `OrderItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `shippingCost` on the `OrderItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `discount` on the `OrderItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to alter the column `loadingFee` on the `OrderItem` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.

*/
-- AlterTable
ALTER TABLE "Order" ALTER COLUMN "digitalFee" SET DATA TYPE INTEGER,
ALTER COLUMN "gstAmount" SET DATA TYPE INTEGER,
ALTER COLUMN "orderTotal" SET DATA TYPE INTEGER,
ALTER COLUMN "discountTotal" SET DATA TYPE INTEGER,
ALTER COLUMN "shippingTotal" SET DATA TYPE INTEGER,
ALTER COLUMN "loadingFeeTotal" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "OrderItem" ALTER COLUMN "unitPrice" SET DATA TYPE INTEGER,
ALTER COLUMN "shippingCost" SET DATA TYPE INTEGER,
ALTER COLUMN "discount" SET DATA TYPE INTEGER,
ALTER COLUMN "loadingFee" SET DATA TYPE INTEGER;
