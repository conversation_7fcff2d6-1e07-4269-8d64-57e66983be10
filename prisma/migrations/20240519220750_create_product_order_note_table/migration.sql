-- CreateEnum
CREATE TYPE "ProductOrderNoteStatus" AS ENUM ('GENERAL', 'CANCELLED', 'ON_HOLD');

-- AlterEnum
ALTER TYPE "OrderStatus" ADD VALUE 'ON_HOLD';

-- CreateTable
CREATE TABLE "ProductOrderNote" (
    "id" TEXT NOT NULL,
    "title" TEXT,
    "message" TEXT NOT NULL,
    "status" "ProductOrderNoteStatus" DEFAULT 'GENERAL',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "productOrderId" TEXT NOT NULL,

    CONSTRAINT "ProductOrderNote_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ProductOrderNote" ADD CONSTRAINT "ProductOrderNote_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddF<PERSON><PERSON>Key
ALTER TABLE "ProductOrderNote" ADD CONSTRAINT "ProductOrderNote_productOrderId_fkey" FOREIGN KEY ("productOrderId") REFERENCES "ProductOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;
