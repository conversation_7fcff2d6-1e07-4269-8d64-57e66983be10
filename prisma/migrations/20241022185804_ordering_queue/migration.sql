-- CreateEnum
CREATE TYPE "OrderProcessingStatus" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED');

-- CreateTable
CREATE TABLE "OrderProcessingQueue" (
    "queueId" SERIAL NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "productOrderId" TEXT NOT NULL,
    "dateCreated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dateStarted" TIMESTAMP(3),
    "dateCompleted" TIMESTAMP(3),
    "lastUpdated" TIMESTAMP(3) NOT NULL,
    "error" TEXT,
    "status" "OrderProcessingStatus" NOT NULL DEFAULT 'PENDING',

    CONSTRAINT "OrderProcessingQueue_pkey" PRIMARY KEY ("queueId")
);

-- CreateTable
CREATE TABLE "ProcessingLock" (
    "id" TEXT NOT NULL,
    "isProcessing" BOOLEAN NOT NULL DEFAULT false,
    "lastUpdated" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProcessingLock_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "OrderProcessingQueue_orderNumber_key" ON "OrderProcessingQueue"("orderNumber");

-- CreateIndex
CREATE UNIQUE INDEX "OrderProcessingQueue_productOrderId_key" ON "OrderProcessingQueue"("productOrderId");

-- CreateIndex
CREATE INDEX "OrderProcessingQueue_status_idx" ON "OrderProcessingQueue"("status");

-- AddForeignKey
ALTER TABLE "OrderProcessingQueue" ADD CONSTRAINT "OrderProcessingQueue_productOrderId_fkey" FOREIGN KEY ("productOrderId") REFERENCES "ProductOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;
