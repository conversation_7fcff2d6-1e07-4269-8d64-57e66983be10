/*
  Warnings:

  - You are about to drop the column `emailAddress` on the `ProductOrder` table. All the data in the column will be lost.
  - You are about to drop the column `email` on the `ProductOrderItemCard` table. All the data in the column will be lost.
  - Made the column `externalCardReferenceNumber` on table `ProductOrderItemCard` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "ProductOrder" DROP COLUMN "emailAddress",
ADD COLUMN     "lockCodeEmailSent" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "subTotal" INTEGER,
ADD COLUMN     "totalQuantity" INTEGER;

-- AlterTable
ALTER TABLE "ProductOrderItem" ADD COLUMN     "lockCodeEmailSent" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "ProductOrderItemCard" DROP COLUMN "email",
ALTER COLUMN "externalCardReferenceNumber" SET NOT NULL;
