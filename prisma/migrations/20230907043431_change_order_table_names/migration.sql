/*
  Warnings:

  - You are about to drop the `Order` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `OrderItem` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "PrezzyCardType" AS ENUM ('VIRTUAL', 'PHYSICAL');

-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_userId_fkey";

-- DropForeignKey
ALTER TABLE "OrderItem" DROP CONSTRAINT "OrderItem_orderId_fkey";

-- DropForeignKey
ALTER TABLE "OrderItem" DROP CONSTRAINT "OrderItem_productId_fkey";

-- DropForeignKey
ALTER TABLE "OrderItemCard" DROP CONSTRAINT "OrderItemCard_orderItemId_fkey";

-- DropTable
DROP TABLE "Order";

-- DropTable
DROP TABLE "OrderItem";

-- CreateTable
CREATE TABLE "ProductOrder" (
    "id" TEXT NOT NULL,
    "orderStatus" "OrderStatus" NOT NULL DEFAULT 'PENDING',
    "orderNumber" TEXT,
    "orderIncrementNumber" SERIAL NOT NULL,
    "paymentMethod" "PaymentMethod",
    "purchaseOrderNumber" TEXT,
    "billingAddress" TEXT,
    "city" TEXT,
    "country" TEXT,
    "postCode" TEXT,
    "gstNumber" TEXT,
    "emailAddress" TEXT,
    "orderTotal" INTEGER,
    "digitalFeeTotal" INTEGER,
    "shippingTotal" INTEGER,
    "discountTotal" INTEGER,
    "loadingFeeTotal" INTEGER,
    "gstAmount" INTEGER,
    "extraEmbossingLine" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "organizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "ProductOrder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductOrderItem" (
    "id" TEXT NOT NULL,
    "cardType" "PrezzyCardType" NOT NULL,
    "productCode" TEXT NOT NULL,
    "recipientName" TEXT NOT NULL,
    "unitPrice" INTEGER NOT NULL,
    "quantity" INTEGER NOT NULL,
    "lockCode" TEXT,
    "recipientEmail" TEXT,
    "recipientAddressLine1" TEXT,
    "recipientAddressLine2" TEXT,
    "recipientSuburb" TEXT,
    "recipientCity" TEXT,
    "recipientPostCode" TEXT,
    "recipientCountry" TEXT,
    "externalBatchId" TEXT,
    "shippingCost" INTEGER,
    "loadingFee" INTEGER,
    "discount" INTEGER,
    "digitalFee" INTEGER,
    "extraEmbossingLine" TEXT,
    "orderId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,

    CONSTRAINT "ProductOrderItem_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProductOrder_orderNumber_key" ON "ProductOrder"("orderNumber");

-- AddForeignKey
ALTER TABLE "ProductOrder" ADD CONSTRAINT "ProductOrder_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductOrder" ADD CONSTRAINT "ProductOrder_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductOrderItem" ADD CONSTRAINT "ProductOrderItem_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "ProductOrder"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductOrderItem" ADD CONSTRAINT "ProductOrderItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItemCard" ADD CONSTRAINT "OrderItemCard_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "ProductOrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
