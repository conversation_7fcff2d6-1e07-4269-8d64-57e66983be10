-- CreateEnum
CREATE TYPE "FloatFundsTransactionType" AS ENUM ('DEPOSIT', 'WITHDRAW', 'DEBIT', 'CREDIT');

-- CreateTable
CREATE TABLE "FloatFundsTransaction" (
    "id" SERIAL NOT NULL,
    "orgId" TEXT NOT NULL,
    "customerCode" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "balanceAfterTransaction" INTEGER NOT NULL,
    "floatFundsTransactionType" "FloatFundsTransactionType" NOT NULL,
    "transactionType" TEXT NOT NULL,
    "orderId" TEXT,
    "orderNumber" TEXT,
    "description" TEXT,
    "note" TEXT,
    "createdBy" TEXT NOT NULL,
    "transactionDate" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FloatFundsTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrgFloatFund" (
    "orgId" TEXT NOT NULL,
    "customerCode" TEXT NOT NULL,
    "currentBalance" INTEGER NOT NULL,
    "lastUpdatedDate" TIMESTAMP(3) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "updatedBy" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "OrgFloatFund_orgId_key" ON "OrgFloatFund"("orgId");

-- CreateIndex
CREATE UNIQUE INDEX "OrgFloatFund_customerCode_key" ON "OrgFloatFund"("customerCode");
