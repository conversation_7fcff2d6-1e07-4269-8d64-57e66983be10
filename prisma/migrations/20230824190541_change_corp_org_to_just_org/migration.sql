/*
  Warnings:

  - You are about to drop the column `corporateOrganizationId` on the `CardLogo` table. All the data in the column will be lost.
  - You are about to drop the column `corporateOrganizationId` on the `CorporateApplication` table. All the data in the column will be lost.
  - You are about to drop the column `corporateOrganizationId` on the `Order` table. All the data in the column will be lost.
  - You are about to drop the column `corporateOrganizationId` on the `Product` table. All the data in the column will be lost.
  - You are about to drop the column `corporateOrganizationId` on the `User` table. All the data in the column will be lost.
  - You are about to drop the `CorporateOrganization` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `organizationId` to the `Order` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "CardLogo" DROP CONSTRAINT "CardLogo_corporateOrganizationId_fkey";

-- DropForeignKey
ALTER TABLE "CorporateApplication" DROP CONSTRAINT "CorporateApplication_corporateOrganizationId_fkey";

-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_corporateOrganizationId_fkey";

-- DropForeignKey
ALTER TABLE "Product" DROP CONSTRAINT "Product_corporateOrganizationId_fkey";

-- DropForeignKey
ALTER TABLE "User" DROP CONSTRAINT "User_corporateOrganizationId_fkey";

-- AlterTable
ALTER TABLE "CardLogo" DROP COLUMN "corporateOrganizationId",
ADD COLUMN     "organizationId" TEXT;

-- AlterTable
ALTER TABLE "CorporateApplication" DROP COLUMN "corporateOrganizationId",
ADD COLUMN     "organizationId" TEXT;

-- AlterTable
ALTER TABLE "Order" DROP COLUMN "corporateOrganizationId",
ADD COLUMN     "organizationId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Product" DROP COLUMN "corporateOrganizationId",
ADD COLUMN     "organizationId" TEXT;

-- AlterTable
ALTER TABLE "User" DROP COLUMN "corporateOrganizationId",
ADD COLUMN     "organizationId" TEXT;

-- DropTable
DROP TABLE "CorporateOrganization";

-- CreateTable
CREATE TABLE "Organization" (
    "id" TEXT NOT NULL,
    "type" "OrganizationType" NOT NULL,
    "name" TEXT NOT NULL,
    "tradingName" TEXT,
    "nzbn" TEXT NOT NULL,
    "gstNumber" TEXT,
    "address" TEXT,
    "city" TEXT,
    "region" TEXT,
    "zip" TEXT,
    "country" TEXT,
    "principalPlace" TEXT,
    "natureAndPurpose" TEXT,
    "phone" TEXT,
    "cPartnerIds" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Organization_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CorporateApplication" ADD CONSTRAINT "CorporateApplication_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CardLogo" ADD CONSTRAINT "CardLogo_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
