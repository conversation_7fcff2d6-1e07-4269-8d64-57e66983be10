/*
  Warnings:

  - You are about to drop the column `loadingFee` on the `CardDesign` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `CardDesign` table. All the data in the column will be lost.
  - You are about to drop the column `cardType` on the `ProductOrderItem` table. All the data in the column will be lost.
  - Added the required column `loadingFee` to the `Product` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "CardType" AS ENUM ('PHYSICAL', 'VIRTUAL');

-- CreateEnum
CREATE TYPE "DeliveryMethod" AS ENUM ('PICKUP', 'COURIER', 'EMAIL', 'SMS');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "ProductType" AS ENUM ('PREZZY', 'GIFT_STATION', 'CUSTOM');

-- AlterTable
ALTER TABLE "CardDesign" DROP COLUMN "loadingFee",
DROP COLUMN "name";

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "cardTypes" "CardType"[],
ADD COLUMN     "categories" TEXT[],
ADD COLUMN     "conditions" TEXT,
ADD COLUMN     "deliveryMethods" "DeliveryMethod"[],
ADD COLUMN     "details" TEXT,
ADD COLUMN     "digitalFee" INTEGER,
ADD COLUMN     "fixedValues" INTEGER[],
ADD COLUMN     "loadingFee" INTEGER NOT NULL,
ADD COLUMN     "maxValue" INTEGER,
ADD COLUMN     "minValue" INTEGER,
ADD COLUMN     "redeem" TEXT,
ADD COLUMN     "type" "ProductType" NOT NULL DEFAULT 'GIFT_STATION';

-- AlterTable
ALTER TABLE "ProductOrder" ADD COLUMN     "submittedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "ProductOrderItem" DROP COLUMN "cardType";
