/*
  Warnings:

  - You are about to drop the column `lockCode` on the `ProductOrderItem` table. All the data in the column will be lost.
  - You are about to drop the `OrderItemCard` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "OrderItemCard" DROP CONSTRAINT "OrderItemCard_orderItemId_fkey";

-- AlterTable
ALTER TABLE "ProductOrderItem" DROP COLUMN "lockCode";

-- DropTable
DROP TABLE "OrderItemCard";

-- CreateTable
CREATE TABLE "ProductOrderItemCard" (
    "id" TEXT NOT NULL,
    "externalCardReferenceNumber" TEXT,
    "lockCode" TEXT,
    "isActivated" BOOLEAN NOT NULL DEFAULT false,
    "email" TEXT,
    "orderItemId" TEXT NOT NULL,

    CONSTRAINT "ProductOrderItemCard_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ProductOrderItemCard" ADD CONSTRAINT "ProductOrderItemCard_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "ProductOrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
