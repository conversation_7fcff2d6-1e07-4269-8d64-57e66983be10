-- CreateEnum
CREATE TYPE "OrderType" AS ENUM ('PRODUCT', 'STOCK', 'LOAD_FUNDS');

-- AlterTable
ALTER TABLE "CustomProductOrderItem" ADD COLUMN     "designType" "DesignType" NOT NULL DEFAULT 'PREZZY';

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "option1Fee" INTEGER,
ADD COLUMN     "option2Fee" INTEGER;

-- AlterTable
ALTER TABLE "ProductOrder" ADD COLUMN     "orderType" "OrderType" NOT NULL DEFAULT 'PRODUCT';

-- AlterTable
ALTER TABLE "ProductOrderItem" ADD COLUMN     "itemFee" INTEGER,
ADD COLUMN     "options" JSONB;
