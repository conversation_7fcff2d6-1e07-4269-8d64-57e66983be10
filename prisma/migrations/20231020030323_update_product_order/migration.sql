/*
  Warnings:

  - You are about to drop the column `extraEmbossingLine` on the `ProductOrder` table. All the data in the column will be lost.
  - You are about to drop the column `orderId` on the `ProductOrderItem` table. All the data in the column will be lost.
  - You are about to drop the column `recipientAddressLine1` on the `ProductOrderItem` table. All the data in the column will be lost.
  - You are about to drop the column `recipientAddressLine2` on the `ProductOrderItem` table. All the data in the column will be lost.
  - You are about to drop the column `shippingCost` on the `ProductOrderItem` table. All the data in the column will be lost.
  - You are about to alter the column `extraEmbossingLine` on the `ProductOrderItem` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(25)`.
  - You are about to drop the column `orderItemId` on the `ProductOrderItemCard` table. All the data in the column will be lost.
  - Added the required column `deliveryMethod` to the `ProductOrderItem` table without a default value. This is not possible if the table is not empty.
  - Added the required column `productOrderId` to the `ProductOrderItem` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `productCode` on the `ProductOrderItem` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `productOrderItemId` to the `ProductOrderItemCard` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "ProductOrderItem" DROP CONSTRAINT "ProductOrderItem_orderId_fkey";

-- DropForeignKey
ALTER TABLE "ProductOrderItemCard" DROP CONSTRAINT "ProductOrderItemCard_orderItemId_fkey";

-- AlterTable
ALTER TABLE "ProductOrder" DROP COLUMN "extraEmbossingLine";

-- AlterTable
ALTER TABLE "ProductOrderItem" DROP COLUMN "orderId",
DROP COLUMN "recipientAddressLine1",
DROP COLUMN "recipientAddressLine2",
DROP COLUMN "shippingCost",
ADD COLUMN     "deliveryBatchId" VARCHAR(40),
ADD COLUMN     "deliveryMethod" "DeliveryMethod" NOT NULL,
ADD COLUMN     "giftStationMessage" VARCHAR(500),
ADD COLUMN     "productOrderId" TEXT NOT NULL,
ADD COLUMN     "recipientAddress" TEXT,
DROP COLUMN "productCode",
ADD COLUMN     "productCode" INTEGER NOT NULL,
ALTER COLUMN "extraEmbossingLine" SET DATA TYPE VARCHAR(25);

-- AlterTable
ALTER TABLE "ProductOrderItemCard" DROP COLUMN "orderItemId",
ADD COLUMN     "productOrderItemId" TEXT NOT NULL;

-- DropEnum
DROP TYPE "PrezzyCardType";

-- AddForeignKey
ALTER TABLE "ProductOrderItem" ADD CONSTRAINT "ProductOrderItem_productOrderId_fkey" FOREIGN KEY ("productOrderId") REFERENCES "ProductOrder"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductOrderItemCard" ADD CONSTRAINT "ProductOrderItemCard_productOrderItemId_fkey" FOREIGN KEY ("productOrderItemId") REFERENCES "ProductOrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
