-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('ORG_ADMIN', 'ORG_MANAGER', 'ORG_BASIC', 'EPAY_ADMIN', 'EPAY_COSTUMER_SERVICE', 'EPAY_REPORTS', 'EPAY_COMPLIANCE');

-- CreateEnum
CREATE TYPE "UserLoginNotification" AS ENUM ('APPLICATION_CELEBRATION');

-- CreateEnum
CREATE TYPE "OrganizationType" AS ENUM ('LIMITED_LIABILITY_COMPANY', 'SOLE_TRADER', 'OTHER');

-- Create<PERSON>num
CREATE TYPE "CorporateApplicationStatus" AS ENUM ('DRAFT', 'PENDING', 'REVIEWING', 'DECLINED', 'APPROVED');

-- C<PERSON><PERSON>num
CREATE TYPE "IdentityType" AS ENUM ('PASSPORT', 'DRIVER_LICENSE');

-- Create<PERSON>num
CREATE TYPE "ProductStatus" AS ENUM ('PENDING', 'DECLINED', 'APPROVED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "PaymentMethod" AS ENUM ('CREDIT_CARD', 'BANK_TRANSFER');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "role" "UserRole",
    "firstName" TEXT,
    "lastName" TEXT,
    "branch" TEXT,
    "phoneNbr" TEXT,
    "loginNotification" "UserLoginNotification",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastUpdatedAt" TIMESTAMP(3) NOT NULL,
    "corporateOrganizationId" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CorporateApplication" (
    "id" TEXT NOT NULL,
    "organizationType" "OrganizationType" NOT NULL DEFAULT 'LIMITED_LIABILITY_COMPANY',
    "name" TEXT,
    "tradingName" TEXT,
    "nzbn" TEXT,
    "gstNumber" TEXT,
    "address" TEXT,
    "city" TEXT,
    "region" TEXT,
    "zip" TEXT,
    "country" TEXT,
    "principalPlace" TEXT,
    "natureAndPurpose" TEXT,
    "phone" TEXT,
    "status" "CorporateApplicationStatus" NOT NULL DEFAULT 'DRAFT',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "submittedAt" TIMESTAMP(3),
    "userId" TEXT NOT NULL,
    "corporateOrganizationId" TEXT,

    CONSTRAINT "CorporateApplication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CorporateOrganization" (
    "id" TEXT NOT NULL,
    "type" "OrganizationType" NOT NULL,
    "name" TEXT NOT NULL,
    "tradingName" TEXT,
    "nzbn" TEXT NOT NULL,
    "gstNumber" TEXT,
    "address" TEXT,
    "city" TEXT,
    "region" TEXT,
    "zip" TEXT,
    "country" TEXT,
    "principalPlace" TEXT,
    "natureAndPurpose" TEXT,
    "phone" TEXT,
    "cPartnerIds" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CorporateOrganization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Director" (
    "id" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "address" TEXT,
    "city" TEXT,
    "country" TEXT,
    "zip" TEXT,
    "identificationType" "IdentityType",
    "identificationNbr" TEXT,
    "driverLicenseVersion" TEXT,
    "identificationPhotoUrl" TEXT,
    "corporateApplicationId" TEXT,

    CONSTRAINT "Director_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BeneficialOwner" (
    "id" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "dateOfBirth" TIMESTAMP(3),
    "address" TEXT,
    "city" TEXT,
    "country" TEXT,
    "zip" TEXT,
    "identificationType" "IdentityType",
    "identificationNbr" TEXT,
    "driverLicenseVersion" TEXT,
    "identificationPhotoUrl" TEXT,
    "corporateApplicationId" TEXT,

    CONSTRAINT "BeneficialOwner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CorporateApplicationNote" (
    "id" TEXT NOT NULL,
    "title" TEXT,
    "message" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT,
    "corporateApplicationId" TEXT,

    CONSTRAINT "CorporateApplicationNote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CardLogo" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "logoUrl" TEXT NOT NULL,
    "externalCardLogoId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "corporateOrganizationId" TEXT,

    CONSTRAINT "CardLogo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CardDesign" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "cardDesignUrl" TEXT NOT NULL,
    "externalCardDesignId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CardDesign_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Product" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "productCode" SERIAL NOT NULL,
    "isArchived" BOOLEAN NOT NULL DEFAULT false,
    "status" "ProductStatus" NOT NULL,
    "corporateOrganizationId" TEXT,
    "designId" TEXT NOT NULL,
    "logoId" TEXT,

    CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Address" (
    "id" TEXT NOT NULL,
    "line1" TEXT NOT NULL,
    "line2" TEXT,
    "suburb" TEXT,
    "city" TEXT NOT NULL,
    "country" TEXT,
    "postCode" TEXT,
    "isBilling" BOOLEAN NOT NULL,
    "isShipping" BOOLEAN NOT NULL,

    CONSTRAINT "Address_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "corporateOrganizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "orderIncrementNumber" SERIAL NOT NULL,
    "paymentMethod" "PaymentMethod" NOT NULL,
    "billingAddressId" TEXT NOT NULL,
    "gstNumber" TEXT,
    "purchaseOrderNumber" TEXT,
    "promoCode" TEXT,
    "purchaseFee" DECIMAL(65,30),
    "digitalFee" DECIMAL(65,30),
    "loadFee" DECIMAL(65,30),
    "gstAmount" DECIMAL(65,30),
    "invoiceAddress" TEXT,
    "emailAddress" TEXT,

    CONSTRAINT "Order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderItem" (
    "id" TEXT NOT NULL,
    "productCode" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "unitPrice" DECIMAL(65,30) NOT NULL,
    "recipientName" TEXT NOT NULL,
    "recipientEmail" TEXT,
    "recipientAddressLine1" TEXT,
    "recipientAddressLine2" TEXT,
    "recipientSuburb" TEXT,
    "recipientCity" TEXT,
    "recipientPostCode" TEXT,
    "recipientCountry" TEXT,
    "productId" TEXT,

    CONSTRAINT "OrderItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderItemCard" (
    "id" TEXT NOT NULL,
    "externalCardReferenceNumber" TEXT,
    "lockCode" TEXT,
    "hasBeenActivatedByPortal" BOOLEAN NOT NULL,
    "orderItemId" TEXT NOT NULL,

    CONSTRAINT "OrderItemCard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuditTrail" (
    "id" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "schema" TEXT NOT NULL,
    "tableName" TEXT NOT NULL,
    "primaryKey" TEXT NOT NULL,
    "secondaryKey" TEXT,
    "tertiaryKey" TEXT,
    "action" JSONB NOT NULL,
    "userId" TEXT,

    CONSTRAINT "AuditTrail_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Order_orderNumber_key" ON "Order"("orderNumber");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_corporateOrganizationId_fkey" FOREIGN KEY ("corporateOrganizationId") REFERENCES "CorporateOrganization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CorporateApplication" ADD CONSTRAINT "CorporateApplication_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CorporateApplication" ADD CONSTRAINT "CorporateApplication_corporateOrganizationId_fkey" FOREIGN KEY ("corporateOrganizationId") REFERENCES "CorporateOrganization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Director" ADD CONSTRAINT "Director_corporateApplicationId_fkey" FOREIGN KEY ("corporateApplicationId") REFERENCES "CorporateApplication"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BeneficialOwner" ADD CONSTRAINT "BeneficialOwner_corporateApplicationId_fkey" FOREIGN KEY ("corporateApplicationId") REFERENCES "CorporateApplication"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CorporateApplicationNote" ADD CONSTRAINT "CorporateApplicationNote_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CorporateApplicationNote" ADD CONSTRAINT "CorporateApplicationNote_corporateApplicationId_fkey" FOREIGN KEY ("corporateApplicationId") REFERENCES "CorporateApplication"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CardLogo" ADD CONSTRAINT "CardLogo_corporateOrganizationId_fkey" FOREIGN KEY ("corporateOrganizationId") REFERENCES "CorporateOrganization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_corporateOrganizationId_fkey" FOREIGN KEY ("corporateOrganizationId") REFERENCES "CorporateOrganization"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_designId_fkey" FOREIGN KEY ("designId") REFERENCES "CardDesign"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_logoId_fkey" FOREIGN KEY ("logoId") REFERENCES "CardLogo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_corporateOrganizationId_fkey" FOREIGN KEY ("corporateOrganizationId") REFERENCES "CorporateOrganization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_billingAddressId_fkey" FOREIGN KEY ("billingAddressId") REFERENCES "Address"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_id_fkey" FOREIGN KEY ("id") REFERENCES "Order"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItemCard" ADD CONSTRAINT "OrderItemCard_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "OrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditTrail" ADD CONSTRAINT "AuditTrail_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
