-- DropForeign<PERSON>ey
ALTER TABLE "ProductOrderItem" DROP CONSTRAINT "ProductOrderItem_productOrderId_fkey";

-- DropForeignKey
ALTER TABLE "ProductOrderItemCard" DROP CONSTRAINT "ProductOrderItemCard_productOrderItemId_fkey";

-- AddForeignKey
ALTER TABLE "ProductOrderItem" ADD CONSTRAINT "ProductOrderItem_productOrderId_fkey" FOREIGN KEY ("productOrderId") REFERENCES "ProductOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductOrderItemCard" ADD CONSTRAINT "ProductOrderItemCard_productOrderItemId_fkey" FOREIGN KEY ("productOrderItemId") REFERENCES "ProductOrderItem"("id") ON DELETE CASCADE ON UPDATE CASCADE;
