-- CreateTable
CREATE TABLE "FundingProductOrderItem" (
    "id" TEXT NOT NULL,
    "externalCardReferenceNumber" TEXT NOT NULL,
    "lockCode" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "activated" BOOLEAN NOT NULL DEFAULT false,
    "activatedAt" TIMESTAMP(3),
    "blocked" BOOLEAN NOT NULL DEFAULT false,
    "blockedAt" TIMESTAMP(3),
    "lockCodeSet" BOOLEAN NOT NULL DEFAULT false,
    "lockCodeSetAt" TIMESTAMP(3),
    "lockCodeError" TEXT,
    "unitPriceInCents" INTEGER NOT NULL,
    "productOrderId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,

    CONSTRAINT "FundingProductOrderItem_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FundingProductOrderItem_externalCardReferenceNumber_key" ON "FundingProductOrderItem"("externalCardReferenceNumber");

-- AddForeignKey
ALTER TABLE "FundingProductOrderItem" ADD CONSTRAINT "FundingProductOrderItem_productOrderId_fkey" FOREIGN KEY ("productOrderId") REFERENCES "ProductOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FundingProductOrderItem" ADD CONSTRAINT "FundingProductOrderItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
