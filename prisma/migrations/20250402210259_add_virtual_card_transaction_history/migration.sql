-- AlterTable
ALTER TABLE "ProductOrderItemCard" ADD COLUMN     "virtualCardSendCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "virtualCardUpdatedAt" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "ReissueTransactionHistory" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "currentProxyNumber" TEXT NOT NULL,
    "newProxyNumber" TEXT NOT NULL,
    "sequenceNumber" INTEGER NOT NULL,
    "currentRecipientEmail" TEXT NOT NULL,
    "newRecipientEmail" TEXT NOT NULL,
    "createdByUserId" TEXT NOT NULL,
    "productOrderItemCardId" TEXT NOT NULL,

    CONSTRAINT "ReissueTransactionHistory_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ReissueTransactionHistory" ADD CONSTRAINT "ReissueTransactionHistory_createdByUserId_fkey" FOREIGN KEY ("createdByUserId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReissueTransactionHistory" ADD CONSTRAINT "ReissueTransactionHistory_productOrderItemCardId_fkey" FOREIGN KEY ("productOrderItemCardId") REFERENCES "ProductOrderItemCard"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
