/*
  Warnings:

  - You are about to drop the column `orgDiscountCents` on the `Organization` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Organization" DROP COLUMN "orgDiscountCents";

-- CreateTable
CREATE TABLE "Discount" (
    "id" TEXT NOT NULL,
    "loadingFee" INTEGER,
    "digitalFee" INTEGER,
    "expiryDate" TIMESTAMP(3),
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "Discount_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Discount" ADD CONSTRAINT "Discount_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
