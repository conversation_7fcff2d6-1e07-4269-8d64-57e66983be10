/*
  Warnings:

  - A unique constraint covering the columns `[orderId]` on the table `FloatFundsTransaction` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterEnum
ALTER TYPE "OrderType" ADD VALUE 'FLOAT_FUNDS';

-- CreateIndex
CREATE UNIQUE INDEX "FloatFundsTransaction_orderId_key" ON "FloatFundsTransaction"("orderId");

-- AddForeignKey
ALTER TABLE "FloatFundsTransaction" ADD CONSTRAINT "FloatFundsTransaction_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "ProductOrder"("id") ON DELETE CASCADE ON UPDATE CASCADE;
