import { FloatFundsTransactionType, PrismaClient } from '@prisma/client'

// Helper function to create dates in sequence
function createDate(daysAgo: number): string {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date.toISOString()
}

// Add these constants after other exports
const SEEDED_FLOAT_TRANSACTIONS = [
  // Most recent transactions first (30 days of activity)
  {
    amount: 5000,
    balanceAfterTransaction: 42000,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Large business deposit',
    orderNumber: '200030',
    transactionDate: createDate(0),
  },
  {
    amount: 750,
    balanceAfterTransaction: 37000,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Bulk card purchase',
    orderNumber: '200029',
    transactionDate: createDate(1),
  },
  {
    amount: 1200,
    balanceAfterTransaction: 37750,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Refund for order #A123',
    orderNumber: '200028',
    transactionDate: createDate(2),
  },
  {
    amount: 3000,
    balanceAfterTransaction: 36550,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Monthly withdrawal',
    orderNumber: '200027',
    transactionDate: createDate(3),
  },
  {
    amount: 8000,
    balanceAfterTransaction: 39550,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Weekly float top-up',
    orderNumber: '200026',
    transactionDate: createDate(4),
  },
  {
    amount: 450,
    balanceAfterTransaction: 31550,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Card order #B456',
    orderNumber: '200025',
    transactionDate: createDate(5),
  },
  {
    amount: 300,
    balanceAfterTransaction: 32000,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Adjustment credit',
    orderNumber: '200024',
    transactionDate: createDate(6),
  },
  {
    amount: 2500,
    balanceAfterTransaction: 31700,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Scheduled withdrawal',
    orderNumber: '200023',
    transactionDate: createDate(7),
  },
  {
    amount: 10000,
    balanceAfterTransaction: 34200,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Monthly float deposit',
    orderNumber: '200022',
    transactionDate: createDate(8),
  },
  {
    amount: 890,
    balanceAfterTransaction: 24200,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Card batch purchase',
    orderNumber: '200021',
    transactionDate: createDate(9),
  },
  {
    amount: 1500,
    balanceAfterTransaction: 25090,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Emergency top-up',
    orderNumber: '200020',
    transactionDate: createDate(10),
  },
  {
    amount: 675,
    balanceAfterTransaction: 23590,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Rush card order',
    orderNumber: '200019',
    transactionDate: createDate(11),
  },
  {
    amount: 250,
    balanceAfterTransaction: 24265,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Service credit',
    orderNumber: '200018',
    transactionDate: createDate(12),
  },
  {
    amount: 4000,
    balanceAfterTransaction: 24015,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Quarterly adjustment',
    orderNumber: '200017',
    transactionDate: createDate(13),
  },
  {
    amount: 12000,
    balanceAfterTransaction: 28015,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Quarterly float deposit',
    orderNumber: '200016',
    transactionDate: createDate(14),
  },
  {
    amount: 925,
    balanceAfterTransaction: 16015,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Standard card order',
    orderNumber: '200015',
    transactionDate: createDate(15),
  },
  {
    amount: 480,
    balanceAfterTransaction: 16495,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Return credit',
    orderNumber: '200014',
    transactionDate: createDate(16),
  },
  {
    amount: 1800,
    balanceAfterTransaction: 16015,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Scheduled withdrawal',
    orderNumber: '200013',
    transactionDate: createDate(17),
  },
  {
    amount: 7500,
    balanceAfterTransaction: 17815,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Weekly deposit',
    orderNumber: '200012',
    transactionDate: createDate(18),
  },
  {
    amount: 550,
    balanceAfterTransaction: 10315,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Express card order',
    orderNumber: '200011',
    transactionDate: createDate(19),
  },
  {
    amount: 350,
    balanceAfterTransaction: 10865,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Compensation credit',
    orderNumber: '200010',
    transactionDate: createDate(20),
  },
  {
    amount: 2200,
    balanceAfterTransaction: 10515,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Monthly withdrawal',
    orderNumber: '200009',
    transactionDate: createDate(21),
  },
  {
    amount: 9000,
    balanceAfterTransaction: 12715,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Monthly float deposit',
    orderNumber: '200008',
    transactionDate: createDate(22),
  },
  {
    amount: 780,
    balanceAfterTransaction: 3715,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Bulk order #C789',
    orderNumber: '200007',
    transactionDate: createDate(23),
  },
  {
    amount: 420,
    balanceAfterTransaction: 4495,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Refund processing',
    orderNumber: '200006',
    transactionDate: createDate(24),
  },
  {
    amount: 1600,
    balanceAfterTransaction: 4075,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Scheduled withdrawal',
    orderNumber: '200005',
    transactionDate: createDate(25),
  },
  {
    amount: 5000,
    balanceAfterTransaction: 5675,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Initial float setup',
    orderNumber: '200004',
    transactionDate: createDate(26),
  },
  {
    amount: 825,
    balanceAfterTransaction: 675,
    transactionType: FloatFundsTransactionType.DEBIT,
    floatFundsTransactionType: FloatFundsTransactionType.DEBIT,
    description: 'Standard order',
    orderNumber: '200003',
    transactionDate: createDate(27),
  },
  {
    amount: 290,
    balanceAfterTransaction: 1500,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Service adjustment',
    orderNumber: '200002',
    transactionDate: createDate(28),
  },
  {
    amount: 1500,
    balanceAfterTransaction: 1210,
    transactionType: FloatFundsTransactionType.CREDIT,
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
    description: 'Initial deposit',
    orderNumber: '200001',
    transactionDate: createDate(29),
  },
]

export function seedOrgFloatFund(
  prisma: PrismaClient,
  orgId: string,
  customerCode: string,
  updatedBy: string,
  balance: number
) {
  return prisma.orgFloatFund.upsert({
    where: {
      orgId,
    },
    create: {
      orgId,
      customerCode,
      currentBalance: balance,
      lastUpdatedDate: new Date().toISOString(),
      isActive: true,
      updatedBy,
    },
    update: {},
  })
}

export async function seedFloatFunds(
  prisma: PrismaClient,
  orgId: string,
  customerCode: string,
  updatedBy: string
) {
  // Create OrgFloatFund first
  await seedOrgFloatFund(prisma, orgId, customerCode, updatedBy, 42000)

  // Create all transactions
  let transactionId = 0
  for (let i = 0; i < SEEDED_FLOAT_TRANSACTIONS.length; i++) {
    transactionId++

    await prisma.floatFundsTransaction.create({
      data: {
        id: transactionId,
        orgId,
        customerCode,
        createdBy: updatedBy,
        ...SEEDED_FLOAT_TRANSACTIONS[i],
      },
    })
  }

  // Reset the sequence to start from the next available ID
  const nextId = transactionId + 1
  await prisma.$executeRaw`SELECT setval('"FloatFundsTransaction_id_seq"', ${nextId}, true);`
}
