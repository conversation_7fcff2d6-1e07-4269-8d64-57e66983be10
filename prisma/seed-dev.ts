import {
  DeliveryMethod,
  Prisma,
  PrismaClient,
  ProductStatus,
  ProductType,
  UserRole,
} from '@prisma/client'
import { seedCards } from './seed-cards'
import { seedOrders } from './seed-orders'
const prisma = new PrismaClient()

const ORG_ADMIN: Prisma.UserCreateInput[] = [
  {
    id: '65ad8890620593ff94638d5e',
    email: '<EMAIL>',
    role: 'ORG_ADMIN',
    firstName: 'Emma',
    lastName: 'Slight',
  },
  {
    id: '6595fc5d01dafe8ccf5e3efc',
    email: '<EMAIL>',
    role: 'ORG_ADMIN',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
  },
  {
    id: '0a9fbea9-6e1b-4d57-9594-6e77a559e21b',
    email: '<EMAIL>',
    role: 'ORG_ADMIN',
    firstName: '<PERSON>',
    lastName: '<PERSON>omb<PERSON>',
  },
  {
    id: '65e8dc91a99f8fbb38d4f8c3',
    email: '<EMAIL>',
    role: 'ORG_ADMIN',
    firstName: 'John',
    lastName: 'Ken',
  },
  {
    id: '65e8ddba8a4aed4380971165',
    email: '<EMAIL>',
    role: UserRole.EPAY_ADMIN,
    firstName: 'Ken',
    lastName: 'Gee',
  },
  {
    id: '66903ec7616c1c4790f0fe73',
    email: '<EMAIL>',
    role: UserRole.EPAY_ADMIN,
    firstName: 'Nate',
    lastName: 'Admin-Dawg',
  },
]

const EPAY_COMPLIANCE: {
  id: string
  email: string
  role: UserRole
  firstName: string
  lastName: string
}[] = [
  {
    id: 'afbc5c4e-1f95-4164-b298-a57f902b9998',
    email: '<EMAIL>',
    role: 'EPAY_COMPLIANCE',
    firstName: 'Ross',
    lastName: 'Arcombe',
  },
  {
    id: '659e00fa6ab73b980f3310ca',
    email: '<EMAIL>',
    role: 'EPAY_ADMIN',
    firstName: 'Ross',
    lastName: 'Emma',
  },
]

const PREZZY_CARDS: Prisma.ProductCreateInput[] = [
  {
    id: '319b2c65-882a-4542-8803-caaadf1faa9b',
    name: 'Prezzy Red',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    isArchived: false,
    type: 'PREZZY',
    cardTypes: ['PHYSICAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    minValue: 2000,
    maxValue: 500000,
    deliveryMethods: ['COURIER', 'PICKUP'],
    productCode: 1,
    design: {
      connectOrCreate: {
        where: {
          id: '92e55c8b-ec5e-4143-9e5d-18bfb06caf76',
        },
        create: {
          id: '92e55c8b-ec5e-4143-9e5d-18bfb06caf76',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/6.png',
          externalCardDesignId: '6',
        },
      },
    },
  },
  {
    id: 'e2b9daee-abb2-4c14-9f35-6c6949039382',
    name: 'Prezzy Black',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    isArchived: false,
    type: 'PREZZY',
    cardTypes: ['PHYSICAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    minValue: 2000,
    maxValue: 500000,
    deliveryMethods: ['COURIER', 'PICKUP'],
    productCode: 2,
    design: {
      connectOrCreate: {
        where: {
          id: '452391b0-30f3-4e0b-954c-38b73d01853a',
        },
        create: {
          id: '452391b0-30f3-4e0b-954c-38b73d01853a',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/1063.png',
          externalCardDesignId: '1063',
        },
      },
    },
  },
  {
    id: '9509d556-26c6-4555-a5d9-0d6bd3c33e8b',
    name: 'Prezzy Bach',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    isArchived: false,
    type: 'PREZZY',
    cardTypes: ['PHYSICAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    minValue: 2000,
    maxValue: 500000,
    deliveryMethods: ['COURIER', 'PICKUP'],
    productCode: 3,
    design: {
      connectOrCreate: {
        where: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f40',
        },
        create: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f40',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/993.png',
          externalCardDesignId: '993',
        },
      },
    },
  },
  {
    id: '516ac5c7-ff2c-405d-af09-3ef7226d775c',
    name: 'Prezzy Virtual',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'PREZZY',
    cardTypes: ['VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    minValue: 2000,
    maxValue: 500000,
    deliveryMethods: ['EMAIL'],
    productCode: 4,
    design: {
      connectOrCreate: {
        where: {
          id: 'c33bfa2c-b27a-4da1-992a-8be8215eeca8',
        },
        create: {
          id: 'c33bfa2c-b27a-4da1-992a-8be8215eeca8',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/90405.png',
          externalCardDesignId: '90405',
        },
      },
    },
  },
]

const GIFT_SATION_CARDS: Prisma.ProductCreateInput[] = [
  {
    id: 'e2b9daee-abb2-4c14-9f35-6c6949039282',
    name: 'Burnsc',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'GIFT_STATION',
    cardTypes: ['PHYSICAL', 'VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    deliveryMethods: ['COURIER', 'PICKUP', 'EMAIL'],
    productCode: 5,
    design: {
      connectOrCreate: {
        where: {
          id: '452391b0-30f4-4e0b-954c-38b73d01853a',
        },
        create: {
          id: '452391b0-30f4-4e0b-954c-38b73d01853a',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/9001.png',
          externalCardDesignId: '9001',
        },
      },
    },
  },
  {
    id: '9509d556-26c6-4555-a5d9-0d6bd3c33e81',
    name: 'Farmers',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'GIFT_STATION',
    cardTypes: ['PHYSICAL', 'VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    minValue: 2000,
    maxValue: 100000,
    deliveryMethods: ['COURIER', 'PICKUP', 'EMAIL'],
    productCode: 6,
    design: {
      connectOrCreate: {
        where: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f41',
        },
        create: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f41',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/9002.png',
          externalCardDesignId: '9002',
        },
      },
    },
  },
  {
    id: '9509d556-26c6-4555-a5d9-0d6bd3c33e82',
    name: 'Itunes',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'GIFT_STATION',
    cardTypes: ['PHYSICAL', 'VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    deliveryMethods: ['COURIER', 'PICKUP', 'EMAIL'],
    productCode: 7,
    design: {
      connectOrCreate: {
        where: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f42',
        },
        create: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f42',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/9003.png',
          externalCardDesignId: '9003',
        },
      },
    },
  },
  {
    id: '9509d556-26c6-4555-a5d9-0d6bd3c33e83',
    name: 'Noel leening',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'GIFT_STATION',
    cardTypes: ['PHYSICAL', 'VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    deliveryMethods: ['COURIER', 'PICKUP', 'EMAIL'],
    productCode: 8,
    design: {
      connectOrCreate: {
        where: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f43',
        },
        create: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f43',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/9004.png',
          externalCardDesignId: '9004',
        },
      },
    },
  },
  {
    id: '9509d556-26c6-4555-a5d9-0d6bd3c33e84',
    name: 'Resaurant',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'GIFT_STATION',
    cardTypes: ['PHYSICAL', 'VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    deliveryMethods: ['COURIER', 'PICKUP', 'EMAIL'],
    productCode: 9,
    design: {
      connectOrCreate: {
        where: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f44',
        },
        create: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f44',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/9005.png',
          externalCardDesignId: '9005',
        },
      },
    },
  },
  {
    id: '9509d556-26c6-4555-a5d9-0d6bd3c33e85',
    name: 'Walker hall',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'GIFT_STATION',
    cardTypes: ['PHYSICAL', 'VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    deliveryMethods: ['COURIER', 'PICKUP', 'EMAIL'],
    productCode: 10,
    design: {
      connectOrCreate: {
        where: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f45',
        },
        create: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f45',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/9006.png',
          externalCardDesignId: '9006',
        },
      },
    },
  },
  {
    id: '9509d556-26c6-4555-a5d9-0d6bd3c33e86',
    name: 'Warehouse stationary',
    status: ProductStatus.APPROVED,
    loadingFee: 595,
    digitalFee: 150,
    isArchived: false,
    type: 'GIFT_STATION',
    cardTypes: ['PHYSICAL', 'VIRTUAL'],
    details: 'some details',
    conditions: 'some conditions',
    fixedValues: [2000, 5000, 10000],
    deliveryMethods: ['COURIER', 'PICKUP', 'EMAIL'],
    productCode: 11,
    design: {
      connectOrCreate: {
        where: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f46',
        },
        create: {
          id: '83903a64-4cd0-44bc-bcef-e71c5e815f46',
          cardDesignUrl:
            'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/9007.png',
          externalCardDesignId: '9007',
        },
      },
    },
  },
]

async function main() {
  const nextChapter = await prisma.organization.upsert({
    where: {
      id: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
    },
    create: {
      id: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      type: 'LIMITED_LIABILITY_COMPANY',
      name: 'Next Chapter Studio Limited',
      tradingName: '',
      nzbn: '9429048680271',
      gstNumber: '',
      address: '',
      city: '',
      suburb: '',
      zip: '',
      country: '',
      principalPlace: '',
      natureAndPurpose: '',
      phone: '',
      cPartnerIds: ['122322'],
      createdAt: '2023-06-20T01:03:54.668Z',
      updatedAt: '2023-06-20T01:05:38.432Z',
    },
    update: {},
  })

  const adminUpserts = ORG_ADMIN.map((user) => {
    return prisma.user.upsert({
      where: {
        id: user.id,
      },

      create: {
        ...user,
        organization: {
          connect: {
            id: nextChapter.id,
          },
        },
      },
      update: {},
    })
  })
  await Promise.all(adminUpserts)

  await seedCards(prisma)
  await seedOrders(prisma, nextChapter.id, '66903ec7616c1c4790f0fe73')

  const epayUpserts = EPAY_COMPLIANCE.map((user) => {
    return prisma.user.upsert({
      where: {
        id: user.id,
      },
      create: {
        ...user,
      },
      update: {},
    })
  })
  await Promise.all(epayUpserts)
}

main()
  .then(async () => {
    console.log('Seed success')
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
