import {
  CardType,
  DeliveryMethod,
  PrismaClient,
  ProductStatus,
  ProductType,
} from '@prisma/client'
const prisma = new PrismaClient()

const PHYSICAL_DETAILS =
  '<p>Prezzy card is the ultimate gift of choice. The recipient can choose exactly what they want from over 32 million Visa outlets – in store and online.</p><p>Card can be loaded from $25 (+$5.95 card fee) to $5000 (+5.95 card fee). Please note all values shown are exclusive of the $5.95 load fee.</p><p style={{ color: "#ff7300", fontWeight: "300" }}><i> The card fee is not included in the balance available on the card</i></p>'

const CONDITIONS =
  '<p>Click <a style={{ color: "#ff7300", fontWeight: "300" }} href="https://www.prezzycard.co.nz/terms-and-conditions" target="_blank">HERE</a> for Prezzy Card Terms and Conditions</p>'

const VIRTUAL_DETAILS =
  "<p>Our new Prezzy Virtual card is the perfect way to show appreciation, share the love and give the gift of choice! Our virtual gift card is not only a convenient and hassle-free option, but it's also eco-friendly and contactless – making it easy to send and receive gifts in today's world. Whether you're celebrating a special occasion or just want to brighten someone's day, our virtual gift card is the perfect choice.</p><p><b>Note – Your virtual card can be used online and added to your digital wallet (Apple pay & Google pay) for in-store purchases.</b></p><p><b>Digital Delivery</b></p><p>Please note digital orders are not delivered immediately. Orders are checked and processed between 8.30am and 5pm, Monday to Saturday, NZ standard time.</p>"

const FIXED_VALUES = [2000, 5000, 7500, 10000, 15000, 20000, 50000]

const MIN_VALUE = 2500

const MAX_VALUE = 500000

function toDeliveryMethods(methods: string[]): DeliveryMethod[] {
  return methods.map(
    (method) => DeliveryMethod[method as keyof typeof DeliveryMethod]
  )
}

function toCardTypes(types: string[]): CardType[] {
  return types.map((type) => CardType[type as keyof typeof CardType])
}

const CARD_DESIGNS = [
  {
    cardDesign: {
      externalCardDesignId: '6',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/6.png',
      isPrezzyDesign: true,
    },
    product: {
      name: 'Prezzy Red',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['PHYSICAL'],
      details: PHYSICAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['COURIER'],
    },
  },
  {
    cardDesign: {
      externalCardDesignId: '1063',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/1063.png',
      isPrezzyDesign: true,
    },
    product: {
      name: 'Prezzy Card - Black Glitter',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['PHYSICAL'],
      details: PHYSICAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['COURIER'],
    },
  },
  {
    cardDesign: {
      externalCardDesignId: '993',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/993.png',
      isPrezzyDesign: true,
    },
    product: {
      name: 'Prezzy Card - Kiwi Bach',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['PHYSICAL'],
      details: PHYSICAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['COURIER'],
    },
  },
  {
    cardDesign: {
      externalCardDesignId: '90405',
      cardDesignUrl:
        'https://dev-public-epay-portal-assets.s3.ap-southeast-2.amazonaws.com/card-designs/90405.png',
      isPrezzyDesign: false,
    },
    product: {
      name: 'Prezzy Virtual',
      status: ProductStatus.APPROVED,
      loadingFee: 595,
      digitalFee: 150,
      isArchived: false,
      type: ProductType.PREZZY,
      cardTypes: ['VIRTUAL'],
      details: VIRTUAL_DETAILS,
      conditions: CONDITIONS,
      fixedValues: FIXED_VALUES,
      minValue: MIN_VALUE,
      maxValue: MAX_VALUE,
      deliveryMethods: ['EMAIL'],
    },
  },
]

async function main() {
  for (const prezzyProducts of CARD_DESIGNS) {
    const createdCardDesign = await prisma.cardDesign.create({
      data: {
        cardDesignUrl: prezzyProducts.cardDesign.cardDesignUrl,
        externalCardDesignId: prezzyProducts.cardDesign.externalCardDesignId,
        isPrezzyDesign: prezzyProducts.cardDesign.isPrezzyDesign,
      },
    })

    await prisma.product.create({
      data: {
        name: prezzyProducts.product.name,
        status: prezzyProducts.product.status,
        loadingFee: prezzyProducts.product.loadingFee,
        digitalFee: prezzyProducts.product.digitalFee,
        isArchived: prezzyProducts.product.isArchived,
        type: prezzyProducts.product.type,
        details: prezzyProducts.product.details,
        conditions: prezzyProducts.product.conditions,
        fixedValues: prezzyProducts.product.fixedValues,
        minValue: prezzyProducts.product.minValue,
        maxValue: prezzyProducts.product.maxValue,
        deliveryMethods: toDeliveryMethods(
          prezzyProducts.product.deliveryMethods
        ),
        cardTypes: toCardTypes(prezzyProducts.product.cardTypes),
        designId: createdCardDesign.id,
      },
    })
  }
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
