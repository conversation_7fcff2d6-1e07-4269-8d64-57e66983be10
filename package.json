{"name": "epay-api", "version": "1.0.0", "main": "index.js", "author": "Next Chapter Studio", "license": "MIT", "scripts": {"dev": "NODE_ENV=local nodemon ./src/index.ts", "build": "pnpm prisma generate && tsc -p ./tsconfig.build.json", "start:prod": "NODE_PATH=./dist/src node ./dist/src/index.js", "start:migrate:prod": "prisma migrate deploy && pnpm start:prod", "test": "vitest", "test:unit": "vitest run", "test:int": "vitest -c ./vitest.integration.config.ts sendOrderDetail", "test:all": "pnpm test run && pnpm test:int run ", "build:local": "pnpm prisma generate && tsc", "prisma:create": "pnpm prisma migrate dev --create-only && pnpm prisma generate", "prisma:migrate": "pnpm prisma migrate dev && pnpm prisma generate", "prisma:seed": "ts-node prisma/seed.ts", "prisma:seedtest": "prisma migrate reset --skip-seed && ts-node prisma/seed-test.ts", "prisma:seeddev": "prisma migrate reset --skip-seed && ts-node prisma/seed-dev.ts", "gen:schema": "awk 1 ./prisma/schemas/*.prisma > ./prisma/schema.prisma && npx prisma format", "lint": "eslint src/**/*.ts integration/** --max-warnings 0", "format": "eslint src/**/*.ts --fix", "db:reset": "ts-node scripts/reset-db.ts", "prepare": ""}, "keywords": [], "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.651.1", "@aws-sdk/client-s3": "^3.651.1", "@aws-sdk/client-sns": "^3.651.1", "@aws-sdk/lib-storage": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.651.1", "@prisma/client": "^4.16.2", "@prisma/engines": "^4.16.2", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^7.119.0", "@sentry/profiling-node": "^1.3.5", "@sentry/tracing": "^7.114.0", "@types/sqlstring": "^2.3.2", "@types/ssh2-sftp-client": "^9.0.4", "amazon-cognito-identity-js": "^6.3.12", "auth0": "^4.10.0", "awesome-phonenumber": "^3.4.0", "aws-jwt-verify": "^4.0.1", "axios": "^0.27.2", "axios-rate-limit": "^1.4.0", "axios-retry": "^3.9.1", "chalk": "4.1.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "express": "^4.21.0", "express-async-errors": "^3.1.1", "express-oauth2-jwt-bearer": "^1.6.0", "express-request-id": "^2.0.1", "fast-levenshtein": "^3.0.0", "jsonwebtoken": "^9.0.2", "neverthrow": "^6.2.2", "pnpm": "^8.15.9", "prisma": "^4.16.2", "save-dev": "0.0.1-security", "soap": "^1.1.3", "sqlstring": "^2.3.3", "ssh2-sftp-client": "^11.0.0", "tggl-client": "^1.15.5", "twilio": "^5.2.3", "uuid": "^9.0.1", "vitest": "^1.6.0", "xml2js": "^0.6.2", "zod": "^3.23.8"}, "devDependencies": {"@types/chai": "^4.3.19", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/express-serve-static-core": "^4.19.5", "@types/fast-levenshtein": "^0.0.2", "@types/jsonwebtoken": "^9.0.6", "@types/mocha": "^9.1.1", "@types/node": "^18.19.50", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.8", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitest/coverage-c8": "^0.22.1", "axios-mock-adapter": "^1.22.0", "csv-parser": "^3.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-config-standard-with-typescript": "^22.0.0", "eslint-plugin-neverthrow": "^1.1.4", "husky": "^9.1.6", "nodemon": "^2.0.22", "prettier": "^2.8.8", "prisma-dbml-generator": "^0.10.0", "supertest": "^7.0.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.2"}}