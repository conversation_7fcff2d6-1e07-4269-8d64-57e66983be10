{"family": "<TASK_DEFINITION_FAMILY>", "taskRoleArn": "arn:aws:iam::<AWS_ACCOUNT>:role/<TASK_ROLE>", "executionRoleArn": "arn:aws:iam::<AWS_ACCOUNT>:role/<EXECUTION_ROLE>", "containerDefinitions": [{"name": "<CONTAINER_NAME>", "image": "<ECR_REPOSITORY_URL>", "cpu": 1024, "memory": 4096, "stopTimeout": 120, "essential": true, "portMappings": [{"name": "epay-api-8000-tcp", "containerPort": 8000, "hostPort": 8000, "protocol": "tcp", "appProtocol": "http"}], "secrets": [{"name": "<JSON_SECRETS>", "valueFrom": "<ARN_JSON_SECRETS>"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:ap-southeast-2:<AWS_ACCOUNT>:parameter/<ENV_SECRET>/DATABASE_URL"}, {"name": "<ENV_UPPER>_SFTP_PRIVATE_KEY", "valueFrom": "<ARN_SMTP_PRIVATE_KEY>"}, {"name": "<ENV_UPPER>_EPAY_SFTP_PRIVATE_KEY", "valueFrom": "<ARN_EPAY_SMTP_PRIVATE_KEY>"}, {"name": "<ENV_UPPER>_REN_DOMAIN_CERT", "valueFrom": "<ARN_REN_DOMAIN_CERT>"}, {"name": "<ENV_UPPER>_REN_INTERMEDIATE_CERT", "valueFrom": "<ARN_REN_INTERMEDIATE_CERT>"}, {"name": "<ENV_UPPER>_REN_CA_CERT", "valueFrom": "<ARN_REN_CA_CERT>"}], "environment": [{"name": "ENV", "value": "<ENV>"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/<ENV>-epay-api-definition", "awslogs-region": "ap-southeast-2", "awslogs-stream-prefix": "ecs"}}}], "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "cpu": "1024", "memory": "4GB"}