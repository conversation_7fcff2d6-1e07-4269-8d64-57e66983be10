CLIENT_DOMAIN=nextchapter.tech
CLIENT_URL_BASE=https://epay-dev-corp-portal.nextchapter.tech #check its not an auth0 thing before deleting
CORPORATE_PORTAL_URL_BASE=https://epay-dev-corp-portal.nextchapter.tech
ADMIN_PORTAL_URL_BASE=https://epay-dev-admin-portal.nextchapter.tech
API_DOMAIN=epay.nextchapter.tech
API_URL_BASE=https://epay-dev-api.nextchapter.tech

LOG_LEVEL=debug
PORT=8000
TEST_LOG_MESSAGES=false
SENTRY_SAMPLE_RATE=0
SENTRY_DSN=https://<EMAIL>/****************

AWS_ADMIN_ID=AKIAXJMEWYXQDPURTJPB
AWS_REGION=ap-southeast-2
# Cognito variables are accessible from client side
#AWS_COGNITO_USER_POOL_ID=ap-southeast-2_jLcp3bKaB
#AWS_COGNITO_CLIENT_ID=5ah0f0b2vk4lablbgvbhfc97p3
AWS_S3_BUCKET=prezzy-corporate-application-identity
AWS_S3_BUCKET_CARD=prezzy-card
AWS_S3_BUCKET_REPORT=dev-epay-reports

INFO_LOG_BASE_URL=https://api2.infolog.nz
INFO_LOG_CLIENT_ID=e8ddca4f-0a0f-49a4-8f0a-dd87f8394899
INFO_LOG_USERNAME=ePayAPI

#I2C can't be accessed from dev enironment
I2C_URL=https://ws2.mycardplace.com:6443
I2C_ID=acqtest # fake id
I2C_USER_ID=blurb #fake user id

SOAP_GIFT_CARD_URL='https://serv002.ezipay.net.nz:41443/GiftCard.asmx'
SOAP_GIFT_CARD_ACTION='http://compass.net.nz/WebServices/GiftCard/MessageExchange'
SOAP_AUTH_USERNAME='NextChapterLive0818'
SOAP_MESSAGE_EXCHANGE_MESSAGE_TYPE='PrezzyBatchDetails'
SOAP_MESSAGE_EXCHANGE_USER_CODE='145226'

USE_MOCKING=false

WINDCAVE_URL=https://uat.windcave.com/api/v1/sessions
WINDCAVE_USER=epayREST_dev

SENDER_EMAIL=<EMAIL>

SFTP_HOST=************
SFTP_PORT=22
SFTP_USERNAME=dev-sftp-user
SFTP_REMOTE_PATH=dev

EPAY_SFTP_HOST=************
EPAY_SFTP_PORT=22
EPAY_SFTP_USERNAME=dev-sftp-user
EPAY_SFTP_REMOTE_PATH=dev/reports
EPAY_INTEGRATION_URL_1=https://serv001.ezipay.net.nz:52571
EPAY_INTEGRATION_URL_2=https://serv002.ezipay.net.nz:52571

EPAY_ADMIN_EMAIL=<EMAIL>
EPAY_SUPPORT_EMAIL=<EMAIL>
EPAY_KYC_EMAIL=<EMAIL>
EPY_KYC_ALTERNATIVE_EMAIL=<EMAIL>

AUTH0_ISSUER_BASE_URL=https://dev-v680tdqhjrva51ok.au.auth0.com
AUTH0_AUDIENCE=https://dev-local-api.epayworldwide.co.nz
AUTH0_CLIENT_ID=8DDOsLzuOOtf4FcWF2EnI6EMKqQ9IBFx
AUTH0_CONNECTION=Username-Password-Authentication
AUTH0_CONNECTION_ID=con_4z6GZ9yamwY2iXO8
AUTH0_ADMIN_CONNECTION=Admin-Authentication
AUTH0_ADMIN_CONNECTION_ID=con_JMNNfAKO0HIoQ31v

TEAM_NOTIFICATION_EMAIL=<EMAIL>

SEND_ORDER_TO_EPAY=true

REN_URL=https://prezzy_nz_uat.eftapme.com
REN_URL2=https://prezzynzuat.eftapme.com
#REN_URL2=https://************:443
REN_VCARD_BRANCH=100003
REN_PART_ID=103 #103

ORDER_LOCK_TIMEOUT_IN_MINUTES=10
DEFERRED_DELAY_BEFORE_RETRY_IN_MINUTES=5

TWILIO_ACCOUNT_SID=ACbca5cdd7bd39b20c7f01404483ad4a5d
TWILIO_VERIFY_SERVICE_SID=VA9ba16454a197d9b17378d7c859f3c33b
