import type { UserRole } from '@prisma/client'
import * as Sentry from '@sentry/node'

/* eslint-disable @typescript-eslint/no-unused-vars */
// --- Environment variables must be loaded first
import './utils/config'
// ---------------

// import * as Sentry from "@sentry/node";

import cors from 'cors'
import express, { NextFunction, Request, Response } from 'express'
import 'express-async-errors' // catches errors in async functions

import fs from 'fs'
import path from 'path'

import { checkJwt } from 'middlewares/check-jwt'

import { healthRouter } from 'routes/health.route'
import { authRouter } from 'routes/auth.route'
import { notificationRouter } from 'routes/notification.route'

import { corporateApplicationRouter } from 'routes/corporate-application.route'
import { onboardingRouter } from 'routes/onboarding.route'
import { userRouter } from 'routes/user.route'
import { organizationRouter } from 'routes/organization.route'
import { organizationCPartnerRouter } from 'routes/organization.c-partner.route'
import { cardRouter } from 'routes/card.route'
import { activateCardRoute } from 'routes/activate-card.route'
import { productOrderRouter } from 'routes/product.order.route'
import { productRouter } from 'routes/product.route'
import { customProductRouter } from 'routes/product.custom.route'
import { stockRouter } from 'routes/stock.route'
import { virtualCardRouter } from 'routes/virtual-card.route'

import { epayOrderRouter } from 'routes/epay.order.route'
import { epayKycRouter } from 'routes/epay.kyc.route'
import { epayCardRouter } from 'routes/epay.card.route'
import { epayUserRouter } from 'routes/epay.user.route'
import { epayLogosRouter } from 'routes/epay.logos.route'
import { epayAccountsRouter } from 'routes/epay.accounts.route'
import { epayProductRouter } from 'routes/epay.product.route'
import { epaySuperAdminRouter } from 'routes/epay.super-admin.route'

import { emailRouter } from 'routes/email.route'

import {
  ADMIN_PORTAL_URL_BASE,
  CORPORATE_PORTAL_URL_BASE,
  ENVIRONMENT,
  IS_DEV,
  IS_LOCAL,
} from 'utils/config'
import * as logger from 'utils/logger'

import { epayIntegrationRouter } from 'routes/epay.integration.route'
import { otpRouter } from 'routes/otp.route'
import { epayCardsRouter } from 'routes/epay.cards.route'
import { addressRouter } from 'routes/address.route'
import { epayDiscountsRouter } from 'routes/epay.discounts.route'
import { fundingRouter } from 'routes/funding.route'
import { epayReportRouter } from 'routes/epay.report.route'
import { epayCardDesignRouter } from 'routes/epay.card-design.route'
import { floatFundsRouter } from 'routes/float-funds.route'
import { epayFloatFundsRouter } from './routes/epay.float-funds.route'
import { epayOrganizationRouter } from 'routes/epay.organization.route'

declare module 'express-serve-static-core' {
  interface Request {
    userId?: string
    role?: UserRole
    orgId?: string
    userEmail?: string
    phoneNumber?: string
    permissions?: string[]
  }
}

export type UserContext = {
  userId?: string
  orgId?: string
  role: UserRole
}
export function createApp(testContext?: UserContext) {
  // Initialize tmp directory at startup
  const tmpDir = path.join(process.cwd(), 'tmp')
  fs.mkdirSync(tmpDir, { recursive: true })

  const app = express()

  logger.initLogger('epay-api', app, process.env.SENTRY_DSN)

  app.use(Sentry.Handlers.requestHandler())
  app.use(Sentry.Handlers.tracingHandler())

  app.use(express.json())
  app.use(express.urlencoded({ extended: true }))

  type StaticOrigin = boolean | string | RegExp | (boolean | string | RegExp)[]

  const origin: StaticOrigin = [
    ADMIN_PORTAL_URL_BASE,
    CORPORATE_PORTAL_URL_BASE,
  ]

  app.use(
    cors({
      origin,
      credentials: true,
    })
  )

  app.use((_, res, next) => {
    res.setHeader('X-Frame-Options', 'DENY')
    next()
  })

  app.get('/test-sentry', (req, res) => {
    logger.error('Test Sentry Handled Error')

    // Intentionally cause an error
    throw new Error('Sentry Test Unhandled error')
  })

  app.use('/health', healthRouter)
  app.use('/otp', otpRouter)
  app.use('/epay/card', epayCardRouter)
  // ***** /notification ****** is an External API routes *****
  app.use('/notification', notificationRouter)
  // ***** /notification ****** is an External API routes *****
  app.use('/auth', authRouter)
  app.use('/activate-card', activateCardRoute)
  app.use('/epay/integration', epayIntegrationRouter)

  // If testContext is provided, inject it into the request
  if (testContext) {
    if (IS_LOCAL || IS_DEV) {
      app.use((req, _, next) => {
        req.userId = testContext.userId
        req.orgId = testContext.orgId
        req.role = testContext.role
        next()
      })
    } else {
      throw new Error(
        'Test context can only be used in local or dev environment'
      )
    }
  } else {
    app.use(checkJwt)
  }

  app.use('/address', addressRouter)
  app.use('/corporate-application', corporateApplicationRouter)
  app.use('/organization', organizationRouter)
  app.use('/organization/c-partner', organizationCPartnerRouter)
  app.use('/onboarding', onboardingRouter)
  app.use('/user', userRouter)

  app.use('/product', productRouter)
  app.use('/product/order', productOrderRouter)
  app.use('/product/custom', customProductRouter)

  app.use('/card', cardRouter)

  app.use('/email', emailRouter)

  app.use('/stock', stockRouter)
  app.use('/funding', fundingRouter)
  app.use('/float-funds', floatFundsRouter)

  app.use('/virtual-card', virtualCardRouter)

  app.use('/epay/cards', epayCardsRouter)
  app.use('/epay/kyc', epayKycRouter)
  app.use('/epay/order', epayOrderRouter)
  app.use('/epay/user', epayUserRouter)
  app.use('/epay/logos', epayLogosRouter)
  app.use('/epay/accounts', epayAccountsRouter)
  app.use('/epay/product', epayProductRouter)
  app.use('/epay/discounts', epayDiscountsRouter)
  app.use('/epay/super-admin', epaySuperAdminRouter)
  app.use('/epay/report', epayReportRouter)
  app.use('/epay/card-design', epayCardDesignRouter)
  app.use('/epay/organization', epayOrganizationRouter)

  app.use('/epay/float-funds', epayFloatFundsRouter)

  // UNHANDLED EXCEPTIONS - this overrides the default html response so
  // don't change the signature
  app.use(Sentry.Handlers.errorHandler())

  app.use((err: unknown, req: Request, res: Response, next: NextFunction) => {
    logger.error(err, 'Unhandled exception. Check Sentry logs.')

    return res.status(500).json({
      message: `Oops! Queue the log sifting minions.`,
    })
  })

  // NOT FOUND
  app.use((req: Request, res: Response, next: NextFunction) => {
    //logger.debug(`Invalid path or method [${req.method}] ${req.originalUrl}`)

    res.status(404).json({
      code: 'not_found',
      message: "Can't find the URL or method (GET/POST) you're after",
    })
  })

  return app
}
