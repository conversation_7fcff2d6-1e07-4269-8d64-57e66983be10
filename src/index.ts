// src/server.ts
import './utils/config'
import { createApp } from './app'
import { initializeFeatureFlags, isFeatureActive } from 'utils/flags'
import { setShuttingDown } from 'utils/shutting-down'

async function startServer() {
  const app = createApp()

  // Initializse feature flags has to come after creating the app because
  // it needs the logging initialization to raise errors to sentry if required
  try {
    await initializeFeatureFlags()

    if (await isFeatureActive({ key: 'testTggl' })) {
      console.log('Feature flag [testTggl] is active')
    } else {
      console.log('Fetaure flag [testTggl] is not active')
    }
  } catch (error) {
    console.log(error, 'Failed to initialize Tggl:')
  }

  const port = Number(process.env.PORT ?? '4567')
  const server = app.listen(port, '0.0.0.0', () => {
    console.log(`epay-api listening at http://127.0.0.1:${port}`)
  })

  // 2. Add SIGTERM handler
  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, initiating graceful shutdown...')
    setShuttingDown(true)

    // Stop accepting new connections
    server.close(() => {
      console.log('Closed out remaining connections.')
      // Exit the process if necessary
      process.exit(0)
    })
  })
}

startServer()
