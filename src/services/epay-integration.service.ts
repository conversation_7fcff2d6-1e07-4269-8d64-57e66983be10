import { PaymentMethod, ProductOrder, ProductOrderItem } from '@prisma/client'
import RequestWithBackoff from 'helpers/request-with-backoff'

import { err, fromPromise } from 'neverthrow'
import {
  EPAY_API_PASSWORD,
  EPAY_API_USERNAME,
  EPAY_INTEGRATION_URL_1,
  EPAY_INTEGRATION_URL_2,
} from 'utils/config'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'

type Discount = {
  type: string
  value: number
  tag?: string
  _ref?: string | null
  quantity?: number
}

type ChargeItem = {
  type: string
  value: number
  paid: number
  due: number
  discount?: Discount[]
}

enum EpayPaymentMethod {
  CREDIT_CARD = 'CREDITCARD',
  BANK_TRANSFER = 'BANKTRANSFER',
  FLOAT_FUNDS = 'FLOATFUNDS',
}

type Payment = {
  PaymentMethod: EpayPaymentMethod
  PaymentSessionID?: string | null
  PaymentTransactionID?: string | null
  PaymentAmount: number
}

type OrderItemCost = {
  type: string
  value: number
  tag?: string
  discount?: Discount[]
}

type OrderItem = {
  ProductCode: string
  SentToThirdParty: number
  UnitAmount: number
  Currency: string
  Quantity: number
  ItemRef: string
  DeliveryType: string
  LockCode?: string
  BatchRef: string
  OrderBatchRef?: string
  ItemCostArray: OrderItemCost[]
}

type OrderCost = {
  type: string
  tag?: string
  value: number
  discount?: Discount[]
}

type Order = {
  MessageType: string
  OrganizationID: string
  SubOranizationID: number | null
  OrganizationBranchID: string | null
  OrderRef: string
  TotalQuantity: number
  TotalAmount: ChargeItem[]
  Payment: Payment[]
  HostTimeStamp: string
  OrderTimeStamp: string
  OrderItemArray: OrderItem[]
  OrderCostArray: OrderCost[]
}

function createTotalAmount(productOrder: ProductOrder): ChargeItem[] {
  const totalAmount: ChargeItem[] = [
    {
      type: 'LoadValue',
      value: productOrder.subTotal ?? 0,
      paid: productOrder.subTotal ?? 0,
      due: 0,
    },
    {
      type: 'LoadFee',
      value: productOrder.loadingFeeTotal ?? 0,
      paid: productOrder.loadingFeeTotal
        ? productOrder.loadingFeeTotal - productOrder.loadingFeeDiscountTotal!
        : 0,
      due: 0,
      discount: [
        {
          type: 'margin',
          value: productOrder.loadingFeeDiscountTotal ?? 0,
        },
      ],
    },
  ]

  if (productOrder.shippingTotal && productOrder.shippingTotal > 0) {
    totalAmount.push({
      type: 'Freight',
      value: productOrder.shippingTotal,
      paid: productOrder.shippingTotal - productOrder.shippingFeeDiscountTotal!,
      due: 0,
      discount: productOrder.shippingFeeDiscountTotal
        ? [
            {
              type: 'margin',
              value: productOrder.shippingFeeDiscountTotal!,
            },
          ]
        : [],
    })
  }

  if (productOrder.digitalFeeTotal && productOrder.digitalFeeTotal > 0) {
    totalAmount.push({
      type: 'DigitalFee',
      value: productOrder.digitalFeeTotal,
      paid:
        productOrder.digitalFeeTotal - productOrder.digitalFeeDiscountTotal!,
      due: 0,
      discount: productOrder.digitalFeeDiscountTotal
        ? [
            {
              type: 'margin',
              value: productOrder.digitalFeeDiscountTotal!,
            },
          ]
        : [],
    })
  }

  if (productOrder.creditCardFee && productOrder.creditCardFee > 0) {
    totalAmount.push({
      type: 'CreditCardFee',
      value: productOrder.creditCardFee,
      paid: productOrder.creditCardFee,
      due: 0,
      discount: [],
    })
  }

  return totalAmount
}

function createOrderCostArray(productOrder: ProductOrder): OrderCost[] {
  const orderCostArray: OrderCost[] = []

  if (productOrder.shippingTotal && productOrder.shippingTotal > 0) {
    orderCostArray.push({
      type: 'Freight',
      tag: 'freight',
      value: productOrder.shippingTotal,
      discount: productOrder.shippingFeeDiscountTotal
        ? [{ type: 'margin', value: productOrder.shippingFeeDiscountTotal }]
        : [],
    })
  }

  if (productOrder.digitalFeeTotal && productOrder.digitalFeeTotal > 0) {
    orderCostArray.push({
      type: 'DigitalFee',
      tag: 'digitalFee',
      value: productOrder.digitalFeeTotal,
      discount: productOrder.digitalFeeDiscountTotal
        ? [{ type: 'margin', value: productOrder.digitalFeeDiscountTotal }]
        : [],
    })
  }

  if (productOrder.paymentMethod === 'CREDIT_CARD') {
    orderCostArray.push({
      type: 'CreditCardFee',
      tag: 'creditCardFee',
      value: productOrder.creditCardFee ?? 0,
      discount: [],
    })
  }

  return orderCostArray
}

function createOrderItems(productOrderItems: ProductOrderItem[]): OrderItem[] {
  return productOrderItems.map(
    (item): OrderItem => ({
      ProductCode: item.productCode.toString(),
      SentToThirdParty: 1,
      UnitAmount: item.unitPrice,
      Currency: 'NZD',
      Quantity: item.quantity,
      ItemRef: item.lineNumber?.toString() ?? '',
      DeliveryType: item.deliveryMethod,
      LockCode: '', // Not available at time until ren release
      BatchRef: item.deliveryBatchId ?? '',
      OrderBatchRef: item.externalBatchId ?? '',
      ItemCostArray: [
        {
          type: 'LoadValue',
          value: item.unitPrice,
        },
        {
          type: 'LoadFee',
          value: item.loadingFee ? item.loadingFee / item.quantity : 0,
          discount: [
            {
              type: 'margin',
              value: item.loadingFeeDiscount
                ? item.loadingFeeDiscount / item.quantity
                : 0,
              quantity: 1,
            },
          ],
        },
      ],
    })
  )
}

function createOrderDetail(
  productOrder: ProductOrder & { productOrderItems: ProductOrderItem[] }
): Order {
  const totalAmount = createTotalAmount(productOrder)
  const orderCostArray = createOrderCostArray(productOrder)
  const orderItems = createOrderItems(productOrder.productOrderItems)

  const payment: Payment[] = [
    {
      PaymentMethod: EpayPaymentMethod[productOrder.paymentMethod!],
      PaymentSessionID: productOrder.windcaveSessionId ?? null,
      PaymentTransactionID: null,
      PaymentAmount: productOrder.orderTotal ?? 0,
    },
  ]

  return {
    MessageType: 'ORQ',
    OrganizationID: productOrder.organizationId,
    SubOranizationID: null,
    OrganizationBranchID: null,
    OrderRef: productOrder.orderNumber ?? '',
    TotalQuantity: productOrder.totalQuantity ?? 0,
    TotalAmount: totalAmount,
    Payment: payment,
    HostTimeStamp: new Date().toISOString().slice(0, 19),
    OrderTimeStamp: productOrder.createdAt.toISOString(),
    OrderItemArray: orderItems,
    OrderCostArray: orderCostArray,
  }
}

export function sendOrderDetail(
  order: ProductOrder & { productOrderItems: ProductOrderItem[] }
) {
  logger.info(`[Epay] Sending order detail to ePay Integration`)

  const body = createOrderDetail(order)

  const username = EPAY_API_USERNAME
  const password = EPAY_API_PASSWORD
  const token = Buffer.from(`${username}:${password}`).toString('base64')

  const apiUrls: string[] = [
    EPAY_INTEGRATION_URL_1,
    EPAY_INTEGRATION_URL_2,
  ].filter(Boolean)

  if (apiUrls.length === 0) {
    const message =
      'EPAY_INTEGRATION_URL_1 OR EPAY_INTEGRATION_URL_2 are null. You need at least one specified'
    logger.error(message)
    return err({ code: ERRORS.CONFIG.code, message })
  }

  const epayApi = new RequestWithBackoff({
    apiUrls: apiUrls,
    initialTimeout: 10000,
  })

  return fromPromise(
    epayApi
      .request({
        url: `/api/portal/newOrder`,
        method: 'post',
        headers: {
          Authorization: `Basic ${token}`,
        },
        data: body,
      })
      .then((res) => {
        logger.info(
          `[Epay] Order detail sent to ePay Integration ${JSON.stringify(
            res.data
          )}`
        )
        return res.data
      }),
    (error) => {
      logger.error(error, `failed to send order detail to ePay Integration`)
      return ERRORS.EXTERNAL_API
    }
  )
}
