import twilio from 'twilio'
import { errAsync, okAsync } from 'neverthrow'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import {
  TWILIO_ACCOUNT_SID,
  TWILIO_AUTH_TOKEN,
  TWILIO_VERIFY_SERVICE_SID,
} from 'utils/config'
import { log } from 'console'

// Initialize Twilio client
const client = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

async function sendVerification({
  phoneNumber,
  channel,
}: {
  phoneNumber: string
  channel: 'whatsapp' | 'sms'
}) {
  try {
    logger.debug(
      `sendVerification.. channel [${channel}], phoneNumber [${phoneNumber}]`
    )

    const to =
      channel === 'whatsapp' ? `whatsapp:${phoneNumber}` : `${phoneNumber}`

    logger.debug(`sendVerification.. to [${to}]`)

    const verificationCheck = await client.verify.v2
      .services(TWILIO_VERIFY_SERVICE_SID)
      .verifications.create({
        channel,
        to,
      })
    logger.inspect(
      verificationCheck.channel,
      'sendVerification..verificationCheck'
    )
    return okAsync(verificationCheck.status)
  } catch (error) {
    logger.error(error, `Error creating verification challenge via ${channel}`)
    return errAsync(
      new Error(`Error creating verification challenge via ${channel}`)
    )
  }
}

export async function sendWhatsAppChallenge(phoneNumber: string) {
  logger.debug(`sendWhatsAppChallenge.. phoneNumber: ${phoneNumber}`)
  const result = await sendVerification({ phoneNumber, channel: 'whatsapp' })

  if (result.isOk()) {
    return okAsync('Verification initiated via WhatsApp')
  }

  return errAsync({
    code: ERRORS.EXTERNAL_API.code,
    message:
      'Unable to send verification via WhatsApp. Please try resending via SMS.',
  })
}

export async function sendSMSChallenge(phoneNumber: string) {
  logger.debug(`sendSMSChallenge.. phoneNumber: ${phoneNumber}`)
  const result = await sendVerification({ phoneNumber, channel: 'sms' })

  if (result.isOk()) {
    return okAsync('Verification resent via SMS')
  }

  return errAsync({
    code: ERRORS.EXTERNAL_API.code,
    message: 'Unable to send verification via SMS. Please try again later.',
  })
}

export async function verificationCheck({
  phoneNumber,
  code,
}: {
  phoneNumber: string
  code: string
}) {
  try {
    const verificationCheck = await client.verify.v2
      .services(TWILIO_VERIFY_SERVICE_SID)
      .verificationChecks.create({
        code: `${code}`,
        to: `${phoneNumber}`,
      })
    return okAsync({
      valid: verificationCheck.valid,
      phoneNumber: verificationCheck.to,
    })
  } catch (error) {
    logger.error(error, 'Error checking verification challenge')
    return errAsync({
      code: ERRORS.EXTERNAL_API.code,
      message: 'Error checking verification challenge',
    })
  }
}
