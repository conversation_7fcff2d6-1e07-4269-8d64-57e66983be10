import { err, errAsync, from<PERSON>rom<PERSON>, ok, okAsync } from 'neverthrow'

import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { centsToDollars } from 'utils/numeric'
import { prisma } from 'utils/prisma'

import { OrderStatus, OrderType, Prisma } from '@prisma/client'
import {
  EPAY_GST_NUMBER,
  EPAY_LIVE_AGENT,
  EPAY_SUPPORT_EMAIL,
} from 'constants/admin'
import { EpayOrdersListProps } from 'types/epay.order'
import { PAYMENT_RECEIVED } from '../helpers/email-templates'
import { findCustomProductByOrderNumber } from './custom-product.service'
import { aggregateFundingInvoiceLines } from './funding.service'
import { sendEmailWithTemplate } from './postmark.service'
import { aggregateInvoiceLines } from './product-order.service'
import { getUserFirstNameLastName } from './user.service'
import {
  epayFetchInvoiceDataForProductOrder,
  ProductOrderWithAdminRelations,
} from 'epay-data/epay-product-order.data'

// product.service.ts

export function getInvoiceForAdmin(orderNumber: string) {
  return epayFetchInvoiceDataForProductOrder(orderNumber).andThen(
    processProductOrderForAdmin
  )
}

function processProductOrderForAdmin(
  productOrder: ProductOrderWithAdminRelations | null
) {
  if (!productOrder) {
    return errAsync(ERRORS.NOT_FOUND)
  }

  const commonInvoiceData = getAdminInvoiceData(productOrder)

  switch (productOrder.orderType) {
    case OrderType.STOCK:
      return processAdminStockOrder(productOrder, commonInvoiceData)
    case OrderType.SINGLE_FUNDS_LOAD:
      return processAdminFundingOrder(productOrder, commonInvoiceData)
    default:
      return processAdminDefaultOrder(productOrder, commonInvoiceData)
  }
}

function getAdminInvoiceData(productOrder: ProductOrderWithAdminRelations) {
  return {
    orderId: productOrder.id,
    orderNumber: productOrder.orderNumber,
    orderStatus: productOrder.orderStatus,
    orderType: productOrder.orderType,
    organizationName: productOrder.organization?.name,
    billingAddress: productOrder.billingAddress,
    city: productOrder.city,
    country: productOrder.country,
    postcode: productOrder.postCode,
    paymentMethod: productOrder.paymentMethod,
    purchaseOrderNumber: productOrder.purchaseOrderNumber,
    invoiceDate: productOrder.createdAt,
    releaseDate: productOrder.submittedAt,
    contactPerson: productOrder.createdByLiveAgent
      ? EPAY_LIVE_AGENT
      : `${productOrder.user.firstName} ${productOrder.user.lastName}`,
    emailAddress: productOrder.createdByLiveAgent
      ? EPAY_SUPPORT_EMAIL
      : productOrder.user.email,
    gstNumber: EPAY_GST_NUMBER,
    orderTotal: centsToDollars(productOrder.orderTotal),
    creditCardFee: centsToDollars(productOrder.creditCardFee),
    shippingTotal: centsToDollars(productOrder.shippingTotal),
    discountTotal: centsToDollars(
      productOrder.discountTotal! - productOrder.loadingFeeDiscountTotal!
    ),
    loadFeeDiscountTotal: centsToDollars(productOrder.loadingFeeDiscountTotal!),
    gstAmount: centsToDollars(productOrder.gstAmount),
    subTotal: centsToDollars(productOrder.subTotal),
    releasedBy: productOrder.releasedBy,
    paymentDate: productOrder.paymentDate,
    scheduledDate: productOrder.scheduledDate,
    notes: productOrder.productOrderNotes,
    authorizedByEmail: productOrder.authorizedByEmail,
    authorizedByName: productOrder.authorizedByName,
  }
}

function processAdminStockOrder(
  productOrder: ProductOrderWithAdminRelations,
  commonInvoiceData: ReturnType<typeof getAdminInvoiceData>
) {
  const stockCardInvoiceItems = productOrder.productOrderItems.map((item) => ({
    productCode: item.productCode,
    productName: item.product.name,
    quantity: item.quantity,
    resolution: (item.options as { resolution?: 'LOW' | 'HIGH' })?.resolution,
    discount: centsToDollars(item.discount || 0),
    printItemPrice: centsToDollars(item.itemFee || 0),
  }))

  return okAsync({
    ...commonInvoiceData,
    stockCardInvoiceItems,
    totalQuantity: productOrder.totalQuantity,
  })
}

function processAdminFundingOrder(
  productOrder: ProductOrderWithAdminRelations,
  commonInvoiceData: ReturnType<typeof getAdminInvoiceData>
) {
  const invoiceItems = aggregateFundingInvoiceLines({
    productOrderItems: productOrder.FundingProductOrderItem,
  })

  return okAsync({
    ...commonInvoiceData,
    loadingFeeTotal: centsToDollars(productOrder.loadingFeeTotal),
    digitalFeeTotal: centsToDollars(productOrder.digitalFeeTotal),
    invoiceItems,
  })
}

function processAdminDefaultOrder(
  productOrder: ProductOrderWithAdminRelations,
  commonInvoiceData: ReturnType<typeof getAdminInvoiceData>
) {
  const invoiceItems = aggregateInvoiceLines({
    productOrderItems: productOrder.productOrderItems,
  })

  return okAsync({
    ...commonInvoiceData,
    loadingFeeTotal: centsToDollars(productOrder.loadingFeeTotal),
    digitalFeeTotal: centsToDollars(productOrder.digitalFeeTotal),
    invoiceItems,
  })
}

export function getShippingDetailsForAdmin(orderNumber: string) {
  return fromPromise(
    prisma.productOrder.findUnique({
      where: { orderNumber },
      select: {
        productOrderItems: {
          select: {
            productCode: true,
            quantity: true,
            unitPrice: true,
            recipientName: true,
            recipientAddress: true,
            recipientSuburb: true,
            recipientCity: true,
            recipientPostCode: true,
            recipientEmail: true,
            deliveryMethod: true,
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.info(`Error getting order [${orderNumber}]`, error)

      return ERRORS.DATABASE_ERROR
    }
  ).map((productOrder) => {
    if (!productOrder) {
      return err(ERRORS.NOT_FOUND)
    }

    return productOrder.productOrderItems.map((item) => {
      return {
        productName: item.product?.name,
        productCode: item.productCode,
        quantity: item.quantity,
        faceValue: centsToDollars(item.unitPrice),
        recipientName: item.recipientName,
        address: [
          item.recipientAddress,
          item.recipientSuburb,
          item.recipientCity,
          item.recipientPostCode,
        ]
          .filter(Boolean)
          .join(' '),
        email: item.recipientEmail,
        deliveryMethod: item.deliveryMethod,
      }
    })
  })
}

export function getAllCustomProductOrders({
  filterText,
  endDate,
  startDate,
  status,
  page,
  pageSize,
}: EpayOrdersListProps) {
  logger.info(`Getting all custom product orders for admin portal`)
  const where: Prisma.CustomProductOrderWhereInput = {
    OR: [
      {
        orderNumber: {
          contains: filterText,
          mode: 'insensitive',
        },
      },
      {
        organization: {
          name: {
            contains: filterText,
            mode: 'insensitive',
          },
        },
      },
    ],

    orderStatus: status ?? {
      notIn: [OrderStatus.CANCELLED, OrderStatus.PENDING],
    },

    createdAt: {
      gte: startDate,
      lte: endDate,
    },
  }

  const query = prisma.customProductOrder.findMany({
    where,
    select: {
      id: true,
      orderTotal: true,
      orderNumber: true,
      orderStatus: true,
      createdAt: true,
      organization: {
        select: {
          name: true,
        },
      },
    },
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
      createdAt: 'desc',
    },
  })

  const count = prisma.customProductOrder.count({
    where,
  })

  return fromPromise(prisma.$transaction([count, query]), (error) => {
    logger.warn(`Error getting all custom product orders`, error)
    return err(ERRORS.DATABASE_ERROR)
  }).map(([count, orders]) => {
    const items = orders.map((order) => {
      return {
        id: order.id,
        orderNumber: order.orderNumber,
        orderTotal: centsToDollars(order.orderTotal),
        orderStatus: order.orderStatus,
        createdAt: order.createdAt,
        organizationName: order.organization?.name,
      }
    })

    return { count, items }
  })
}

export function releaseCustomOrder({
  orderId,
  paymentDate,
  userId,
}: {
  orderId: string
  paymentDate?: Date
  userId?: string
}) {
  logger.info(`Releasing custom order [${orderId}]`)

  if (userId) {
    return getUserFirstNameLastName({ userId }).andThen((user) => {
      if (!user) {
        logger.warn(`User [${userId}] not found`)
        return errAsync(ERRORS.NOT_FOUND)
      }

      return releaseOrder({
        orderId,
        paymentDate: paymentDate!,
        releasedBy: `${user.firstName} ${user.lastName}`,
      })
    })
  }

  return releaseOrder({
    orderId,
    paymentDate: new Date(),
    releasedBy: 'Windcave payment',
  })
}

function releaseOrder({
  orderId,
  paymentDate,
  releasedBy,
}: {
  orderId: string
  paymentDate: Date
  releasedBy: string
}) {
  return fromPromise(
    prisma.customProductOrder.update({
      where: {
        id: orderId,
      },
      data: {
        orderStatus: OrderStatus.PROCESSING,
        releasedBy,
        paymentDate,
        submittedAt: new Date(),
      },
      select: {
        orderStatus: true,
        orderNumber: true,
        orderTotal: true,
        paymentDate: true,
        submittedAt: true,
        releasedBy: true,
        user: {
          select: {
            firstName: true,
            email: true,
          },
        },
      },
    }),
    (error) => {
      logger.warn(`Error releasing custom order [${orderId}]`, error)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((order) => {
    if (!order) {
      logger.warn(`Custom order [${orderId}] not found`)
      return err({
        code: ERRORS.NOT_FOUND,
        message: `Custom order [${orderId}] not found`,
      })
    }
    sendEmailWithTemplate({
      email: order.user.email,
      templateId: PAYMENT_RECEIVED,
      templateModel: {
        orderNumber: order.orderNumber,
        orderTotal: centsToDollars(order.orderTotal),
        firstName: order.user.firstName,
      },
    })

    return ok({
      id: orderId,
      orderStatus: order.orderStatus,
      releaseDate: order.submittedAt,
      releasedBy: order.releasedBy,
      paymentDate: order.paymentDate,
    })
  })
}

export function getCustomProductOrderInvoice({
  orderNumber,
}: {
  orderNumber: string
}) {
  logger.info(`Getting custom product order invoice for ${orderNumber}`)

  return findCustomProductByOrderNumber(orderNumber).map(
    (customProductOrder) => {
      if (!customProductOrder) {
        logger.warn(`Custom product order ${orderNumber} not found`)

        return err({
          code: ERRORS.NOT_FOUND.code,
          message: `Custom product order ${orderNumber} not found`,
        })
      }

      return {
        orderId: customProductOrder.id,
        orderNumber: customProductOrder.orderNumber,
        orderStatus: customProductOrder.orderStatus,
        organizationName: customProductOrder.organization?.name,
        billingAddress: customProductOrder.billingAddress,
        city: customProductOrder.city,
        country: customProductOrder.country,
        postcode: customProductOrder.postCode,
        paymentMethod: customProductOrder.paymentMethod,
        purchaseOrderNumber: customProductOrder.purchaseOrderNumber,
        invoiceDate: customProductOrder.createdAt,
        contactPerson: `${customProductOrder.user.firstName} ${customProductOrder.user.lastName}`,
        emailAddress: customProductOrder.user.email,
        discountTotal: centsToDollars(customProductOrder.discountTotal),
        orderTotal: centsToDollars(customProductOrder.orderTotal),
        gstAmount: centsToDollars(customProductOrder.gstAmount),
        gstNumber: EPAY_GST_NUMBER,
        unitPrice: centsToDollars(customProductOrder.logoUploadFee),
        discount: centsToDollars(customProductOrder.discountTotal),
        creditCardFee: centsToDollars(customProductOrder.creditCardFee),
        releasedBy: customProductOrder.releasedBy,
        releaseDate: customProductOrder.submittedAt,
        paymentDate: customProductOrder.paymentDate,
      }
    }
  )
}

export function releaseCustomOrderByOrderNumber(orderNumber: string) {
  return findCustomProductByOrderNumber(orderNumber).andThen((res) => {
    if (res?.id) {
      return releaseCustomOrder({ orderId: res.id })
    }

    logger.warn(`cant find custom product order number ${res?.id}`)
    return errAsync(ERRORS.NOT_FOUND)
  })
}
