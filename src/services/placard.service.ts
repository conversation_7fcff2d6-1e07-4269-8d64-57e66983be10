import SFTPClient from 'ssh2-sftp-client'
import * as fs from 'fs'
import { getFileStreamFromS3 } from 'utils/aws'
import logger from 'utils/logger'
import { errAsync, okAsync } from 'neverthrow'
import {
  AWS_S3_BUCKET_CARD,
  IS_LOCAL,
  PLACARD_SFTP_PASSWORD,
  SFTP_HOST,
  SFTP_PORT,
  SFTP_PRIVATE_KEY,
  SFTP_REMOTE_PATH,
  SFTP_USERNAME,
} from 'utils/config'
import path from 'path'
import { ERRORS } from 'utils/error'
import { notifyTeam } from './postmark.service'
import { uploadToSftp } from 'utils/sftp'

function getPrivateKey() {
  if (IS_LOCAL) {
    return fs.readFileSync('./sftp_private_key').toString()
  }
  return SFTP_PRIVATE_KEY
}

export async function uploadLogoToSftp({ key }: { key: string }) {
  logger.debug(`uploadLogoToSftp... key=${key}`)
  const fsResult = await getFileStreamFromS3({
    key,
    bucket: AWS_S3_BUCKET_CARD,
  })

  if (fsResult.isErr()) {
    logger.info(
      `error getting file stream from s3 ${key} ${JSON.stringify(
        fsResult.error
      )}`
    )
    return errAsync(fsResult.error)
  }
  logger.debug(`successfully got file stream from s3 ${key}`)
  const filename = path.basename(key)

  const uploadResult = await upload({
    fileStream: fsResult.value,
    remotePath: `./${SFTP_REMOTE_PATH}/${filename}`,
  })

  if (uploadResult.isErr()) {
    notifyTeam({
      summary: `[logo] - failed to upload to Placard`,
      description: `${filename}`,
    })
    logger.debug("Couldn't upload to Placard SFTP - notifying team")
    return errAsync(uploadResult.error)
  } else {
    notifyTeam({
      summary: `[logo] - uploaded to Placard`,
      description: `${filename}`,
    })
    return okAsync({ message: 'Successfully uploaded logo to Placard SFTP' })
  }
}

async function upload({
  fileStream,
  remotePath,
}: {
  fileStream: NodeJS.ReadableStream
  remotePath: string
}) {
  try {
    logger.debug(`uploading to sftp ${remotePath}...`)

    const options = {
      host: `${SFTP_HOST}`,
      port: Number(SFTP_PORT),
      username: `${SFTP_USERNAME}`,
      privateKey: getPrivateKey(),
    } as SFTPClient.ConnectOptions

    if (PLACARD_SFTP_PASSWORD) {
      options.passphrase = PLACARD_SFTP_PASSWORD
    }

    const sftpUploadResult = await uploadToSftp({
      fileStream: fileStream,
      remotePath: remotePath,
      options: options,
    })

    if (sftpUploadResult.isErr()) {
      logger.warn("Couldn't upload to Placard SFTP")
      return errAsync(sftpUploadResult.error)
    }

    logger.info('Successfully uploaded logo to Placard SFTP')
    return okAsync({ message: 'Successfully uploaded logo to Placard SFTP' })
  } catch (error) {
    logger.error(
      error,
      `Error with upload to placard ${remotePath}: ${JSON.stringify(error)}`
    )
    return errAsync(ERRORS.SFTP_ERROR)
  }
}
