import {
  FloatFundsTransactionType,
  OrderStatus,
  PaymentMethod,
  UserR<PERSON>,
} from '@prisma/client'
import { ok, err, errAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import {
  createFloatFundsTransaction,
  getFloatFundsTransactions,
  getOrgFloatFundsBalance,
  upsertFloatFundOrg,
  activateFloatFundOrg,
  deactivateFloatFundOrg,
  createFloatFundsOrder,
  getUnprocessedFloat<PERSON>undsOrder,
  getFloatFundsTransactionById,
} from 'data/float-funds.data'
import { getUserById } from './user.service'
import { epayUpdateProductOrderStatusByOrder } from 'epay-data/epay-product-order.data'
import logger from 'utils/logger'

export function createTransaction({
  orgId,
  amount,
  transactionType,
  createdByUserId,
  orderNumber,
  orderId,
  description,
  note,
  bankAccountName,
  bankAccountNumber,
}: {
  orgId: string
  amount: number
  transactionType: FloatFundsTransactionType
  createdByUserId: string
  orderNumber?: string
  orderId?: string
  description: string
  note?: string
  bankAccountName?: string
  bankAccountNumber?: string
}) {
  if (amount <= 0) {
    return err({
      code: errorCodes.db.BAD_INPUT,
      message: 'Amount must be greater than 0',
    } as SimpleError)
  }

  return getUserById({ id: createdByUserId }).andThen((user) => {
    if (!user) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'User not found',
      } as SimpleError)
    }
    if (user.role !== UserRole.EPAY_ADMIN && user.organization?.id !== orgId) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message:
          'Only EPAY_ADMIN can create transactions for other organizations',
      } as SimpleError)
    }

    return createFloatFundsTransaction({
      orgId,
      amount,
      transactionType,
      createdByUserId,
      orderNumber,
      orderId,
      description,
      note,
      bankAccountName,
      bankAccountNumber,
    })
  })
}

export function getTransactions({
  requstedByUserId,
  orgId,
  startDate,
  endDate,
  transactionType,
  page,
  pageSize,
  orderNumber,
}: {
  requstedByUserId: string
  orgId: string
  startDate?: Date
  endDate?: Date
  transactionType?: FloatFundsTransactionType
  page: number
  pageSize: number
  orderNumber?: string
}) {
  return getUserById({ id: requstedByUserId }).andThen((user) => {
    if (!user) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'User not found',
      } as SimpleError)
    }
    if (user.role !== UserRole.EPAY_ADMIN && user.organization?.id !== orgId) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'Only EPAY_ADMIN can view other organization transactions',
      } as SimpleError)
    }

    return getFloatFundsTransactions({
      orgId,
      startDate,
      endDate,
      transactionType,
      page,
      pageSize,
      orderNumber,
    })
  })
}

export function getTransactionById({
  transactionId,
  orgId,
  userId,
}: {
  transactionId: number
  orgId: string
  userId: string
}) {
  return getUserById({ id: userId }).andThen((user) => {
    if (!user) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'User not found',
      } as SimpleError)
    }
    if (user.role !== UserRole.EPAY_ADMIN && user.organization?.id !== orgId) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'Only EPAY_ADMIN can view other organization transactions',
      } as SimpleError)
    }

    return getFloatFundsTransactionById(transactionId)
  })
}

export function getBalance({
  requstedByUserId,
  orgId,
}: {
  requstedByUserId: string
  orgId: string
}) {
  return getUserById({ id: requstedByUserId }).andThen((user) => {
    if (!user) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'User not found',
      } as SimpleError)
    }
    if (user.role !== UserRole.EPAY_ADMIN && user.organization?.id !== orgId) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'Only EPAY_ADMIN can view other organization balances',
      } as SimpleError)
    }

    return getOrgFloatFundsBalance(orgId).andThen((balance) => {
      // EPAY_ADMIN can view inactive float funds accounts
      if (user.role !== UserRole.EPAY_ADMIN && !balance.isActive) {
        return err({
          code: errorCodes.db.BAD_INPUT,
          message: 'Float funds account is not active',
        } as SimpleError)
      }
      return ok(balance)
    })
  })
}

export function activateFloatFundsForOrganization({
  orgId,
  updatedByUserId,
}: {
  orgId: string
  updatedByUserId: string
}) {
  return getUserById({ id: updatedByUserId }).andThen((user) => {
    if (user!.role !== UserRole.EPAY_ADMIN) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'Only EPAY_ADMIN can activate float funds',
      } as SimpleError)
    }

    return upsertFloatFundOrg({
      orgId,
      updatedByUserId,
    }).andThen(() => activateFloatFundOrg(orgId))
  })
}

export function deactivateFloatFundsForOrganization({
  orgId,
  updatedByUserId,
}: {
  orgId: string
  updatedByUserId: string
}) {
  return getUserById({ id: updatedByUserId }).andThen((user) => {
    if (user!.role !== UserRole.EPAY_ADMIN) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'Only EPAY_ADMIN can deactivate float funds',
      } as SimpleError)
    }

    return upsertFloatFundOrg({
      orgId,
      updatedByUserId,
    }).andThen(() => deactivateFloatFundOrg(orgId))
  })
}

export function createOrder({
  orgId,
  userId,
  amount,
  billingAddress,
  city,
  country,
  postCode,
  paymentMethod,
  purchaseOrderNumber,
}: {
  orgId: string
  userId: string
  amount: number
  billingAddress: string
  city: string
  country: string
  postCode: string
  paymentMethod: PaymentMethod
  purchaseOrderNumber?: string
}) {
  return getUserById({ id: userId }).andThen((user) => {
    if (!user) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'User not found',
      } as SimpleError)
    }

    if (user.role !== UserRole.EPAY_ADMIN && user.organization?.id !== orgId) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message:
          'Only EPAY_ADMIN can create float funds top up order for other organizations',
      } as SimpleError)
    }

    return createFloatFundsOrder({
      orgId,
      userId,
      amount,
      billingAddress,
      city,
      country,
      postCode,
      paymentMethod,
      purchaseOrderNumber,
    })
  })
}

export function processFloatFundsOrder({
  orderId,
  paymentDate,
  userId,
}: {
  orderId: string
  paymentDate: Date
  userId: string
}) {
  return getUserById({ id: userId }).andThen((user) => {
    if (user?.role !== UserRole.EPAY_ADMIN) {
      return err({
        code: errorCodes.UNAUTHORIZED,
        message: 'Only EPAY_ADMIN can process float funds order',
      } as SimpleError)
    }

    logger.info('(A). processFloatFundsOrder: Find order')
    return getUnprocessedFloatFundsOrder({ orderId }).andThen(
      (productOrder) => {
        if (!productOrder) {
          const message = `No unprocessed float funds order found for orderId: ${orderId}`
          const logRef = logger.error(new Error(message), message)
          return errAsync({
            code: errorCodes.db.ITEM_NOT_FOUND,
            message,
            logRef,
          } as SimpleError)
        }

        const orderNumber = productOrder.orderNumber!

        logger.info('(B). processFloatFundsOrder: create DEPOSIT transaction')
        return createFloatFundsTransaction({
          orgId: productOrder.organizationId,
          amount: productOrder.orderTotal || 0,
          transactionType: FloatFundsTransactionType.CREDIT,
          createdByUserId: productOrder.userId,
          orderNumber,
          orderId,
          description: 'Customer top-up',
        })
          .andThen(() => {
            logger.info(
              '(B). processFloatFundsOrder: updating order status to COMPLETED'
            )
            return epayUpdateProductOrderStatusByOrder({
              orderStatus: OrderStatus.COMPLETED,
              releasedAt: new Date(),
              orderNumber,
              paymentDate,
              releasedBy: `${user.firstName} ${user.lastName}`,
            })
          })
          .orElse((error) => {
            return epayUpdateProductOrderStatusByOrder({
              orderStatus: OrderStatus.ON_HOLD,
              releasedAt: new Date(),
              orderNumber,
              paymentDate,
              releasedBy: `${user.firstName} ${user.lastName}`,
            }).andThen(() => {
              const logRef = logger.error(
                error,
                `ERROR processFloatFundsOrder.. Failed to complete processing of order [${orderNumber}]`
              )
              return errAsync({
                code: errorCodes.order.PROCESSING_ERROR,
                message: `processFloatFundsOrder.. Failed to complete processing of order [${orderNumber}]`,
                logRef,
              } as SimpleError)
            })
          })
      }
    )
  })
}
