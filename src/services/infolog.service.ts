import {
  err,
  errAsync,
  from<PERSON>rom<PERSON>,
  ok,
  okAsync,
  Result,
  ResultAsync,
} from 'neverthrow'
import levenshtein from 'fast-levenshtein'

import { ERRORS } from 'utils/error'
import {
  getEntity,
  infoLogSessionReference,
  pepCheck,
  searchInfolog,
} from 'utils/infolog'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

import type { NzbnEntity, NzbnShareholder } from '../types/infolog'

import { BeneficialOwnerType, Prisma } from '@prisma/client'
import { containsSpecialCharOrAccent } from 'utils/contains-special-chars'
import { findInfologSessionId } from './corporate-application.service'
import { checkOrgAlreadyExists } from './organization.service'
import { ZCorporateApplication } from 'routes/corporate-application.route'

function mapEntityDataToPrismaTypes(entityData: NzbnEntity) {
  const corporateApplicationData: Prisma.CorporateApplicationUpdateInput = {
    name: entityData.EntityName,
    nzbn: entityData.NZBN,
    tradingName: entityData.TradingNames[0]?.Name,
    address: entityData.RegisteredAddress[0]?.Address1,
    city: entityData.RegisteredAddress[0]?.Address3,
    suburb: entityData.RegisteredAddress[0]?.Address2,
    zip: entityData.RegisteredAddress[0]?.PostCode,
    country: entityData.RegisteredAddress[0]?.CountryCode,
    principalPlace: entityData.PrincipalPlaceOfActivity[0]?.Address1,
    natureAndPurpose:
      entityData.IndustryClassification[0]?.ClassificationDescription,
    phone: entityData.PhoneNumber[0]?.PhoneNumber,
  }

  const directors: Prisma.DirectorCreateInput[] = entityData.Roles.filter(
    (role) => role.RoleType === 'Director' && role.RoleStatus === 'ACTIVE'
  ).map((director) => ({
    firstName: director.RolePerson.FirstName,
    lastName: director.RolePerson.LastName,
    address: director.RoleAddress[0]?.Address1,
    city: director.RoleAddress[0]?.Address3,
    country: director.RoleAddress[0]?.CountryCode,
    zip: director.RoleAddress[0]?.PostCode,
  }))

  const totalShares = entityData.Company.Shareholding.NumberOfShares ?? 1
  const shareAllocations = entityData.Company.Shareholding.ShareAllocation

  const beneficialOwners: Prisma.BeneficialOwnerCreateInput[] = []
  let hasEntityOwnerOver75Percent = false

  for (const allocation of shareAllocations) {
    const ownershipPercentage = (allocation.Allocation ?? 0) / totalShares

    if (ownershipPercentage >= 0.25) {
      for (const shareholder of allocation.Shareholder) {
        const beneficialOwner = mapShareholderToBeneficialOwner(shareholder)
        beneficialOwners.push(beneficialOwner)

        if (ownershipPercentage > 0.75 && beneficialOwner.entityName) {
          hasEntityOwnerOver75Percent = true
        }
      }
    }
  }

  const hasNoIndividualBeneficialOwner = hasEntityOwnerOver75Percent

  return okAsync({
    corporateApplicationData: {
      ...corporateApplicationData,
      hasNoIndividualBeneficialOwner,
    },
    directors,
    beneficialOwners,
  })
}

function mapShareholderToBeneficialOwner(
  shareholder: NzbnShareholder
): Prisma.BeneficialOwnerCreateInput {
  if (shareholder.IndividualShareholder) {
    return {
      beneficialOwnerType: BeneficialOwnerType.INDIVIDUAL,
      firstName: shareholder.IndividualShareholder.FirstName,
      lastName: shareholder.IndividualShareholder.LastName,
      address: shareholder.ShareholderAddress?.Address1,
      city: shareholder.ShareholderAddress?.Address3,
      country: shareholder.ShareholderAddress?.CountryCode,
      zip: shareholder.ShareholderAddress?.PostCode,
    }
  } else if (shareholder.OtherShareholder) {
    return {
      firstName: null,
      lastName: null,
      beneficialOwnerType: BeneficialOwnerType.ENTITY,
      address: shareholder.ShareholderAddress?.Address1,
      city: shareholder.ShareholderAddress?.Address3,
      country: shareholder.ShareholderAddress?.CountryCode,
      zip: shareholder.ShareholderAddress?.PostCode,
      entityName: shareholder.OtherShareholder.CurrentEntityName,
      nzbn: shareholder.OtherShareholder.NZBN,
    }
  } else {
    logger.warn('Unexpected shareholder type', shareholder)
    return {
      firstName: null,
      lastName: null,
    }
  }
}

function updateApplicationWithInfologData(
  corporateApplicationId: string,
  data: {
    corporateApplicationData: Prisma.CorporateApplicationUpdateInput
    directors: Prisma.DirectorCreateInput[]
    beneficialOwners: Prisma.BeneficialOwnerCreateInput[]
  }
) {
  return fromPromise(
    prisma.corporateApplication.update({
      where: { id: corporateApplicationId },
      data: {
        ...data.corporateApplicationData,
        directors: {
          deleteMany: {},
          create: data.directors,
        },
        beneficialOwners: {
          deleteMany: {},
          create: data.beneficialOwners,
        },
      },
      include: {
        directors: true,
        beneficialOwners: true,
      },
    }),
    (error) => {
      logger.warn(
        `Error updating corporate application: ${corporateApplicationId}`,
        error
      )
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((application) => {
    if (!application) {
      logger.warn(
        `Failed to update corporate application: ${corporateApplicationId}`
      )
      return errAsync(ERRORS.DATABASE_ERROR)
    }

    logger.info(
      `Successfully updated corporate application: ${corporateApplicationId}`
    )

    logger.info('application', application)
    return okAsync({ applicationData: application, hasInfologSession: false })
  })
}

function mappEntityDataAndSaveApplication({
  entityData,
  applicationId,
}: {
  entityData: NzbnEntity
  applicationId: string
}) {
  return mapEntityDataToPrismaTypes(entityData).andThen((data) => {
    return updateApplicationWithInfologData(applicationId, data)
  })
}

function getEntityFromInfolog_v2({
  nzbn,
  infologSessionId,
}: {
  nzbn: string
  infologSessionId?: string
}) {
  return fromPromise(
    getEntity({ NZBN: nzbn, SearchId: infologSessionId }),
    (error) => {
      logger.warn(`Error fetching entity from Infolog for NZBN: ${nzbn}`, error)
      return ERRORS.EXTERNAL_API
    }
  ).andThen(({ data, status }) => {
    if (status !== 200) {
      logger.warn(`Received non-200 status from Infolog API: ${status}`)
      return errAsync(ERRORS.EXTERNAL_API)
    }
    logger.debug(`Successfully retrieved entity from Infolog for NZBN: ${nzbn}`)
    return okAsync(data.Entity)
  })
}

// export function searchInfologAndVerifyNZBN_v2({
//   companyName,
//   nzbn,
//   corporateApplicationId,
//   userId,
// }: {
//   companyName: string
//   nzbn: string
//   corporateApplicationId: string
//   userId: string
// }) {
//   logger.info(
//     `Starting searchInfologAndVerifyNZBN for company: ${companyName}, NZBN: ${nzbn}`
//   )
//   return checkOrgAlreadyExists({ nzbn }).andThen(({ exists }) => {
//     if (exists) {
//       logger.debug(`Organization with NZBN ${nzbn} already exists`)
//       return errAsync(ERRORS.KYC_ORG_ALREADY_EXISTS)
//     }

//     return findInfologSessionId({ corporateApplicationId, userId }).andThen(
//       (infologSessionId) => {
//         if (infologSessionId?.infologSessionId) {
//           logger.debug(
//             `Infolog session ID found for corporate application: ${corporateApplicationId}. Skipping Infolog requests`
//           )

//           return okAsync({ hasInfologSession: true })
//         }

//         if (containsSpecialCharOrAccent(companyName)) {
//           return getEntityWithNoSessionReference({
//             nzbn,
//             applicationId: corporateApplicationId,
//           })
//         } else {
//           return searchInfologAndCreateSessionReference({
//             companyName,
//             nzbn,
//             corporateApplicationId,
//           })
//         }
//       }
//     )
//   })
// }

function searchInfologAndCreateSessionReference_v2({
  companyName,
  nzbn,
  corporateApplicationId,
}: {
  companyName: string
  nzbn: string
  corporateApplicationId: string
}) {
  logger.info(`Searching Infolog for company: ${companyName}, NZBN: ${nzbn}`)
  return fromPromise(
    searchInfolog({
      Name: companyName,
      SessionReference: infoLogSessionReference(companyName, nzbn),
    }),
    (error) => {
      logger.error(error, 'searching infolog')
      return { ...ERRORS.EXTERNAL_API, data: {} }
    }
  ).andThen(({ data, status }) => {
    if (status !== 200) {
      logger.error(data, 'searchInfolog failed')
      return errAsync({ ...ERRORS.EXTERNAL_API, data: {} })
    }

    const nzbnMatch = data.MatchOnNameOnlyItems.find(
      (item) => item.NZBN === nzbn
    )

    if (!nzbnMatch) {
      logger.warn(
        `No NZBN match found for company: ${companyName}, NZBN: ${nzbn}`
      )
      return errAsync({
        ...ERRORS.KYC_ORG_NAME,
        data: { orgName: companyName },
      })
    }

    logger.info(
      `NZBN match found for company: ${companyName}. Saving Infolog session ID.`
    )
    return saveInfologSessionId({
      corporateApplicationId,
      infologSessionId: data.SearchId,
    }).andThen((application) => {
      if (!application || !application.infologSessionId)
        return errAsync(ERRORS.DATABASE_ERROR)

      return getEntityFromInfolog({
        nzbn,
        infologSessionId: application.infologSessionId,
      }).andThen((entityData) => {
        if (!entityData) return errAsync(ERRORS.EXTERNAL_API)

        return mappEntityDataAndSaveApplication({
          entityData,
          applicationId: corporateApplicationId,
        })
      })
    })
  })
}

function getEntityWithNoSessionReference_v2({
  nzbn,
  applicationId,
}: {
  nzbn: string
  applicationId: string
}) {
  logger.info(`Getting entity without session reference for NZBN: ${nzbn},`)

  return getEntityFromInfolog({ nzbn }).andThen((infoLogEntity) => {
    if (!infoLogEntity) {
      return errAsync(ERRORS.EXTERNAL_API)
    }

    return mappEntityDataAndSaveApplication({
      entityData: infoLogEntity,
      applicationId,
    })
  })
}

// Save Infolog session ID
function saveInfologSessionId_v2({
  corporateApplicationId,
  infologSessionId,
}: {
  corporateApplicationId: string
  infologSessionId: string
}) {
  logger.info(
    `Saving Infolog session ID: ${infologSessionId} for corporate application: ${corporateApplicationId}`
  )
  return fromPromise(
    prisma.corporateApplication.update({
      where: { id: corporateApplicationId },
      data: { infologSessionId },
    }),
    (error) => {
      logger.error(error, 'saving infolog session id')
      return { ...ERRORS.DATABASE_ERROR, data: {} }
    }
  )
}

export type PepCheckData = {
  firstName: string
  lastName: string
  dateOfBirth: Date
  driverLicenseVersion?: string
  identificationNbr: string
  passportExpiryDate?: Date
  identificationType?: 'DRIVER_LICENSE' | 'PASSPORT'
}

// Verify identities
export function verifyIdentities_v2({
  SessionReference,
  beneficialOwners,
}: {
  SessionReference: string
  beneficialOwners: PepCheckData[]
}): ResultAsync<IdentityVerificationResult, typeof ERRORS.NOT_FOUND> {
  logger.info(
    `Starting identity verification for ${beneficialOwners.length} beneficial owners, SessionReference: ${SessionReference}`
  )

  const beneficialOwnerPromises = beneficialOwners.map(
    async (beneficialOwner) => {
      logger.debug(
        `Verifying beneficial owner: ${beneficialOwner.firstName} ${beneficialOwner.lastName}, ID Type: ${beneficialOwner.identificationType}`
      )
      if (beneficialOwner.identificationType === 'DRIVER_LICENSE') {
        logger.debug(
          `Performing PEP check with driver's license for ${beneficialOwner.firstName} ${beneficialOwner.lastName}`
        )
        return pepCheck({
          SessionReference,
          FirstName: beneficialOwner.firstName ?? '',
          LastName: beneficialOwner.lastName ?? '',
          DateOfBirth: beneficialOwner.dateOfBirth!,
          DriversLicenceNumber: beneficialOwner.identificationNbr ?? '',
          DriversLicenceVersion: beneficialOwner.driverLicenseVersion ?? '',
          IncludeDriverLicenseCheck: true,
        })
      }

      logger.debug(
        `Performing PEP check with passport for ${beneficialOwner.firstName} ${beneficialOwner.lastName}`
      )
      return pepCheck({
        SessionReference,
        FirstName: beneficialOwner.firstName ?? '',
        LastName: beneficialOwner.lastName ?? '',
        DateOfBirth: beneficialOwner.dateOfBirth!,
        TravelDocNum: beneficialOwner.identificationNbr ?? '',
        ExpiryDate: beneficialOwner.passportExpiryDate!,
        IncludePassportCheck: true,
        PassportConsent: true,
      })
    }
  )

  return ResultAsync.fromPromise(
    Promise.all(beneficialOwnerPromises),
    (error) => {
      logger.error(error, 'PEP check failed for beneficial owners')
      return ERRORS.NOT_FOUND
    }
  ).map((beneficialOwnerResults) => {
    const invalidIdentities = beneficialOwnerResults.filter(
      (identity) => !identity.valid
    )

    const valid = invalidIdentities.length === 0
    logger.info(
      `Identity verification completed. Valid: ${valid}, Invalid identities: ${invalidIdentities.length}`
    )

    return { valid, invalidIdentities }
  })
}

export function searchInfologAndVerifyNZBN({
  companyName,
  nzbn,
  corporateApplicationId,
  userId,
}: {
  companyName: string
  nzbn: string
  corporateApplicationId: string
  userId: string
}) {
  logger.info(
    `Starting searchInfologAndVerifyNZBN for company: ${companyName}, NZBN: ${nzbn}`
  )
  return checkOrgAlreadyExists({ nzbn }).andThen(({ exists }) => {
    if (exists) {
      logger.debug(`Organization with NZBN ${nzbn} already exists`)
      return errAsync(ERRORS.KYC_ORG_ALREADY_EXISTS)
    }

    return findInfologSessionId({ corporateApplicationId, userId }).andThen(
      (infologSessionId) => {
        if (infologSessionId) {
          logger.debug(
            `Infolog session ID found for corporate application: ${corporateApplicationId}. Skipping Infolog requests`
          )

          return okAsync({ exists: true, validNZBN: true })
        }

        if (containsSpecialCharOrAccent(companyName)) {
          return getEntityWithNoSessionReference({ nzbn, companyName })
        } else {
          return searchInfologAndCreateSessionReference({
            companyName,
            nzbn,
            corporateApplicationId,
          })
        }
      }
    )
  })
}

function searchInfologAndCreateSessionReference({
  companyName,
  nzbn,
  corporateApplicationId,
}: {
  companyName: string
  nzbn: string
  corporateApplicationId: string
}) {
  logger.info(`Searching Infolog for company: ${companyName}, NZBN: ${nzbn}`)
  return fromPromise(
    searchInfolog({
      Name: companyName,
      SessionReference: infoLogSessionReference(companyName, nzbn),
    }),
    (error) => {
      logger.error(error, 'searching infolog')
      return { ...ERRORS.EXTERNAL_API, data: {} }
    }
  ).andThen(({ data, status }) => {
    if (status !== 200) {
      logger.error(data, 'searchInfolog failed')
      return errAsync({ ...ERRORS.EXTERNAL_API, data: {} })
    }

    const nzbnMatch = data.MatchOnNameOnlyItems.find(
      (item) => item.NZBN === nzbn
    )

    if (!nzbnMatch) {
      logger.warn(
        `No NZBN match found for company: ${companyName}, NZBN: ${nzbn}`
      )
      return errAsync({
        ...ERRORS.KYC_ORG_NAME,
        data: { orgName: companyName },
      })
    }

    logger.info(
      `NZBN match found for company: ${companyName}. Saving Infolog session ID.`
    )
    return saveInfologSessionId({
      corporateApplicationId,
      infologSessionId: data.SearchId,
    }).map(() => {
      return { exists: false, validNZBN: nzbnMatch }
    })
  })
}

function getEntityWithNoSessionReference({
  nzbn,
  companyName,
}: {
  nzbn: string
  companyName: string
}) {
  logger.info(
    `Getting entity without session reference for NZBN: ${nzbn}, Company: ${companyName}`
  )
  return fromPromise(
    getEntity({
      NZBN: nzbn,
    }),
    (error) => {
      logger.error(error, 'getEntityWithNoSessionReference')
      return ERRORS.EXTERNAL_API
    }
  ).andThen(({ data, status }) => {
    if (status !== 200) {
      logger.error(`Failed to get entity for NZBN: ${nzbn}. Status: ${status}`)
      return err(ERRORS.EXTERNAL_API)
    }

    logger.info(
      `Successfully retrieved entity for NZBN: ${nzbn}. Verifying organization name.`
    )
    return verifyOrganizationName(companyName, data.Entity).map(() => {
      return { exits: false, validNZBN: true }
    })
  })
}

// Save Infolog session ID
function saveInfologSessionId({
  corporateApplicationId,
  infologSessionId,
}: {
  corporateApplicationId: string
  infologSessionId: string
}) {
  logger.info(
    `Saving Infolog session ID: ${infologSessionId} for corporate application: ${corporateApplicationId}`
  )
  return fromPromise(
    prisma.corporateApplication.update({
      where: { id: corporateApplicationId },
      data: { infologSessionId },
    }),
    (error) => {
      logger.error(error, 'saving infolog session id')
      return { ...ERRORS.DATABASE_ERROR, data: {} }
    }
  )
}
// Verify organization entity

type VerificationResult = {
  hasNoIndividualBeneficialOwner: boolean
  beneficialEntities: string[]
}

type ErrorData = {
  incorrectBeneficialOwners: string[]
  missingBeneficialOwners: string[]
}

export function verifyOrganizationEntity({
  infologSessionId,
  entity,
}: {
  infologSessionId?: string
  entity: ZCorporateApplication
}) {
  logger.info(
    `Starting organization entity verification for session ${infologSessionId}`
  )

  return getEntityFromInfolog({
    infologSessionId,
    nzbn: entity.organization.nzbn,
  })
    .andThen((infoLogEntity) => {
      logger.debug(
        `Entity retrieved from Infolog for NZBN: ${entity.organization.nzbn}`
      )
      return verifyOrganizationName(entity.organization.name, infoLogEntity)
    })
    .andThen((infoLogEntity) => {
      logger.debug(
        `Organization name verified for: ${entity.organization.name}`
      )
      return verifyBeneficialOwners(entity, infoLogEntity)
    })
    .map((result) => {
      logger.info(
        `Organization entity verification completed for session ${infologSessionId}`
      )
      return result
    })
    .mapErr((error) => {
      logger.warn(
        `Organization entity verification failed for session ${infologSessionId}`,
        error
      )
      return error
    })
}

function getEntityFromInfolog({
  infologSessionId,
  nzbn,
}: {
  infologSessionId?: string
  nzbn: string
}) {
  logger.debug(`Fetching entity from Infolog for NZBN: ${nzbn}`)
  return fromPromise(
    getEntity({
      SearchId: infologSessionId,
      NZBN: nzbn,
    }),
    (error) => {
      logger.error(
        error,
        `Error fetching entity from Infolog for NZBN: ${nzbn}`
      )
      return ERRORS.EXTERNAL_API
    }
  ).andThen(({ data, status }) => {
    if (status !== 200) {
      logger.warn(`Received non-200 status from Infolog API: ${status}`)
      return err(ERRORS.EXTERNAL_API)
    }
    logger.debug(`Successfully retrieved entity from Infolog for NZBN: ${nzbn}`)
    return ok(data.Entity)
  })
}

function verifyOrganizationName(
  orgName: string,
  infoLogEntity: NzbnEntity
): Result<NzbnEntity, typeof ERRORS.KYC_ORG_NAME> {
  logger.debug(`Verifying organization name: ${orgName}`)
  const orgNameDistance = levenshtein.get(orgName, infoLogEntity.EntityName, {
    useCollator: true,
  })

  logger.debug(`Levenshtein distance for organization name: ${orgNameDistance}`)

  if (orgNameDistance > 10) {
    logger.warn(
      `Organization name verification failed. Distance: ${orgNameDistance}, Org Name: ${orgName}, InfoLog Name: ${infoLogEntity.EntityName}`
    )
    return err({
      ...ERRORS.KYC_ORG_NAME,
      data: {
        orgName: orgName,
        entityName: infoLogEntity.EntityName,
      },
    })
  }

  logger.debug(`Organization name verified successfully: ${orgName}`)
  return ok(infoLogEntity)
}

function verifyBeneficialOwners(
  entity: ZCorporateApplication,
  infoLogEntity: NzbnEntity
): Result<
  VerificationResult,
  typeof ERRORS.KYC_IDENTITY | typeof ERRORS.BAD_INPUT
> {
  logger.debug(
    `Verifying beneficial owners for entity: ${entity.organization.name}`
  )

  if (entity.beneficialOwners.hasNoIndividualBeneficialOwner) {
    logger.info(
      `Entity has no individual beneficial owner: ${entity.organization.name}`
    )
    return ok({
      hasNoIndividualBeneficialOwner: true,
      beneficialEntities: [],
    })
  }

  if (entity.beneficialOwners.beneficialOwners === undefined) {
    logger.warn(
      `Beneficial owners undefined for entity: ${entity.organization.name}`
    )
    return err(ERRORS.BAD_INPUT)
  }

  const { errorData, beneficialEntities } = processBeneficialOwners(
    entity.beneficialOwners.beneficialOwners,
    infoLogEntity
  )

  if (
    errorData.incorrectBeneficialOwners.length > 0 ||
    errorData.missingBeneficialOwners.length > 0
  ) {
    logger.warn(
      `Beneficial owner verification failed for entity: ${entity.organization.name}`,
      errorData
    )
    return err({ ...ERRORS.KYC_IDENTITY, data: errorData })
  }

  logger.info(
    `Beneficial owners verified successfully for entity: ${entity.organization.name}`
  )
  return ok({
    hasNoIndividualBeneficialOwner: false,
    beneficialEntities,
  })
}

type ProcessingResult = {
  errorData: ErrorData
  beneficialEntities: string[]
}

function processBeneficialOwners(
  beneficialOwners: NonNullable<
    ZCorporateApplication['beneficialOwners']['beneficialOwners']
  >,
  infoLogEntity: NzbnEntity
): ProcessingResult {
  logger.debug(
    `Processing beneficial owners for entity: ${infoLogEntity.EntityName}`
  )

  const errorData: ErrorData = {
    incorrectBeneficialOwners: [],
    missingBeneficialOwners: [],
  }
  const beneficialEntities: string[] = []
  const beneficialOwnersCopy = [...beneficialOwners]

  const numberOfShares = infoLogEntity.Company.Shareholding.NumberOfShares
  if (numberOfShares && numberOfShares > 0) {
    logger.debug(`Processing shareholdings. Total shares: ${numberOfShares}`)
    for (const shareAllocation of infoLogEntity.Company.Shareholding
      .ShareAllocation) {
      if (
        shareAllocation.Allocation &&
        shareAllocation.Allocation / numberOfShares >= 0.25
      ) {
        logger.debug(
          `Processing share allocation: ${shareAllocation.Allocation} shares`
        )
        processShareAllocation(
          shareAllocation.Shareholder,
          beneficialOwnersCopy,
          errorData,
          beneficialEntities
        )
      }
    }
  }

  errorData.incorrectBeneficialOwners = beneficialOwnersCopy.map(
    (beneficialOwner) =>
      `${beneficialOwner.firstName} ${beneficialOwner.lastName}`
  )

  logger.debug(
    `Beneficial owner processing completed. Errors: ${JSON.stringify(
      errorData
    )}, Entities: ${JSON.stringify(beneficialEntities)}`
  )
  return { errorData, beneficialEntities }
}

function processShareAllocation(
  shareholders: NzbnShareholder[],
  beneficialOwners: NonNullable<
    ZCorporateApplication['beneficialOwners']['beneficialOwners']
  >,
  errorData: ErrorData,
  beneficialEntities: string[]
) {
  const { index, type, name } = findBeneficialOwnerIndex({
    beneficialOwners,
    shareholders,
  })
  logger.debug(
    `Processing share allocation for: ${name}, Type: ${type}, Index: ${index}`
  )
  if (type === 'entity') {
    beneficialEntities.push(name)
    logger.debug(`Added entity to beneficial entities: ${name}`)
  } else if (index === -1) {
    errorData.missingBeneficialOwners.push(name)
    logger.warn(`Missing beneficial owner: ${name}`)
  } else {
    beneficialOwners.splice(index, 1)
    logger.debug(`Removed beneficial owner from list: ${name}`)
  }
}

// Find the index of a beneficial owner
function findBeneficialOwnerIndex({
  beneficialOwners,
  shareholders,
}: {
  beneficialOwners: Pick<
    ZCorporateApplication['beneficialOwners'],
    'beneficialOwners'
  >['beneficialOwners']
  shareholders: NzbnShareholder[]
}): { index: number; type: string; name: string } {
  const shareholder = shareholders[0]

  if (!beneficialOwners) {
    return {
      index: -1,
      type: 'undefined' as const,
      name: 'No beneficial owners provided',
    }
  }

  if (shareholder.IndividualShareholder) {
    const index = beneficialOwners.findIndex((beneficialOwner) => {
      return (
        beneficialOwner.firstName?.trim().toLowerCase() ===
          shareholder.IndividualShareholder.FirstName.toLowerCase() &&
        beneficialOwner.lastName?.trim().toLowerCase() ===
          shareholder.IndividualShareholder.LastName.toLowerCase()
      )
    })
    return {
      index,
      type: 'individual' as const,
      name: `${shareholder.IndividualShareholder.FirstName} ${shareholder.IndividualShareholder.LastName}`,
    }
  }

  return {
    index: -1,
    type: 'entity' as const,
    name: shareholder.OtherShareholder.CurrentEntityName,
  }
}

type InvalidIdentity = {
  valid: boolean
  unmatchedFields: string[]
  infoLogErrors: string[]
  name?: string
}
type IdentityVerificationResult = {
  valid: boolean
  invalidIdentities: InvalidIdentity[]
}

// Verify identities
export function verifyIdentities({
  SessionReference,
  beneficialOwners,
}: {
  SessionReference: string
  beneficialOwners: NonNullable<
    ZCorporateApplication['beneficialOwners']['beneficialOwners']
  >
}): ResultAsync<IdentityVerificationResult, typeof ERRORS.NOT_FOUND> {
  logger.info(
    `Starting identity verification for ${beneficialOwners.length} beneficial owners, SessionReference: ${SessionReference}`
  )

  const beneficialOwnerPromises = beneficialOwners.map(
    async (beneficialOwner) => {
      logger.debug(
        `Verifying beneficial owner: ${beneficialOwner.firstName} ${beneficialOwner.lastName}, ID Type: ${beneficialOwner.identificationType}`
      )
      if (beneficialOwner.identificationType === 'DRIVER_LICENSE') {
        logger.debug(
          `Performing PEP check with driver's license for ${beneficialOwner.firstName} ${beneficialOwner.lastName}`
        )
        return pepCheck({
          SessionReference,
          FirstName: beneficialOwner.firstName ?? '',
          LastName: beneficialOwner.lastName ?? '',
          DateOfBirth: beneficialOwner.dateOfBirth!,
          DriversLicenceNumber: beneficialOwner.identificationNbr ?? '',
          DriversLicenceVersion: beneficialOwner.driverLicenseVersion ?? '',
          IncludeDriverLicenseCheck: true,
        })
      }

      logger.debug(
        `Performing PEP check with passport for ${beneficialOwner.firstName} ${beneficialOwner.lastName}`
      )
      return pepCheck({
        SessionReference,
        FirstName: beneficialOwner.firstName ?? '',
        LastName: beneficialOwner.lastName ?? '',
        DateOfBirth: beneficialOwner.dateOfBirth!,
        TravelDocNum: beneficialOwner.identificationNbr ?? '',
        ExpiryDate: beneficialOwner.passportExpiryDate!,
        IncludePassportCheck: true,
        PassportConsent: true,
      })
    }
  )

  return ResultAsync.fromPromise(
    Promise.all(beneficialOwnerPromises),
    (error) => {
      logger.error(error, 'PEP check failed for beneficial owners')
      return ERRORS.NOT_FOUND
    }
  ).map((beneficialOwnerResults) => {
    const invalidIdentities = beneficialOwnerResults.filter(
      (identity) => !identity.valid
    )

    const valid = invalidIdentities.length === 0
    logger.info(
      `Identity verification completed. Valid: ${valid}, Invalid identities: ${invalidIdentities.length}`
    )

    return { valid, invalidIdentities }
  })
}
