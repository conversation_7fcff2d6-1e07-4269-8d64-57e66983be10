// For operations across organisations

import { UserRole } from '@prisma/client'
import { err, errAsync, fromPromise, okAsync } from 'neverthrow'
import { ERRORS } from 'utils/error'
import { createUser, updateUser } from './user.service'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'
import { centsToDollars } from 'utils/numeric'

export async function createEpayUser({
  firstName,
  lastName,
  email,
  role,
  requesterRole,
}: {
  firstName: string
  lastName: string
  email: string
  role: UserRole
  requesterRole: UserRole
}) {
  if (requesterRole !== UserRole.EPAY_ADMIN) {
    return errAsync(ERRORS.UNAUTHORIZED)
  }

  logger.debug('epay-admin.service :: Creating epay user...')
  logger.debug(JSON.stringify({ firstName, lastName, email, role }))
  return await createUser({ firstName, lastName, email, role })
}

export async function updateEpayUser({
  userId,
  firstName,
  lastName,
  email,
  role,
  requesterRole,
}: {
  userId: string
  firstName: string
  lastName: string
  email: string
  role: UserRole
  requesterRole: UserRole
}) {
  if (requesterRole !== UserRole.EPAY_ADMIN) {
    return errAsync(ERRORS.UNAUTHORIZED)
  }

  // you can't delete the last org admin user
  if (role === UserRole.EPAY_ADMIN) {
    const roleCount = await getRoleCount({
      role: UserRole.ORG_ADMIN,
    })
    if (roleCount.isErr()) {
      return errAsync(roleCount.error)
    }

    if (roleCount.value < 2) {
      return errAsync({
        code: ERRORS.ILLEGAL_ACTION.code,
        message: 'Cannot remove the last admin user',
      })
    }
  }

  const updatedUser = await updateUser({
    id: userId,
    data: { firstName, lastName, role },
  })

  if (updatedUser.isErr()) {
    return errAsync(updatedUser.error)
  }

  return okAsync(updatedUser.value)
}

export function getRoleCount({ role }: { role: UserRole }) {
  return fromPromise(
    prisma.user.count({
      where: {
        role,
      },
    }),
    (error) => {
      logger.warn(`Error getting role count for role ${role}`, error)
      return err(ERRORS.DATABASE_ERROR)
    }
  )
}
