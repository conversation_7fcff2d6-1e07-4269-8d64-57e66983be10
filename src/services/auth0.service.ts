// reference docs https://auth0.com/docs/api/management/v2/users/post-users
import axios, { AxiosError, AxiosResponse } from 'axios'
import {
  AUTH0_ISSUER_BASE_URL,
  AUTH0_CLIENT_ID,
  AUTH0_CLIENT_SECRET,
  AUTH0_CONNECTION,
  AUTH0_CONNECTION_ID,
  AUTH0_ADMIN_CONNECTION,
  AUTH0_ADMIN_CONNECTION_ID,
} from '../utils/config'
import logger from 'utils/logger'
import { errAsync, okAsync } from 'neverthrow'
import { ERRORS } from 'utils/error'

let cachedToken: string | null = null
let tokenExpiry: Date | null = null

function isTokenExpired() {
  if (!cachedToken || !tokenExpiry) return true
  const now = new Date()
  return now >= tokenExpiry
}

const MANAGEMENT_API = `${AUTH0_ISSUER_BASE_URL}/api/v2`

async function getManagementToken() {
  if (!isTokenExpired()) {
    return okAsync(cachedToken)
  }

  logger.debug(`management API URL: ${AUTH0_ISSUER_BASE_URL}/oauth/token`)
  logger.debug(`clientID: ${AUTH0_CLIENT_ID}`)

  const options = {
    method: 'POST',
    url: `${AUTH0_ISSUER_BASE_URL}/oauth/token`,
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: `${AUTH0_CLIENT_ID}`,
      client_secret: `${AUTH0_CLIENT_SECRET}`,
      audience: `${AUTH0_ISSUER_BASE_URL}/api/v2/`,
    }),
  }

  try {
    const response = await axios.request(options)

    cachedToken = response.data.access_token

    const expiresIn = response.data.expires_in

    tokenExpiry = new Date(new Date().getTime() + expiresIn * 1000)

    logger.debug(`Token expires at ${tokenExpiry}`)

    return okAsync(response.data.access_token)
  } catch (error) {
    logger.error(error, 'Failed to get auth0 management token')

    return errAsync(ERRORS.EXTERNAL_API)
  }
}

const successResponse = (response: AxiosResponse) => {
  logger.debug(JSON.stringify(response.data))
  return okAsync(response.data)
}

const errorResponse = (errMsg: string) => (error: AxiosError) => {
  logger.error(error, errMsg)
  logger.info(JSON.stringify(error.response?.data))
  return errAsync(ERRORS.EXTERNAL_API)
}

export async function getUserInfoById({ userId }: { userId: string }) {
  const token = await getManagementToken()
  if (token.isErr()) {
    return errAsync(token.error)
  }
  const url = `${MANAGEMENT_API}/users/${userId}`
  try {
    const response = await axios.get(url, {
      headers: {
        authorization: `Bearer ${token.value}`,
      },
    })
    return okAsync(response.data)
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError
      if (axiosError.response!.status === 400) {
        logger.debug(`Auth0 user not found with id [${userId}]`)
        return errAsync(ERRORS.NOT_FOUND)
      }
    }
    logger.error(error, `Failed to get auth0 getUserInfoById with [${userId}]`)
    return errAsync(ERRORS.EXTERNAL_API)
  }
}

export async function getUserInfoByEmail({ email }: { email: string }) {
  const token = await getManagementToken()
  if (token.isErr()) {
    return errAsync(token.error)
  }
  const url = `${MANAGEMENT_API}/users-by-email?email=${email}`
  try {
    const response = await axios.get(url, {
      headers: {
        authorization: `Bearer ${token.value}`,
      },
    })
    return okAsync(response.data)
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError
      if (axiosError.response!.status === 400) {
        logger.debug(`auth0 user not found with email [${email}]`)
        return errAsync(ERRORS.NOT_FOUND)
      }
    }
    logger.error(
      error,
      `Error with auth0 service getUserInfoByEmail [${email}]`
    )
    return errAsync(ERRORS.EXTERNAL_API)
  }
}

export async function createAuth0User({
  firstName,
  lastName,
  email,
  password,
  isAdmin,
}: {
  firstName: string
  lastName: string
  email: string
  password: string
  isAdmin: boolean
}) {
  logger.info('Creating auth0 user')

  const token = await getManagementToken()
  if (token.isErr()) {
    return errAsync(token.error)
  }

  logger.info('Success getting token from auth0 management API')

  const data = JSON.stringify({
    email,
    blocked: false,
    email_verified: false,
    given_name: `${firstName}`,
    family_name: `${lastName}`,
    connection: isAdmin ? AUTH0_ADMIN_CONNECTION : AUTH0_CONNECTION,
    verify_email: false,
    password: password,
  })

  const config = {
    maxBodyLength: Infinity,
    headers: {
      'Content-Type': 'application/json',
      authorization: `Bearer ${token.value}`,
    },
  }

  logger.debug('Creating auth0 user with:')
  logger.debug(data)

  return axios
    .post(`${MANAGEMENT_API}/users`, data, config)
    .then(successResponse)
    .catch(errorResponse('Failed to create auth0 user'))
}

export async function createChangePasswordTicket({
  email,
  isAdmin,
}: {
  email: string
  isAdmin: boolean
}) {
  const token = await getManagementToken()
  if (token.isErr()) {
    return errAsync(token.error)
  }

  const data = JSON.stringify({
    ttl_sec: 600,
    mark_email_as_verified: true,
    email: email,
    connection_id: isAdmin ? AUTH0_ADMIN_CONNECTION_ID : AUTH0_CONNECTION_ID,
  })

  const config = {
    maxBodyLength: Infinity,
    headers: {
      'Content-Type': 'application/json',
      authorization: `Bearer ${token.value}`,
    },
  }

  return axios
    .post(`${MANAGEMENT_API}/tickets/password-change`, data, config)
    .then(successResponse)
    .catch(errorResponse('Failed to create auth0 change password ticket'))
}

export async function sendResetPasswordEmail({
  email,
  isAdmin,
}: {
  email: string
  isAdmin: boolean
}) {
  const token = await getManagementToken()
  if (token.isErr()) {
    return errAsync(token.error)
  }

  const data = JSON.stringify({
    client_id: AUTH0_CLIENT_ID,
    connection: isAdmin ? AUTH0_ADMIN_CONNECTION : AUTH0_CONNECTION,
    email: email,
  })

  const config = {
    method: 'post',
    url: `${AUTH0_ISSUER_BASE_URL}/dbconnections/change_password`,
    headers: {
      'Content-Type': 'application/json',
      authorization: `Bearer ${token.value}`,
    },
    data,
  }

  return axios
    .request(config)
    .then(successResponse)
    .catch(errorResponse('Failed to send auth0 reset password email'))
}

export async function deleteUserFromAuth0({ userId }: { userId: string }) {
  const token = await getManagementToken()
  if (token.isErr()) {
    return errAsync(token.error)
  }

  const data = JSON.stringify({
    blocked: true,
  })

  const config = {
    method: 'delete',
    maxBodyLength: Infinity,
    url: `${MANAGEMENT_API}/users/${userId}`,
    headers: {
      'Content-Type': 'application/json',
      authorization: `Bearer ${token.value}`,
    },
    data: data,
  }

  return axios
    .request(config)
    .then(successResponse)
    .catch(errorResponse(`Failed to delete auth0 user ${userId}`))
}

export async function blockUserFromAuth0({ userId }: { userId: string }) {
  const token = await getManagementToken()
  if (token.isErr()) {
    return errAsync(token.error)
  }

  const config = {
    method: 'patch',
    maxBodyLength: Infinity,
    url: `${MANAGEMENT_API}/users/${userId}`,
    headers: {
      'Content-Type': 'application/json',
      authorization: `Bearer ${token.value}`,
    },
    data: JSON.stringify({
      blocked: true,
    }),
  }

  return axios
    .request(config)
    .then(successResponse)
    .catch(errorResponse(`Failed to block auth0 user ${userId}`))
}
