import { DeliveryMethod } from '@prisma/client'
import { errAsync, okAsync, ResultAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import { generateRandomNumbers } from 'utils/numeric'

export const NO_LOCK_CODE = 'no_lock_code'

export function getShippingMethod({
  deliveryMethod,
  secureDelivery,
}: {
  deliveryMethod: DeliveryMethod
  secureDelivery: boolean
}) {
  if (secureDelivery) {
    return '6'
  }

  if (deliveryMethod === 'COURIER') {
    return '2'
  }

  return ''
}

export function getLockCode(
  orderLockCodeOption: string | null | undefined
): string {
  if (orderLockCodeOption) {
    if (orderLockCodeOption === NO_LOCK_CODE) {
      return ''
    }

    return orderLockCodeOption
  }

  return generateRandomNumbers(4).toString()
}

export function getLockCodeAsync(
  orderLockCodeOption: string | null | undefined
) {
  if (orderLockCodeOption) {
    if (orderLockCodeOption === NO_LOCK_CODE) {
      return okAsync('')
    }

    return okAsync(orderLockCodeOption)
  }

  const lockCode = generateRandomNumbers(4).toString()
  return okAsync(lockCode)
}

export function validateLockCode(
  lockCode: string | undefined | null
): ResultAsync<string, SimpleError> {
  if (!lockCode) {
    return errAsync({
      code: errorCodes.card.LOCK_CODE_NOT_FOUND,
      message: 'Invalid lock code on card',
    } as SimpleError)
  }
  return okAsync(lockCode)
}

export function createBatches<T>(items: T[], batchSize: number): T[][] {
  const batches: T[][] = []
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize))
  }
  return batches
}
