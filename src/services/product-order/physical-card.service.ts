import { ProductOrderNoteStatus } from '@prisma/client'
import {
  updateProductOrderItemError,
  updateProductOrderItemWithBatchId,
} from 'data/product-order-item.data'
import { getCardDesignByExternalCardDesignId } from 'epay-data/epay-card-design.data'
import { epayAdminCreateProductOrderNote } from 'epay-data/product-order-note.data'
import { createBatchOrder } from 'external-apis/ren/card-batch-order.api'
import { err, errAsync, okAsync, ResultAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { generateRandomNumbers } from 'utils/numeric'
import { ProductOrderItemProp } from './order-cards.service'
import { getShippingMethod } from './product-order-util'

type CardOptions = {
  resolution: string
  externalCardDesignId: string
}

function getCardTypes(
  options: string | object
): ResultAsync<{ cardType: string; cardSubType: string } | null, SimpleError> {
  if (!options) {
    return okAsync(null)
  }

  const cardOptions = options as CardOptions

  return getCardDesignByExternalCardDesignId(
    cardOptions.externalCardDesignId
  ).andThen((cardDesign) => {
    if (!cardDesign) {
      return errAsync({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Card design not found for id ${cardOptions.externalCardDesignId}`,
      } as SimpleError)
    }

    if (!cardDesign.externalCardType || !cardDesign.externalCardDesignId) {
      return errAsync({
        code: errorCodes.db.BAD_INPUT,
        message: `Card design ${cardOptions.externalCardDesignId} missing required external IDs`,
      } as SimpleError)
    }

    return okAsync({
      cardType: cardDesign.externalCardType,
      cardSubType: cardDesign.externalCardDesignId,
    })
  })
}

export function orderPhysicalCardBatch({
  userEmail,
  productOrderItem,
  secureDelivery,
  orderNumber,
  useRen,
}: {
  userEmail: string
  productOrderItem: ProductOrderItemProp
  secureDelivery: boolean
  orderNumber: string
  useRen: boolean
}) {
  if (useRen) {
    const cardTypesResult = productOrderItem.options
      ? getCardTypes(productOrderItem.options as string)
      : okAsync(null)

    return cardTypesResult
      .andThen((cardTypes) => {
        const cardType =
          cardTypes?.cardType ??
          productOrderItem.product.design.externalCardType ??
          ''
        const cardSubType =
          cardTypes?.cardSubType ??
          productOrderItem.product.design.externalCardDesignId ??
          ''

        logger.info('cardType', cardType)
        logger.info('cardSubType', cardSubType)

        return createBatchOrder({
          cardType,
          cardSubType,
          recipientName: productOrderItem.recipientName,
          addressLine1: productOrderItem.recipientAddress ?? '',
          addressLine2: productOrderItem.recipientAddressLine2 ?? '',
          addressLine3: productOrderItem.recipientSuburb ?? '',
          city: productOrderItem.recipientCity ?? '',
          email: productOrderItem.recipientEmail ?? userEmail,
          embossLine2: productOrderItem.extraEmbossingLine ?? '',
          quantity: productOrderItem.quantity,
          deliveryOrderId: `${orderNumber}-${
            productOrderItem.deliveryBatchId ?? 'error'
          }`,
          logoFilename: productOrderItem.product.logo?.name ?? '',
          shippingMethod: getShippingMethod({
            deliveryMethod: productOrderItem.deliveryMethod,
            secureDelivery,
          }),
          postCode: productOrderItem.recipientPostCode ?? '',
          denomination: `${productOrderItem.unitPrice}`,
        })
      })
      .andThen((response) => {
        return updateProductOrderItemWithBatchId({
          id: productOrderItem.id,
          externalBatchId:
            response.instCrdOrderDetailResponse.response.uniqueId,
        }).map(() => {
          return { productOrderItem }
        })
      })
      .orElse((error) => {
        return updateProductOrderItemError({
          id: productOrderItem.id,
          error: JSON.stringify(error),
        })
          .andThen(() => {
            const { logRef, message, code } = error
            return epayAdminCreateProductOrderNote({
              title: 'Product order item error',
              message: `
              <span><b>Code:</b> ${code}</span><br />
              <span><b>Message:</b> ${message}</span><br />
              <span><b>Log Reference:</b> ${logRef}</span><br />
              <span><b>Order Item ID:</b> ${productOrderItem.id}</span><br />
            `
                .trim()
                .replace(/\s+/g, ' '),
              status: ProductOrderNoteStatus.ERROR,
              productOrderId: productOrderItem.productOrderId,
            })
          })
          .andThen(() => {
            return err({
              code: errorCodes.external.ren.REQUEST_FAILED,
              message: 'Failed to create batch order',
            } as SimpleError)
          })
      })
  } else {
    // MOCKING
    logger.warn('mocking createBatchOrder')
    return updateProductOrderItemWithBatchId({
      id: productOrderItem.id,
      externalBatchId: `mock-${generateRandomNumbers(10)}`,
    })
  }
}
