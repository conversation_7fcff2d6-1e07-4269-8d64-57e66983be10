import { OrderStatus } from '@prisma/client'
import { createProductOrderItemCardV2 } from 'data/product-order-item-card.data'
import { updateProductOrderStatusV2 } from 'data/product-order.data'
import {
  epayGetOrderForProcessing,
  EpayOrderForProcessing,
} from 'epay-data/epay-product-order.data'
import {
  epayProcessNextQueueItem,
  epayUpdateQueueItemStatusCompleted,
  OrderQueueItemWithProductOrder,
} from 'epay-data/order-processing-queue.data'
import {
  epayReleaseProcessingLock,
  epayTryAcquireProcessingLock,
  epayUpdateProcessingLockLastUpdated,
  ORDER_PROCESSING_LOCK_ID,
} from 'epay-data/processing-lock.data'
import { getCardsByBatch } from 'external-apis/ren/get-cards-by-batch'
import { errAsync, okAsync, ResultAsync } from 'neverthrow'
import { sendLockCodeEmailsByOrderNumber } from 'services/epay.service'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { generateRandomNumbers } from 'utils/numeric'
import { isShuttingDown } from 'utils/shutting-down'
import { getLockCodeAsync, validateLockCode } from './product-order-util'
import {
  InitializeFundsResult,
  initializePhysicalCardWithFundsAndLockCode,
} from './load-card-funds.service'
import { processVirtualCards } from './order-processor-virtual.service'

export async function processQueue(orderAgeInMinutes: number, useRen: boolean) {
  if (isShuttingDown()) {
    return okAsync(false)
  }

  logger.debug(`processQueue.. begin. useRen: [${useRen}]`)
  const lockResult = await epayTryAcquireProcessingLock(
    ORDER_PROCESSING_LOCK_ID
  )

  if (lockResult.isErr()) {
    logger.error(lockResult.error, 'Error acquiring processing lock')
    return okAsync(false)
  }

  if (!lockResult.value) {
    // Another process is already handling the queue
    logger.info('Processing is already in progress by another instance.')
    return okAsync(false)
  }

  const lockUpdateInterval = 5 * 60 * 1000 // Update lock every 5 minutes
  let lockUpdateIntervalId: NodeJS.Timeout | null = null

  try {
    // Start periodic lock update
    lockUpdateIntervalId = setInterval(async () => {
      await epayUpdateProcessingLockLastUpdated(
        ORDER_PROCESSING_LOCK_ID
      ).mapErr((error) => {
        logger.error(error, 'Error updating processing lock lastUpdated time')
      })
    }, lockUpdateInterval)

    while (!isShuttingDown()) {
      const processResult = await processNextQueueItem({
        orderAgeInMinutes,
        useRen: useRen,
      })

      if (processResult.isErr()) {
        // queue items in error are already surfaced to logs, db, and sentry
        continue
      }

      if (!processResult.value) {
        // No more items to process
        break
      }
    }
  } catch (err) {
    logger.error(err, 'Error processing queue')
  } finally {
    // Clear the lock update interval
    if (lockUpdateIntervalId) {
      clearInterval(lockUpdateIntervalId)
    }

    const releaseResult = await epayReleaseProcessingLock(
      ORDER_PROCESSING_LOCK_ID
    )
    if (releaseResult.isErr()) {
      logger.error(releaseResult.error, 'Error releasing processing lock')
      // reutrn error to set an alarm on the queue processing
      return okAsync(false)
    } else {
      logger.info('Processing lock released successfully.')
      return okAsync(true)
    }
  }
}

export function processNextQueueItem({
  orderAgeInMinutes,
  useRen,
}: {
  orderAgeInMinutes: number
  useRen: boolean
}): ResultAsync<boolean, SimpleError> {
  if (isShuttingDown()) {
    return okAsync(false)
  }

  return epayProcessNextQueueItem(orderAgeInMinutes).andThen((queueItem) => {
    logger.debug('processNextQueueItem.. begin')
    if (!queueItem) {
      logger.debug('processNextQueueItem.. no more items to process')
      return okAsync(false) // No more items to process
    }

    logger.debug(
      `processNextQueueItem.. getting an unprocessed  order: ${queueItem.orderNumber}`
    )
    // Get unprocessed items for this order
    return epayGetOrderForProcessing(queueItem.productOrderId)
      .andThen((productOrder) => {
        if (
          productOrder.orderStatus !== OrderStatus.PROCESSING &&
          productOrder.orderStatus !== OrderStatus.SCHEDULED
        ) {
          // IMPORTANT: to propogate UNEXPECTED_STATUS error so that the order status is not updated
          return errAsync({
            code: errorCodes.order.UNEXPECTED_STATUS,
            message: `Not processed. Order [${productOrder.orderNumber}] has a status of [${productOrder.orderStatus}] instead of [PROCESSING]. `,
          } as SimpleError)
        }

        logger.debug(
          `processNextQueueItem.. got unprocessed order: ${productOrder.orderNumber}`
        )

        if (productOrder.orderStatus === OrderStatus.SCHEDULED) {
          return processVirtualCards({
            orderQueueItem: queueItem,
            productOrder,
            useRen,
          })
        }

        return processPhysicalCards({
          orderQueueItem: queueItem,
          productOrder,
          useRen,
        })
      })
      .orElse((error: SimpleError) => {
        const logRef = logger.error(
          error,
          `processNextQueueItem.. unexpected error for order [${queueItem.orderNumber}]: ${error.message}`
        )
        // don't update the order status if it's unexpected because
        // that means the order was not in PENDING status e.g. could have had CANCELLED
        if (error.code === errorCodes.order.UNEXPECTED_STATUS) {
          return epayUpdateQueueItemStatusCompleted({
            queueId: queueItem.queueId,
            errorMessage: `[${logRef}]: ${error.message}`,
          }).map(() => true)
        } else {
          return updateProductOrderStatusV2({
            orderNumber: queueItem.orderNumber,
            orderStatus: OrderStatus.ON_HOLD,
          }).andThen(() => {
            return epayUpdateQueueItemStatusCompleted({
              queueId: queueItem.queueId,
              errorMessage: `[${logRef}]: ${error.message}`,
            }).map(() => true)
          })
        }
      })
  })
}

function processPhysicalCards({
  productOrder,
  orderQueueItem,
  useRen,
}: {
  orderQueueItem: OrderQueueItemWithProductOrder
  productOrder: EpayOrderForProcessing
  useRen: boolean
}) {
  const processBatchPromises = productOrder.productOrderItems.map(
    (orderItem) => {
      if (useRen) {
        return processPhysicalBatchV2({
          batchId: orderItem.externalBatchId!,
          orderNumber: productOrder.orderNumber!,
          lockCodeOption: productOrder.lockCode,
          productOrderItemId: orderItem.id,
          unitPriceInCents: orderItem.unitPrice,
        }).andThen((result) => {
          if (result.totalCardsFailed === 0) {
            logger.debug(
              `processNextQueueItem.. completed batch for orderItem: ${orderItem.id}`
            )
            return okAsync(result)
          }

          logger.debug(
            `processNextQueueItem.. failed batch for orderItem: ${orderItem.id}`
          )
          return errAsync({
            code: errorCodes.card.BATCH_PROCESSING_ERROR,
            message: `Failed to process ${result.totalCardsFailed} cards in productOrderItem [${orderItem.id}], externalBatchId [${orderItem.externalBatchId}]`,
          } as SimpleError)
        })
      } else {
        // MOCKING
        return epayProcessMockPhysicalBatch({
          id: orderItem.id,
          unitPrice: orderItem.unitPrice,
          quantity: orderItem.quantity,
          lockCode: productOrder.lockCode,
        })
      }
    }
  )

  return ResultAsync.combineWithAllErrors(processBatchPromises)
    .mapErr((errors: SimpleError[]): SimpleError => {
      // Combine multiple errors into a single error message
      logger.debug(
        `processNextQueueItem.. multiple errors processing batches for order: ${productOrder.orderNumber}`
      )
      const errorMessages = errors.map((e) => e.message).join('; ')
      const logRef = logger.error(
        errorMessages,
        `Multiple errors processing batches for order ${productOrder.orderNumber}`
      )
      return {
        code: errorCodes.card.BATCH_PROCESSING_ERROR,
        message: `[${logRef}]: Thre were ${errors.length} errors processing batches for order ${productOrder.orderNumber}`,
        logRef,
      } as SimpleError
    })
    .map((results) => {
      // Process all results to determine if there were any errors
      const errors = results
        .filter((result) => result.totalCardsFailed > 0)
        .map((result) => `Failed to process ${result.totalCardsFailed} cards`)

      return errors
    })
    .andThen(
      // Completed
      (errors: string[]) => {
        logger.debug('processNextQueueItem.. completed')
        const errorMessage = errors.length > 0 ? errors.join('; ') : null
        return epayUpdateQueueItemStatusCompleted({
          queueId: orderQueueItem.queueId,
          errorMessage,
        })
          .andThen(() => {
            return updateProductOrderStatusV2({
              orderNumber: orderQueueItem.orderNumber,
              orderStatus: OrderStatus.CONFIRMED,
            })
          })
          .andThen(() => {
            sendLockCodeEmailsByOrderNumber(orderQueueItem.orderNumber)
            return okAsync(true)
          })
      }
    )
}

type ProcessPhysicalBatchResultV2 = {
  totalCardsSuccessfullyProcessed: number
  totalCardsFailed: number
}

function processPhysicalBatchV2({
  productOrderItemId,
  batchId,
  orderNumber,
  lockCodeOption,
  unitPriceInCents,
}: {
  productOrderItemId: string
  batchId: string
  orderNumber: string
  lockCodeOption: string | null | undefined
  unitPriceInCents: number
}) {
  logger.debug(
    `processPhysicalBatch.. get batch order [${orderNumber}], batchId [${batchId}]`
  )
  const PAGE_SIZE = 10
  let totalProcessed = 0
  let failedInitializations = 0

  const failedProductOrderItemIds: string[] = []

  // Helper function to process a single page
  function processPage(
    index: number,
    pendingRecords: number
  ): ResultAsync<ProcessPhysicalBatchResultV2, SimpleError> {
    if (pendingRecords <= 0) {
      logger.debug(
        `processPhysicalBatch.. processPage: ${index}, pendingRecords: ${pendingRecords}`
      )
      return okAsync({
        totalCardsSuccessfullyProcessed: totalProcessed - failedInitializations,
        totalCardsFailed: failedInitializations,
      })
    }

    return getCardsByBatch({
      batchId,
      index,
      limit: PAGE_SIZE,
    }).andThen((searchResponse) => {
      const processCardPromises =
        searchResponse.searchCardListResponse.cardInfo.map((card) => {
          logger.debug(
            `processPhysicalBatch.. processPage: ${index}, card: ${card.proxyCardNumber}`
          )
          return getLockCodeAsync(lockCodeOption).andThen((lockCode) => {
            return processPhysicalCardV2({
              crn: card.proxyCardNumber,
              lockCode,
              unitPriceInCents,
              productOrderItemId,
              orderNumber,
            })
              .andThen((result) => {
                totalProcessed++
                return okAsync(result)
              })
              .mapErr((error) => {
                logger.debug(
                  `processPhysicalBatch.. order [${orderNumber}], batchId [${batchId}], error processing card: [${card.proxyCardNumber}]`
                )
                failedProductOrderItemIds.push(productOrderItemId)
                failedInitializations++
                return error
              })
          })
        })

      return ResultAsync.combineWithAllErrors(processCardPromises)
        .mapErr((errors) => {
          logger.debug(
            `processPhysicalBatch.. processPage: ${index}, errors: ${errors}`
          )
          const errorMessages = errors.map((e) => e.message).join('; ')
          const logRef = logger.error(
            { errors },
            `processPhysicalBatch.. Multiple errors processing cards for batch ${batchId}`
          )
          return {
            code: errorCodes.card.BATCH_PROCESSING_ERROR,
            message: errorMessages,
            logRef,
          }
        })
        .andThen(() => {
          logger.debug(
            `processPhysicalBatch.. progress order [${orderNumber}], batchId [${batchId}]`,
            {
              batchId,
              totalProcessed,
              pendingRecords:
                searchResponse.searchCardListResponse.pendingRecords,
              totalRecords: searchResponse.searchCardListResponse.totalRecords,
              failed: failedProductOrderItemIds.length,
            }
          )

          return epayUpdateProcessingLockLastUpdated(
            ORDER_PROCESSING_LOCK_ID
          ).andThen(() => {
            // Process next page if there are more records
            return processPage(
              index + 1,
              searchResponse.searchCardListResponse.pendingRecords
            )
          })
        })
    })
  }

  logger.debug('processPhysicalBatch.. kickoff processPage')
  return processPage(0, 1).map((result) => {
    logger.info(
      `processPhysicalBatch.. order [${orderNumber}], batchId [${batchId}], Finished processing physical batch`,
      {
        batchId,
        totalProcessed,
        failed: failedProductOrderItemIds.length,
      }
    )
    return result
  })
}

function processPhysicalCardV2({
  crn,
  lockCode,
  unitPriceInCents,
  productOrderItemId,
  orderNumber,
}: {
  crn: string
  lockCode?: string
  unitPriceInCents: number
  productOrderItemId: string
  orderNumber: string
}) {
  logger.debug(
    `processPhysicalCardV2.. order [${orderNumber}], crn [${crn}], unitPriceInCents [${unitPriceInCents}]`
  )

  if (unitPriceInCents === 0) {
    // Stock cards with zero blance don't need funds loaded or activation code set
    return createProductOrderItemCardV2({
      activated: false,
      productOrderItemId,
      externalCardReferenceNumber: crn,
      unitPriceInCents: 0,
      lockCode: undefined, // no lock code for 0 balance cards
    }).map(
      (card) =>
        ({
          cardProxyNumber: card.externalCardReferenceNumber,
        } as InitializeFundsResult)
    )
  }

  return validateLockCode(lockCode)
    .andThen((code) => {
      return createProductOrderItemCardV2({
        activated: false,
        productOrderItemId,
        externalCardReferenceNumber: crn,
        unitPriceInCents,
        lockCode: code,
      })
    })
    .andThen((card) => {
      return initializePhysicalCardWithFundsAndLockCode({
        cardProxyNumber: card.externalCardReferenceNumber,
        lockCode: card.lockCode ?? '',
        amountInCents: card.unitPriceInCents!,
        orderNumber,
        productOrderItemCardId: card.id,
      })
    })
}

export function epayProcessMockPhysicalBatch({
  id,
  unitPrice,
  quantity,
  lockCode,
}: {
  id: string
  unitPrice: number
  quantity: number
  lockCode: string | null
}) {
  logger.info(`Processing mock physical batch for product order item [${id}]`)

  const promises = []

  for (let i = 0; i < quantity; i++) {
    const res = getLockCodeAsync(lockCode).andThen((cardLockCode) => {
      return createProductOrderItemCardV2({
        activated: false,
        productOrderItemId: id,
        externalCardReferenceNumber: `mock-${generateRandomNumbers(10)}`,
        unitPriceInCents: unitPrice,
        lockCode: cardLockCode,
      })
    })

    promises.push(res)
  }

  return ResultAsync.combine(promises).map(
    () => (
      logger.info(
        `Mock physical batch processed for product order item [${id}], unitPrice [${unitPrice}], lockCode [${lockCode}], totalCardsSuccessfullyProcessed: 1`
      ),
      {
        totalCardsFailed: 0,
        totalCardsSuccessfullyProcessed: quantity,
      }
    )
  )
}
