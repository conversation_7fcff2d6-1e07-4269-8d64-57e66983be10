import { SimpleError } from 'types/simple-error'

import { OrderType } from '@prisma/client'

import { OrderStatus } from '@prisma/client'
import { epayUpdateProductOrderStatusByOrder } from 'epay-data/epay-product-order.data'
import { fromPromise, ok } from 'neverthrow'
import { USE_MOCKING } from 'utils/config'
import { processFundingOrder } from './process-funding-order.service'
import logger from 'utils/logger'
import { errorCodes } from 'types/simple-error'
import { processScheduledOrder } from './process-scheduled-order.service'
import { processFloatFundsOrder } from 'services/float-funds.service'
import { orderCardsFromSupplierByOrderId } from './order-cards.service'

export function releaseProductOrder({
  orderId,
  paymentDate: paymentDateString,
  userId,
  isWindcavePayment = false,
}: {
  orderId: string
  paymentDate: string
  userId: string
  isWindcavePayment: boolean
}) {
  const paymentDate = new Date(paymentDateString)

  return epayUpdateProductOrderStatusByOrder({
    orderStatus: OrderStatus.RELEASING,
    releasedAt: new Date(),
    paymentDate,
    orderId,
  }).andThen((order) => {
    if (order.orderType === OrderType.SINGLE_FUNDS_LOAD) {
      fromPromise(
        processFundingOrder({
          orderId,
          paymentDate,
          userId,
          useRen: !USE_MOCKING,
          isWindcavePayment,
        }),
        (error) => {
          const logRef = logger.error(
            error,
            'Background releasing funding order process failed'
          )
          return {
            code: errorCodes.order.PROCESSING_ERROR,
            message: 'Background releasing funding order process failed',
            logRef,
          } as SimpleError
        }
      )
    } else if (order.orderType === OrderType.FLOAT_FUNDS) {
      fromPromise(
        processFloatFundsOrder({
          orderId,
          paymentDate: new Date(paymentDate),
          userId,
        }),
        (error) => {
          const logRef = logger.error(
            error,
            'Background processing scheduled order failed'
          )
          return {
            code: errorCodes.order.PROCESSING_ERROR,
            message: 'Background processing scheduled order failed',
            logRef,
          } as SimpleError
        }
      )
    } else if (order.scheduledDate) {
      fromPromise(
        processScheduledOrder({
          orderId,
          paymentDate,
          userId,
          isWindcavePayment,
        }),
        (error) => {
          const logRef = logger.error(
            error,
            'Background processing scheduled order failed'
          )
          return {
            code: errorCodes.order.PROCESSING_ERROR,
            message: 'Background processing scheduled order failed',
            logRef,
          } as SimpleError
        }
      )
    } else {
      fromPromise(
        orderCardsFromSupplierByOrderId({
          orderId,
          paymentDate,
          useRen: !USE_MOCKING,
          userId,
        }),
        (error) => {
          const logRef = logger.error(
            error,
            'Background releasing order process failed'
          )
          return {
            code: errorCodes.order.PROCESSING_ERROR,
            message: 'Background releasing order process failed',
            logRef,
          } as SimpleError
        }
      )
    }

    return ok({ orderId: order.id, orderNumber: order.orderNumber })
  })
}
