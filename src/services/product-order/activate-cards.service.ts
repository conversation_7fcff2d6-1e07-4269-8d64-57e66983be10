import { ProductOrderItemCard } from '@prisma/client'
import {
  getManyPrezzyCardsByCrn,
  getProductOrderItemCardsByOrder,
  updateProductOrderItemCardActivatedV2,
  updateProductOrderItemCardBlockedStatus,
  updateProductOrderItemCardError,
} from 'data/product-order-item-card.data'
import { cardActivation } from 'external-apis/ren/card-activation.api'
import { getCardDetails } from 'external-apis/ren/get-card-details.api'
import { err, ResultAsync } from 'neverthrow'
import logger from 'utils/logger'
import { validateLockCode } from './product-order-util'
import { cardStatusChange } from 'external-apis/ren/card-status-change.api'
import { SimpleError } from 'types/simple-error'
import {
  activateManyOrdersIfAllCardsActivated,
  getUniqueOrderNumbersForCrnList,
} from 'data/product-order.data'

const BATCH_SIZE = 5

export function activateOrder({
  orderNumber,
  orgId,
}: {
  orgId: string
  orderNumber: string
}) {
  return getProductOrderItemCardsByOrder({ orderNumber, orgId })
    .andThen((productOrderItemCards) => {
      const crns = productOrderItemCards.map(
        (card) => card.externalCardReferenceNumber
      )

      return activateCardsByCrns({ crns, orgId })
    })
    .map((result) => {
      return {
        activated: result.totalActivated,
        failed: result.totalFailed,
      }
    })
}

interface BatchResult {
  totalCards: number
  totalActivated: number
  totalFailed: number
}

export function activateCardsByCrns({
  crns,
  orgId,
}: {
  crns: string[]
  orgId?: string
}) {
  let totalActivated = 0
  let totalFailed = 0
  const ordersToCheck: string[] = []

  const batches: string[][] = []
  for (let i = 0; i < crns.length; i += BATCH_SIZE) {
    batches.push(crns.slice(i, i + BATCH_SIZE))
  }

  return ResultAsync.fromPromise(
    (async () => {
      for (const batch of batches) {
        
        const batchResult = await activateManyCardsByCrn({
          crns: batch,
          orgId,
        })

        batchResult.match(
          (result) => {
            totalActivated += result.activated
            totalFailed += result.failed
            logger.debug(
              `Batch completed: ${result.activated} activated, ${result.failed} failed`
            )
          },
          () => {
            totalFailed += batch.length
          }
        )

        const orderListResult = await getUniqueOrderNumbersForCrnList({
          crns: batch,
          orgId: orgId!,
        })
        if (orderListResult.isOk()) {
          orderListResult.value.forEach((order) => {
            if (order.orderNumber) {
              ordersToCheck.push(order.orderNumber!)
            }
          })
        }
      }

      // check if all the cards have been activated in an order and
      // change status to ACTIVATED if so
      const orderActivationResult = await activateManyOrdersIfAllCardsActivated(
        ordersToCheck
      )
      if (orderActivationResult.isOk()) {
        logger.info(
          `Activated ${orderActivationResult.value.updated} orders to status [${orderActivationResult.value.toStatus}]`
        )
      }

      return {
        totalCards: crns.length,
        totalActivated,
        totalFailed,
      } as BatchResult
    })(),
    (error) => {
      // should never happen
      return error as SimpleError
    }
  )
}

type ActivateManyCardsByCrnResult = {
  activated: number
  failed: number
}
function activateManyCardsByCrn({
  crns,
  orgId,
}: {
  crns: string[]
  orgId?: string // Epay admins don't have an orgId
}) {
  return getManyPrezzyCardsByCrn({ crns, orgId })
    .andThen((productOrderItemCards) => {
      const activationPromises = productOrderItemCards.map((card) =>
        activateCard({
          productOrderItemCard: card,
        })
      )

      return ResultAsync.combineWithAllErrors(activationPromises)
        .map((results) => {
          const activated = results.filter((card) => card.activated)
          const result: ActivateManyCardsByCrnResult = {
            activated: activated.length,
            failed: crns.length - activated.length,
          }
          return result
        })
        .mapErr((errors) => {
          return {
            activated: crns.length - errors.length,
            failed: errors.length,
          } as ActivateManyCardsByCrnResult
        })
    })
    .mapErr(() => {
      return {
        activated: 0,
        failed: crns.length,
      } as ActivateManyCardsByCrnResult
    })
}

/***
 * Activation of card requires a activtion code (aka lock code)
 */
function activateCard({
  productOrderItemCard,
}: {
  productOrderItemCard: ProductOrderItemCard
}) {
  return validateLockCode(productOrderItemCard.lockCode)
    .andThen(() => {
      return getCardDetails(productOrderItemCard.externalCardReferenceNumber)
    })

    .andThen((cardDetail) => {
      if (productOrderItemCard.activated) {
        // If the card is already activated, just unblock it
        return cardStatusChange({
          proxyNumber: cardDetail.proxyNumber,
          status: 'unblock',
        }).andThen(() => {
          return updateProductOrderItemCardBlockedStatus({
            productOrderItemCardId: productOrderItemCard.id,
            status: 'unblock',
          })
        })
      } else {
        return cardActivation({
          activationCode: productOrderItemCard.lockCode!,
          cardDetail,
        }).andThen(() => {
          return updateProductOrderItemCardActivatedV2({
            productOrderItemCardId: productOrderItemCard.id,
          })
        })
      }
    })
    .orElse((error) => {
      updateProductOrderItemCardError({
        id: productOrderItemCard.id,
        errorString: JSON.stringify(error),
      })

      return err(error)
    })
}
