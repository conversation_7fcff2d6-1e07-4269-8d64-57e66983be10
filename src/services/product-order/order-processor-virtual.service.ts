import { OrderStatus } from '@prisma/client'
import { updateProductOrderStatusV2 } from 'data/product-order.data'
import {
  EpayOrderForProcessing,
  epayValidateVirtualOnlyOrder,
} from 'epay-data/epay-product-order.data'
import {
  epayUpdateQueueItemStatusCompleted,
  OrderQueueItemWithProductOrder,
} from 'epay-data/order-processing-queue.data'
import { SCHEDULED_ORDER_RELEASED } from 'helpers/email-templates'
import { errAsync, ok, okAsync, ResultAsync } from 'neverthrow'
import { sendEmailWithTemplate } from 'services/postmark.service'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { centsToDollars } from 'utils/numeric'
import { processOrderItems } from './order-cards.service'

export function processVirtualCards({
  orderQueueItem,
  productOrder,
  useRen,
}: {
  orderQueueItem: OrderQueueItemWithProductOrder
  productOrder: EpayOrderForProcessing
  useRen: boolean
}): ResultAsync<boolean, SimpleError> {
  logger.info('processVirtualCards.. begin')
  const orderNumber = productOrder.orderNumber!

  return processOrderItems(productOrder, useRen)
    .andThen(() => {
      logger.info('(A). processVirtualCards: validating order')
      return validateVirtualOrder(orderNumber)
    })
    .andThen(() => {
      logger.info('(B). processVirtualCards: order validated successfully')
      return updateProductOrderStatusV2({
        orderNumber,
        orderStatus: OrderStatus.COMPLETED,
      })
    })
    .andThen(() => {
      logger.info(
        '(C). processVirtualCards: Successfully updated status to COMPLETED'
      )
      return sendEmailWithTemplate({
        email: productOrder.user.email,
        templateId: SCHEDULED_ORDER_RELEASED,
        templateModel: {
          orderNumber: productOrder.orderNumber,
          firstName: productOrder.user.firstName,
          orderTotal: centsToDollars(productOrder.orderTotal),
        },
      })
        .map(() => productOrder)
        .orElse(() => ok(productOrder))
    })
    .andThen(() => {
      logger.info('(D). processVirtualCards: Email confirmation sent to user')
      return epayUpdateQueueItemStatusCompleted({
        queueId: orderQueueItem.queueId,
        errorMessage: null,
      })
    })
    .andThen(() => {
      logger.info(
        '(E). processVirtualCards: Updated queue item status to COMPLETED'
      )
      return okAsync(true)
    })
    .orElse(() => {
      const message = 'processVirtualCards processing error'
      const logRef = logger.error('processVirtualCards processing error')
      return errAsync({
        code: errorCodes.order.PROCESSING_ERROR,
        message,
        logRef,
      } as SimpleError)
    })
}

function validateVirtualOrder(orderNumber: string) {
  return epayValidateVirtualOnlyOrder(orderNumber).andThen((result) => {
    if (!result) {
      // No valid order was found
      const logRef = logger.error(
        `validateVirtualOrder.. Order [${orderNumber}] not found`
      )
      return errAsync({
        code: errorCodes.order.PROCESSING_ERROR,
        message: `Order [${orderNumber}] not found`,
        logRef,
      } as SimpleError)
    } else {
      // Order was found - now validate the card count
      const itemsWithMismatch = result.productOrderItems.filter(
        (item) => item.quantity !== item._count.cardItems
      )

      if (itemsWithMismatch.length > 0) {
        const details = itemsWithMismatch.map((item) => ({
          itemId: item.id,
          expectedQuantity: item.quantity,
          actualCardCount: item._count.cardItems,
        }))
        const message = `validateVirtualOrder.. Card count mismatch found in order items, orderNumber [${orderNumber}], mismatches: ${JSON.stringify(
          details
        )}`
        const logRef = logger.error(message)
        return errAsync({
          code: errorCodes.order.PROCESSING_ERROR,
          message,
          logRef,
        } as SimpleError)
      }

      return ok({ validatedOrder: orderNumber })
    }
  })
}
