import { errAsync, from<PERSON>rom<PERSON>, okAsync, ResultAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import { initializePhysicalCardWithFundsAndLockCode } from './load-card-funds.service'
import logger from 'utils/logger'
import {
  epayGetProductOrderItemCardByCrn,
  epayGetUnprocessedFundingOrder,
  epayUpdateFundingProductOrderItemFundsLoaded,
  UnprocessedFundingOrderData,
} from 'epay-data/epay-funding-product.order.item.data'
import { createBatches } from './product-order-util'
import {
  epayUpdateProductOrderStatusByOrder,
  epayValidateOrderFundedInFull,
} from 'epay-data/epay-product-order.data'
import { OrderStatus } from '@prisma/client'
import { getUserFirstNameLastName } from 'services/user.service'
import { updateProductOrderItemCardFundsLoaded } from 'data/product-order-item-card.data'
import {
  epayUpdateLockCodeInPOIC,
  epayUpdateUnitPriceInPOIC,
} from 'epay-data/epay-product-order-item-card.data'
import { sendEmailWithTemplate } from 'services/postmark.service'
import { UpdatedProductOrderData } from 'data/product-order.data'
import { centsToDollars } from 'utils/numeric'
import { FUNDED_ORDER_RELEASE_EMAIL } from 'helpers/email-templates'
import { release } from 'os'

const BATCH_SIZE = 5

interface ProcessingResult {
  totalFunded: number
  totalFailed: number
}

interface BatchResult {
  funded: number
  failed: number
}

type FundingItem =
  UnprocessedFundingOrderData['FundingProductOrderItem'][number]

export function processFundingOrder({
  userId,
  orderId,
  paymentDate,
  useRen,
  isWindcavePayment,
}: {
  userId: string
  orderId: string
  paymentDate: Date
  useRen: boolean
  isWindcavePayment: boolean
}): ResultAsync<ProcessingResult, SimpleError> {
  let releasedByName = ''
  let orderNumber = ''

  return fromPromise(
    (async () => {
      if (isWindcavePayment) {
        releasedByName = 'Windcave Payment'
      } else {
        const result = await getUserFirstNameLastName({ userId })
        if (result.isOk()) {
          releasedByName = `${result.value.firstName} ${result.value.lastName}`
        } else {
          releasedByName = 'Unknown' // should never happen
        }
      }
      return releasedByName
    })(),
    (error) => {
      return error as SimpleError
    }
  )
    .andThen(() => {
      return epayGetUnprocessedFundingOrder({ orderId }).andThen((order) => {
        if (!order) {
          const message = `No unprocessed funding order found for orderId: ${orderId}`
          const logRef = logger.error(new Error(message), message)
          return errAsync({
            code: errorCodes.db.ITEM_NOT_FOUND,
            message,
            logRef,
          } as SimpleError)
        }
        orderNumber = order.orderNumber!

        return processFundingOrderItems({ order, useRen: useRen })
      })
    })
    .andThen((result) => {
      return completeProcessingFundingOnlyItems({
        orderId: orderId,
        paymentDate,
        releasedByName,
      }).map(() => result)
    })
    .orElse((error) => {
      return epayUpdateProductOrderStatusByOrder({
        orderStatus: OrderStatus.ON_HOLD,
        releasedAt: new Date(),
        orderNumber,
        paymentDate,
        releasedBy: releasedByName,
      }).andThen(() => {
        const logRef = logger.error(
          error,
          `3: ERROR processFundingOrder.. Failed to complete processing of order [${orderNumber}]`
        )
        return errAsync({
          code: errorCodes.order.PROCESSING_ERROR,
          message: `processFundingOrder.. Failed to complete processing of order [${orderNumber}]`,
          logRef,
        } as SimpleError)
      })
    })
}

function processFundingOrderItems({
  order,
  useRen,
}: {
  order: UnprocessedFundingOrderData
  useRen: boolean
}): ResultAsync<ProcessingResult, SimpleError> {
  const batches = createBatches(order.FundingProductOrderItem, BATCH_SIZE)

  function processNextBatch(
    batchIndex: number,
    results: ProcessingResult
  ): ResultAsync<ProcessingResult, SimpleError> {
    // If we've processed all batches, return the results
    if (batchIndex >= batches.length) {
      return ResultAsync.fromPromise(
        Promise.resolve(results),
        (error): SimpleError => ({
          code: errorCodes.card.BATCH_PROCESSING_ERROR,
          message: 'wrapping results',
          logRef: logger.error(
            error,
            `processNextBatch.. Error wrapping results for [${order.orderNumber}]`
          ),
        })
      )
    }

    const currentBatch = batches[batchIndex]
    logger.debug(`Processing batch ${batchIndex + 1} of ${batches.length}`)

    return processFundingItemsBatch({
      fundingItems: currentBatch,
      useRen: useRen,
    }).andThen((batchResult) => {
      // Combine results and process next batch
      const updatedResults = {
        totalFunded: results.totalFunded + batchResult.funded,
        totalFailed: results.totalFailed + batchResult.failed,
      }

      logger.debug(
        `Batch ${batchIndex + 1} completed for [${order.orderNumber}]: ${
          batchResult.funded
        } funded, ${batchResult.failed} failed`
      )

      return processNextBatch(batchIndex + 1, updatedResults)
    })
  }

  return processNextBatch(0, { totalFunded: 0, totalFailed: 0 }).mapErr(
    (error) => {
      const logRef = logger.error(
        error,
        `Failed to process funding order items for order ${order.orderNumber}`
      )
      return {
        code: errorCodes.card.BATCH_PROCESSING_ERROR,
        message: 'Failed to process funding order items',
        logRef,
      } as SimpleError
    }
  )
}

function mockFundingCards({ item }: { item: FundingItem }) {
  return epayGetProductOrderItemCardByCrn({
    crn: item.externalCardReferenceNumber,
  })
    .andThen((productOrderItemCard) => {
      return updateProductOrderItemCardFundsLoaded({
        productOrderItemCardId: productOrderItemCard.id,
      })
    })
    .andThen(() => {
      return epayUpdateFundingProductOrderItemFundsLoaded({
        fundingProductOrderItemId: item.id,
      })
    })
    .map((result) => {
      return {
        cardProxyNumber: result.externalCardReferenceNumber,
      }
    })
}

function processFundingItemsBatch({
  fundingItems,
  useRen,
}: {
  fundingItems: FundingItem[]
  useRen: boolean
}): ResultAsync<BatchResult, SimpleError> {
  const fundingItemPromises = fundingItems.map((item) => {
    if (useRen) {
      return epayGetProductOrderItemCardByCrn({
        crn: item.externalCardReferenceNumber,
      })
        .andThen((productOrderItemCard) => {
          return epayUpdateLockCodeInPOIC({
            id: productOrderItemCard.id,
            lockCode: item.lockCode,
          })
        })
        .andThen((productOrderItemCard) => {
          return initializePhysicalCardWithFundsAndLockCode({
            cardProxyNumber: item.externalCardReferenceNumber,
            amountInCents: item.unitPriceInCents,
            lockCode: item.lockCode,
            orderNumber: item.productOrder.orderNumber!,
            productOrderItemCardId: productOrderItemCard?.id,
          })
            .andThen((result) => {
              return epayUpdateUnitPriceInPOIC({
                id: result.productOrderItemCardId,
                unitPriceInCents: result.amountInCents,
              })
            })
            .andThen(() => {
              return epayUpdateFundingProductOrderItemFundsLoaded({
                fundingProductOrderItemId: item.id,
              })
            })
            .map((result) => {
              return {
                cardProxyNumber: result.externalCardReferenceNumber,
              }
            })
        })
    } else {
      return mockFundingCards({ item })
    }
  })

  return ResultAsync.combineWithAllErrors(fundingItemPromises)
    .map((results) => {
      const loaded = results.filter((result) => result.cardProxyNumber)
      return {
        funded: loaded.length,
        failed: fundingItems.length - loaded.length,
      }
    })
    .mapErr((errors) => {
      const logRef = logger.error(
        errors[0],
        `Batch processing failed with ${errors.length} errors`
      )
      return {
        code: errorCodes.card.BATCH_PROCESSING_ERROR,
        message: 'Batch processing failed',
        logRef,
      } as SimpleError
    })
}

function completeProcessingFundingOnlyItems({
  orderId,
  paymentDate,
  releasedByName,
}: {
  orderId: string
  paymentDate: Date
  releasedByName: string
}) {
  return epayValidateOrderFundedInFull({ orderId })
    .andThen(() => {
      logger.info(
        '(A). completeProcessingFundingOnlyItems.. Successfully validated order items.'
      )
      return epayUpdateProductOrderStatusByOrder({
        orderStatus: OrderStatus.COMPLETED,
        releasedAt: new Date(),
        orderId,
        paymentDate,
        releasedBy: releasedByName,
      })
    })
    .andThen((updatedProductOrder) => {
      return sendFundingOrderReleasedEmail(updatedProductOrder).andThen(() =>
        okAsync(updatedProductOrder)
      )
    })
    .map((updatedProductOrder) => {
      logger.debug(
        `(B). completeProcessingFundingOnlyItems.. Successfully updated status to COMPLETED`
      )

      return {
        orderNumber: updatedProductOrder.orderNumber,
        paymentDate,
        releasedByName,
        orderId,
      }
    })
}

function sendFundingOrderReleasedEmail(productOrder: UpdatedProductOrderData) {
  const sections = productOrder.FundingProductOrderItem.map(
    (fundedOrderItem) => {
      return {
        reference: fundedOrderItem.externalCardReferenceNumber ?? '',
        lockCode: fundedOrderItem.lockCode ?? '',
        amount: centsToDollars(fundedOrderItem.unitPriceInCents),
      }
    }
  )

  return sendEmailWithTemplate({
    email: productOrder.user.email,
    templateId: FUNDED_ORDER_RELEASE_EMAIL,
    templateModel: {
      name: productOrder.user.firstName,
      sections,
    },
  })
}
