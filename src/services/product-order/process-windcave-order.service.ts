import { OrderType } from '@prisma/client'
import { epayGetProductOrder } from 'epay-data/epay-product-order.data'
import { processFundingOrder } from './process-funding-order.service'
import { orderCardsFromSupplierV2 } from './order-cards.service'
import { errorCodes } from 'types/simple-error'
import { ok, ResultAsync } from 'neverthrow'
import logger from 'utils/logger'
import { processScheduledOrder } from './process-scheduled-order.service'

export type WindcaveSessionIdProps = {
  sessionId: string
  amount: number
  useRen: boolean
  userId?: string
}

// triggered from a windcave callback so process these in the background
export function processWindcaveOrder(props: WindcaveSessionIdProps) {
  return epayGetProductOrder({ windcaveSessionId: props.sessionId }).andThen(
    (order) => {
      if (order.orderStatus === 'RELEASING') {
        if (order.orderType === OrderType.SINGLE_FUNDS_LOAD) {
          ResultAsync.fromPromise(
            processFundingOrder({
              orderId: order.id,
              paymentDate: new Date(),
              userId: props.userId!,
              useRen: props.useRen,
              isWindcavePayment: true,
            }),
            (error) => {
              const logRef = logger.error(
                error,
                `processWindcaveOrder.. Failed to process funding order [${order.orderNumber}]`
              )
              return {
                code: errorCodes.order.PROCESSING_ERROR,
                message: `processWindcaveOrder.. Failed to process funding order [${order.orderNumber}]`,
                logRef,
              }
            }
          )
        } else if (order.scheduledDate) {
          ResultAsync.fromPromise(
            processScheduledOrder({
              orderId: order.id,
              paymentDate: new Date(),
              userId: props.userId!,
              isWindcavePayment: true,
            }),
            (error) => {
              const logRef = logger.error(
                error,
                `processWindcaveOrder.. Failed to process scheduled order [${order.orderNumber}]`
              )
              return {
                code: errorCodes.order.PROCESSING_ERROR,
                message: `processWindcaveOrder.. Failed to process scheduled order [${order.orderNumber}]`,
                logRef,
              }
            }
          )
        } else {
          ResultAsync.fromPromise(
            orderCardsFromSupplierV2({
              orderId: order.id,
              releasedByName: 'Windcave Payment',
              paymentDate: new Date(),
              useRen: props.useRen,
            }),
            (error) => {
              const logRef = logger.error(
                error,
                `processWindcaveOrder.. Failed to process order [${order.orderNumber}]`
              )
              return {
                code: errorCodes.order.PROCESSING_ERROR,
                message: `processWindcaveOrder.. Failed to process order [${order.orderNumber}]`,
                logRef,
              }
            }
          )
        }
      }
      return ok({ received: true })
    }
  )
}
