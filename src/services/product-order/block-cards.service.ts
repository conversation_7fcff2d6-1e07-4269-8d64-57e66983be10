import {
  getManyPrezzyCardsByCrn,
  updateProductOrderItemCardBlockedStatus,
  updateProductOrderItemCardError,
} from 'data/product-order-item-card.data'
import { cardStatusChange } from 'external-apis/ren/card-status-change.api'
import { err, fromPromise } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'

export function blockManyCardsByCrnV2({
  crns,
  orgId,
}: {
  crns: string[]
  orgId?: string // epay admins don't have an orgId
}) {
  return getManyPrezzyCardsByCrn({ crns, orgId }).andThen(
    (productOrderItemCards) => {
      const blockPromises = productOrderItemCards.map((productOrderItemCard) =>
        blockCard({
          crn: productOrderItemCard.externalCardReferenceNumber,
          productOrderItemCardId: productOrderItemCard.id,
        })
      )
      return fromPromise(Promise.all(blockPromises), (error) => {
        const logRef = logger.error(error, 'Failed to BLOCK all cards')
        return {
          code: errorCodes.card.BATCH_PROCESSING_ERROR,
          message: 'Failed to activate all cards',
          logRef,
        } as SimpleError
      })
    }
  )
}

export function blockCard({
  crn,
  productOrderItemCardId,
}: {
  crn: string
  productOrderItemCardId: string
}) {
  return cardStatusChange({
    proxyNumber: crn,
    status: 'block',
  })
    .andThen(() => {
      logger.debug(`blockCard.. success [${crn}]`)
      return updateProductOrderItemCardBlockedStatus({
        productOrderItemCardId,
        status: 'block',
      })
    })
    .orElse((error) => {
      logger.error(`blockCard.. error [${crn}]`)
      updateProductOrderItemCardError({
        id: productOrderItemCardId,
        errorString: JSON.stringify(error),
      })

      return err(error)
    })
}

/***
 * Removes the block on a card. This is NOT card activation
 */
export function unBlockCard({
  crn,
  productOrderItemCardId,
}: {
  crn: string
  productOrderItemCardId: string
}) {
  return cardStatusChange({
    proxyNumber: crn,
    status: 'unblock',
  })
    .andThen(() => {
      return updateProductOrderItemCardBlockedStatus({
        productOrderItemCardId,
        status: 'unblock',
      })
    })
    .orElse((error) => {
      updateProductOrderItemCardError({
        id: productOrderItemCardId,
        errorString: JSON.stringify(error),
      })

      return err(error)
    })
}
