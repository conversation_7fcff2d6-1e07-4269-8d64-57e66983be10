import { Prisma, ProductOrderItem } from '@prisma/client'
import {
  createProductOrderItems,
  getProductOrderItemsByProductOrderId,
  ProductOrderItemWithProduct,
} from 'data/product-order-item.data'
import {
  deletePendingProductOrderAndItemsById,
  updateProductOrderFees,
} from 'data/product-order.data'
import { err, fromPromise, okAsync } from 'neverthrow'
import {
  CourierDelivery,
  CreateProductOrderProps,
} from 'routes/product.order.route'
import {
  calcShippingTotal,
  convertOrderItemsToProductOrderItemData,
  CourierDeliveryMap,
  getCourierKey,
  getOrgDiscount,
} from 'services/product-order.service'
import { errorCodes, SimpleError } from 'types/simple-error'
import { CREDIT_CARD_SURCHARGE } from 'utils/config'
import logger from 'utils/logger'
import { getGst } from 'utils/numeric'
import { prisma } from 'utils/prisma'

export function createProductOrderItemsFromOrderItems({
  orderItems,
  orgId,
  productOrderId,
}: {
  orderItems: CreateProductOrderProps
  orgId: string
  productOrderId: string
}) {
  return convertOrderItemsToProductOrderItemData({
    orgId,
    productOrderItems: orderItems,
  })
    .map(({ orderItemsData }) => {
      return orderItemsData.map((orderItem) => ({
        ...orderItem,
        productId: orderItem.product.connect?.id,
        productOrderId,
        product: undefined,
      })) as Prisma.ProductOrderItemCreateManyInput[]
    })
    .andThen((productOrderItems) => createProductOrderItems(productOrderItems))
    .orElse((error) => {
      return deletePendingProductOrderAndItemsById({
        productOrderId,
        orgId,
      }).andThen(() => err(error))
    })
}

export function calculateAndCompleteProductOrder({
  productOrderId,
  orgId,
}: {
  productOrderId: string
  orgId: string
}) {
  return getProductOrderItemsByProductOrderId({
    productOrderId,
    orgId,
  })
    .andThen((productOrderItems) => {
      return getOrgDiscount(orgId)
        .andThen(({ shippingFeeDiscountCents }) => {
          return calcAndProcessProductOrderItemsShipping({
            productOrderItems,
            shippingFeeDiscountCents,
          }).map(({ shippingFeeDiscountTotal, shippingTotal }) => ({
            shippingFeeDiscountTotal,
            shippingTotal,
            productOrderItems,
          }))
        })
        .map(calcTotalOrderItemFees)
        .andThen((data) =>
          updateProductOrderFees({
            productOrderId,
            ...data,
          })
        )
    })
    .orElse((error) => {
      return deletePendingProductOrderAndItemsById({
        productOrderId,
        orgId,
      }).andThen(() => err(error))
    })
}

function getCourierDeliveryMapFromProductOrderItems(
  productOrderItems: ProductOrderItem[]
) {
  let counter = 1
  const result = productOrderItems.reduce<CourierDeliveryMap>((acc, item) => {
    const deliveryMethod = {
      type: 'COURIER',
      address: item.recipientAddress!,
      addressLine2: item.recipientAddressLine2!,
      city: item.recipientCity!,
      postcode: item.recipientPostCode!,
      suburb: item.recipientSuburb!,
    } as CourierDelivery

    const key = getCourierKey(deliveryMethod)
    if (acc[key]) {
      acc[key].quantity += item.quantity
    } else {
      acc[key] = {
        deliveryMethod,
        quantity: item.quantity,
        deliveryBatchId: `${counter}`,
      }
      counter += 1
    }
    return acc
  }, {})

  return result
}

function splitProductOrderItemsToI2cAndGiftStation(
  productOrderItems: ProductOrderItemWithProduct[]
) {
  const split = productOrderItems.reduce(
    (acc, poi) => {
      if (poi.deliveryMethod === 'COURIER') {
        if (poi.product.type === 'PREZZY' && poi.extraEmbossingLine) {
          acc.i2c.push(poi)
        } else {
          acc.giftStation.push(poi)
        }
      }
      return acc
    },
    {
      i2c: [] as ProductOrderItem[],
      giftStation: [] as ProductOrderItem[],
    }
  )
  return okAsync(split)
}

function updateProductOrderItemsDeliveryBatchId({
  i2c,
  giftStation,
  i2cDeliveryMap,
  giftStationDeliveryMap,
}: {
  i2c: ProductOrderItem[]
  giftStation: ProductOrderItem[]
  i2cDeliveryMap: CourierDeliveryMap
  giftStationDeliveryMap: CourierDeliveryMap
}) {
  const updates = [
    ...i2c.map((poi) => {
      const key = getCourierKey({
        address: poi.recipientAddress!,
        city: poi.recipientCity!,
      })
      return prisma.productOrderItem.update({
        where: { id: poi.id },
        data: {
          deliveryBatchId: i2cDeliveryMap[key]?.deliveryBatchId,
        },
      })
    }),
    ...giftStation.map((poi) => {
      const key = getCourierKey({
        address: poi.recipientAddress!,
        city: poi.recipientCity!,
      })
      return prisma.productOrderItem.update({
        where: { id: poi.id },
        data: {
          deliveryBatchId: giftStationDeliveryMap[key]?.deliveryBatchId,
        },
      })
    }),
  ]

  return fromPromise(prisma.$transaction(updates), (error) => {
    const logRef = logger.error(
      error,
      'Failed to update product items delivery batch ID'
    )
    return {
      code: errorCodes.db.UNKNOWN_ERROR,
      message: 'Failed to update product items delivery batch ID',
      logRef,
    } as SimpleError
  })
}

function calcAndProcessProductOrderItemsShipping({
  productOrderItems,
  shippingFeeDiscountCents,
}: {
  productOrderItems: ProductOrderItemWithProduct[]
  shippingFeeDiscountCents: number | null
}) {
  return splitProductOrderItemsToI2cAndGiftStation(productOrderItems).andThen(
    ({ i2c, giftStation }) => {
      const i2cDeliveryMap = getCourierDeliveryMapFromProductOrderItems(i2c)

      const giftStationDeliveryMap =
        getCourierDeliveryMapFromProductOrderItems(giftStation)

      return updateProductOrderItemsDeliveryBatchId({
        i2c,
        giftStation,
        i2cDeliveryMap,
        giftStationDeliveryMap,
      }).andThen(() =>
        calcShippingTotal({
          i2c: i2cDeliveryMap,
          giftStation: giftStationDeliveryMap,
          shippingFeeDiscount: shippingFeeDiscountCents ?? 0,
        })
      )
    }
  )
}

function calcTotalOrderItemFees({
  shippingFeeDiscountTotal,
  shippingTotal,
  productOrderItems,
}: {
  shippingFeeDiscountTotal: number
  shippingTotal: number
  productOrderItems: Pick<
    ProductOrderItem,
    | 'loadingFee'
    | 'discount'
    | 'digitalFee'
    | 'loadingFeeDiscount'
    | 'digitalFeeDiscount'
    | 'unitPrice'
    | 'quantity'
  >[]
}) {
  const {
    digitalFeeTotal,
    discountTotalShippingExcluded,
    loadingFeeTotal,
    subTotal,
    totalQuantity,
    digitalFeeDiscountTotal,
    loadingFeeDiscountTotal,
  } = productOrderItems.reduce<{
    loadingFeeTotal: number
    discountTotalShippingExcluded: number
    digitalFeeTotal: number
    loadingFeeDiscountTotal: number
    digitalFeeDiscountTotal: number
    subTotal: number // unitPrice * quantity
    totalQuantity: number
  }>(
    (acc, item) => {
      acc.loadingFeeTotal += item.loadingFee ?? 0
      acc.discountTotalShippingExcluded += item.discount ?? 0
      acc.digitalFeeTotal += item.digitalFee ?? 0
      acc.loadingFeeDiscountTotal += item.loadingFeeDiscount ?? 0
      acc.digitalFeeDiscountTotal += item.digitalFeeDiscount ?? 0
      acc.subTotal += item.unitPrice! * item.quantity!
      acc.totalQuantity += item.quantity!

      return acc
    },
    {
      loadingFeeTotal: 0,
      discountTotalShippingExcluded: 0,
      digitalFeeTotal: 0,
      subTotal: 0,
      totalQuantity: 0,
      loadingFeeDiscountTotal: 0,
      digitalFeeDiscountTotal: 0,
    }
  )

  const discountTotal = discountTotalShippingExcluded + shippingFeeDiscountTotal

  const baseTotal =
    subTotal + shippingTotal + loadingFeeTotal + digitalFeeTotal - discountTotal

  const digitalFeeGST = getGst(digitalFeeTotal)
  const shippingGST = getGst(shippingTotal - shippingFeeDiscountTotal)
  const discountGST = getGst(discountTotalShippingExcluded)

  const gstAmount = Math.round(digitalFeeGST + shippingGST - discountGST)

  const creditCardFee = Math.round(baseTotal * CREDIT_CARD_SURCHARGE)

  const orderTotal = baseTotal + creditCardFee

  return {
    digitalFeeTotal,
    discountTotal,
    loadingFeeTotal,
    subTotal,
    totalQuantity,
    orderTotal,
    creditCardFee,
    gstAmount,
    shippingTotal,
    digitalFeeDiscountTotal,
    loadingFeeDiscountTotal,
    shippingFeeDiscountTotal,
  }
}
