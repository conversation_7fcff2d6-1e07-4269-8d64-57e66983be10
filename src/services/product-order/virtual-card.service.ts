import { SimpleError } from 'types/simple-error'

import { ProductOrderItemCard, ProductOrderNoteStatus } from '@prisma/client'
import { err, ResultAsync } from 'neverthrow'
import logger from 'utils/logger'

import {
  createProductOrderItemCardV2,
  incrementResendVirtualCardCount,
  updateProductOrderItemCardAfterReissue,
  updateProductOrderItemCardFundsLoaded,
} from 'data/product-order-item-card.data'
import {
  updateProductOrderItemError,
  updateProductOrderItemRecipientEmail,
} from 'data/product-order-item.data'

import { createReissueTransactionHistory } from 'data/reissue-transaction-history.data'

import { epayAdminCreateProductOrderNote } from 'epay-data/product-order-note.data'

import {
  InitializeFundsResult,
  loadFundsOnCard,
} from './load-card-funds.service'
import { ProductOrderItemProp } from './order-cards.service'

import {
  createVirtualCard,
  CreateVirtualCardArgs,
} from 'external-apis/ren/card-issue.api'
import { reissueVirtualCard } from 'external-apis/ren/card-reissue-virtual-card.api'
import { resendVirtualCard } from 'external-apis/ren/card-resend-virtual-card.api'
import {
  CardDetail,
  getCardDetails,
} from 'external-apis/ren/get-card-details.api'

import { getLockCode, NO_LOCK_CODE } from './product-order-util'

import { ERRORS } from 'utils/error'
import { generateRandomNumbers } from 'utils/numeric'

export function orderVirtualCard({
  useRen: useRen,
  productOrderItem,
  orderNumber,
}: {
  useRen: boolean
  productOrderItem: ProductOrderItemProp
  orderNumber: string
  lockCodeOption: string
}): ResultAsync<InitializeFundsResult, SimpleError> {
  if (useRen) {
    const createVirtualCardArgs: CreateVirtualCardArgs = {
      orderNumber,
      recipientName: productOrderItem.recipientName,
      recipientEmail: productOrderItem.recipientEmail ?? '',
      cardType: productOrderItem.product.design.externalCardType ?? 'null',
      cardSubType:
        productOrderItem.product.design.externalCardDesignId ?? 'null',
      productCode:
        productOrderItem.product.design.externalProductCode ?? 'null',
      productBin: productOrderItem.product.design.externalBinNumber ?? 'null',
      customMessage: productOrderItem.giftStationMessage ?? '',
    }

    return createVirtualCard(createVirtualCardArgs)
      .andThen((response) => {
        logger.info(
          `1. createVirtualCard.. created Virtual Card with proxyNumber: ${response.cardIssuanceResponse.cardInfo.proxyNumber}`
        )
        return createProductOrderItemCardV2({
          activated: true,
          productOrderItemId: productOrderItem.id,
          externalCardReferenceNumber:
            response.cardIssuanceResponse.cardInfo.proxyNumber,
          unitPriceInCents: productOrderItem.unitPrice,
          lockCode: getLockCode(NO_LOCK_CODE),
          isVirtualCard: true,
        })
      })
      .andThen((productOrderItemCard) => {
        logger.info(
          `2. createVirtualCard.. sucessfully created card order item: ${productOrderItemCard.externalCardReferenceNumber}`
        )
        return loadFundsOnCard({
          amountInCents: productOrderItem.unitPrice,
          cardProxyNumber: productOrderItemCard.externalCardReferenceNumber,
          orderNumber,
          productOrderItemCardId: productOrderItemCard.id,
        })
      })
      .mapErr((error) => {
        logger.info(
          `3. createVirtualCard.. failed to initialize card with funds: ${error}`
        )
        return updateProductOrderItemError({
          id: productOrderItem.id,
          error: JSON.stringify(error),
        })
          .andThen(() => {
            const { logRef, message, code } = error
            return epayAdminCreateProductOrderNote({
              title: 'Product order item error',
              message: `
                <span><b>Code:</b> ${code}</span><br />
                <span><b>Message:</b> ${message}</span><br />
                <span><b>Log Reference:</b> ${logRef}</span><br />
                <span><b>Order Item ID:</b> ${productOrderItem.id}</span><br />
              `
                .trim()
                .replace(/\s+/g, ' '),
              status: ProductOrderNoteStatus.ERROR,
              productOrderId: productOrderItem.productOrderId,
            })
          })
          .map(() => error)
          .unwrapOr(error)
      })
  } else {
    // MOCKING
    logger.info('1. Mocking createVirtualCard - create card')
    return createProductOrderItemCardV2({
      activated: true,
      productOrderItemId: productOrderItem.id,
      externalCardReferenceNumber: `mock-${generateRandomNumbers(10)}`,
      unitPriceInCents: productOrderItem.unitPrice,
      lockCode: getLockCode(NO_LOCK_CODE),
    })
      .andThen((productOrderItemCard) => {
        logger.info('2. Mocking createVirtualCard - load funds on card')
        return updateProductOrderItemCardFundsLoaded({
          productOrderItemCardId: productOrderItemCard.id,
        })
      })
      .map((created: ProductOrderItemCard) => {
        logger.info('3. Mocking createVirtualCard - completed')
        return {
          cardProxyNumber: created.externalCardReferenceNumber,
        } as InitializeFundsResult
      })
  }
}

export function resendVirtualCardToRecipientEmail({
  cardProxyNumber,
  productOrderItemCardId,
}: {
  cardProxyNumber: string
  productOrderItemCardId: string
}) {
  return getCardDetails(cardProxyNumber)
    .andThen((cardDetails) => {
      return resendVirtualCard({
        cardSubType: cardDetails.cardSubType,
        cardType: cardDetails.cardType,
        crn: cardDetails.proxyNumber,
        internalCustomerId: cardDetails.customerId,
        recipientEmail: cardDetails.recipientEmail,
        recipientName: cardDetails.recipientFirstName,
        embossLine1: cardDetails.embossLine1 ?? '',
        embossLine2: cardDetails.embossLine2 ?? '',
      })
    })
    .andThen((response) => {
      if (!response) {
        logger.warn(
          `resendVirtualCardToRecipientEmail... failed to resend virtual card to recipient email: ${cardProxyNumber}`
        )
        return err({
          code: ERRORS.EXTERNAL_API.code,
          message: 'Failed to resend virtual card to recipient email',
        } as SimpleError)
      }

      return incrementResendVirtualCardCount({
        productOrderItemCardId,
      })
    })
}

export function reissueVirtualCardToNewRecipientEmailAddress({
  orgId,
  createdByUserId,
  cardProxyNumber,
  newRecipientEmail,
  productOrderItemId,
  productOrderItemCardId,
}: {
  orgId: string
  createdByUserId: string
  cardProxyNumber: string
  newRecipientEmail: string
  productOrderItemId: string
  productOrderItemCardId: string
}) {
  let cardInfo: CardDetail | null = null
  return getCardDetails(cardProxyNumber)
    .andThen((cardDetails) => {
      cardInfo = cardDetails
      return reissueVirtualCard({
        cardSubType: cardDetails.cardSubType,
        cardType: cardDetails.cardType,
        crn: cardDetails.proxyNumber,
        customerId: cardDetails.customerId,
        recipientEmail: newRecipientEmail,
        recipientName: cardDetails.recipientFirstName,
        embossLine1: cardDetails.embossLine1 ?? '',
        embossLine2: cardDetails.embossLine2 ?? '',
      })
    })
    .andThen((response) => {
      return createReissueTransactionHistory({
        orgId,
        currentProxyNumber: cardInfo?.proxyNumber ?? '',
        newProxyNumber: response.debitCardIssuanceResponse.cardInfo.proxyNumber,
        createdByUserId,
        currentRecipientEmail: cardInfo?.recipientEmail ?? '',
        newRecipientEmail,
        productOrderItemCardId,
        sequenceNumber: parseInt(cardInfo?.cardseqNo ?? '1'),
      })
    })
    .andThen((result) => {
      return updateProductOrderItemCardAfterReissue({
        orgId,
        productOrderItemCardId,
        newProxyNumber: result.newProxyNumber,
      })
    })
    .andThen(() => {
      return updateProductOrderItemRecipientEmail({
        orgId,
        productOrderItemId,
        recipientEmail: newRecipientEmail,
      })
    })
}
