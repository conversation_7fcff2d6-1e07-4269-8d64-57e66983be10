import { OrderStatus } from '@prisma/client'
import { UpdatedProductOrderData } from 'data/product-order.data'
import {
  epayGetUnprocessedScheduledOrder,
  epayUpdateProductOrderStatusByOrder,
} from 'epay-data/epay-product-order.data'
import { epayAddOrderToProcessQueue } from 'epay-data/order-processing-queue.data'
import { errAsync, ok } from 'neverthrow'
import { getOrderReleasedByName } from 'services/user.service'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { sendOrderDetailsToEpay } from './order-cards.service'
import { IS_LOCAL } from 'utils/config'
import { sendEmailWithTemplate } from 'services/postmark.service'
import { centsToDollars } from 'utils/numeric'
import {
  CUSTOMER_ORDER_CREDIT_CARD,
  PAYMENT_RECEIVED,
} from 'helpers/email-templates'

export function processScheduledOrder({
  paymentDate,
  orderId,
  userId,
  isWindcavePayment,
}: {
  paymentDate: Date
  orderId: string
  userId: string
  isWindcavePayment: boolean
}) {
  return epayGetUnprocessedScheduledOrder(orderId).andThen((order) => {
    return getOrderReleasedByName({ isWindcavePayment, userId }).andThen(
      (releasedByName) => {
        const orderNumber = order.orderNumber!

        return completeProcessScheduledOrder({
          orderId,
          paymentDate,
          releasedByName,
        }).orElse((error) => {
          return epayUpdateProductOrderStatusByOrder({
            orderStatus: OrderStatus.ON_HOLD,
            releasedAt: new Date(),
            orderNumber,
            paymentDate,
            releasedBy: releasedByName,
          }).andThen(() => {
            const logRef = logger.error(
              error,
              `1: ERROR processScheduledOrder.. Failed to complete processing of order [${orderNumber}]`
            )
            return errAsync({
              code: errorCodes.order.PROCESSING_ERROR,
              message: `processScheduledOrder.. Failed to complete processing of order [${orderNumber}]`,
              logRef,
            } as SimpleError)
          })
        })
      }
    )
  })
}

function completeProcessScheduledOrder({
  orderId,
  paymentDate,
  releasedByName,
}: {
  orderId: string
  paymentDate: Date
  releasedByName: string
}) {
  return epayUpdateProductOrderStatusByOrder({
    orderStatus: OrderStatus.SCHEDULED,
    releasedAt: new Date(),
    orderId,
    paymentDate,
    releasedBy: releasedByName,
  })
    .andThen((updatedProductOrder) => {
      logger.info(
        '(A). completeProcessScheduledOrder.. Successfully updated product order to SCHEDULED'
      )
      return epayAddOrderToProcessQueue({
        orderNumber: updatedProductOrder.orderNumber!,
        productOrderId: updatedProductOrder.id,
      }).map(() => {
        logger.info(
          '(B). completeProcessScheduledOrder.. Successfully added order to queue '
        )
        return updatedProductOrder
      })
    })
    .andThen((updatedProductOrder: UpdatedProductOrderData) => {
      return sendConfirmationEmail(updatedProductOrder)
    })
    .andThen((updatedProductOrder) => {
      logger.info(
        '(C). completeProcessScheduledOrder.. Successfully sent confirmation email to customer PAYMENT RECEIVED'
      )
      if (!IS_LOCAL) {
        return sendOrderDetailsToEpay(updatedProductOrder)
      }
      return ok(updatedProductOrder)
    })
    .map((updatedProductOrder) => {
      if (!IS_LOCAL) {
        logger.info(
          `(D). completeProcessScheduledOrder.. Successfully sent order to epay`
        )
      }

      return updatedProductOrder
    })
}

function sendConfirmationEmail(productOrder: UpdatedProductOrderData) {
  if (
    productOrder.paymentMethod === 'BANK_TRANSFER' ||
    productOrder.paymentMethod === 'CREDIT_CARD'
  ) {
    const templateId =
      productOrder.paymentMethod === 'BANK_TRANSFER'
        ? PAYMENT_RECEIVED
        : CUSTOMER_ORDER_CREDIT_CARD
    return sendEmailWithTemplate({
      email: productOrder.user.email,
      templateId,
      templateModel: {
        orderNumber: productOrder.orderNumber,
        firstName: productOrder.user.firstName,
        orderTotal: centsToDollars(productOrder.orderTotal),
      },
    })
      .map(() => productOrder)
      .orElse(() => ok(productOrder))
  }

  return ok(productOrder)
}
