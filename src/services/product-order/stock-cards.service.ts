import { OrderStatus, PaymentMethod } from '@prisma/client'
import {
  CUSTOMER_DIRECT_CREDIT_ORDER_SUBMITTED,
  EPAY_ORDER_DIRECT_CREDIT,
} from 'helpers/email-templates'
import { errAsync, ok } from 'neverthrow'
import { SubmitStockCardOrderProps } from 'routes/stock.route'
import { sendEmailWithTemplate } from 'services/postmark.service'
import {
  getProductOrder,
  setWhoParcelIsAddressedTo,
  updateOrderWithPaymentDetails,
  updateProductOrderItemsWithoutRecipients,
} from 'services/product-order.service'
import { EPAY_ADMIN_EMAIL } from 'utils/config'
import { ERRORS } from 'utils/error'
import { centsToDollars } from 'utils/numeric'
import { NO_LOCK_CODE } from './product-order-util'

export function submitPaymentForStockCardOrder({
  billingAddress,
  city,
  country,
  orderNumber,
  paymentMethod,
  postcode,
  purchaseOrderNumber,
  selectedDeliveryRecipients,
  isLiveAgent,
}: SubmitStockCardOrderProps) {
  return getProductOrder({ orderNumber })
    .andThen((productOrder) => {
      if (!productOrder) {
        return errAsync(ERRORS.NOT_FOUND)
      }

      return setWhoParcelIsAddressedTo({ selectedDeliveryRecipients })
        .andThen(() =>
          updateProductOrderItemsWithoutRecipients(
            productOrder.productOrderItems
          )
        )
        .andThen(() =>
          updateOrderWithPaymentDetails({
            orgId: productOrder.organizationId,
            orderNumber,
            billingAddress,
            city,
            country,
            paymentMethod,
            postcode,
            purchaseOrderNumber,
            lockCode: NO_LOCK_CODE,
            orderTotal: productOrder.orderTotal!,
            creditCardFee: productOrder.creditCardFee,
            isLiveAgent,
          })
        )
        .map(() => productOrder)
    })
    .andThen((productOrder) => {
      if (paymentMethod === PaymentMethod.CREDIT_CARD) {
        return createPaymentSession({ orderNumber })
      } else {
        // Send email notifications for bank transfer
        sendEmailWithTemplate({
          email: productOrder.user.email,
          templateId: CUSTOMER_DIRECT_CREDIT_ORDER_SUBMITTED,
          templateModel: {
            orderNumber,
            firstName: productOrder.user.firstName,
            orderTotal: centsToDollars(productOrder.orderTotal),
          },
        })

        sendEmailWithTemplate({
          email: EPAY_ADMIN_EMAIL,
          templateId: EPAY_ORDER_DIRECT_CREDIT,
          templateModel: {
            orderNumber,
            orderTotal: centsToDollars(productOrder.orderTotal),
            companyName: productOrder.organization.name,
          },
        })

        return ok({
          orderStatus: OrderStatus.SUBMITTED,
          redirect: undefined,
        })
      }
    })
}
function createPaymentSession(arg0: { orderNumber: string }): any {
  throw new Error('Function not implemented.')
}
