import { CardType, OrderStatus, PaymentMethod } from '@prisma/client'
import { UpdatedProductOrderData } from 'data/product-order.data'
import { errAsync, ok, okAsync, ResultAsync } from 'neverthrow'

import { getLatestProductOrderDetailsAndSendToEpay } from 'services/product-order.service'
import { getUserFirstNameLastName } from 'services/user.service'
import logger from 'utils/logger'
import { sendEmailWithTemplate } from 'services/postmark.service'
import {
  CUSTOMER_ORDER_CREDIT_CARD,
  PAYMENT_RECEIVED,
} from 'helpers/email-templates'
import { centsToDollars } from 'utils/numeric'
import { IS_LOCAL, SEND_ORDER_TO_EPAY } from 'utils/config'
import { getLockCode } from './product-order-util'
import { epayAddOrderToProcessQueue } from 'epay-data/order-processing-queue.data'
import {
  epayUpdateProductOrderStatusByOrder,
  ProductOrderValidationResult,
  epayValidateVirtualOnlyOrder,
  UnprocessedProductOrderData,
  epayGetUnprocessedOrderByWindcaveSessionId,
  epayGetUnprocessedOrder,
  epayGetProductOrderContentType,
  ProductOrderContent,
} from 'epay-data/epay-product-order.data'
import { errorCodes, SimpleError } from 'types/simple-error'
import { orderVirtualCard } from './virtual-card.service'
import { orderPhysicalCardBatch } from './physical-card.service'
import { InitializeFundsResult } from './load-card-funds.service'
import { isShuttingDown } from 'utils/shutting-down'

type OrderIdProps = {
  orderId: string
  useRen: boolean
  paymentDate: Date
  userId?: string
}
export type WindcaveSessionIdProps = {
  sessionId: string
  amount: number
  useRen: boolean
  userId?: string
}

export type ProductOrderItemProp =
  UnprocessedProductOrderData['productOrderItems'][number]

export function orderCardsFromSupplierByWindcaveSessionId(
  props: WindcaveSessionIdProps
) {
  return epayGetUnprocessedOrderByWindcaveSessionId({
    windcaveSessionId: props.sessionId,
    amount: props.amount,
  }).andThen((order: UnprocessedProductOrderData) => {
    return orderCardsFromSupplierV2({
      orderId: order.id,
      releasedByName: 'Windcave Payment',
      paymentDate: new Date(),
      useRen: props.useRen,
    })
  })
}

export function orderCardsFromSupplierByOrderId(props: OrderIdProps) {
  logger.info('orderCardsFromSupplierByOrderId... starting')

  return getUserFirstNameLastName({ userId: props.userId! })
    .map((user) => {
      return `${user.firstName} ${user.lastName}`
    })
    .andThen((releasedByName) => {
      return orderCardsFromSupplierV2({
        orderId: props.orderId,
        releasedByName: releasedByName,
        paymentDate: props.paymentDate,
        useRen: props.useRen,
      })
    })
}

/***
 * This is a shared function that is used by both the order by order id and by windcave
 */
export function orderCardsFromSupplierV2({
  orderId,
  releasedByName,
  paymentDate,
  useRen,
}: {
  orderId: string
  releasedByName: string
  paymentDate: Date
  useRen: boolean
}) {
  let productOrder: UnprocessedProductOrderData
  return epayGetUnprocessedOrder(orderId)
    .andThen((order: UnprocessedProductOrderData) => {
      productOrder = order
      logger.info(
        '1. orderCardsFromSupplier.. Successfully retrieved ProductOrderData.'
      )
      return processOrderItems(productOrder, useRen)
    })
    .andThen(() => {
      return epayGetProductOrderContentType(productOrder.orderNumber!)
        .andThen((contentType) => {
          logger.info(
            '2. orderCardsFromSupplier.. Successfully retrieved product order content type.'
          )
          if (contentType === ProductOrderContent.CONTAINS_PHYSICAL) {
            return completeProcessingWithPhysicalItems({
              releasedByName,
              orderNumber: productOrder.orderNumber!,
              paymentDate,
            })
          }
          return completeProcessingVirtualOnlyItems({
            orderNumber: productOrder.orderNumber!,
            paymentDate,
            releasedByName: releasedByName,
          })
        })
        .map((result) => {
          logger.info(
            `3. orderCardsFromSupplier.. Successfully completed the releasing of an order`
          )
          return result
        })
        .orElse((error) => {
          return epayUpdateProductOrderStatusByOrder({
            orderStatus: OrderStatus.ON_HOLD,
            releasedAt: new Date(),
            orderNumber: productOrder.orderNumber!,
            paymentDate: new Date(),
            releasedBy: releasedByName,
          }).andThen(() => {
            logger.info(
              `3: Error orderCardsFromSupplier.. Changed  [${productOrder.orderNumber!}]`
            )
            const logRef = logger.error(
              error,
              `3: ERROR orderCardsFromSupplier.. Failed to complete processing of order [${productOrder.orderNumber!}]`
            )
            return errAsync({
              code: errorCodes.order.PROCESSING_ERROR,
              message: `Failed to complete processing of order [${productOrder.orderNumber!}]`,
              logRef,
            } as SimpleError)
          })
        })
    })
}

function completeProcessingWithPhysicalItems({
  releasedByName,
  orderNumber,
  paymentDate,
}: {
  releasedByName: string
  orderNumber: string
  paymentDate: Date
}) {
  return epayUpdateProductOrderStatusByOrder({
    orderStatus: OrderStatus.PROCESSING,
    releasedAt: new Date(),
    orderNumber,
    paymentDate,
    releasedBy: releasedByName,
  })
    .andThen((updatedProductOrder) => {
      logger.info(
        '(A). completeProcessingWithPhysicalItems.. Successfully updated product order to PROCESSING'
      )
      return epayAddOrderToProcessQueue({
        orderNumber: updatedProductOrder.orderNumber!,
        productOrderId: updatedProductOrder.id,
      }).map(() => {
        logger.info(
          '(B). completeProcessingWithPhysicalItems.. Successfully added order to queue '
        )
        return updatedProductOrder
      })
    })
    .andThen((updatedProductOrder: UpdatedProductOrderData) => {
      return sendConfirmationEmail(updatedProductOrder)
    })
    .andThen((updatedProductOrder) => {
      logger.info(
        '(C). completeProcessingWithPhysicalItems.. Successfully sent confirmation email to customer PAYMENT RECEIVED'
      )
      if (!IS_LOCAL) {
        return sendOrderDetailsToEpay(updatedProductOrder)
      }
      return ok(updatedProductOrder)
    })
    .map((updatedProductOrder) => {
      if (!IS_LOCAL) {
        logger.info(
          `(D). completeProcessingWithPhysicalItems.. Successfully sent order to epay`
        )
      }

      return { orderReleased: updatedProductOrder.orderNumber }
    })
}

function validateCardsCreatedResult({
  result,
  orderNumber,
  paymentDate,
  releasedByName,
}: {
  result: ProductOrderValidationResult
  orderNumber: string
  paymentDate: Date
  releasedByName: string
}) {
  if (!result) {
    // No valid order was found
    return epayUpdateProductOrderStatusByOrder({
      orderStatus: OrderStatus.ON_HOLD,
      releasedAt: new Date(),
      orderNumber,
      paymentDate,
      releasedBy: releasedByName,
    }).andThen(() => {
      const logRef = logger.error(
        `completeProcessingVirtualOnlyItems.. Order [${orderNumber}] is in error and has been put ON_HOLD`
      )
      return errAsync({
        code: errorCodes.order.PROCESSING_ERROR,
        message: `Order [${orderNumber}] is in error and has been put ON_HOLD`,
        logRef,
      } as SimpleError)
    })
  } else {
    // Order was found - now validate the card count
    const itemsWithMismatch = result.productOrderItems.filter(
      (item) => item.quantity !== item._count.cardItems
    )

    if (itemsWithMismatch.length > 0) {
      const details = itemsWithMismatch.map((item) => ({
        itemId: item.id,
        expectedQuantity: item.quantity,
        actualCardCount: item._count.cardItems,
      }))
      return epayUpdateProductOrderStatusByOrder({
        orderStatus: OrderStatus.ON_HOLD,
        releasedAt: new Date(),
        orderNumber,
        paymentDate,
        releasedBy: releasedByName,
      }).andThen(() => {
        const message = `completeProcessingVirtualOnlyItems.. Card count mismatch found in order items, orderNumber [${orderNumber}], mismatches: ${JSON.stringify(
          details
        )}`
        const logRef = logger.error(message)
        return errAsync({
          code: errorCodes.order.PROCESSING_ERROR,
          message,
          logRef,
        } as SimpleError)
      })
    }

    return ok({ validatedOrder: orderNumber })
  }
}

export function completeProcessingVirtualOnlyItems({
  orderNumber,
  paymentDate,
  releasedByName,
}: {
  orderNumber: string
  paymentDate: Date
  releasedByName: string
}) {
  return epayValidateVirtualOnlyOrder(orderNumber)
    .andThen((result) => {
      logger.info(
        '(A). completeProcessingVirtualOnlyItems.. Successfully validated order items.'
      )
      return validateCardsCreatedResult({
        result,
        orderNumber,
        paymentDate,
        releasedByName,
      })
    })
    .andThen(() => {
      logger.info(
        '(B). completeProcessingVirtualOnlyItems.. validated order successfully'
      )
      return epayUpdateProductOrderStatusByOrder({
        orderStatus: OrderStatus.COMPLETED,
        releasedAt: new Date(),
        orderNumber,
        paymentDate,
        releasedBy: releasedByName,
      })
    })
    .andThen((updatedProductOrder: UpdatedProductOrderData) => {
      logger.info(
        '(C). completeProcessingVirtualOnlyItems.. Successfully updated status to COMPLETED'
      )
      return sendConfirmationEmail(updatedProductOrder)
    })
    .andThen((updatedProductOrder) => {
      logger.info(
        '(D). completeProcessingVirtualOnlyItems.. Successfully sent confirmation email to customer PAYMENT RECEIVED'
      )
      if (!IS_LOCAL) {
        return sendOrderDetailsToEpay(updatedProductOrder)
      }
      return ok(updatedProductOrder)
    })
    .map((updatedProductOrder) => {
      if (!IS_LOCAL) {
        logger.info(
          `(E). completeProcessingVirtualOnlyItems.. Successfully sent order to epay`
        )
      }

      return { orderReleased: updatedProductOrder.orderNumber }
    })
}

// NEVER return an error so that order can be completed
export function sendOrderDetailsToEpay(
  updatedProductOrder: UpdatedProductOrderData
) {
  if (SEND_ORDER_TO_EPAY) {
    return getLatestProductOrderDetailsAndSendToEpay(
      updatedProductOrder.orderNumber!
    )
      .map(() => {
        logger.info(
          `Successfully sent orderNumber [${updatedProductOrder.orderNumber}] details to epay`
        )
        return updatedProductOrder
      })
      .orElse(() => {
        // always return ok
        logger.error(
          `Failed to send orderNumber [${updatedProductOrder.orderNumber}] details to epay`
        )
        return ok(updatedProductOrder)
      })
  }

  logger.info(
    `Not configured to sent orderNumber [${updatedProductOrder.orderNumber}] details to epay`
  )
  return ok(updatedProductOrder)
}

// always return ok even if email sending fails
export function sendConfirmationEmail(productOrder: UpdatedProductOrderData) {
  if (
    productOrder.paymentMethod === PaymentMethod.BANK_TRANSFER ||
    productOrder.paymentMethod === PaymentMethod.FLOAT_FUNDS
  ) {
    return sendEmailWithTemplate({
      email: productOrder.user.email,
      templateId: PAYMENT_RECEIVED,
      templateModel: {
        orderNumber: productOrder.orderNumber,
        firstName: productOrder.user.firstName,
        orderTotal: centsToDollars(productOrder.orderTotal),
      },
    })
      .map(() => {
        return productOrder
      })
      .orElse(() => {
        return ok(productOrder)
      })
  } else if (productOrder.paymentMethod === PaymentMethod.CREDIT_CARD) {
    return sendEmailWithTemplate({
      email: productOrder.user.email,
      templateId: CUSTOMER_ORDER_CREDIT_CARD,
      templateModel: {
        orderNumber: productOrder.orderNumber,
        firstName: productOrder.user.firstName,
        orderTotal: centsToDollars(productOrder.orderTotal),
      },
    })
      .map(() => {
        return productOrder
      })
      .orElse(() => {
        return ok(productOrder)
      })
  }

  return ok(productOrder)
}

export function processOrderItems(
  productOrder: UnprocessedProductOrderData,
  useRen: boolean
) {
  const { i2c } = splitProductOrderI2CAndGiftStation(
    productOrder.productOrderItems
  )
  const { withRecipient, withoutRecipient } =
    groupProductOrderItemsByDeliveryRecipient(i2c)

  return processAllProductOrderItems({
    withRecipient,
    withoutRecipient,
    productOrder,
    userEmail: productOrder.user.email,
    useRen: useRen,
  })
}

function processAllProductOrderItems({
  withRecipient,
  withoutRecipient,
  productOrder,
  userEmail,
  useRen,
}: {
  withRecipient: ProductOrderItemProp[]
  withoutRecipient: ProductOrderItemProp[]
  productOrder: UnprocessedProductOrderData
  userEmail: string
  useRen: boolean
}) {
  const processWithRecipient = processProductOrderItems({
    productOrderItems: withRecipient,
    secureDelivery: productOrder.secureDelivery,
    orderNumber: productOrder.orderNumber!,
    userEmail,
    useRen: useRen,
    lockCodeOption: productOrder.lockCode,
  })

  const processWithoutRecipient = processProductOrderItems({
    productOrderItems: withoutRecipient,
    secureDelivery: productOrder.secureDelivery,
    orderNumber: productOrder.orderNumber!,
    userEmail,
    useRen: useRen,
    lockCodeOption: productOrder.lockCode,
  })

  return ResultAsync.combine([
    processWithRecipient,
    processWithoutRecipient,
  ]).map(() => {
    logger.info('All product orderitems processed successfully')
    return productOrder
  })
}

function processProductOrderItems({
  productOrderItems,
  secureDelivery,
  orderNumber,
  userEmail,
  useRen,
  lockCodeOption,
}: {
  productOrderItems: ProductOrderItemProp[]
  secureDelivery: boolean
  orderNumber: string
  userEmail: string
  useRen: boolean
  lockCodeOption: string | null
}) {
  return addCardsForProductOrderItemsV2({
    productOrderItems,
    orderNumber,
    userEmail,
    secureDelivery,
    useRen: useRen,
    lockCodeOption,
  })
}

type AddCardResult = {
  [k: string]: any
}

export function addCardsForProductOrderItemsV2({
  useRen,
  orderNumber,
  userEmail,
  secureDelivery,
  productOrderItems,
  lockCodeOption,
}: {
  useRen: boolean
  orderNumber: string
  userEmail: string
  secureDelivery: boolean
  productOrderItems: ProductOrderItemProp[]
  lockCodeOption: string | null
}) {
  const PAGE_SIZE = 5

  const pages = batchAddCardsForProductOrderItems(productOrderItems, PAGE_SIZE)

  return pages.reduce<ResultAsync<AddCardResult[], SimpleError>>(
    (accP, page, idx) =>
      accP.andThen((acc) => {
        if (isShuttingDown()) {
          logger.error(
            `Shutting down, skipping card creation for order [${orderNumber}]`
          )
          return okAsync(acc)
        }

        return ResultAsync.combine(
          page.map((productOrderItem) => {
            // split product order items into physical and virtual
            const cardType: CardType =
              productOrderItem.deliveryMethod === 'COURIER'
                ? 'PHYSICAL'
                : 'VIRTUAL'

            // order a batch // card not processed until batch is finished processing
            if (cardType === 'PHYSICAL') {
              // Physical cards are ordered by batch and there is a delay until
              // they are ready to be processed further
              // Further processing is triggered by a chron job (or callback it gets implemented)
              return orderPhysicalCardBatch({
                userEmail,
                productOrderItem,
                useRen: useRen,
                secureDelivery,
                orderNumber,
              })
            } else {
              // virtual card product order items should have a quantity of 1 and
              // are processed immediately
              return orderVirtualCard({
                useRen: useRen,
                productOrderItem: productOrderItem,
                lockCodeOption: getLockCode(lockCodeOption),
                orderNumber,
              })
            }
          })
        ).map((results) => {
          logger.info(
            `Batch ${idx + 1}/${pages.length}: ${results.length} items`
          )
          return [...acc, ...results]
        })
      }),
    okAsync<AddCardResult[]>([])
  )
}

function batchAddCardsForProductOrderItems(
  items: ProductOrderItemProp[],
  pageSize: number
) {
  const pages = [] as ProductOrderItemProp[][]
  for (let i = 0; i < items.length; i += pageSize) {
    pages.push(items.slice(i, i + pageSize))
  }
  return pages
}

function splitProductOrderI2CAndGiftStation(
  productOrderItems: ProductOrderItemProp[]
) {
  return { i2c: productOrderItems }
  // FIXME: this is a temporary solution until we need to implement gift station
  // return {
  //   i2c: productOrderItems.filter(item =>
  //     item.product.type === 'CUSTOM' ||
  //     (item.product.type === 'PREZZY' && item.deliveryMethod === 'EMAIL') ||
  //     (item.product.type === 'PREZZY' && item.extraEmbossingLine)
  //   ),
  //   giftStation: productOrderItems.filter(item =>
  //     !(item.product.type === 'CUSTOM' ||
  //       (item.product.type === 'PREZZY' && item.deliveryMethod === 'EMAIL') ||
  //       (item.product.type === 'PREZZY' && item.extraEmbossingLine))
  //   )
  // }
}

function groupProductOrderItemsByDeliveryRecipient(
  productOrderItems: ProductOrderItemProp[]
) {
  return {
    withRecipient: productOrderItems.filter((item) => item.deliveryRecipient),
    withoutRecipient: productOrderItems.filter(
      (item) => !item.deliveryRecipient
    ),
  }
}
