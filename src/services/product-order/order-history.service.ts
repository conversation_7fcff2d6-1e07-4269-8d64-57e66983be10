import { Prisma } from '@prisma/client'
import { getOrderVisibilityRestriction } from 'data/organization.data'
import { searchProductOrders } from 'data/product-order.data'
import { errAsync } from 'neverthrow'
import { ListProps } from 'routes/product.order.route'
import { errorCodes, SimpleError } from 'types/simple-error'
import { centsToDollars } from 'utils/numeric'

type SearchParams = {
  orgId: string
  userId: string
  userRole: string
} & ListProps

function buildOrderHistoryWhereClause({
  orgId,
  userId,
  userRole,
  orderNumber,
  startDate,
  endDate,
  status,
  enableOrderVisibilityRestriction,
}: SearchParams & {
  enableOrderVisibilityRestriction: boolean
}): Prisma.ProductOrderWhereInput {
  const baseWhere: Prisma.ProductOrderWhereInput = {
    organizationId: orgId,
    orderNumber: {
      contains: orderNumber,
      mode: 'insensitive',
    },
    orderStatus: status || { not: 'PENDING' },
    createdAt: {
      gte: startDate,
      lte: endDate,
    },
  }

  if (enableOrderVisibilityRestriction && userRole === 'ORG_BASIC') {
    return { ...baseWhere, userId }
  }

  return baseWhere
}

function formatOrderHistorySearchResults([productOrders, count]: [
  any[],
  number
]) {
  return {
    count,
    productOrders: productOrders.map((order) => ({
      id: order.id,
      orderType: order.orderType,
      orderNumber: order.orderNumber,
      orderStatus: order.orderStatus,
      orderTotal: centsToDollars(order.orderTotal),
      createdAt: order.createdAt,
      totalQuantity: order.totalQuantity,
      user: order.user,
    })),
  }
}

export function searchOrdersForOrg(params: SearchParams) {
  return getOrderVisibilityRestriction(params.orgId).andThen((org) => {
    if (!org) {
      return errAsync({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Organization not found: [${params.orgId}]`,
      } as SimpleError)
    }

    const where = buildOrderHistoryWhereClause({
      ...params,
      enableOrderVisibilityRestriction: org.enableOrderVisibilityRestriction,
    })

    return searchProductOrders({
      where,
      page: params.page,
      pageSize: params.pageSize,
    }).map(formatOrderHistorySearchResults)
  })
}
