import {
  updateProductOrderItemCardError,
  updateProductOrderItemCardFundsLoaded,
  updateProductOrderItemCardLockCodeSet,
} from 'data/product-order-item-card.data'
import {
  epayUpdateFundingOrderItemLockCodeSet,
  epayUpdateFundingProductOrderItemError,
  epayUpdateFundingProductOrderItemFundsLoaded,
} from 'epay-data/epay-funding-product.order.item.data'
import {
  CardDetail,
  getCardDetails,
} from 'external-apis/ren/get-card-details.api'
import { loadFunds } from 'external-apis/ren/submit-transaction.api'
import { setActivationCode } from 'external-apis/ren/update-info.api'
import { err, ok } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'

export interface InitializeFundsResult {
  cardProxyNumber: string
  productOrderItemCardId: string
  amountInCents: number
  orderNumber: string
}

export function initializePhysicalCardWithFundsAndLockCode({
  cardProxyNumber,
  amountInCents,
  lockCode,
  orderNumber,
  productOrderItemCardId,
}: {
  cardProxyNumber: string
  amountInCents: number
  lockCode: string
  orderNumber: string
  productOrderItemCardId: string
}) {
  logger.debug(
    `initialize card with funds for proxy number [${cardProxyNumber}]`
  )

  return getCardDetails(cardProxyNumber)
    .andThen((cardDetail: CardDetail) => {
      logger.debug(
        `1. getCardDetails successful cardProxyNumber: [${cardProxyNumber}]`
      )
      return setActivationCode({
        activationCode: lockCode,
        cardDetail,
      })
    })
    .andThen((cardDetail: CardDetail) => {
      logger.debug(
        `2. set activation code successful cardProxyNumber: [${cardProxyNumber}]`
      )
      return updateProductOrderItemCardLockCodeSet({
        productOrderItemCardId,
      }).andThen(() => {
        return ok(cardDetail)
      })
    })
    .andThen((cardDetail: CardDetail) => {
      logger.debug(
        `3. updated db with lockCodeSet [true] successful cardProxyNumber: [${cardProxyNumber}]`
      )
      return loadFunds({
        amountInCents,
        cardDetail,
        orderNumber,
      })
    })
    .andThen((cardDetail: CardDetail) => {
      logger.debug(
        `4. Funds loaded successful cardProxyNumber: [${cardProxyNumber}]`
      )
      return updateProductOrderItemCardFundsLoaded({
        productOrderItemCardId,
      }).andThen(() => {
        return ok(cardDetail)
      })
    })
    .map((card) => {
      logger.debug(
        `5. updated db with fundsLoaded [true] successful cardProxyNumber: [${cardProxyNumber}]`
      )
      logger.debug(
        `6. finished initializing card successfully cardProxyNumber: [${cardProxyNumber}]`
      )
      return {
        cardProxyNumber: card.proxyNumber,
        productOrderItemCardId,
        amountInCents,
        orderNumber,
      } as InitializeFundsResult
    })
    .orElse((error: SimpleError) => {
      return updateProductOrderItemCardError({
        id: productOrderItemCardId,
        errorString: JSON.stringify(error),
      }).andThen(() => {
        const logRef = logger.error(
          error,
          `Failed to complete initialize funds sequence. cardProxyNumber: [${cardProxyNumber}]`
        )
        return err({
          code: errorCodes.external.ren.REQUEST_FAILED,
          message: 'Failed to initialize funds',
          logRef,
        } as SimpleError)
      })
    })
}

export function loadFundsOnCard({
  cardProxyNumber,
  amountInCents,
  orderNumber,
  productOrderItemCardId,
}: {
  cardProxyNumber: string
  amountInCents: number
  orderNumber: string
  productOrderItemCardId: string
}) {
  logger.debug(`initializeVirtualCardWithFunds..begin [${cardProxyNumber}]`)

  return getCardDetails(cardProxyNumber)
    .andThen((cardDetail: CardDetail) => {
      logger.debug(`1. get card details successful [${cardProxyNumber}]`)
      return loadFunds({
        amountInCents,
        cardDetail,
        orderNumber,
      })
    })
    .andThen((cardDetail: CardDetail) => {
      logger.debug(`2. Funds loaded successful [${cardProxyNumber}]`)
      return updateProductOrderItemCardFundsLoaded({
        productOrderItemCardId,
      }).andThen(() => {
        return ok(cardDetail)
      })
    })
    .map((card) => {
      logger.debug(
        `4. updated db with fundsLoaded [true] successful [${cardProxyNumber}]`
      )
      logger.debug(
        `7. finished initializing card successfully [${cardProxyNumber}]`
      )
      return {
        cardProxyNumber: card.proxyNumber,
      } as InitializeFundsResult
    })
    .orElse((error: SimpleError) => {
      return updateProductOrderItemCardError({
        id: productOrderItemCardId,
        errorString: JSON.stringify(error),
      }).andThen(() => {
        const logRef = logger.error(
          error,
          'Failed to complete initialize funds sequence.'
        )
        return err({
          code: errorCodes.external.ren.REQUEST_FAILED,
          message: 'Failed to initialize funds',
          logRef,
        } as SimpleError)
      })
    })
}

// export function initializePhysicalCardForFundingItem({
//   cardProxyNumber,
//   amountInCents,
//   lockCode,
//   orderNumber,
//   fundingProductOrderItemId,
// }: {
//   cardProxyNumber: string
//   amountInCents: number
//   lockCode: string
//   orderNumber: string
//   fundingProductOrderItemId: string
// }) {
//   logger.debug(
//     `initialize card with funds for proxy number [${cardProxyNumber}]`
//   )

//   return getCardDetails(cardProxyNumber)
//     .andThen((cardDetail: CardDetail) => {
//       logger.debug(
//         `1. getCardDetails successful cardProxyNumber: [${cardProxyNumber}]`
//       )
//       return setActivationCode({
//         activationCode: lockCode,
//         cardDetail,
//       })
//     })
//     .andThen((cardDetail: CardDetail) => {
//       logger.debug(
//         `2. set activation code successful cardProxyNumber: [${cardProxyNumber}]`
//       )
//       return epayUpdateFundingOrderItemLockCodeSet({
//         fundingProductOrderItemId,
//       }).andThen(() => {
//         return ok(cardDetail)
//       })
//     })
//     .andThen((cardDetail: CardDetail) => {
//       logger.debug(
//         `3. updated db with lockCodeSet [true] successful cardProxyNumber: [${cardProxyNumber}]`
//       )
//       return loadFunds({
//         amountInCents,
//         cardDetail,
//         orderNumber,
//       })
//     })
//     .andThen((cardDetail: CardDetail) => {
//       logger.debug(
//         `4. Funds loaded successful cardProxyNumber: [${cardProxyNumber}]`
//       )
//       return epayUpdateFundingProductOrderItemFundsLoaded({
//         fundingProductOrderItemId,
//       }).andThen(() => {
//         return ok(cardDetail)
//       })
//     })
//     .map((card) => {
//       logger.debug(
//         `5. updated db with fundsLoaded [true] successful cardProxyNumber: [${cardProxyNumber}]`
//       )
//       logger.debug(
//         `6. finished initializing card successfully cardProxyNumber: [${cardProxyNumber}]`
//       )
//       return {
//         cardProxyNumber: card.proxyNumber,
//         unitPriceInCents: amountInCents,
//         fundsLoaded: true,
//       }
//     })
//     .orElse((error: SimpleError) => {
//       return epayUpdateFundingProductOrderItemError({
//         id: fundingProductOrderItemId,
//         errorString: JSON.stringify(error),
//       }).andThen(() => {
//         const logRef = logger.error(
//           error,
//           `initializePhysicalCardForFundingItem.. Failed to complete initialize funds sequence. cardProxyNumber: [${cardProxyNumber}]`
//         )
//         return err({
//           code: errorCodes.external.ren.REQUEST_FAILED,
//           message: 'Failed to initialize funds',
//           logRef,
//         } as SimpleError)
//       })
//     })
// }
