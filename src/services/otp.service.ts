import jwt from 'jsonwebtoken'
import {
  ResultAsync,
  err,
  errAsync,
  fromPromise,
  ok,
  okAsync,
} from 'neverthrow'
import { sendTextMessage } from 'utils/aws'
import { AUTH0_REDIRECT_SECRET } from 'utils/config'
import { ERRORS, ErrorResponse } from 'utils/error'
import logger from 'utils/logger'
import { generateRandomNumbers } from 'utils/numeric'
import { prisma } from 'utils/prisma'

const MAX_FAILED_ATTEMPTS = 5

/***
 * This doesn't create the challenge anymore because we use Twillio service for that.
 * This DOES store the phone number against the userId so that we can securely
 * verify the challenge with twillio
 */
export function saveUserPhoneNumber({
  userId,
  phoneNumber,
}: {
  userId: string
  phoneNumber: string
}): ResultAsync<{ otp: string }, ErrorResponse> {
  if (!userId) {
    return errAsync({
      code: ERRORS.BAD_INPUT.code,
      message: 'userId is required',
    } as ErrorResponse)
  }

  return fromPromise(
    prisma.oneTimePassword
      .upsert({
        where: { userId },
        create: {
          userId,
          otp: 'now using twillio',
          createdAt: new Date(),
          verified: false, // not used
          phoneNumber, // The important field!!!
        },
        update: {
          otp: 'now using twillio',
          createdAt: new Date(),
          verified: false, // not used
          phoneNumber, // The important field!!!
        },
      })
      .then((otpRecord) => {
        return { otp: otpRecord.otp }
      }),
    (error) => {
      logger.error(error, `Error creating OTP challenge for user [${userId}]`)
      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: 'Error creating OTP challenge for user',
      } as ErrorResponse
    }
  )
}

export function getPhoneOtpNumberForUser(userId: string) {
  return fromPromise(
    prisma.oneTimePassword.findFirst({
      where: { userId },
    }),
    (error) => {
      logger.error(error, `db error getting OTP for user [${userId}]`)
      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: 'Error finding OTP for user',
      } as ErrorResponse
    }
  ).andThen((otpRecord) => {
    if (!otpRecord) {
      return errAsync({
        code: ERRORS.NOT_FOUND.code,
        message: 'Error finding OTP for user',
      } as ErrorResponse)
    }

    return okAsync(otpRecord.phoneNumber)
  })
}

function getChallengePassword(userId: string) {
  return fromPromise(
    prisma.oneTimePassword.findFirst({
      where: { AND: [{ userId }, { verified: false }] },
    }),
    (error) => {
      logger.error(error, `db error getting OTP for user [${userId}]`)
      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: 'Error finding OTP for user',
      } as ErrorResponse
    }
  ).andThen((otpRecord) => {
    if (otpRecord) {
      if (
        new Date(otpRecord.createdAt).getTime() <
        new Date().getTime() - 10 * 60 * 1000 // 10 minutes in milliseconds
      ) {
        return errAsync({
          code: ERRORS.NOT_FOUND.code,
          message: 'OTP is older than 30 days',
        } as ErrorResponse)
      }

      return okAsync(otpRecord)
    } else {
      return errAsync({
        code: ERRORS.NOT_FOUND.code,
        message: 'Error finding OTP for user',
      } as ErrorResponse)
    }
  })
}

function updateOneTimePassword({
  userId,
  verified,
  failedAttempts,
}: {
  userId: string
  verified: boolean
  failedAttempts: number
}) {
  return fromPromise(
    prisma.oneTimePassword.update({
      where: { userId },
      data: { verified, failedAttempts },
    }),
    (error) => {
      logger.error(
        error,
        `Error marking OTP challenge as used for user [${userId}]`
      )
      return {
        code: ERRORS.NOT_FOUND.code,
        message: 'Error marking OTP challenge as used for user',
      } as ErrorResponse
    }
  ).andThen((otpRecord) => {
    if (otpRecord) {
      return okAsync({
        otp: {
          userId: otpRecord.userId,
          verified: otpRecord.verified,
        },
      })
    } else {
      return errAsync({
        code: ERRORS.NOT_FOUND.code,
        message: 'Error marking OTP challenge as used for user',
      } as ErrorResponse)
    }
  })
}

export function verifyChallenge({
  userId,
  otpSubmission,
}: {
  userId: string
  otpSubmission: string
}) {
  return getChallengePassword(userId).andThen((otpRecord) => {
    const success = otpRecord.otp === otpSubmission
    if (success) {
      return updateOneTimePassword({
        userId,
        verified: true,
        failedAttempts: 0,
      }).andThen(() => okAsync(otpRecord))
    } else {
      const failedAttempts = otpRecord.failedAttempts + 1

      return updateOneTimePassword({
        userId,
        verified: false,
        failedAttempts,
      }).andThen(() => {
        if (failedAttempts > MAX_FAILED_ATTEMPTS - 1) {
          return errAsync({
            code: ERRORS.UNAUTHORIZED.code,
            message: 'Too many failed OTP attempts.',
          } as ErrorResponse)
        }

        return errAsync({
          code: ERRORS.BAD_INPUT.code,
          message: "The code you entered doesn't match. Please try again.",
        } as ErrorResponse)
      })
    }
  })
}

export function sendChallenge({
  userId,
  phoneNumber,
}: {
  userId: string
  phoneNumber: string
}) {
  return saveUserPhoneNumber({ userId, phoneNumber })
    .andThen((otpRecord) => {
      return sendTextMessage({
        phoneNumber: phoneNumber,
        message: `Your OTP is ${otpRecord.otp}`,
      })
    })
    .mapErr((error) => {
      return error as ErrorResponse
    })
}

export function createRedirectToken({
  authToken,
  state,
  phoneNumber,
}: {
  authToken: string
  state: string
  phoneNumber: string
}) {
  const issuedAt = Math.floor(Date.now() / 1000)
  const decoded = jwt.decode(authToken, { json: true })

  const payload = {
    ...decoded,
    state,
    iat: issuedAt,
    exp: issuedAt + 60, // Expires in one minute
    phoneNumber,
  }

  return jwt.sign(payload, AUTH0_REDIRECT_SECRET)
}
