import { Prisma, UserRole } from '@prisma/client'
import { err, errAsync, fromPromise, ok, okAsync } from 'neverthrow'
import type { UpdateUserValidationProps } from 'routes/user.route'
import { ERRORS, type PickErrorCode<PERSON><PERSON> } from 'utils/error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'
import { findUserById } from '../models/user'

import { randomUUID } from 'crypto'
import {
  ADMIN_PORTAL_URL_BASE,
  CORPORATE_PORTAL_URL_BASE,
  ENVIRONMENT,
} from 'utils/config'
import { NEW_USER_CREATED } from '../helpers/email-templates'
import {
  createAuth0User,
  createChangePasswordTicket,
  deleteUserFromAuth0,
  getUserInfoByEmail,
} from './auth0.service'
import {
  deleteOrganizationById,
  getRoleCountForOrg,
} from './organization.service'
import { sendEmailWithTemplate } from './postmark.service'

const AUTH0_USER_ID_PREFIX = 'auth0|'

export function getUserById({ id }: { id: string }) {
  return fromPromise(
    prisma.user.findUnique({
      where: {
        id,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        organization: true,
        role: true,
      },
    }),
    (error) => {
      logger.warn(`failed to query user [${id}]`, error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function getOrgUserById({ id, orgId }: { id: string; orgId: string }) {
  return fromPromise(
    prisma.user.findFirst({
      where: {
        AND: [{ id }, { organizationId: orgId }],
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        organization: true,
        role: true,
      },
    }),
    (error) => {
      logger.warn(`failed to query user [${id}]`, error)
      return err(ERRORS.DATABASE_ERROR)
    }
  ).andThen((user) => {
    if (!user) {
      return err(ERRORS.NOT_FOUND)
    }

    return ok(user)
  })
}

export function getUser({ id }: { id: string }) {
  logger.info(`getUser.... begin getting user with id [${id}]...`)

  return findUserById(id, {
    corporateApplications: {
      select: {
        id: true,
        name: true,
        organizationType: true,
        submittedAt: true,
        status: true,
        updatedAt: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            role: true,
          },
        },
      },
    },
    organization: {
      select: {
        id: true,
        name: true,
        enableCustomerOrderReference: true,
        enableBrandedStockCards: true,
        enableOrderVisibilityRestriction: true,
      },
    },
  }).andThen((user) => {
    const userData = {
      id: user.id,
      role: user.role,
      email: user.email,

      firstName: user.firstName,
      lastName: user.lastName,
      phoneNbr: user.phoneNbr,
      branch: user.branch,

      loginNotification: user.loginNotification,

      corporateApplications: user.corporateApplications,

      organization: user.organization,
    }

    if (user.loginNotification) {
      return updateUser({
        id: user.id,
        data: {
          loginNotification: null,
        },
      }).map(() => {
        return userData
      })
    }

    return okAsync(userData)
  })
}

function getCorporateOrganizationFromUserId({ userId }: { userId: string }) {
  return fromPromise(
    prisma.user
      .findUnique({
        where: {
          id: userId,
        },
        include: {
          organization: true,
        },
      })
      .then((user) => {
        if (user && user.organization) {
          return user.organization
        }
        throw new Error('NOT_FOUND')
      }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'NOT_FOUND'> => {
      logger.error(error)

      if (error !== 'NOT_FOUND') {
        return 'DATABASE_ERROR'
      }

      return 'NOT_FOUND'
    }
  )
}

/**
 * Gets user with id if they exist within the same org as userId
 * @param id - user id to get
 * @param userId - user id from who is requesting
 */
export function findUserWithinOrg({
  id,
  orgId,
}: {
  id: string
  orgId: string
}) {
  return fromPromise(
    prisma.user.findFirst({
      where: {
        id,
        organizationId: orgId,
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to query user [${id}] within org [${orgId}]`)

      return 'DATABASE_ERROR'
    }
  ).andThen((user) => {
    if (!user) {
      return err('NOT_FOUND' as const)
    }

    return ok(user)
  })
}

export function getUserByEmail({ email }: { email: string }) {
  return fromPromise(
    prisma.user.findFirst({
      where: {
        email,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        organization: true,
        role: true,
      },
    }),
    (error) => {
      logger.warn(`failed to get user by email [${email}]`, error)
      return err(ERRORS.DATABASE_ERROR)
    }
  ).andThen((user) => {
    if (!user) {
      return err(ERRORS.NOT_FOUND)
    }

    return ok(user)
  })
}

export async function createUser({
  orgId,
  role,
  firstName,
  lastName,
  email: userEmail,
  branch,
}: {
  orgId?: string
  role: UserRole
  firstName: string
  lastName: string
  email: string
  branch?: string | undefined
}) {
  const email = userEmail.toLowerCase()

  logger.info(
    `user.service :: begin creating org user with email [${email}]...`
  )

  const auth0User = await getUserInfoByEmail({ email })
  if (auth0User.isOk()) {
    logger.info(`user with email [${email}] already exists in Auth0`)

    if (Array.isArray(auth0User.value) && auth0User.value.length > 0) {
      return errAsync(ERRORS.ALREADY_EXIST)
    }
  }

  const user = await getUserByEmail({ email })
  if (user.isOk()) {
    logger.info(`user with email [${email}] already exists in local DB`)
    return errAsync(ERRORS.ALREADY_EXIST)
  }

  const isAdmin = role.substring(0, 4) === 'EPAY'

  const randomPassword = randomUUID()
    .replace(`-`, ``)
    .substring(0, 20)
    .toUpperCase()
  const newAuth0User = await createAuth0User({
    firstName,
    lastName,
    email,
    password: randomPassword,
    isAdmin,
  })

  if (newAuth0User.isErr()) {
    return errAsync(newAuth0User.error)
  }

  const auth0UserId = newAuth0User.value.user_id
  logger.debug(`created user in Auth0 with id [${auth0UserId}]`)
  const epayUserId = auth0UserId.replace(AUTH0_USER_ID_PREFIX, '')

  const newUser = await createUserInDb({
    orgId,
    userId: epayUserId,
    role,
    firstName,
    lastName,
    email,
    branch,
  })

  if (newUser.isErr()) {
    logger.info(
      `failed to create user with email [${email}] in local DB`,
      newUser.error
    )
    return errAsync(newUser.error)
  }

  const resultUrl = isAdmin ? ADMIN_PORTAL_URL_BASE : CORPORATE_PORTAL_URL_BASE

  const changePassword = await createChangePasswordTicket({
    email,
    isAdmin,
  })
  if (changePassword.isErr()) {
    logger.info(
      `failed to create change password ticket for user with email [${email}] in Auth0`,
      changePassword.error
    )
    return errAsync(changePassword.error)
  }

  const changePasswordUrl = changePassword.value.ticket

  const sendInvite = await sendEmailWithTemplate({
    email,
    templateModel: {
      name: firstName,
      product_name: `Prezzy ${isAdmin ? 'Admin' : 'Corporate'} Portal`,
      action_url: changePasswordUrl,
      env: ENVIRONMENT,
      password: randomPassword,
      website_url: resultUrl,
    },
    templateId: NEW_USER_CREATED,
  })

  if (sendInvite.isErr()) {
    logger.info(
      `failed to send invite email to user with email [${email}]`,
      sendInvite.error
    )
    return errAsync({
      code: ERRORS.EXTERNAL_API.code,
      message: 'Failed to send invite email',
    })
  }

  return okAsync(newUser.value)
}

function createUserWithoutOrg({
  userId,
  role,
  firstName,
  lastName,
  email,
  branch,
}: {
  userId: string
  role: UserRole
  firstName: string
  lastName: string
  email: string
  branch?: string
}) {
  return fromPromise(
    prisma.user.create({
      data: {
        id: userId,
        firstName,
        lastName,
        email,
        role,
        branch,
      },
    }),
    (error) => {
      logger.warn(`failed to create user with [${email}]`, error)
      return err(ERRORS.DATABASE_ERROR)
    }
  )
}

function createUserInDb({
  userId,
  orgId,
  role,
  firstName,
  lastName,
  email,
  branch,
}: {
  userId: string
  orgId?: string
  role: UserRole
  firstName: string
  lastName: string
  email: string
  branch?: string
}) {
  if (orgId) {
    return createUserWithOrg({
      userId,
      orgId,
      role,
      firstName,
      lastName,
      email,
      branch,
    })
  } else {
    return createUserWithoutOrg({
      userId,
      role,
      firstName,
      lastName,
      email,
      branch,
    })
  }
}

// Creates a user in the database with the auth0 user id
function createUserWithOrg({
  userId,
  orgId,
  role,
  firstName,
  lastName,
  email,
  branch,
}: {
  userId: string
  orgId: string
  role: UserRole
  firstName: string
  lastName: string
  email: string
  branch?: string
}) {
  return fromPromise(
    prisma.user.create({
      data: {
        id: userId,
        firstName,
        lastName,
        email: email.toLowerCase(),
        role,
        branch,
        organization: {
          connect: {
            id: orgId,
          },
        },
      },
    }),
    (error) => {
      logger.warn(`failed to create user with [${email}]`, error)
      return err(ERRORS.DATABASE_ERROR)
    }
  )
}

export async function updateUserWithinOrg({
  requesterId: requesterId,
  userId,
  requesterRole: requesterRole,
  orgId,
  firstName,
  lastName,
  email,
  role,
  branch,
}: UpdateUserValidationProps & {
  requesterId: string
  userId: string
  requesterRole: UserRole
  orgId: string
}) {
  // check user exists in org - you can't update a user in a different org
  const user = await getOrgUserById({ id: userId, orgId })
  if (user.isErr()) {
    return errAsync(user.error)
  }

  if (
    requesterId !== userId &&
    requesterRole != UserRole.ORG_ADMIN &&
    requesterRole != UserRole.ORG_MANAGER
  ) {
    return errAsync({
      code: ERRORS.ILLEGAL_ACTION.code,
      message:
        'You are not authorized to update this user. You must be an Admin or Manger',
    })
  }

  if (requesterRole === 'ORG_MANAGER' && role === 'ORG_ADMIN') {
    return errAsync({
      code: ERRORS.ILLEGAL_ACTION.code,
      message:
        'You are not authorized to update this user. A Manager cannot update an Admin',
    })
  }

  if (
    requesterRole === UserRole.ORG_ADMIN &&
    requesterId === userId &&
    role !== UserRole.ORG_ADMIN
  ) {
    // you can't update yourself to a non-admin if you are the last admin
    if (user.value.role === UserRole.ORG_ADMIN) {
      const roleCount = await getRoleCountForOrg({
        orgId,
        role: UserRole.ORG_ADMIN,
      })
      if (roleCount.isErr()) {
        return errAsync(roleCount.error)
      }

      if (roleCount.value < 2) {
        return errAsync({
          code: ERRORS.ILLEGAL_ACTION.code,
          message: 'Cannot delete the last admin user',
        })
      }
    }

    return errAsync({
      code: ERRORS.ILLEGAL_ACTION.code,
      message:
        'You cannot update your role if you are an Admin. You must get another Admin to change your role.',
    })
  }

  const updateUserRes = await updateUser({
    id: requesterId,
    data: {
      firstName,
      lastName,
      email: email?.toLowerCase(),
      role,
      branch,
    },
  })

  if (updateUserRes.isErr()) {
    return errAsync(updateUserRes.error)
  }

  return okAsync('user updated')
}

export function updateUser({
  id,
  data,
}: {
  id: string
  data: Pick<
    Prisma.UserUpdateInput,
    | 'firstName'
    | 'lastName'
    | 'email'
    | 'role'
    | 'branch'
    | 'phoneNbr'
    | 'loginNotification'
  >
}) {
  return fromPromise(
    prisma.user.update({
      where: {
        id,
      },
      data,
    }),
    (error) => {
      logger.error(error)

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        return ERRORS.NOT_FOUND
      }

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function upsertUser({
  id,
  role,
  email,
}: {
  id: string
  role: UserRole
  email: string
}) {
  return fromPromise(
    prisma.user.upsert({
      where: {
        id,
      },
      create: {
        id,
        role,
        email: email.toLowerCase(),
      },
      update: {
        email: email.toLowerCase(),
        role,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    }),
    (error) => {
      logger.warn('failed to upsert user', error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function deleteUserFromDb({ userId }: { userId: string }) {
  return fromPromise(
    prisma.user.delete({
      where: {
        id: userId,
      },
    }),

    (error) => {
      logger.warn(`Failed to delete user with id [${userId}]`, error)
      return errAsync(ERRORS.DATABASE_ERROR)
    }
  )
}

export function getEpayUsers() {
  const epayRoles = Object.values(UserRole).filter((role) =>
    role.startsWith('EPAY_')
  )

  return fromPromise(
    prisma.user.findMany({
      where: {
        role: {
          in: epayRoles,
        },
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      return 'DATABASE_ERROR'
    }
  )
}

export function findUserWithoutOrg({ id }: { id: string }) {
  return fromPromise(
    prisma.user.findFirst({
      where: {
        id,
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to query user [${id}]`)

      return 'DATABASE_ERROR'
    }
  ).andThen((user) => {
    if (!user) {
      return err('NOT_FOUND' as const)
    }

    return ok(user)
  })
}

export async function deleteUserCompletely({
  userId,
  orgId,
}: {
  userId: string
  orgId: string
}) {
  // check user exists in org - you can't delete a user in a different org
  const user = await getOrgUserById({ id: userId, orgId })
  if (user.isErr()) {
    return errAsync(user.error)
  }

  // you can't delete the last org admin user
  if (user.value.role === 'ORG_ADMIN') {
    const roleCount = await getRoleCountForOrg({
      orgId,
      role: UserRole.ORG_ADMIN,
    })
    if (roleCount.isErr()) {
      return errAsync(roleCount.error)
    }

    if (roleCount.value < 2) {
      return errAsync({
        code: ERRORS.ILLEGAL_ACTION.code,
        message: 'Cannot delete the last admin user',
      })
    }
  }

  const deletedUser = await deleteUserFromDb({ userId })
  if (deletedUser.isErr()) {
    return errAsync(deletedUser.error)
  }

  await deleteUserFromAuth0({
    userId: `${AUTH0_USER_ID_PREFIX}${userId}`,
  })

  return okAsync({ result: `Deleted uep user ${userId}` })
}

export function getUserFirstNameLastName({ userId }: { userId: string }) {
  return fromPromise(
    prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        firstName: true,
        lastName: true,
      },
    }),
    (error) => {
      logger.error(error, 'Failed to get user first name and last name')
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((user) => {
    if (!user) {
      return err(ERRORS.NOT_FOUND)
    }
    return ok(user)
  })
}

export function deleteOrgAndUserByUserId({ userId }: { userId: string }) {
  return getUserById({ id: userId }).andThen((user) => {
    if (!user) {
      return errAsync(ERRORS.NOT_FOUND)
    }

    if (user.organization) {
      return deleteUserFromDb({ userId }).andThen(() =>
        //eslint-disable-next-line
        //@ts-ignore
        deleteOrganizationById(user.organization.id!).andThen(() =>
          okAsync({
            userId,
            orgId: user.organization?.id,
          })
        )
      )
    }

    return okAsync(null)
  })
}

export function getOrderReleasedByName({
  isWindcavePayment,
  userId,
}: {
  isWindcavePayment: boolean
  userId: string
}) {
  if (isWindcavePayment) {
    return okAsync('Windcave Payment')
  } else {
    return getUserFirstNameLastName({ userId })
      .map((user) => `${user.firstName} ${user.lastName}`)
      .orElse(() => okAsync('Unknown'))
  }
}
