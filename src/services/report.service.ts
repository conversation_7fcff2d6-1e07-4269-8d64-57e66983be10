import { ReportJob, ReportJobStatus } from '@prisma/client'

import { createObjectCsvStringifier } from 'csv-writer'
import fs from 'fs'
import { errAsync, fromPromise, okAsync, ResultAsync } from 'neverthrow'
import path from 'path'

import {
  createReportJob,
  finalizeReportJob,
  updateReportJobProgress,
  updateReportJobStatus,
} from 'epay-data/epay-report-job'

import { ReportDefinition } from 'types/reports'
import { errorCodes, SimpleError } from 'types/simple-error'

import { getS3SignedUrlGet, s3Put, streamToS3 } from 'utils/aws'
import { AWS_S3_BUCKET_REPORT } from 'utils/config'
import logger from 'utils/logger'
import { ERRORS } from 'utils/error'
import { formatDateDDMMYYYY } from 'helpers/data-helper'

// Constants
const BATCH_SIZE = 1000
const LARGE_FILE_THRESHOLD = 5 * 1024 * 1024 // 5MB

export function createAndProcessReportJob({
  reportDef,
  startDate,
  endDate,
  userId,
  filters,
}: {
  reportDef: ReportDefinition
  startDate: Date
  endDate: Date
  userId: string
  filters?: Record<string, any>
}): ResultAsync<string, SimpleError> {
  return createReportJob({
    reportDef,
    startDate,
    endDate,
    userId,
    filters,
  })
    .andThen((job) => {
      // Start processing in background
      processReport(job.id, reportDef).match(
        () => {
          // Success case - no action needed
        },
        (error) => {
          logger.warn('Failed to process report', { error, jobId: job.id })
        }
      )

      return okAsync(job.id)
    })
    .mapErr((error) => {
      logger.warn('Failed to create report job', error)
      return {
        code: errorCodes.report.CREATE_ERROR,
        message: 'Failed to create report job',
      } as SimpleError
    })
}

export function processReport(
  jobId: string,
  reportDef: ReportDefinition
): ResultAsync<void, SimpleError> {
  logger.info(`processReport... starting for job [${jobId}]`)

  const tempFile = path.join(process.cwd(), 'tmp', `${jobId}.csv`)
  return updateReportJobStatus({
    jobId,
    status: ReportJobStatus.PROCESSING,
  })
    .andThen((job) => {
      if (!job) {
        logger.warn(`processReport... job [${jobId}] not found`)

        return errAsync({
          code: errorCodes.report.NOT_FOUND,
          message: 'Report job not found',
        } as SimpleError)
      }

      // Initialize CSV file with headers
      const headers = reportDef.getHeaders()
      const headerRow = createCsvHeaderString(headers)
      fs.writeFileSync(tempFile, headerRow)

      let totalProcessed = 0
      let fileSize = Buffer.byteLength(headerRow)

      const processBatchesRecursive = (
        page?: number
      ): ResultAsync<void, SimpleError> => {
        return reportDef
          .fetchBatch({
            startDate: job.startDate,
            endDate: job.endDate,
            page,
            batchSize: BATCH_SIZE,
            filters: job.filters ?? {},
          })
          .andThen((batchResult) => {
            return processBatch({
              jobId,
              tempFile,
              fileSize,
              batchResult,
              reportDef,
            }).andThen((newFileSize) => {
              fileSize = newFileSize

              const { nextPage, records, totalCount } = batchResult

              if (!nextPage) {
                return uploadToS3(job, tempFile, fileSize).andThen(() =>
                  cleanup(tempFile)
                )
              }

              if (totalCount) {
                const progress = Math.floor((totalProcessed / totalCount) * 100)
                return updateReportJobProgress({ jobId, progress }).andThen(
                  () => {
                    totalProcessed += records.length
                    return processBatchesRecursive(nextPage)
                  }
                )
              }

              totalProcessed += records.length
              return processBatchesRecursive(nextPage)
            })
          })
      }

      return processBatchesRecursive()
    })
    .mapErr((error) => {
      logger.error(error, `processReport... failed for job [${jobId}]`)
      cleanup(tempFile)
      return updateReportJobStatus({
        jobId,
        status: ReportJobStatus.FAILED,
        error: error.message,
      })
        .map(() => error)
        .mapErr((updateError) => {
          logger.warn('Failed to update job status', updateError)
          return error
        })
        .unwrapOr(error)
    })
}

function processBatch({
  jobId,
  tempFile,
  fileSize,
  batchResult,
  reportDef,
}: {
  jobId: string
  tempFile: string
  fileSize: number
  batchResult: {
    records: any[]
    totalCount?: number
  }
  reportDef: ReportDefinition
}): ResultAsync<number, SimpleError> {
  logger.debug(`processBatch... processing batch for job [${jobId}]`)

  const transformedRecords = batchResult.records.map(reportDef.transformRecord)
  const csvContent = createCsvString(transformedRecords, reportDef.getHeaders())

  return fromPromise(
    new Promise<number>((resolve) => {
      fs.appendFile(tempFile, csvContent, () => {
        resolve(fileSize + Buffer.byteLength(csvContent))
      })
    }),
    (error) => {
      logger.error(
        error,
        `processBatch... failed to write batch for job [${jobId}]`
      )
      return {
        code: 'FILE_WRITE_ERROR',
        message: 'Failed to write batch to file',
      } as SimpleError
    }
  )
}

function uploadToS3(job: ReportJob, tempFile: string, fileSize: number) {
  logger.info(`uploadToS3... uploading file for job [${job.id}]`)

  const startDateStr = formatDateDDMMYYYY(job.startDate)
  const endDateStr = formatDateDDMMYYYY(job.endDate)
  const finalKey = `${job.type}-${startDateStr}-${endDateStr}-${job.id}.csv`
  const fileStream = fs.createReadStream(tempFile)

  if (fileSize < LARGE_FILE_THRESHOLD) {
    // Small file - direct upload
    return s3Put({
      bucket: AWS_S3_BUCKET_REPORT,
      key: finalKey,
      body: fileStream,
      contentType: 'text/csv',
    }).andThen(() => finalizeReportJob({ jobId: job.id, finalKey }))
  }

  // Large file - streaming upload

  return streamToS3({
    bucket: AWS_S3_BUCKET_REPORT,
    key: finalKey,
    contentType: 'text/csv',
    stream: fileStream,
  }).andThen(() => finalizeReportJob({ jobId: job.id, finalKey }))
}

function cleanup(tempFile: string): ResultAsync<void, SimpleError> {
  logger.debug(`cleanup... removing temporary directory [${tempFile}]`)
  try {
    fs.unlinkSync(tempFile)
    return okAsync(undefined)
  } catch (error) {
    logger.warn(
      `cleanup... failed to remove temporary directory [${tempFile}]`,
      error
    )
    return errAsync({
      code: 'CLEANUP_ERROR',
      message: 'Failed to cleanup temporary files',
    })
  }
}

function createCsvHeaderString(header: Array<{ id: string; title: string }>) {
  const stringifier = createObjectCsvStringifier({ header })
  return stringifier.getHeaderString() as string
}

function createCsvString(
  records: Record<string, any>[],
  headers: Array<{ id: string; title: string }>
): string {
  const stringifier = createObjectCsvStringifier({ header: headers })
  return stringifier.stringifyRecords(records)
}

export function getSignedUrlForReport(key: string) {
  return fromPromise(
    getS3SignedUrlGet({ key, bucket: AWS_S3_BUCKET_REPORT }),
    (error) => {
      logger.warn(`failed to get signed url for report [${key}]`, error)
      return ERRORS.S3_SIGNED
    }
  )
}
