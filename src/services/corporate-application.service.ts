import type {
  Beneficial<PERSON><PERSON><PERSON>,
  CorporateApplication,
  Director,
  OrganizationType,
} from '@prisma/client'
import type {
  ZCorporateApplication,
  ZCorporateApplicationOptional,
  ZOrganizationTypeOther,
} from 'routes/corporate-application.route'

import { v4 as uuid } from 'uuid'

import { ERRORS } from 'utils/error'
import * as logger from 'utils/logger'
import { prisma } from 'utils/prisma'

import { CorporateApplicationStatus, KycStatus, Prisma } from '@prisma/client'

import {
  err,
  errAsync,
  fromPromise,
  ok,
  okAsync,
  ResultAsync,
} from 'neverthrow'

import {
  deleteBeneficialOwner as deleteBeneficialOwnerModel,
  upsertBeneficialOwners,
} from '../models/beneficial-owner'
import {
  createCorporateApplication as createCorporateApplicationModel,
  findCorporateApplication,
  findManyCorporateApplication,
  updateCorporateApplication,
} from '../models/corporate-application'
import {
  deleteDirector as deleteDirectorModel,
  upsertDirectors,
} from '../models/director'
import {
  findUserWithCorporateApplication,
  findUserWithCorporateApplicationAndDirectorOrBeneficialOwner,
  findUserWithCorporateApplicationAndDirectorsOrBeneficialOwners,
} from '../models/user'
import { updateUser } from './user.service'

import { epayEmails } from 'helpers/email-templates'
import { getS3SignedUrlPut } from 'utils/aws'
import {
  EPAY_KYC_ALTERNATIVE_EMAIL,
  EPAY_KYC_EMAIL,
  EPAY_SUPPORT_EMAIL,
  IS_PROD,
} from 'utils/config'
import { infoLogSessionReference } from 'utils/infolog'
import {
  PepCheckData,
  verifyIdentities,
  verifyOrganizationEntity,
} from './infolog.service'
import {
  checkOrgAlreadyExists,
  createSimpleOrganization,
  generateCustomerCodeForOrg,
  updateCustomerCodeForOrg,
} from './organization.service'
import { sendEmail } from './postmark.service'

export function createCorporateApplication(userId: string) {
  return createCorporateApplicationModel(userId)
}

function createOrgWithCustomerCode({
  corporateApplication,
}: {
  corporateApplication: Omit<
    CorporateApplication,
    | 'createdAt'
    | 'updatedAt'
    | 'corporateOrganizationId'
    | 'status'
    | 'submittedAt'
  >
}) {
  return createOrganisationFromApprovedApplication({
    corporateApplication,
  }).andThen((organization) => {
    if (!organization) {
      return err(ERRORS.DATABASE_ERROR)
    }
    return generateCustomerCodeForOrg(organization.id).andThen(
      (customerCode) => {
        if (!customerCode) {
          return err(ERRORS.DATABASE_ERROR)
        }
        return updateCustomerCodeForOrg(customerCode).map(() => {
          return organization
        })
      }
    )
  })
}

function createOrganisationFromApprovedApplication({
  corporateApplication,
}: {
  corporateApplication: Omit<
    CorporateApplication,
    | 'createdAt'
    | 'updatedAt'
    | 'corporateOrganizationId'
    | 'status'
    | 'submittedAt'
  >
}) {
  //we check in the higher level if the name and nzbn are present

  return fromPromise(
    prisma.organization.create({
      data: {
        name: corporateApplication.name!,
        nzbn: corporateApplication.nzbn!,
        type: corporateApplication.organizationType,
        tradingName: corporateApplication.tradingName,
        address: corporateApplication.address,
        city: corporateApplication.city,
        suburb: corporateApplication.suburb,
        zip: corporateApplication.zip,
        country: corporateApplication.country,
        principalPlace: corporateApplication.principalPlace,
        natureAndPurpose: corporateApplication.natureAndPurpose,
        phone: corporateApplication.phone,

        cPartnerIds: corporateApplication.cPartnerUserId
          ? [corporateApplication.cPartnerUserId]
          : [],

        users: {
          connect: {
            id: corporateApplication.userId,
          },
        },

        corporateApplications: {
          connect: {
            id: corporateApplication.id,
          },
        },
      },
      include: {
        users: true,
      },
    }),
    (error) => {
      logger.error(error)

      return ERRORS.DATABASE_ERROR
    }
  ).andThen((organization) => {
    if (!organization) {
      return err(ERRORS.DATABASE_ERROR)
    }

    return ok(organization)
  })
}

export function createOrganisationAndWelcomeUser({
  corporateApplication,
}: {
  corporateApplication: Omit<
    CorporateApplication,
    | 'createdAt'
    | 'updatedAt'
    | 'corporateOrganizationId'
    | 'status'
    | 'submittedAt'
  >
}) {
  logger.debug('creating corporate organization from corporate application', {
    corporateApplication,
  })
  if (!corporateApplication.name || !corporateApplication.nzbn) {
    return err(ERRORS.MISSING_REQUIRED_FIELD)
  }

  return createOrgWithCustomerCode({
    corporateApplication,
  }).andThen((organization) => {
    logger.info('Organization created, setting APPLICATION CELEBRATION', {
      organization,
    })

    return setApplicationCelebration(corporateApplication.userId).map(() => {
      return organization
    })
  })
}

function setApplicationCelebration(userId: string) {
  return fromPromise(
    prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        loginNotification: 'APPLICATION_CELEBRATION',
      },
    }),
    (error) => {
      logger.warn('Failed to update user login notification', error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

export async function getCorporateApplication({
  userId,
  corporateApplicationId,
}: {
  userId: string
  corporateApplicationId: string
}) {
  return findCorporateApplication({ userId, corporateApplicationId })
}

export async function listCorporateApplication({ userId }: { userId: string }) {
  return findManyCorporateApplication({ userId })
}

export function updateApplicationWithCompanyDetails({
  id,
  userId,
  data,
}: {
  id: string
  userId: string
  data: Prisma.CorporateApplicationUpdateInput
}) {
  return findUserWithCorporateApplication({
    userId,
    corporateApplicationId: id,
  }).andThen((user) => {
    if (!user) {
      logger.warn('Failed to find user with corporate application', {
        userId,
        id,
      })
      return err(ERRORS.NOT_FOUND)
    }

    return updateCorporateApplication({ id, data })
  })
}

export function updateApplicant(params: {
  id: string
  data: Pick<
    Prisma.UserUpdateInput,
    'firstName' | 'lastName' | 'phoneNbr' | 'branch'
  >
}) {
  logger.info('updating applicant', params)

  return updateUser(params)
}

export function updateDirectors({
  corporateApplicationId,
  userId,
  directors,
}: {
  corporateApplicationId: string
  userId: string
  directors: Partial<Director>[]
}) {
  logger.info('updating directors', {
    corporateApplicationId,
    userId,
    directors,
  })

  const ids = directors
    .filter((director) => {
      return Boolean(director.id)
    })
    .map((director) => {
      return director.id as string
    })

  return findUserWithCorporateApplicationAndDirectorsOrBeneficialOwners({
    userId,
    corporateApplicationId,
    ids,
    idsOf: 'directors',
  }).andThen((user) => {
    if (!user) {
      logger.warn('User not found with corporate application', { userId })
      return err('NOT_FOUND' as const)
    }

    return upsertDirectors({ corporateApplicationId, directors })
  })
}

export async function deleteDirector({
  userId,
  corporateApplicationId,
  directorId,
}: {
  userId: string
  corporateApplicationId: string
  directorId: string
}) {
  const userRes =
    await findUserWithCorporateApplicationAndDirectorOrBeneficialOwner({
      userId,
      corporateApplicationId,
      id: directorId,
      idOf: 'directors',
    })

  if (userRes.isErr()) {
    return err(userRes.error)
  }

  if (userRes.value === null) {
    return err('NOT_FOUND' as const)
  }

  return deleteDirectorModel(directorId)
}

export function updateBeneficialOwners({
  corporateApplicationId,
  userId,
  beneficialOwners,
  hasNoIndividualBeneficialOwner,
}: {
  corporateApplicationId: string
  userId: string
  beneficialOwners?: Partial<BeneficialOwner>[]
  hasNoIndividualBeneficialOwner?: boolean
}) {
  logger.debug('updating beneficial owners', {
    corpId: corporateApplicationId,
    userID: userId,
    beneficialOwners,
    hasNoIndividualBeneficialOwner,
  })

  if (hasNoIndividualBeneficialOwner) {
    return findUserWithCorporateApplication({
      userId,
      corporateApplicationId,
    }).andThen((value) => {
      if (value === null) {
        logger.warn('User not found with corporate application', { userId })
        return err(ERRORS.NOT_FOUND)
      }

      return updateHasNoIndividualBeneficialOwnerFlag({
        corporateApplicationId,
        hasNoIndividualBeneficialOwner,
      })
    })
  } else {
    const ids =
      beneficialOwners
        ?.filter((beneficialOwner) => Boolean(beneficialOwner.id))
        .map((beneficialOwner) => beneficialOwner.id as string) || []

    return findUserWithCorporateApplicationAndDirectorsOrBeneficialOwners({
      userId,
      corporateApplicationId,
      ids,
      idsOf: 'beneficialOwners',
    }).andThen(() => {
      return upsertBeneficialOwners({
        corporateApplicationId,
        beneficialOwners: beneficialOwners || [],
      })
    })
  }
}

export async function getBeneficialOwnerPhotoSignedUrl({
  userId,
  corporateApplicationId,
  contentType,
  fileExtension,
}: {
  userId: string
  corporateApplicationId: string
  contentType: string
  fileExtension: string
}) {
  const userRes = await findUserWithCorporateApplication({
    userId,
    corporateApplicationId,
  })

  if (userRes.isErr()) {
    return errAsync(userRes.error)
  }

  const transformName = `beneficial-owner-${userRes.value?.corporateApplications?.name?.replace(
    /\s+/g,
    '-'
  )}-`

  const imageId = transformName + uuid()

  return getS3SignedUrlPut({
    contentType,
    fileExtension,
    id: imageId!,
  })
}

export async function deleteBeneficialOwner({
  userId,
  corporateApplicationId,
  beneficialOwnerId,
}: {
  userId: string
  corporateApplicationId: string
  beneficialOwnerId: string
}) {
  const userRes =
    await findUserWithCorporateApplicationAndDirectorOrBeneficialOwner({
      userId,
      corporateApplicationId,
      id: beneficialOwnerId,
      idOf: 'beneficialOwners',
    })

  if (userRes.isErr()) {
    return err(userRes.error)
  }

  if (userRes.value === null) {
    return err(ERRORS.NOT_FOUND)
  }

  return deleteBeneficialOwnerModel(beneficialOwnerId)
}

export function save({
  userId,
  corporateApplicationId,
  corporateApplication,
  kycCompletionPreference,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplicationOptional
  kycCompletionPreference?: 'FINISH_LATER'
}) {
  logger.info('Initiating save process for corporate application', {
    userId,
    corporateApplicationId,
    kycCompletionPreference,
    organizationType: corporateApplication.organization.organizationType,
  })

  const applicationStatus =
    kycCompletionPreference === 'FINISH_LATER'
      ? CorporateApplicationStatus.PRE_APPROVED
      : corporateApplication.applicationStatus

  logger.debug('Determined application status', { applicationStatus })

  return saveApplication({
    userId,
    corporateApplicationId,
    corporateApplication,
    applicationStatus,
    submittedAt: new Date(),
  })
    .andThen((application) => {
      if (
        application?.organization?.id ||
        applicationStatus !== CorporateApplicationStatus.PRE_APPROVED
      ) {
        logger.info('Application saved successfully', {
          applicationStatus,
          hasOrganization: !!application?.organization?.id,
        })
        if (applicationStatus === CorporateApplicationStatus.REQUIRES_INFO) {
          sendEmail({
            to: epayEmails,
            subject: `Application updated for ${application?.organization?.name}`,
            html: `<div><h1>The application for ${application?.organization?.name} has been updated</h1><p>Application status: ${applicationStatus}</p><p>Organization NZBN: ${application.organization?.nzbn}</p></div>`,
          })
        }

        return okAsync({ applicationStatus })
      }

      logger.info(
        'Proceeding to create organization for pre-approved application',
        {
          corporateApplicationId,
        }
      )
      return createOrganizationForPreApprovedApplication({
        userId,
        corporateApplicationId,
        corporateApplication,
      })
    })
    .mapErr((error) => {
      logger.warn('Failed to save application', {
        error,
        userId,
        corporateApplicationId,
        applicationStatus,
      })
      return error
    })
}

function createOrganizationForPreApprovedApplication({
  userId,
  corporateApplicationId,
  corporateApplication,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplicationOptional
}) {
  logger.info('Creating organization for pre-approved application', {
    userId,
    corporateApplicationId,
    organizationType: corporateApplication.organization.organizationType,
  })

  const { name, nzbn, organizationType, ...restOfOrganization } =
    corporateApplication.organization
  const orgData: Prisma.OrganizationCreateInput = {
    name: name || '',
    nzbn: nzbn || '',
    type: organizationType as OrganizationType,
    ...Object.fromEntries(
      Object.entries(restOfOrganization).filter(([_, value]) => value != null)
    ),
  }

  logger.debug('Prepared organization data for creation', {
    name: orgData.name,
    nzbn: orgData.nzbn,
    type: orgData.type,
  })

  return createSimpleOrganization({
    userId,
    applicationId: corporateApplicationId,
    orgData,
  })
    .andThen((organisation) => {
      if (!organisation) {
        logger.warn('Organization creation returned null', {
          corporateApplicationId,
        })
        return errAsync({
          code: ERRORS.DATABASE_ERROR.code,
          message: 'Organization creation failed',
        })
      }
      logger.info(
        'Organization created successfully for pre-approved application',
        {
          corporateApplicationId,
          organizationId: organisation.id,
        }
      )

      sendOrganizationCreatedEmail({
        name: organisation.name,
        nzbn: organisation.nzbn,
        email: organisation.users[0].email,
      })

      return okAsync({
        applicationStatus: CorporateApplicationStatus.PRE_APPROVED,
      })
    })
    .mapErr((error) => {
      logger.warn(
        'Failed to create organization for pre-approved application',
        {
          error,
          userId,
          corporateApplicationId,
        }
      )
      return error
    })
}

function saveApplication({
  userId,
  corporateApplicationId,
  corporateApplication,
  applicationStatus,
  submittedAt,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplicationOptional
  applicationStatus?: CorporateApplicationStatus
  submittedAt?: Date
}) {
  return findUserWithCorporateApplication({
    userId,
    corporateApplicationId,
  })
    .andThen((user) => {
      if (!user) {
        return errAsync(ERRORS.NOT_FOUND)
      }
      return okAsync(user)
    })
    .andThen(() =>
      updateDirectors({
        corporateApplicationId,
        userId,
        directors: corporateApplication.directors,
      })
    )
    .andThen(() =>
      updateApplicant({
        id: userId,
        data: corporateApplication.applicant,
      })
    )
    .andThen(() => {
      if (
        corporateApplication.beneficialOwners.hasNoIndividualBeneficialOwner !==
        undefined
      ) {
        return updateBeneficialOwners({
          corporateApplicationId,
          userId,
          hasNoIndividualBeneficialOwner:
            corporateApplication.beneficialOwners
              .hasNoIndividualBeneficialOwner,
        })
      } else {
        return updateBeneficialOwners({
          corporateApplicationId,
          userId,
          beneficialOwners:
            corporateApplication.beneficialOwners.beneficialOwners,
        })
      }
    })
    .andThen(() =>
      updateCorporateApplication({
        id: corporateApplicationId,
        data: {
          ...corporateApplication.organization,
          status: applicationStatus,
          submittedAt: submittedAt ?? null,
        },
      })
    )
    .andThen((organization) => {
      if (!organization) {
        return errAsync(ERRORS.DATABASE_ERROR)
      }

      return okAsync(organization)
    })
    .mapErr((error) => {
      logger.warn('Error saving corporate application', error)
      return error
    })
}

export function sendOrganizationCreatedEmail({
  name,
  nzbn,
  email,
}: {
  name: string
  nzbn: string
  email: string
}) {
  sendEmail({
    to: EPAY_SUPPORT_EMAIL,
    subject: `Organization created: ${name}`,
    html: `<div><h1>Organization created: ${name}</h1><p>NZBN: ${nzbn}</p><p>Contact email: ${email}</p></div>`,
  })
}

function addNoteToApplication({
  corporateApplicationId,
  title,
  message,
}: {
  corporateApplicationId: string
  title?: string
  message: string
}) {
  return fromPromise(
    prisma.corporateApplication.update({
      where: {
        id: corporateApplicationId,
      },
      data: {
        notes: {
          create: {
            message,
            title,
          },
        },
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

type InvalidIdentity = {
  valid: boolean
  unmatchedFields: string[]
  infoLogErrors: string[]
  name?: string
}

function formatInvalidIdentitiesForNote(
  invalidIdentities: InvalidIdentity[]
): string {
  const filteredIdentities = invalidIdentities.filter(
    (identity) =>
      !identity.valid &&
      (identity.unmatchedFields.length > 0 || identity.infoLogErrors.length > 0)
  )

  if (filteredIdentities.length === 0) {
    return ''
  }

  return filteredIdentities
    .map((identity) => {
      const nameContent = `Name: ${identity.name}`
      const unmatchedFieldsContent =
        identity.unmatchedFields.length > 0
          ? `Unmatched Fields: ${identity.unmatchedFields.join(', ')}`
          : ''
      const infoLogErrorsContent =
        identity.infoLogErrors.length > 0
          ? `InfoLog Errors: ${identity.infoLogErrors.join(', ')}`
          : ''

      return [nameContent, unmatchedFieldsContent, infoLogErrorsContent]
        .filter(Boolean)
        .join('\n')
    })
    .join('\n\n')
}

function updateHasNoIndividualBeneficialOwnerFlag({
  corporateApplicationId,
  hasNoIndividualBeneficialOwner,
}: {
  corporateApplicationId: string
  hasNoIndividualBeneficialOwner: boolean
}) {
  return fromPromise(
    prisma.corporateApplication.update({
      where: { id: corporateApplicationId },
      data: { hasNoIndividualBeneficialOwner },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  ).map(() => {
    return { hasNoIndividualBeneficialOwner: true }
  })
}

export function updateApplicationForOtherOrgTypeThenUpdateUserThenCreateOrg({
  userId,
  data,
  applicationId,
}: {
  userId: string
  applicationId: string
  data: Omit<ZOrganizationTypeOther, 'id'>
}) {
  logger.info(
    'updating application for other org type then creating user and organization',
    data
  )

  const proceedWithUpdate = () => {
    return updateApplicationWithCompanyDetails({
      userId,
      id: applicationId,
      data: {
        nzbn: data.nzbn ?? 'no registered nzbn',
        name: data.name,
        tradingName: data.tradingName,
        status: CorporateApplicationStatus.APPROVED,
        kycStatus: KycStatus.NKYC,
        organizationType: 'OTHER',
        submittedAt: new Date(),
      },
    })
      .andThen((application) => {
        if (!application || !application.name || !application.nzbn) {
          logger.warn(
            'Failed to update corporate application with id: ',
            applicationId
          )
          return err(ERRORS.DATABASE_ERROR)
        }
        return createSimpleOrganization({
          userId,
          applicationId,
          orgData: {
            tradingName: application.tradingName,
            name: application.name,
            nzbn: application.nzbn,
            type: 'OTHER',
          },
        })
      })
      .andThen(() => {
        return updateUser({
          id: userId,
          data: {
            firstName: data.firstName,
            lastName: data.lastName,
            phoneNbr: data.phoneNbr,
          },
        })
      })
      .andThen((user) => {
        if (!user) {
          logger.warn('Failed to update user with id: ', userId)
          return err(ERRORS.DATABASE_ERROR)
        }
        const epayEmails = `${EPAY_KYC_EMAIL}, ${EPAY_KYC_ALTERNATIVE_EMAIL}`
        logger.debug('Sending email to epay', epayEmails)
        sendOrganizationCreatedEmail({
          name: data.name,
          nzbn: data.nzbn ?? 'no registered nzbn',
          email: user.email,
        })
        sendEmail({
          subject: 'Manual KYC required for other organization type',
          to: epayEmails,
          html: `<div><h1>Manual KYC required for other organization type</h1><p>Organization name: ${
            data.name
          }</p><p>Organization NZBN: ${
            data.nzbn ?? 'No registered NZBN'
          }</p><p>Contact name: ${data.firstName} ${
            data.lastName
          }</p><p>Contact email: ${user.email}</p><p>Contact phone number: ${
            data.phoneNbr
          }</p></div>`,
        })
        return ok(null)
      })
  }

  if (data.nzbn && data.nzbn.length > 0) {
    logger.debug('Checking if organization already exists', data.nzbn)

    return checkOrgAlreadyExists({ nzbn: data.nzbn }).andThen(({ exists }) => {
      if (exists) {
        return err(ERRORS.ORGANIZATION_ALREADY_EXISTS)
      }
      return proceedWithUpdate()
    })
  } else {
    return proceedWithUpdate()
  }
}

export function findInfologSessionId({
  corporateApplicationId,
  userId,
}: {
  corporateApplicationId: string
  userId: string
}) {
  return findCorporateApplication({
    userId: userId,
    corporateApplicationId,
  }).andThen((application) => {
    if (!application) {
      return errAsync(ERRORS.NOT_FOUND)
    }

    return okAsync({ infologSessionId: application.infologSessionId })
  })
}

export function submit_V2({
  userId,
  corporateApplicationId,
  corporateApplication,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplication
}) {
  logger.info(
    `Starting submit process for application ${corporateApplicationId}`,
    { userId, corporateApplicationId }
  )
  return findUserWithCorporateApplication({
    userId,
    corporateApplicationId,
  }).andThen((user) => {
    if (!user) {
      logger.warn(`User not found for application ${corporateApplicationId}`, {
        userId,
      })
      return errAsync(ERRORS.NOT_FOUND)
    }

    logger.info(`User found, proceeding with application submission`, {
      userId,
      corporateApplicationId,
    })
    return saveApplicationForSubmitFunction({
      userId,
      corporateApplicationId,
      corporateApplication,
    })
      .andThen((application) => {
        logger.info(
          `Processing beneficial owners for application ${corporateApplicationId}`,
          { beneficialOwnersCount: application.beneficialOwners.length }
        )
        return processBeneficialOwners({
          applicationId: corporateApplicationId,
          beneficialOwners: application.beneficialOwners,
          organizationName: corporateApplication.organization.name,
          nzbn: corporateApplication.organization.nzbn,
        })
      })
      .andThen((application) => {
        if (
          application.kycStatus === KycStatus.FKYC ||
          application.kycStatus === KycStatus.NKYC
        ) {
          logger.info(
            `Sending email to Ross for application ${corporateApplicationId}`,
            { kycStatus: application.kycStatus }
          )
          sendEmailToRoss({
            subjectMessage:
              application.kycStatus === KycStatus.FKYC
                ? 'Application failed to pass KYC'
                : 'Application needs manual KYC',
            organizationName: application.name!,
            nzbn: application.nzbn!,
            email: application.user.email,
          })
        }

        logger.info(
          `Creating organisation and welcoming user for application ${corporateApplicationId}`
        )
        return createOrganisationAndWelcomeUser({
          corporateApplication: application,
        })
      })
  })
}

function saveApplicationForSubmitFunction({
  userId,
  corporateApplicationId,
  corporateApplication,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplication
}) {
  return updateApplicant({
    id: userId,
    data: corporateApplication.applicant,
  })
    .andThen(() =>
      updateDirectors({
        corporateApplicationId,
        userId: userId,
        directors: corporateApplication.directors,
      })
    )
    .andThen(() =>
      updateBeneficialOwners({
        corporateApplicationId,
        userId: userId,
        beneficialOwners:
          corporateApplication.beneficialOwners.beneficialOwners,
      })
    )
    .andThen(() =>
      updateCorporateApplication({
        id: corporateApplicationId,
        data: {
          ...corporateApplication.organization,
          status: CorporateApplicationStatus.DRAFT,
          submittedAt: new Date(),
        },
      })
    )
}

function processBeneficialOwners({
  applicationId,
  beneficialOwners,
  organizationName,
  nzbn,
}: {
  applicationId: string
  beneficialOwners: BeneficialOwner[]
  organizationName: string
  nzbn: string
}) {
  logger.info(`Processing beneficial owners for application ${applicationId}`, {
    beneficialOwnersCount: beneficialOwners.length,
  })
  const individualBeneficialOwners = beneficialOwners.filter(
    (owner) => owner.beneficialOwnerType === 'INDIVIDUAL'
  )
  const entityBeneficialOwners = beneficialOwners.filter(
    (owner) => owner.beneficialOwnerType === 'ENTITY'
  )
  const hasIndividualBeneficialOwners = individualBeneficialOwners.length > 0
  const hasEntityBeneficialOwners = entityBeneficialOwners.length > 0

  logger.debug(`Beneficial owners breakdown for application ${applicationId}`, {
    individualCount: individualBeneficialOwners.length,
    entityCount: entityBeneficialOwners.length,
  })

  if (!hasIndividualBeneficialOwners) {
    logger.info(
      `No individual beneficial owners for application ${applicationId}, setting NKYC status`
    )
    return setApplicationAndKycStatus({
      applicationId,
      kycStatus: KycStatus.NKYC,
      status: CorporateApplicationStatus.PRE_APPROVED,
    })
  }

  if (!IS_PROD) {
    logger.info(
      `Skipping verifyIdentities check in non-production environment for application ${applicationId}`
    )
    return setApplicationAndKycStatus({
      applicationId,
      kycStatus: hasEntityBeneficialOwners ? KycStatus.NKYC : KycStatus.DD,
      status: hasEntityBeneficialOwners
        ? CorporateApplicationStatus.PRE_APPROVED
        : CorporateApplicationStatus.APPROVED,
    })
  }

  logger.info(`Verifying identities for application ${applicationId}`)
  return verifyIdentities({
    SessionReference: infoLogSessionReference(organizationName, nzbn),
    beneficialOwners: mapBeneficialOwnersForPepCheck(
      individualBeneficialOwners
    ),
  }).andThen(({ valid, invalidIdentities }) => {
    if (!valid) {
      logger.warn(`Invalid identities found for application ${applicationId}`, {
        invalidIdentitiesCount: invalidIdentities.length,
      })
      const noteContent = formatInvalidIdentitiesForNote(invalidIdentities)
      return addNoteToApplication({
        corporateApplicationId: applicationId,
        message: noteContent,
        title: 'Invalid identities',
      }).andThen(() => {
        logger.info(
          `Setting FKYC status for application ${applicationId} due to invalid identities`
        )
        return setApplicationAndKycStatus({
          applicationId,
          kycStatus: KycStatus.FKYC,
          status: CorporateApplicationStatus.PRE_APPROVED,
        })
      })
    }

    logger.info(
      `All identities valid for application ${applicationId}, setting final status`
    )
    return setApplicationAndKycStatus({
      applicationId,
      kycStatus: hasEntityBeneficialOwners ? KycStatus.NKYC : KycStatus.DD,
      status: hasEntityBeneficialOwners
        ? CorporateApplicationStatus.PRE_APPROVED
        : CorporateApplicationStatus.APPROVED,
    })
  })
}

function mapBeneficialOwnersForPepCheck(
  owners: BeneficialOwner[]
): PepCheckData[] {
  return owners.map((owner) => ({
    firstName: owner.firstName!,
    lastName: owner.lastName!,
    dateOfBirth: owner.dateOfBirth!,
    driverLicenseVersion: owner.driverLicenseVersion ?? undefined,
    identificationNbr: owner.identificationNbr!,
    identificationType: owner.identificationType!,
    passportExpiryDate: owner.passportExpiryDate ?? undefined,
  }))
}

function setApplicationAndKycStatus({
  applicationId,
  kycStatus,
  status,
}: {
  applicationId: string
  kycStatus: KycStatus
  status: CorporateApplicationStatus
}) {
  return fromPromise(
    prisma.corporateApplication.update({
      where: {
        id: applicationId,
      },
      data: {
        kycStatus,
        status,
      },
      include: {
        user: true,
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((application) => {
    if (!application) {
      return err(ERRORS.DATABASE_ERROR)
    }
    return ok(application)
  })
}

function sendEmailToRoss({
  organizationName,
  nzbn,
  email,
  subjectMessage,
}: {
  organizationName: string
  nzbn: string
  email: string
  subjectMessage: string
}) {
  sendEmail({
    to: EPAY_SUPPORT_EMAIL,
    subject: `${subjectMessage} for ${organizationName}`,
    html: `<div><h1>${subjectMessage} for ${organizationName}</h1><p>Organization name: ${organizationName}</p><p>Organization NZBN: ${nzbn}</p><p>Contact email: ${email}</p></div>`,
  })
}

function generateEmailContent({
  corporateApplication,
  invalidIdentitiesContent,
  beneficialEntitiesContent,
  noIndividualBeneficialOwnerContent,
}: {
  corporateApplication: ZCorporateApplication
  invalidIdentitiesContent: string
  beneficialEntitiesContent: string
  noIndividualBeneficialOwnerContent: string
}): string {
  return `
    <div>
      <h1>Application for: ${corporateApplication.organization.name}</h1>
      <h3>Infolog Session Reference: ${infoLogSessionReference(
        corporateApplication.organization.name,
        corporateApplication.organization.nzbn
      )}</h3>
      ${invalidIdentitiesContent}
      ${beneficialEntitiesContent}
      ${noIndividualBeneficialOwnerContent}
    </div>
  `
}

function generateNoteContent({
  invalidIdentitiesContent,
  beneficialEntitiesContent,
  noIndividualBeneficialOwnerContent,
}: {
  invalidIdentitiesContent: string
  beneficialEntitiesContent: string
  noIndividualBeneficialOwnerContent: string
}): { title: string; message: string } {
  let noteTitle = ''
  let noteMessage = ''

  if (noIndividualBeneficialOwnerContent) {
    noteTitle += 'No individual beneficial owner'
    noteMessage += noIndividualBeneficialOwnerContent
  }

  if (invalidIdentitiesContent) {
    if (noteTitle) noteTitle += ' and '
    noteTitle += 'Invalid identities'
    noteMessage += `Invalid identities:\n${invalidIdentitiesContent}`
  }

  if (beneficialEntitiesContent) {
    if (noteTitle) noteTitle += ' and '
    noteTitle += 'Beneficial entities'
    if (noteMessage) noteMessage += '\n\n'
    noteMessage += beneficialEntitiesContent
  }

  return { title: noteTitle, message: noteMessage }
}

function handlePendingStatus({
  corporateApplicationId,
  corporateApplication,
  invalidIdentities,
  beneficialEntities,
  hasNoIndividualBeneficialOwner,
}: {
  corporateApplicationId: string
  corporateApplication: ZCorporateApplication
  invalidIdentities: InvalidIdentity[]
  beneficialEntities: string[]
  hasNoIndividualBeneficialOwner: boolean
}) {
  const invalidIdentitiesContent =
    generateInvalidIdentitiesContent(invalidIdentities)
  const beneficialEntitiesContent =
    generateBeneficialEntitiesContent(beneficialEntities)
  const noIndividualBeneficialOwnerContent =
    formatNoIndividualBeneficialOwnerForNote(hasNoIndividualBeneficialOwner)

  const emailHtml = generateEmailContent({
    corporateApplication,
    invalidIdentitiesContent,
    beneficialEntitiesContent,
    noIndividualBeneficialOwnerContent,
  })
  const epayEmails = `${EPAY_KYC_EMAIL}, ${EPAY_KYC_ALTERNATIVE_EMAIL}`

  sendEmail({
    to: epayEmails,
    subject: `Pending corporate application for ${corporateApplication.organization.name}`,
    html: emailHtml,
  })

  const { title, message } = generateNoteContent({
    invalidIdentitiesContent,
    beneficialEntitiesContent,
    noIndividualBeneficialOwnerContent,
  })

  addNoteToApplication({
    corporateApplicationId,
    title,
    message,
  })
}

function handleUpdateResults({
  userId,
  corporateApplicationId,
  corporateApplication,
  hasNoIndividualBeneficialOwner,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplication
  hasNoIndividualBeneficialOwner: boolean
}) {
  let updateBeneficialOwnersResult

  if (hasNoIndividualBeneficialOwner) {
    updateBeneficialOwnersResult = updateBeneficialOwners({
      userId,
      corporateApplicationId,
      hasNoIndividualBeneficialOwner,
    })
  } else {
    updateBeneficialOwnersResult = updateBeneficialOwners({
      corporateApplicationId,
      userId,
      beneficialOwners: corporateApplication.beneficialOwners.beneficialOwners,
    })
  }

  const updateDirectorsResult = updateDirectors({
    corporateApplicationId,
    userId,
    directors: corporateApplication.directors,
  })

  const updateApplicantResult = updateApplicant({
    id: userId,
    data: corporateApplication.applicant,
  })

  return ResultAsync.combine([
    updateBeneficialOwnersResult,
    updateDirectorsResult,
    updateApplicantResult,
  ])
}

export function submit({
  userId,
  corporateApplicationId,
  corporateApplication,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplication
}) {
  logger.info('submitting corporate application')

  return findUserWithCorporateApplication({
    userId,
    corporateApplicationId,
  })
    .andThen((user) => {
      if (!user) {
        return errAsync(ERRORS.NOT_FOUND)
      }

      return verifyOrganizationEntity({
        infologSessionId: user?.corporateApplications?.infologSessionId ?? '',
        entity: corporateApplication,
      })
    })
    .andThen((entityVerificationResult) =>
      handleEntityVerificationResult({
        corporateApplication,
        entityVerificationResult,
      })
    )
    .andThen((validationResult) => {
      return handleValidationResult({
        userId,
        corporateApplicationId,
        corporateApplication,
        validationResult,
      })
    })
    .mapErr((error) => error)
}

function handleEntityVerificationResult({
  corporateApplication,
  entityVerificationResult,
}: {
  corporateApplication: ZCorporateApplication
  entityVerificationResult: {
    beneficialEntities: string[]
    hasNoIndividualBeneficialOwner: boolean
  }
}) {
  const { beneficialEntities, hasNoIndividualBeneficialOwner } =
    entityVerificationResult

  if (beneficialEntities.length === 0 && !hasNoIndividualBeneficialOwner) {
    if (IS_PROD) {
      return verifyIdentities({
        SessionReference: infoLogSessionReference(
          corporateApplication.organization.name,
          corporateApplication.organization.nzbn
        ),
        beneficialOwners:
          corporateApplication.beneficialOwners.beneficialOwners!,
      }).map(({ valid, invalidIdentities }) => ({
        valid,
        invalidIdentities,
        beneficialEntities,
        hasNoIndividualBeneficialOwner,
      }))
    } else {
      logger.info('Skipping infolog identity check in non-prod environment')
      return okAsync({
        valid: true,
        invalidIdentities: [],
        beneficialEntities,
        hasNoIndividualBeneficialOwner,
      })
    }
  }
  return okAsync({
    valid: false,
    invalidIdentities: [],
    beneficialEntities,
    hasNoIndividualBeneficialOwner,
  })
}

function handleValidationResult({
  userId,
  corporateApplicationId,
  corporateApplication,
  validationResult,
}: {
  userId: string
  corporateApplicationId: string
  corporateApplication: ZCorporateApplication
  validationResult: {
    valid: boolean
    invalidIdentities: InvalidIdentity[]
    beneficialEntities: string[]
    hasNoIndividualBeneficialOwner: boolean
  }
}) {
  const {
    valid,
    invalidIdentities,
    beneficialEntities,
    hasNoIndividualBeneficialOwner,
  } = validationResult
  let status: 'PENDING' | 'APPROVED' | undefined

  logger.info('Handling validation result', validationResult)

  if (valid) {
    status = 'APPROVED'
  } else {
    handlePendingStatus({
      corporateApplicationId,
      corporateApplication,
      invalidIdentities,
      beneficialEntities,
      hasNoIndividualBeneficialOwner,
    })
    status = 'PENDING'
  }

  return handleUpdateResults({
    userId,
    corporateApplicationId,
    corporateApplication,
    hasNoIndividualBeneficialOwner,
  }).andThen(() => {
    return updateCorporateApplication({
      id: corporateApplicationId,
      data: {
        status,
        kycStatus: status === 'APPROVED' ? KycStatus.DD : KycStatus.NKYC,
        submittedAt: new Date(),
        ...corporateApplication.organization,
      },
    }).andThen((corporateApplication) => {
      if (corporateApplication.status === 'APPROVED') {
        sendOrganizationCreatedEmail({
          name: corporateApplication.name!,
          nzbn: corporateApplication.nzbn!,
          email: corporateApplication.user.email,
        })

        return createOrganisationAndWelcomeUser({
          corporateApplication,
        })
          .map(() => ({ applicationStatus: corporateApplication.status }))
          .mapErr((error) => ({ error, data: {} }))
      }

      return okAsync({ applicationStatus: corporateApplication.status })
    })
  })
}

function generateInvalidIdentitiesContent(
  invalidIdentities: InvalidIdentity[]
): string {
  const filteredIdentities = invalidIdentities.filter(
    (identity) =>
      !identity.valid &&
      (identity.unmatchedFields.length > 0 || identity.infoLogErrors.length > 0)
  )

  if (filteredIdentities.length === 0) {
    return ''
  }

  const identitiesContent = filteredIdentities
    .map(
      (identity) => `
        <div>
          <p><b>Name:</b> ${identity.name}</p>
          <p><b>Unmatched Fields:</b> ${identity.unmatchedFields.join(', ')}</p>
          <p><b>InfoLog Errors:</b> ${identity.infoLogErrors.join(', ')}</p>
        </div>`
    )
    .join('')

  return `
    <h3>The application has the following invalid identities:</h3>
    ${identitiesContent}
  `
}

function generateBeneficialEntitiesContent(
  beneficialEntities: string[]
): string {
  return beneficialEntities.length > 0
    ? `
      <h3>Beneficial entities that own 25% or more:</h3>
      <ul>
        ${beneficialEntities.map((entity) => `<li>${entity}</li>`).join('')}
      </ul>
    `
    : ''
}

function formatNoIndividualBeneficialOwnerForNote(
  hasNoIndividualBeneficialOwner: boolean
) {
  return hasNoIndividualBeneficialOwner
    ? '<h4>An entity holds more than 75% of the organisation</h4>'
    : ''
}
