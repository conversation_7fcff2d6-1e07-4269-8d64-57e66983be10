import type { HelpEmail } from 'routes/email.route'

import { sendEmailWithTemplate } from './postmark.service'

import { CONTACT_SUPPORT } from 'helpers/email-templates'

import { EPAY_SUPPORT_EMAIL } from 'utils/config'

import { errAsync, okAsync } from 'neverthrow'

import logger from 'utils/logger'

export async function sendContactSupportEmail({
  customerEmail,
  name,
  phoneNumber,
  message,
  subject,
}: HelpEmail) {
  try {
    const sendEmailResult = await sendEmailWithTemplate({
      templateId: CONTACT_SUPPORT,
      email: EPAY_SUPPORT_EMAIL,
      templateModel: {
        customerEmail,
        name,
        phoneNumber,
        message,
        subject,
      },
    })

    if (sendEmailResult.isErr()) {
      logger.error(sendEmailResult.error, `Error sending contact support email`)
      return errAsync(sendEmailResult.error)
    }

    return okAsync('Email sent successfully')
  } catch (error) {
    logger.error(`Catch Error sending contact support email: ${error}`)
    return errAsync(error)
  }
}
