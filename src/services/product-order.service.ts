import type {
  CourierDelivery,
  CreateProductOrderProps,
  EmailDelivery,
  SubmitProductOrderProps,
  VerifyProps,
} from 'routes/product.order.route'

import { deliverySchema } from 'routes/product.order.route'

import {
  CardDesign,
  CardLogo,
  CardType,
  DeliveryMethod,
  Discount,
  FloatFundsTransactionType,
  GlobalDiscount,
  OrderStatus,
  OrderType,
  PaymentMethod,
  Prisma,
  Product as ProductBase,
  ProductOrder,
  ProductOrderItem,
  ProductOrderNoteStatus,
  ProductType,
  UserRole,
} from '@prisma/client'

import {
  ResultAsync,
  err,
  errAsync,
  fromPromise,
  ok,
  okAsync,
} from 'neverthrow'

import { getAddressType } from 'services/go-sweet-spot.service'

import {
  API_URL_BASE,
  CLIENT_URL_BASE,
  CREDIT_CARD_SURCHARGE,
  EPAY_ADMIN_EMAIL,
  EPAY_SUPPORT_EMAIL,
  IS_PROD,
} from 'utils/config'
import { ERRORS, ErrorResponse, PickErrorCode<PERSON>ey } from 'utils/error'
import logger from 'utils/logger'
import { centsToDollars, dollarsToCents, getGst } from 'utils/numeric'
import { prisma } from 'utils/prisma'
import { createWindcaveSession } from './windcave.service'

import { EPAY_GST_NUMBER, EPAY_LIVE_AGENT } from 'constants/admin'
import {
  CUSTOMER_DIRECT_CREDIT_ORDER_SUBMITTED,
  EPAY_ORDER_DIRECT_CREDIT,
} from '../helpers/email-templates'
import { sendOrderDetail } from './epay-integration.service'
import { sendEmailWithTemplate } from './postmark.service'
import { createProductOrderNote } from './product-order-note.service'
import {
  MAX_ORDER_ITEM_QUANTITY,
  MIN_VALUE,
  TESTING_MIN_VALUE,
} from 'constants/prezzy'
import { StockCardOrder, SubmitStockCardOrderProps } from 'routes/stock.route'

import { aggregateFundingInvoiceLines } from './funding.service'
import { NO_LOCK_CODE } from './product-order/product-order-util'
import { getGlobalDiscounts } from 'data/discount.data'
import { validateEmail } from 'utils/email'
import { createFloatFundsTransaction } from 'data/float-funds.data'
import { releaseProductOrder } from './product-order/order-release.service'

type Product = ProductBase & {
  design: CardDesign
  logo: CardLogo | null
}
type ProductMap = Record<number, Product>

type NewOrderResponse = {
  orderNumber: string
  orderStatus: string
  id: string
}

export function getOrderItemById(orderItemId: string) {
  return fromPromise(
    prisma.productOrderItem.findUnique({
      where: { id: orderItemId },
      select: {
        id: true,
        externalBatchId: true,
        recipientName: true,
        recipientEmail: true,
        quantity: true,
        cardItems: true,
        productOrderId: true,
      },
    }),
    (error) => {
      logger.error(
        error,
        `failed to find product order item for ID [${orderItemId}]`
      )
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((orderItem) =>
    orderItem ? okAsync(orderItem) : errAsync(ERRORS.NOT_FOUND)
  )
}

function updateProductOrderById(orderId: string, data: Partial<ProductOrder>) {
  return fromPromise(
    prisma.productOrder.update({
      where: {
        id: orderId,
      },
      data,
    }),
    (error) => {
      logger.error(error, `failed to update product order [${orderId}]`)
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function updateOrderStatus(orderId: string, orderStatus: OrderStatus) {
  return updateProductOrderById(orderId, { orderStatus })
}

export function getOrgDiscount(orgId: string) {
  return fromPromise(
    prisma.discount.findFirst({
      where: {
        organizationId: orgId,
      },
    }),
    (error) => {
      logger.error(error, `failed to get discount for org id [${orgId}]`)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((discount) => {
    if (
      !discount ||
      (discount.expiryDate && new Date() > discount.expiryDate)
    ) {
      return ok({
        loadingFee: 0,
        digitalFee: 0,
        shippingFeeDiscountCents: 0,
      } as Discount)
    }

    return ok(discount)
  })
}

export function convertOrderItemsToProductOrderItemData({
  productOrderItems,
  orgId,
}: {
  productOrderItems: CreateProductOrderProps
  orgId: string
}) {
  return ResultAsync.combine([getOrgDiscount(orgId), getGlobalDiscounts()])
    .andThen(
      ([
        {
          loadingFee: loadingFeeDiscount,
          digitalFee: digitalFeeDiscount,
          shippingFeeDiscountCents,
        },
        globalDiscount,
      ]) => {
        return validateProductOrder({ productOrderItems, orgId }).map(
          ({ productMap }) => {
            const {
              prezzyPhysical,
              prezzyVirtual,
              giftStationCourier,
              giftStationPickup,
              giftStationVirtual,
            } = splitOrderI2CAndGiftStation({
              productOrderItems,
              productMap,
            })

            return {
              productMap,
              prezzyPhysical,
              prezzyVirtual,
              giftStationCourier,
              giftStationPickup,
              giftStationVirtual,
              loadingFeeDiscount,
              digitalFeeDiscount,
              shippingFeeDiscountCents,
              globalDiscount,
            }
          }
        )
      }
    )
    .andThen(
      ({
        productMap,
        prezzyPhysical,
        prezzyVirtual,
        giftStationCourier,
        giftStationPickup,
        giftStationVirtual,
        loadingFeeDiscount,
        digitalFeeDiscount,
        shippingFeeDiscountCents,
        globalDiscount,
      }) => {
        // TODO step create gift station order
        const i2cAccumulatedCourier = accumalateCourier(prezzyPhysical)
        const giftStationAccumulatedCourier =
          accumalateCourier(giftStationCourier)

        const shippingTotalResultAsync = calcShippingTotal({
          i2c: i2cAccumulatedCourier,
          giftStation: giftStationAccumulatedCourier,
          shippingFeeDiscount: shippingFeeDiscountCents ?? 0,
        })

        const i2cCourierCreateData = prezzyPhysical.map((orderItem) => {
          const data = createProductOrderItemCourier({
            orderItem,
            product: productMap[orderItem.productCode],
            orgId,
            courierDeliveryMap: i2cAccumulatedCourier,
            loadingFeeDiscount: loadingFeeDiscount!,
            globalLoadingFeeDiscount: globalDiscount.loadingFee,
          })
          data.extraEmbossingLine = orderItem.message
          return data
        })

        const giftStationCourierCreateData = giftStationCourier.map(
          (orderItem) => {
            const data = createProductOrderItemCourier({
              orderItem,
              product: productMap[orderItem.productCode],
              orgId,
              courierDeliveryMap: giftStationAccumulatedCourier,
              loadingFeeDiscount: loadingFeeDiscount!,
              globalLoadingFeeDiscount: globalDiscount.loadingFee,
            })

            data.giftStationMessage = orderItem.message
            return data
          }
        )
        const prezzyVirtualOrderItemsCreateData = prezzyVirtual.map(
          (orderItem) => {
            return createProductOrderItemVirtual({
              orderItem,
              product: productMap[orderItem.productCode],
              orgId,
              loadingFeeDiscount: loadingFeeDiscount!,
              digitalFeeDiscount: digitalFeeDiscount!,
              globalDiscount,
            })
          }
        )

        const giftStationVirtualOrderItemsCreateData = giftStationVirtual.map(
          (orderItem) => {
            return createProductOrderItemVirtual({
              orderItem,
              product: productMap[orderItem.productCode],
              orgId,
              loadingFeeDiscount: loadingFeeDiscount!,
              digitalFeeDiscount: digitalFeeDiscount!,
              globalDiscount,
            })
          }
        )

        const giftStationPickupOrderItemsCreateData = giftStationPickup.map(
          (orderItem) => {
            return createProductOrderItemPickup({
              orderItem,
              product: productMap[orderItem.productCode],
              orgId,
              loadingFeeDiscount: loadingFeeDiscount!,
              loadingFeeGlobalDiscount: globalDiscount.loadingFee,
            })
          }
        )

        const orderItemsData = [
          i2cCourierCreateData,
          giftStationCourierCreateData,
          prezzyVirtualOrderItemsCreateData,
          giftStationVirtualOrderItemsCreateData,
          giftStationPickupOrderItemsCreateData,
        ].flat()

        return shippingTotalResultAsync.map(
          ({ shippingTotal, shippingFeeDiscountTotal }) => ({
            orderItemsData,
            shippingTotal,
            shippingFeeDiscountTotal,
          })
        )
      }
    )
}

export function createUniqueProductOrderNumber({
  orderNumber,
  id,
}: {
  orderNumber: string
  id: string
}) {
  return fromPromise(
    prisma.productOrder.update({
      where: {
        id,
      },
      data: {
        orderNumber,
      },
      select: { orderNumber: true, orderStatus: true, id: true },
    }),
    (e) => {
      logger.warn(`failed to update product order [${id}]`, e)

      return ERRORS.DATABASE_ERROR
    }
  ).map((order) => {
    return {
      orderNumber: order.orderNumber,
      orderStatus: order.orderStatus,
      id: order.id,
    } as NewOrderResponse
  })
}

export type CourierDeliveryMap = {
  [key: string]: {
    deliveryMethod: CourierDelivery
    quantity: number
    deliveryBatchId: string
  }
}

function accumalateCourier(productOrderItems: CreateProductOrderProps) {
  let counter = 1
  const result = productOrderItems.reduce<CourierDeliveryMap>(
    (acc, productOrderItem) => {
      const deliveryMethod = productOrderItem.deliveryMethod as CourierDelivery

      const key = getCourierKey(deliveryMethod)

      if (acc[key]) {
        acc[key].quantity += productOrderItem.quantity
      } else {
        acc[key] = {
          deliveryMethod,
          quantity: productOrderItem.quantity,
          deliveryBatchId: `${counter}`,
        }
        counter += 1
      }

      return acc
    },
    {}
  )

  return result
}

export function getCourierKey(deliveryMethod: {
  address: string
  city: string
}) {
  const address = deliveryMethod.address.toLowerCase().trim()
  const city = deliveryMethod.city.toLowerCase().trim()

  return `${address}${city}`
}

function createProductMap({
  productOrderItems,
  orgId,
}: {
  productOrderItems: CreateProductOrderProps
  orgId: string
}) {
  logger.info(`creating product map for product order items`)

  const productCodes = productOrderItems.reduce<Set<number>>(
    (acc, productOrderItem) => {
      acc.add(productOrderItem.productCode)
      return acc
    },
    new Set()
  )
  console.log(productCodes, 'log product codes')
  return fromPromise(
    prisma.product.findMany({
      where: {
        OR: [
          {
            productCode: {
              in: Array.from(productCodes),
            },
            organizationId: null,
          },
          {
            productCode: {
              in: Array.from(productCodes),
            },
            organizationId: orgId,
          },
        ],
      },
      include: {
        design: true,
        logo: true,
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to find products with codes [${productCodes}]`, e)

      return 'DATABASE_ERROR'
    }
  ).andThen((products) => {
    if (products.length === 0) {
      logger.warn(`no products found with codes [${productCodes}]`)

      return err({
        errorType: 'INVALID_PRODUCT_ORDER' as const,
        data: 'invalid product codes',
      })
    }

    const productMap = products.reduce<Record<number, Product>>(
      (acc, product) => {
        acc[product.productCode] = product
        return acc
      },
      {}
    )

    return ok({ productMap })
  })
}

function validateProductOrder({
  productOrderItems,
  orgId,
}: {
  productOrderItems: CreateProductOrderProps
  orgId: string
}) {
  return createProductMap({
    productOrderItems: productOrderItems,
    orgId,
  }).andThen(({ productMap }) => {
    logger.debug('validating product order')

    const { rowErrors, hasError } = productOrderItems.reduce<{
      rowErrors: {
        [row: number]: {
          productCode?: 'invalid product code'
          value?: 'invalid value'
        }
      }
      hasError: boolean
    }>(
      (acc, orderItem, index) => {
        const errors = validateOrderItem({
          orderItem,
          product: productMap[orderItem.productCode],
        })

        if (errors) {
          acc.rowErrors[index] = errors
          acc.hasError = true
          return acc
        }

        return acc
      },
      { rowErrors: {}, hasError: false }
    )

    if (hasError) {
      logger.warn(`invalid product order`, rowErrors)

      return err({
        errorType: 'INVALID_PRODUCT_ORDER' as const,
        data: rowErrors,
      })
    }

    return ok({ productMap })
  })
}

function validateOrderItem({
  orderItem,
  product,
}: {
  orderItem: CreateProductOrderProps[number]
  product?: Product
}) {
  const errors: {
    productCode?: 'invalid product code'
    quantity?: 'invalid quantity'
    value?: 'invalid value'
  } = {}
  if (!product || product.isArchived) {
    errors.productCode = 'invalid product code'
    return errors
  }

  if (
    !isValidStoreValue({
      dollars: orderItem.value,
      minValue: IS_PROD ? product.minValue : TESTING_MIN_VALUE,
      maxValue: product.maxValue,
      fixedValues: product.fixedValues,
    })
  ) {
    errors.value = 'invalid value'
    return errors
  }

  if (!isValidQuantity(orderItem.quantity)) {
    errors.quantity = 'invalid quantity'
    return errors
  }

  return null
}

function isValidQuantity(quantity?: number) {
  return quantity && quantity <= MAX_ORDER_ITEM_QUANTITY ? quantity : null
}

function isValidStoreValue({
  dollars,
  minValue,
  maxValue,
  fixedValues,
}: {
  dollars?: number
  minValue: number | null
  maxValue: number | null
  fixedValues: number[]
}) {
  if (!dollars) {
    return false
  }

  const value = dollarsToCents(dollars)
  if (minValue && maxValue) {
    if (value < minValue || value > maxValue) {
      return false
    }
  } else {
    if (!fixedValues.includes(value)) {
      return false
    }
  }

  return true
}

function splitOrderI2CAndGiftStation({
  productOrderItems,
  productMap,
}: {
  productOrderItems: CreateProductOrderProps
  productMap: ProductMap
}) {
  return productOrderItems.reduce<{
    giftStationCourier: CreateProductOrderProps
    giftStationPickup: CreateProductOrderProps
    giftStationVirtual: CreateProductOrderProps
    prezzyPhysical: CreateProductOrderProps
    prezzyVirtual: CreateProductOrderProps
  }>(
    (acc, orderItem) => {
      const product = productMap[orderItem.productCode]
      if (product.type === 'CUSTOM') {
        acc.prezzyPhysical.push(orderItem)
      } else if (
        product.type === 'PREZZY' &&
        orderItem.deliveryMethod.type === 'EMAIL'
      ) {
        acc.prezzyVirtual.push(orderItem)
      } else if (product.type === 'PREZZY' && orderItem.message) {
        acc.prezzyPhysical.push(orderItem)
      } else {
        switch (orderItem.deliveryMethod.type) {
          case 'COURIER':
            acc.giftStationCourier.push(orderItem)
            break
          case 'EMAIL':
            acc.giftStationVirtual.push(orderItem)
            break
          case 'PICKUP':
            acc.giftStationPickup.push(orderItem)
            break
          default:
            logger.warn(`not supported delivery method`, orderItem)
        }
      }

      logger.debug('splitOrderI2CAndGiftStation.. completed')

      return acc
    },
    {
      giftStationCourier: [],
      giftStationPickup: [],
      giftStationVirtual: [],
      prezzyPhysical: [],
      prezzyVirtual: [],
    }
  )
}

function createProductOrderItemCourier({
  orderItem,
  product,
  orgId,
  courierDeliveryMap,
  loadingFeeDiscount,
  globalLoadingFeeDiscount,
}: {
  orderItem: CreateProductOrderProps[number]
  product: Product
  orgId?: string
  courierDeliveryMap: CourierDeliveryMap
  loadingFeeDiscount: number
  globalLoadingFeeDiscount: number
}) {
  const deliveryMethod = orderItem.deliveryMethod as CourierDelivery
  const key = getCourierKey(deliveryMethod)

  const loadingFeeCents = product.loadingFee * orderItem.quantity

  const loadFeeDiscountCents = orgId
    ? calculateFeeDiscount({
        orgDiscount: loadingFeeDiscount,
        globalDiscount: globalLoadingFeeDiscount,
      }) * orderItem.quantity
    : 0

  const orderItemCreateData: Omit<
    Prisma.ProductOrderItemCreateInput,
    'productOrder'
  > = {
    productCode: product.productCode,
    deliveryMethod: 'COURIER',

    quantity: orderItem.quantity,
    unitPrice: dollarsToCents(orderItem.value),

    loadingFee: loadingFeeCents,
    loadingFeeDiscount: loadFeeDiscountCents,
    discount: loadFeeDiscountCents,

    recipientName: orderItem.recipientName,
    recipientEmail: orderItem.recipientEmail,
    recipientAddress: deliveryMethod.address,
    recipientAddressLine2: deliveryMethod.addressLine2,
    recipientSuburb: deliveryMethod.suburb,
    recipientPostCode: deliveryMethod.postcode,
    recipientCity: deliveryMethod.city,
    recipientCountry: 'NZ',

    customerReference: orderItem.customerReference,

    deliveryBatchId: courierDeliveryMap[key]?.deliveryBatchId,

    lineNumber: orderItem.lineNumber,

    product: {
      connect: {
        id: product.id,
      },
    },
  }

  return orderItemCreateData
}

export const SHIPPING_PRICE = {
  URBAN: 750,
  RURAL: 1050,
  WAIHEKE: 3750,
  NONE: 0,
}

function getShippingType(cost: number) {
  switch (cost) {
    case 750:
      return 'URBAN'
    case 1050:
      return 'RURAL'
    //set default to urban for global shipping discount
    default:
      return 'URBAN'
  }
}

export function calcShippingTotal({
  i2c,
  giftStation,
  shippingFeeDiscount,
}: {
  i2c: CourierDeliveryMap
  giftStation: CourierDeliveryMap
  shippingFeeDiscount: number // in cents
}) {
  logger.info('calculating shipping total')

  return getGlobalDiscounts().andThen((globalDiscount) => {
    const i2cCostResult = Object.values(i2c).map(
      ({ deliveryMethod, quantity }) => {
        return getShippingCosts(deliveryMethod).map((cost) => {
          const multiplier = Math.ceil(quantity / 60)
          const shippingType = getShippingType(cost)

          return {
            cost: multiplier * cost,
            discount:
              multiplier *
              calculateFeeDiscount({
                orgDiscount: shippingFeeDiscount,
                globalDiscount:
                  shippingType === 'URBAN'
                    ? globalDiscount.urbanShipping
                    : globalDiscount.ruralShipping,
              }),
          }
        })
      }
    )

    const giftStationCostResult = Object.values(giftStation).map(
      ({ deliveryMethod, quantity }) => {
        return getShippingCosts(deliveryMethod).map((cost) => {
          //TODO change this to / 200 when Giftstation is properly implemented
          const shippingType = getShippingType(cost)
          const multiplier = Math.ceil(quantity / 60)
          return {
            cost: multiplier * cost,
            discount:
              multiplier *
              calculateFeeDiscount({
                orgDiscount: shippingFeeDiscount,
                globalDiscount:
                  shippingType === 'URBAN'
                    ? globalDiscount.urbanShipping
                    : globalDiscount.ruralShipping,
              }),
          }
        })
      }
    )

    return ResultAsync.combine(
      [...i2cCostResult, ...giftStationCostResult].flat()
    ).map((results) => {
      const totalShippingFee = results.reduce((acc, { cost }) => acc + cost, 0)
      const totalShippingFeeDiscount = results.reduce(
        (acc, { discount }) => acc + discount,
        0
      )

      return {
        shippingTotal: totalShippingFee,
        shippingFeeDiscountTotal: totalShippingFeeDiscount,
      }
    })
  })
}

function getShippingCosts(delivery: CourierDelivery) {
  const isWaiheke = `${delivery.suburb} ${delivery.city}`
    .toLowerCase()
    .includes('waiheke')

  if (isWaiheke) {
    return okAsync(SHIPPING_PRICE.WAIHEKE)
  }

  return getAddressType({
    BuildingName: '',
    StreetAddress: delivery.address,
    Suburb: delivery.city,
    City: '',
    PostCode: delivery.postcode,
    CountryCode: 'NZ',
  })
    .map((addressType) => {
      if (addressType === 'RURAL') {
        return SHIPPING_PRICE.RURAL
      }
      return SHIPPING_PRICE.URBAN
    })
    .orElse((error) => {
      logger.error(
        error,
        `failed to get address type for address [${delivery.address}] from go sweet spot`
      )
      return ok(SHIPPING_PRICE.URBAN)
    })
}

function createProductOrderItemVirtual({
  orderItem,
  product,
  orgId,
  loadingFeeDiscount,
  digitalFeeDiscount,
  globalDiscount,
}: {
  orderItem: CreateProductOrderProps[number]
  product: Product
  orgId?: string
  loadingFeeDiscount: number
  digitalFeeDiscount: number
  globalDiscount: GlobalDiscount
}) {
  const deliveryMethod = orderItem.deliveryMethod as EmailDelivery
  const loadingFeeCents = product.loadingFee * orderItem.quantity
  const digitalFeeCents = product.digitalFee! * orderItem.quantity
  const loadingFeeDiscountCents = orgId
    ? calculateFeeDiscount({
        orgDiscount: loadingFeeDiscount,
        globalDiscount: globalDiscount.loadingFee,
      }) * orderItem.quantity
    : 0
  const digitalFeeDiscountCents = orgId
    ? calculateFeeDiscount({
        orgDiscount: digitalFeeDiscount,
        globalDiscount: globalDiscount.digitalFee,
      }) * orderItem.quantity
    : 0

  const orderItemCreateData: Omit<
    Prisma.ProductOrderItemCreateInput,
    'productOrder'
  > = {
    productCode: product.productCode,
    deliveryMethod: 'EMAIL',

    quantity: orderItem.quantity,
    unitPrice: dollarsToCents(orderItem.value),

    loadingFee: loadingFeeCents,
    digitalFee: digitalFeeCents,
    loadingFeeDiscount: loadingFeeDiscountCents,
    digitalFeeDiscount: digitalFeeDiscountCents,
    discount: loadingFeeDiscountCents + digitalFeeDiscountCents,

    recipientName: orderItem.recipientName,
    recipientEmail: deliveryMethod.email,

    giftStationMessage: orderItem.message,

    lineNumber: orderItem.lineNumber,

    product: {
      connect: {
        id: product.id,
      },
    },
  }

  return orderItemCreateData
}

function createProductOrderItemPickup({
  orderItem,
  product,
  orgId,
  loadingFeeDiscount,
  loadingFeeGlobalDiscount,
}: {
  orderItem: CreateProductOrderProps[number]
  product: Product
  orgId?: string
  loadingFeeDiscount: number
  loadingFeeGlobalDiscount: number
}) {
  const loadingFeeCents = product.loadingFee * orderItem.quantity
  const loadingFeeDiscountCents = orgId
    ? calculateFeeDiscount({
        orgDiscount: loadingFeeDiscount,
        globalDiscount: loadingFeeGlobalDiscount,
      }) * orderItem.quantity
    : 0
  const orderItemCreateData: Omit<
    Prisma.ProductOrderItemCreateInput,
    'productOrder'
  > = {
    productCode: product.productCode,
    deliveryMethod: 'PICKUP',

    quantity: orderItem.quantity,
    unitPrice: dollarsToCents(orderItem.value),

    loadingFee: loadingFeeCents,
    loadingFeeDiscount: loadingFeeDiscountCents,
    discount: loadingFeeDiscountCents,

    recipientName: orderItem.recipientName,
    recipientEmail: orderItem.recipientEmail,

    giftStationMessage: orderItem.message,

    lineNumber: orderItem.lineNumber,

    product: {
      connect: {
        id: product.id,
      },
    },
  }

  return orderItemCreateData
}

type InvoiceLine = {
  productId: string
  productCode: number
  productName: string
  quantity: number
  unitPrice: number
  subTotal: number
  loadingFee: number
  discount: number
  digitalFee: number
}

export type AggregateInvoiceLines = {
  [key: string]: InvoiceLine
}

export function getPaymentSummary({
  orderNumber,
  orgId,
}: {
  orderNumber: string
  orgId: string
}) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        orderNumber,
        organizationId: orgId,
        orderStatus: 'PENDING',
      },
      include: {
        organization: true,
        productOrderItems: {
          include: {
            product: true,
          },
        },
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to query product order number [${orderNumber}]`, e)

      return 'DATABASE_ERROR'
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      return err('NOT_FOUND' as const)
    }

    if (
      !productOrder.productOrderItems ||
      productOrder.productOrderItems.length === 0
    ) {
      const messge = {
        code: 'This should never happen!',
        message: 'Product order items length is 0',
      }
      logger.error(messge)
      return err(messge)
    }

    const invoiceItems = aggregateInvoiceLines({
      productOrderItems: productOrder.productOrderItems,
    })

    const orderHasPhysicalCards = productOrder.productOrderItems.some((item) =>
      item.product.cardTypes.includes('PHYSICAL')
    )

    const recipientNamesByDeliveryBatchId = getRecipientNamesForBatchDeliveryId(
      { productOrderItems: productOrder.productOrderItems }
    )

    logger.debug(
      'recipientNamesByDeliveryBatchId',
      recipientNamesByDeliveryBatchId
    )

    return ok({
      orderNumber: productOrder.orderNumber,
      orderType: productOrder.orderType,
      orderTotal: centsToDollars(productOrder.orderTotal),
      shippingTotal: centsToDollars(productOrder.shippingTotal),
      gstNumber: productOrder.organization?.gstNumber,
      loadingFeeTotal: centsToDollars(productOrder.loadingFeeTotal),
      discountTotal: centsToDollars(productOrder.discountTotal),
      digitalFeeTotal: centsToDollars(productOrder.digitalFeeTotal),
      digitalFeeDiscountTotal: centsToDollars(
        productOrder.digitalFeeDiscountTotal
      ),
      loadingFeeDiscountTotal: centsToDollars(
        productOrder.loadingFeeDiscountTotal
      ),
      shippingFeeDiscountTotal: centsToDollars(
        productOrder.shippingFeeDiscountTotal
      ),
      subTotal: centsToDollars(productOrder.subTotal),
      gstAmount: centsToDollars(productOrder.gstAmount),
      creditCardFee: centsToDollars(productOrder.creditCardFee),
      invoiceItems,
      orderHasPhysicalCards,
      armourguardAvailable: armourguardAvailable({
        firstOrderItem: productOrder.productOrderItems[0],
        invoiceItemLength: invoiceItems.length,
        subTotal: productOrder.subTotal,
      }),
      recipientNamesByDeliveryBatchId,
      lockCode: productOrder.lockCode,
      paymentMethod: productOrder.paymentMethod,
    })
  })
}

type Recipient = {
  name: string
  id: string
}

type DeliveryBatch = {
  deliveryBatchId: string
  address: string
  hasNoRecipient?: boolean
  recipients: Recipient[]
}

function getRecipientNamesForBatchDeliveryId({
  productOrderItems,
}: {
  productOrderItems: ProductOrderItem[]
}): DeliveryBatch[] {
  const batchMap: { [batchId: string]: DeliveryBatch } = {}

  productOrderItems.forEach((item) => {
    const {
      deliveryBatchId,
      recipientName,
      recipientAddress,
      recipientAddressLine2,
      recipientSuburb,
      recipientPostCode,
      recipientCity,
      recipientCountry,
      id,
    } = item

    const fullAddress = [
      recipientAddress,
      recipientAddressLine2,
      recipientSuburb,
      recipientCity,
      recipientPostCode,
      recipientCountry,
    ]
      .filter(Boolean) // Remove any empty strings or falsy values
      .join(', ')

    if (deliveryBatchId !== null) {
      if (batchMap[deliveryBatchId]) {
        batchMap[deliveryBatchId].recipients.push({
          name: recipientName,
          id: id,
        })
      } else {
        batchMap[deliveryBatchId] = {
          deliveryBatchId: deliveryBatchId,
          address: fullAddress,
          recipients: [
            {
              name: recipientName,
              id: id,
            },
          ],
        }
      }

      if (!recipientName) {
        batchMap[deliveryBatchId].hasNoRecipient = true
      }
    }
  })

  const result = Object.values(batchMap).filter(
    (batch) => batch.recipients.length >= 2 || batch.hasNoRecipient
  )

  return result
}

function armourguardAvailable({
  firstOrderItem,
  subTotal,
  invoiceItemLength,
}: {
  firstOrderItem: ProductOrderItem & { product: ProductBase }
  subTotal: number | null
  invoiceItemLength: number
}) {
  if (invoiceItemLength > 1) {
    return false
  }
  if (!subTotal || subTotal < 5000000) {
    return false
  }
  if (firstOrderItem.deliveryMethod !== 'COURIER') {
    return false
  }

  return true
}

export function aggregateInvoiceLines({
  productOrderItems,
}: {
  productOrderItems: (ProductOrderItem & { product: ProductBase })[]
}) {
  const lineItems: AggregateInvoiceLines = {}

  productOrderItems.forEach((item) => {
    const key = `${item.productId}_${item.unitPrice}`
    const loadingFee = item.loadingFee ?? 0
    const digitalFee = item.digitalFee ?? 0

    if (lineItems.hasOwnProperty(key)) {
      lineItems[key].quantity += item.quantity
      lineItems[key].subTotal += item.quantity * item.unitPrice
      lineItems[key].loadingFee += loadingFee
      lineItems[key].discount += item.discount ?? 0
      lineItems[key].digitalFee += digitalFee
    } else {
      lineItems[key] = {
        productId: item.productId,
        productCode: item.productCode,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        productName: item.product?.name,
        subTotal: item.quantity * item.unitPrice,
        discount: item.discount ?? 0,
        loadingFee,
        digitalFee,
      }
    }
  })

  const invoiceItems = Object.values(lineItems).map((item) => ({
    productId: item.productId,
    productCode: item.productCode,
    quantity: item.quantity,
    unitPrice: centsToDollars(item.unitPrice),
    productName: item.productName,
    subTotal: centsToDollars(item.subTotal),
    loadingFee: centsToDollars(item.loadingFee),
    discount: centsToDollars(item.discount),
    digitalFee: centsToDollars(item.digitalFee),
  }))

  return invoiceItems
}

type UpdateOrderWithPaymentDetails = Omit<
  SubmitProductOrderProps,
  'secureDelivery'
> & {
  orderTotal: number
  creditCardFee: number | null
}

export function updateOrderWithPaymentDetails({
  orgId,
  orderNumber,
  billingAddress,
  city,
  country,
  paymentMethod,
  postcode,
  purchaseOrderNumber,
  lockCode,
  creditCardFee,
  orderTotal,
  isLiveAgent,
  authorizedByEmail,
  authorizedByName,
  scheduledDate,
}: UpdateOrderWithPaymentDetails & { orgId: string }) {
  logger.info(
    `updating order with payment details, order number [${orderNumber}]`
  )

  const data: Partial<ProductOrder> = {
    paymentMethod,
    billingAddress: billingAddress,
    city: city,
    country: country,
    postCode: postcode,
    purchaseOrderNumber: purchaseOrderNumber,
    lockCode: lockCode,
    creditCardFee: creditCardFee,
    orderTotal: orderTotal,
    createdByLiveAgent: isLiveAgent,
    authorizedByEmail: authorizedByEmail ? authorizedByEmail : null,
    authorizedByName: authorizedByName ? authorizedByName : null,
    scheduledDate: scheduledDate ? new Date(scheduledDate) : null,
  }

  if (
    paymentMethod === PaymentMethod.BANK_TRANSFER ||
    paymentMethod === PaymentMethod.FLOAT_FUNDS
  ) {
    data.orderStatus = OrderStatus.SUBMITTED
    data.orderTotal! -= data.creditCardFee!
    data.creditCardFee = 0
  }

  return fromPromise(
    prisma.productOrder.updateMany({
      where: {
        orderNumber: orderNumber,
        orderStatus: 'PENDING',
        organizationId: orgId,
      },
      data,
    }),
    (error) => {
      logger.warn(
        `error submitting order, order number [${orderNumber}]`,
        error
      )

      return ERRORS.DATABASE_ERROR
    }
  ).andThen(({ count }) => {
    if (count !== 1) {
      return err(ERRORS.NOT_FOUND)
    }

    return ok(`order ${orderNumber} updated  successfully `)
  })
}

export function getProductOrder({
  orderNumber,
  orderId,
}: {
  orderNumber?: string
  orderId?: string
}) {
  logger.info(
    `getProductOrder.. querying product orderNumber [${orderNumber}], orderId [${orderId}]`
  )

  if (!orderNumber && !orderId) {
    return errAsync(ERRORS.NOT_FOUND)
  }

  const whereClause: Prisma.ProductOrderWhereUniqueInput = orderNumber
    ? { orderNumber }
    : { id: orderId }

  return fromPromise(
    prisma.productOrder.findFirst({
      where: whereClause,
      include: {
        user: true,
        organization: true,
        productOrderItems: {
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
        FundingProductOrderItem: {
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.warn(
        `getProductOrder.. failed to query product orderNumber [${orderNumber}] : orderId [${orderId}]`,
        error
      )

      return ERRORS.NOT_FOUND
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      logger.warn('getProductOrder.. no product order found', orderNumber)
      return errAsync(ERRORS.NOT_FOUND)
    }

    return okAsync(productOrder)
  })
}

export async function applyOrderTotalAdjustments({
  orderNumber,
  shippingTotal,
  orderTotal,
  creditCardFee,
  secureDelivery,
}: {
  orderNumber: string
  creditCardFee?: number
  shippingTotal?: number
  orderTotal: number
  secureDelivery?: boolean
}) {
  logger.info(
    `updating order with secure delivery, order number [${orderNumber}]`
  )
  return fromPromise(
    prisma.productOrder.update({
      where: {
        orderNumber,
      },
      data: {
        creditCardFee,
        shippingTotal,
        orderTotal,
        secureDelivery,
      },
    }),
    (error) => {
      logger.warn(
        `error updating order with secure delivery, order number [${orderNumber}]`,
        error
      )

      return ERRORS.DATABASE_ERROR
    }
  )
}

function updateOrderTotalForSecureShipping({
  isSecureDelivery,
  orderNumber,
  paymentMethod,
  productOrder,
}: {
  isSecureDelivery: boolean
  orderNumber: string
  paymentMethod: PaymentMethod
  productOrder: ProductOrder
}) {
  if (isSecureDelivery || paymentMethod === PaymentMethod.BANK_TRANSFER) {
    const payload = {
      orderNumber,
      shippingTotal: productOrder.shippingTotal ?? 0,
      orderTotal: productOrder.orderTotal!,
      secureDelivery: false,
    }

    if (isSecureDelivery) {
      payload.orderTotal -= payload.shippingTotal
      payload.shippingTotal = 0
      payload.secureDelivery = true
    }

    return applyOrderTotalAdjustments(payload)
  }

  // No adjustments needed
  return ok(null)
}

function orderHasPhysicalCards({ orderNumber }: { orderNumber: string }) {
  logger.info(
    `checking if order has physical cards, order number [${orderNumber}]`
  )
  return fromPromise(
    prisma.productOrderItem.findMany({
      where: {
        productOrder: {
          orderNumber,
        },
      },
      include: {
        product: true,
      },
    }),
    (error) => {
      logger.warn(
        `error querying product order items for order number [${orderNumber}]`,
        error
      )

      return ERRORS.DATABASE_ERROR
    }
  ).andThen((orderItems) => {
    let hasPhysicalCards = false

    hasPhysicalCards = orderItems.some((item) =>
      item.product.cardTypes.includes('PHYSICAL')
    )

    logger.info(`order has physical cards: ${hasPhysicalCards}`)

    return ok(hasPhysicalCards)
  })
}

export async function submitProductOrder({
  orgId,
  orderNumber,
  billingAddress,
  city,
  country,
  paymentMethod,
  postcode,
  purchaseOrderNumber,
  secureDelivery,
  lockCode,
  selectedDeliveryRecipients,
  isLiveAgent,
  authorizedByEmail,
  authorizedByName,
  scheduledDate,
}: SubmitProductOrderProps & { orgId: string }) {
  try {
    const isSecureDeliveryResult = await shouldQualifyForSecureDelivery({
      orderNumber,
      secureDelivery,
    })

    if (isSecureDeliveryResult.isErr()) {
      return errAsync(isSecureDeliveryResult.error)
    }

    const isSecureDelivery = isSecureDeliveryResult.value.withSecureDelivery

    if (selectedDeliveryRecipients) {
      const setWhoParcelIsAddressedToResult = await setWhoParcelIsAddressedTo({
        selectedDeliveryRecipients,
      })

      if (setWhoParcelIsAddressedToResult.isErr()) {
        return errAsync(setWhoParcelIsAddressedToResult.error)
      }
    }

    const productOrderResult = await getProductOrder({
      orderNumber,
    })

    if (productOrderResult.isErr()) {
      return errAsync(productOrderResult.error)
    }

    const productOrder = productOrderResult.value

    const updateWithoutRecipientsResult =
      await updateProductOrderItemsWithoutRecipients(
        productOrder.productOrderItems
      )

    if (updateWithoutRecipientsResult.isErr()) {
      return errAsync(updateWithoutRecipientsResult.error)
    }

    const hasPhysicalCardsResult = await orderHasPhysicalCards({ orderNumber })

    if (hasPhysicalCardsResult.isErr()) {
      return errAsync(hasPhysicalCardsResult.error)
    }

    if (hasPhysicalCardsResult.value) {
      if (!productOrder.orderTotal || !productOrder.shippingTotal) {
        return errAsync({
          code: ERRORS.ILLEGAL_ACTION.code,
          message: `Order total ${productOrder.orderTotal} or shipping total ${productOrder.shippingTotal} is null`,
        })
      }
    }

    const adjustedOrder = await updateOrderTotalForSecureShipping({
      isSecureDelivery,
      orderNumber,
      paymentMethod,
      productOrder,
    })

    if (adjustedOrder.isErr()) {
      return errAsync(adjustedOrder.error)
    }

    const updateOrderResult = await updateOrderWithPaymentDetails({
      orgId,
      orderNumber,
      billingAddress,
      city,
      country,
      paymentMethod,
      postcode,
      purchaseOrderNumber,
      lockCode,
      creditCardFee: productOrder.creditCardFee,
      orderTotal: productOrder.orderTotal!,
      isLiveAgent,
      authorizedByEmail,
      authorizedByName,
      scheduledDate,
    })

    if (updateOrderResult.isErr()) {
      return errAsync(updateOrderResult.error)
    }

    if (paymentMethod === PaymentMethod.CREDIT_CARD) {
      return createPaymentSession({ orderNumber })
    } else if (paymentMethod === PaymentMethod.FLOAT_FUNDS) {
      return submitProductOrderWithFloatFunds({
        orgId: productOrder.organizationId,
        amount: productOrder.orderTotal!,
        transactionType: FloatFundsTransactionType.DEBIT,
        createdByUserId: productOrder.user.id,
        orderNumber,
        orderId: productOrder.id,
      })
    } else {
      sendEmailWithTemplate({
        email: productOrder.user.email,
        templateId: CUSTOMER_DIRECT_CREDIT_ORDER_SUBMITTED,
        templateModel: {
          orderNumber,
          firstName: productOrder.user.firstName,
          orderTotal:
            centsToDollars(productOrder.orderTotal) -
            centsToDollars(productOrder.creditCardFee),
        },
      })

      sendEmailWithTemplate({
        email: EPAY_ADMIN_EMAIL,
        templateId: EPAY_ORDER_DIRECT_CREDIT,
        templateModel: {
          orderNumber,
          orderTotal:
            centsToDollars(productOrder.orderTotal) -
            centsToDollars(productOrder.creditCardFee),
          companyName: productOrder.organization.name,
        },
      })

      return okAsync({ orderStatus: 'SUBMITTED', redirect: undefined })
    }
  } catch (error: unknown) {
    logger.error(error, `failed to submit order number [${orderNumber}]`)
    return errAsync(ERRORS.DATABASE_ERROR)
  }
}

function submitProductOrderWithFloatFunds({
  orgId,
  amount,
  transactionType,
  createdByUserId,
  orderNumber,
  orderId,
}: {
  orgId: string
  amount: number
  transactionType: FloatFundsTransactionType
  createdByUserId: string
  orderNumber: string
  orderId: string
}) {
  logger.info(
    '(A) submitProductOrderWithFloatFunds: creating float funds transaction'
  )
  return createFloatFundsTransaction({
    orgId,
    amount,
    transactionType,
    createdByUserId,
    orderNumber,
    orderId,
    description: `Card order #${orderNumber}`,
  }).andThen((floatFundsTransaction) => {
    logger.info('(B) submitProductOrderWithFloatFunds: releasing product order')
    return releaseProductOrder({
      orderId,
      paymentDate: floatFundsTransaction.transactionDate.toISOString(),
      userId: createdByUserId,
      isWindcavePayment: false,
    }).andThen(() => {
      logger.info(
        '(C) submitProductOrderWithFloatFunds: order released - status completed'
      )
      return okAsync({
        orderStatus: OrderStatus.COMPLETED,
        redirect: undefined,
      })
    })
  })
}

type SelectedDeliveryRecipients = {
  id: string
  value: string
}[]

export function setWhoParcelIsAddressedTo({
  selectedDeliveryRecipients,
}: {
  selectedDeliveryRecipients: SelectedDeliveryRecipients
}) {
  // Construct an array of update operations for productOrderItems
  const updateOperations = selectedDeliveryRecipients.map((recipient) => {
    return prisma.productOrderItem.update({
      where: { id: recipient.id },
      // Set deliveryRecipient to true and update name for selected recipient in case it doesnt have one
      data: { deliveryRecipient: true, recipientName: recipient.value },
    })
  })

  // Now update the database with the modified productOrderItems
  return fromPromise(Promise.all(updateOperations), (error) => {
    logger.warn(`error updating product order items`, error)
    return ERRORS.DATABASE_ERROR
  })
}

function shouldQualifyForSecureDelivery({
  orderNumber,
  secureDelivery,
}: {
  orderNumber: string
  secureDelivery: boolean
}) {
  if (!secureDelivery) {
    return okAsync({ withSecureDelivery: false })
  }
  return fromPromise(
    prisma.productOrder.findUnique({
      where: {
        orderNumber,
      },
      include: {
        productOrderItems: {
          include: {
            product: true,
          },
        },
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to query product order number [${orderNumber}]`, e)

      return 'DATABASE_ERROR'
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      return err('NOT_FOUND' as const)
    }

    const invoiceItems = aggregateInvoiceLines({
      productOrderItems: productOrder.productOrderItems,
    })

    return armourguardAvailable({
      firstOrderItem: productOrder.productOrderItems[0],
      invoiceItemLength: invoiceItems.length,
      subTotal: productOrder.subTotal,
    })
      ? ok({ withSecureDelivery: true })
      : err('ILLEGAL_ACTION' as const)
  })
}

function createPaymentSession({ orderNumber }: { orderNumber: string }) {
  return fromPromise(
    prisma.productOrder.findUnique({
      where: {
        orderNumber,
      },
    }),
    (error) => {
      logger.warn(
        `failed to query product order with order number [${orderNumber}]`,
        error
      )
      return ERRORS.DATABASE_ERROR
    }
  )
    .andThen((productOrder) => {
      if (!productOrder) {
        return errAsync(ERRORS.NOT_FOUND)
      }

      const { orderNumber, orderTotal } = productOrder
      return createWindcaveSession({
        amount: centsToDollars(orderTotal).toFixed(2),
        callbackUrls: {
          approved: `${CLIENT_URL_BASE}/order-history/invoice/${orderNumber}?transactionStatus=approved`,
          declined: `${CLIENT_URL_BASE}/select-card/cart/checkout/${orderNumber}?transactionStatus=declined`,
          cancelled: `${CLIENT_URL_BASE}/select-card/cart/checkout/${orderNumber}?transactionStatus=cancelled`,
        },
        notificationUrl: `${API_URL_BASE}/notification/product/order/payment`,
      })
    })
    .andThen(({ id, redirect }) => {
      return fromPromise(
        prisma.productOrder.update({
          where: {
            orderNumber,
          },
          data: {
            windcaveSessionId: id,
            orderStatus: OrderStatus.PENDING,
          },
        }),
        (error) => {
          logger.warn(
            `failed to update product order with order number [${orderNumber}] with windcave session id [${id}]`,
            error
          )

          return ERRORS.DATABASE_ERROR
        }
      ).map(() => {
        return { redirect, orderStatus: OrderStatus.PENDING }
      })
    })
}

export function updateProductOrderAfterSubmitting({
  orderStatus,
  submittedAt,
  orderNumber,
  releasedBy,
  paymentDate,
}: {
  orderStatus: OrderStatus
  submittedAt: Date
  orderNumber: string
  releasedBy: string
  paymentDate: Date
}) {
  return fromPromise(
    prisma.productOrder.update({
      where: { orderNumber },
      data: {
        orderStatus,
        submittedAt,
        releasedBy,
        paymentDate,
      },
      include: {
        user: true,
      },
    }),
    (error) => {
      logger.warn('error updating product order to processing', error)

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function groupProductOrderItemsByDeliveryRecipient<
  T extends ProductOrderItem & {
    product: Product
  }
>({ productOrderItems }: { productOrderItems: T[] }) {
  return productOrderItems.reduce(
    (acc, productOrderItem) => {
      if (productOrderItem.deliveryRecipient) {
        acc.withRecipient.push(productOrderItem)
      } else {
        acc.withoutRecipient.push(productOrderItem)
      }
      return acc
    },
    { withRecipient: [] as T[], withoutRecipient: [] as T[] }
  )
}

export function updateProductOrderItem({
  id,
  externalBatchId,
}: {
  id: string
  externalBatchId: string
}) {
  return fromPromise(
    prisma.productOrderItem.update({
      where: { id },
      data: { externalBatchId },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.error(
        error,
        `error updating OrderItem [${id}] externalBatchId[${externalBatchId}]`
      )

      return 'DATABASE_ERROR'
    }
  ).map((updatedOrderItem) => {
    logger.debug('OrderItem updated:', updatedOrderItem)

    return updatedOrderItem
  })
}

export function createProductOrderItemCard({
  productOrderItemId,
  externalCardReferenceNumber,
}: {
  productOrderItemId: string
  externalCardReferenceNumber: string
}) {
  return fromPromise(
    prisma.productOrderItemCard.create({
      data: {
        externalCardReferenceNumber,
        productOrderItemId,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.error(
        error,
        `error updating OrderItem [${productOrderItemId}] externalBatchId[${externalCardReferenceNumber}]`
      )

      return 'DATABASE_ERROR'
    }
  ).map((productOrderItemCard) => {
    logger.debug(
      `OrderItemCard added to ${productOrderItemId}:`,
      productOrderItemCard
    )

    return productOrderItemCard
  })
}

function createProductOrderItemCardBatch(
  orderItemId: string,
  crns: string[],
  lockCodes: string[]
) {
  return fromPromise(
    prisma.productOrderItemCard.createMany({
      data: crns.map((cr, i) => ({
        externalCardReferenceNumber: cr,
        productOrderItemId: orderItemId,
        lockCode: lockCodes[i],
      })),
    }),
    (error) => {
      logger.error(
        error,
        `error updating OrderItem [${orderItemId}] externalBatchId[${crns}]`
      )

      return 'DATABASE_ERROR'
    }
  ).map((productOrderItemCard) => {
    logger.debug(`OrderItemCard added to ${orderItemId}:`, productOrderItemCard)

    return productOrderItemCard
  })
}

export function splitProductOrderI2CAndGiftStation<
  T extends ProductOrderItem & {
    product: Product
  }
>({ productOrderItems }: { productOrderItems: T[] }) {
  return productOrderItems.reduce<{
    i2c: T[]
    giftStation: T[]
  }>(
    (acc, orderItem) => {
      acc.i2c.push(orderItem)
      // FIXME - Gift station logic, uncomment code and remove above line
      // if (orderItem.product.type === 'CUSTOM') {
      //   acc.i2c.push(orderItem)
      // } else if (
      //   orderItem.product.type === 'PREZZY' &&
      //   orderItem.deliveryMethod === 'EMAIL'
      // ) {
      //   acc.i2c.push(orderItem)
      // } else if (
      //   orderItem.product.type === 'PREZZY' &&
      //   orderItem.extraEmbossingLine
      // ) {
      //   acc.i2c.push(orderItem)
      // } else {
      //   acc.giftStation.push(orderItem)
      // }

      return acc
    },
    {
      i2c: [],
      giftStation: [],
    }
  )
}

type ProductOrderWithRelations = Prisma.ProductOrderGetPayload<{
  include: {
    productOrderItems: {
      include: {
        product: true
      }
    }
    FundingProductOrderItem: {
      include: {
        product: true
      }
    }
    organization: true
    user: true
  }
}>

export function getInvoice(orderNumber: string, organizationId: string) {
  return fetchProductOrder(orderNumber, organizationId).andThen(
    processProductOrder
  )
}

function fetchProductOrder(
  orderNumber: string,
  organizationId: string
): ResultAsync<ProductOrderWithRelations | null, typeof ERRORS.DATABASE_ERROR> {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: { orderNumber, organizationId },
      include: {
        productOrderItems: { include: { product: true } },
        FundingProductOrderItem: { include: { product: true } },
        organization: true,
        user: true,
      },
    }),
    (error) => {
      logger.warn(`Error fetching order [${orderNumber}]`, error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

function processProductOrder(productOrder: ProductOrderWithRelations | null) {
  if (!productOrder) {
    return errAsync(ERRORS.NOT_FOUND)
  }

  const commonInvoiceData = getCommonInvoiceData(productOrder)

  switch (productOrder.orderType) {
    case OrderType.STOCK:
      return processStockOrder(productOrder, commonInvoiceData)
    case OrderType.SINGLE_FUNDS_LOAD:
      return processFundingOrder(productOrder, commonInvoiceData)
    default:
      return processDefaultOrder(productOrder, commonInvoiceData)
  }
}

function getCommonInvoiceData(productOrder: ProductOrderWithRelations) {
  return {
    orderType: productOrder.orderType,
    orderNumber: productOrder.orderNumber,
    orderStatus: productOrder.orderStatus,
    organizationName: productOrder.organization?.name,
    billingAddress: productOrder.billingAddress,
    city: productOrder.city,
    country: productOrder.country,
    postcode: productOrder.postCode,
    paymentMethod: productOrder.paymentMethod,
    purchaseOrderNumber: productOrder.purchaseOrderNumber,
    invoiceDate: productOrder.createdAt,
    contactPerson: productOrder.createdByLiveAgent
      ? EPAY_LIVE_AGENT
      : `${productOrder.user.firstName} ${productOrder.user.lastName}`,
    emailAddress: productOrder.createdByLiveAgent
      ? EPAY_SUPPORT_EMAIL
      : productOrder.user.email,
    gstNumber: EPAY_GST_NUMBER,
    orderTotal: centsToDollars(productOrder.orderTotal),
    creditCardFee: centsToDollars(productOrder.creditCardFee),
    shippingTotal: centsToDollars(productOrder.shippingTotal),
    discountTotal: centsToDollars(
      productOrder.discountTotal! - productOrder.loadingFeeDiscountTotal!
    ),
    loadFeeDiscountTotal: centsToDollars(productOrder.loadingFeeDiscountTotal),
    gstAmount: centsToDollars(productOrder.gstAmount),
    subTotal: centsToDollars(productOrder.subTotal),
    authorizedByEmail: productOrder.authorizedByEmail,
    authorizedByName: productOrder.authorizedByName,
  }
}

function processStockOrder(
  productOrder: ProductOrderWithRelations,
  commonInvoiceData: ReturnType<typeof getCommonInvoiceData>
) {
  const stockCardInvoiceItems = productOrder.productOrderItems.map((item) => ({
    productCode: item.productCode,
    productName: item.product.name,
    quantity: item.quantity,
    resolution: (item.options as { resolution?: 'LOW' | 'HIGH' })?.resolution,
    discount: centsToDollars(item.discount || 0),
    printItemPrice: centsToDollars(item.itemFee || 0),
  }))

  return okAsync({
    ...commonInvoiceData,
    stockCardInvoiceItems,
    totalQuantity: productOrder.totalQuantity,
  })
}

function processFundingOrder(
  productOrder: ProductOrderWithRelations,
  commonInvoiceData: ReturnType<typeof getCommonInvoiceData>
) {
  const invoiceItems = aggregateFundingInvoiceLines({
    productOrderItems: productOrder.FundingProductOrderItem,
  })

  return okAsync({
    ...commonInvoiceData,
    loadingFeeTotal: centsToDollars(productOrder.loadingFeeTotal),
    digitalFeeTotal: centsToDollars(productOrder.digitalFeeTotal),
    invoiceItems,
  })
}

function processDefaultOrder(
  productOrder: ProductOrderWithRelations,
  commonInvoiceData: ReturnType<typeof getCommonInvoiceData>
) {
  const invoiceItems = aggregateInvoiceLines({
    productOrderItems: productOrder.productOrderItems,
  })

  return okAsync({
    ...commonInvoiceData,
    loadingFeeTotal: centsToDollars(productOrder.loadingFeeTotal),
    digitalFeeTotal: centsToDollars(productOrder.digitalFeeTotal),
    invoiceItems,
  })
}

export function getShippingDetails(orderNumber: string, orgId: string) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: { orderNumber, organizationId: orgId },
      select: {
        scheduledDate: true,
        productOrderItems: {
          select: {
            productCode: true,
            quantity: true,
            unitPrice: true,
            recipientName: true,
            recipientAddress: true,
            recipientAddressLine2: true,
            recipientSuburb: true,
            recipientCity: true,
            recipientPostCode: true,
            recipientEmail: true,
            deliveryMethod: true,
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.info(`Error getting order [${orderNumber}]`, error)

      return 'DATABASE_ERROR'
    }
  ).map((productOrder) => {
    if (!productOrder) {
      return err('NOT_FOUND' as const)
    }

    const shippingDetails = productOrder.productOrderItems.map((item) => {
      return {
        productName: item.product?.name,
        productCode: item.productCode,
        quantity: item.quantity,
        faceValue: centsToDollars(item.unitPrice),
        recipientName: item.recipientName,
        address: [
          item.recipientAddress,
          item.recipientAddressLine2,
          item.recipientSuburb,
          item.recipientCity,
          item.recipientPostCode,
        ]
          .filter(Boolean)
          .join(' '),
        email: item.recipientEmail,
        deliveryMethod: item.deliveryMethod,
      }
    })

    return {
      scheduledDate: productOrder.scheduledDate,
      shippingDetails,
    }
  })
}

type CsvItem = {
  productCode: number
  productName: string
  productImageUrl: string
  recipientName: string | null
  quantity: number | null
  value: number | null
  customerReference?: string | null
  rowNumber: number
  deliveryMethod:
    | {
        type: 'COURIER'
        address: string | null
        addressLine2: string | null
        suburb: string | null
        city: string | null
        postcode: string | null
      }
    | { type: 'EMAIL'; email: string | null }
    | {
        type: 'PICKUP'
      }
    | null
  email: string
  message: string
} | null

type PartialProduct = Pick<
  Product,
  | 'id'
  | 'name'
  | 'productCode'
  | 'cardTypes'
  | 'fixedValues'
  | 'details'
  | 'conditions'
  | 'redeem'
  | 'deliveryMethods'
  | 'organizationId'
  | 'loadingFee'
  | 'digitalFee'
> & {
  designUrl: string
  logoUrl: string | undefined
  min: number | null
  max: number | null
  discount: number
}

// Separate virtual cards into its own item if their quantity is more than 1
function splitVirtualCards(
  items: VerifyProps['items'],
  productMap: Record<number, PartialProduct>
) {
  return items.reduce((acc, item) => {
    const isVirtual = productMap?.[item.productCode!]?.cardTypes.includes(
      CardType.VIRTUAL
    )
    if (isVirtual) {
      const qty = item.quantity || 0
      for (let i = 0; i < qty; i++) {
        acc.push({
          ...item,
          quantity: 1,
        })
      }
    } else {
      acc.push(item)
    }
    return acc
  }, [] as VerifyProps['items'])
}

export function verifyCsvProductOrder({
  orgId,
  items,
  recipientSettings,
}: { orgId: string } & VerifyProps) {
  const productCodes = items.reduce<Set<number>>((acc, item) => {
    if (item.productCode) {
      acc.add(item.productCode)
    }
    return acc
  }, new Set())

  return fromPromise(
    prisma.product.findMany({
      where: {
        OR: [
          {
            productCode: {
              in: Array.from(productCodes),
            },
            organizationId: null,
          },
          {
            productCode: {
              in: Array.from(productCodes),
            },
            organizationId: orgId,
          },
        ],
      },
      include: {
        design: true,
        logo: true,
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to find products with codes [${productCodes}]`, e)

      return 'DATABASE_ERROR'
    }
  )
    .andThen((products) => {
      const productMap = products.reduce<Record<number, PartialProduct>>(
        (acc, product) => {
          acc[product.productCode] = {
            id: product.id,
            name: product.name,
            productCode: product.productCode,
            designUrl: product.design.cardDesignUrl,
            logoUrl: product.logo?.logoUrl,
            cardTypes: product.cardTypes,
            fixedValues: product.fixedValues.map((v) => centsToDollars(v)),
            min: centsToDollars(IS_PROD ? product.minValue : TESTING_MIN_VALUE),
            max: centsToDollars(product.maxValue),
            details: product.details,
            conditions: product.conditions,
            redeem: product.redeem,
            deliveryMethods: product.deliveryMethods,
            organizationId: product.organizationId,
            loadingFee: centsToDollars(product.loadingFee),
            digitalFee: centsToDollars(product.digitalFee),
            discount: orgId ? centsToDollars(45) : 0,
          }

          return acc
        },
        {}
      )

      return ok({ productMap })
    })
    .map(({ productMap }) => {
      const splitItems = splitVirtualCards(items, productMap)

      const { orderItems, errorRows } = splitItems.reduce<{
        orderItems: (CsvItem | null)[]
        errorRows: number[]
      }>(
        (acc, item) => {
          if (
            !item ||
            Object.values(item).every((val) => val === null || val === '')
          ) {
            return acc
          }

          const product = item.productCode ? productMap[item.productCode] : null

          if (!product) {
            if (Object.values(item).some((val) => val !== null && val !== '')) {
              acc.errorRows.push(item.rowNumber)
              acc.orderItems.push(null)
            }
            return acc
          }

          const isVirtual = product.cardTypes.includes(CardType.VIRTUAL)

          // Trim email if present
          const email = item.email ? item.email.trim() : ''

          const orderItem: CsvItem = {
            productCode: product.productCode,
            productName: product.name,
            productImageUrl: product.designUrl,
            recipientName: item.recipientName ?? null,
            quantity: isValidQuantity(item.quantity),
            value: isValidStoreValue({
              dollars: item.storeValue,
              minValue: dollarsToCents(product.min),
              maxValue: dollarsToCents(product.max),
              fixedValues: product.fixedValues.map((value) =>
                dollarsToCents(value)
              ),
            })
              ? item.storeValue!
              : null,
            deliveryMethod: getDeliveryMethod({ item, product }),
            email: email,
            message: (() => {
              if (!item.message) return ''
              return isVirtual
                ? item.message.slice(0, 255)
                : item.message.slice(0, 25)
            })(),
            customerReference: item.customerReference ?? null,
            rowNumber: item.rowNumber,
          }

          const validDeliveryMethod = deliverySchema.safeParse(
            orderItem.deliveryMethod
          )

          if (
            (recipientSettings === 'HAS_RECIPIENT' &&
              !orderItem.recipientName) ||
            !orderItem.quantity ||
            !orderItem.value ||
            !validDeliveryMethod.success ||
            (email && !validateEmail(email))
          ) {
            acc.errorRows.push(item.rowNumber)
          }
          acc.orderItems.push(orderItem)

          return acc
        },
        {
          orderItems: [],
          errorRows: [],
        }
      )

      return {
        orderItems,
        errorRows,
        productMap,
      }
    })
}

function getDeliveryMethod({
  item,
  product,
}: {
  item: VerifyProps['items'][number]
  product: PartialProduct
}) {
  const deliveryMethod = item.deliveryMethod?.trim()?.toUpperCase()

  switch (deliveryMethod) {
    case DeliveryMethod.COURIER: {
      if (product.deliveryMethods.includes(DeliveryMethod.COURIER)) {
        return {
          type: DeliveryMethod.COURIER,
          address: item.address ?? null,
          addressLine2: item.addressLine2 ?? null,
          suburb: item.suburb ?? null,
          city: item.city ?? null,
          postcode: item.postcode ?? null,
        }
      }
      return null
    }

    case DeliveryMethod.EMAIL: {
      if (product.deliveryMethods.includes(DeliveryMethod.EMAIL)) {
        return {
          type: DeliveryMethod.EMAIL,
          email: item.email ?? null,
        }
      }
      return null
    }

    case DeliveryMethod.PICKUP: {
      if (product.deliveryMethods.includes(DeliveryMethod.PICKUP)) {
        return {
          type: DeliveryMethod.PICKUP,
        }
      }
      return null
    }

    default:
      return null
  }
}

export function cancelProductOrder({
  orgId,
  orderNumber,
}: {
  orgId: string
  orderNumber: string
}) {
  logger.info(`cancelling order [${orderNumber}] for org [${orgId}]`)
  return fromPromise(
    prisma.productOrder.updateMany({
      where: {
        orderNumber,
        organizationId: orgId,
        orderStatus: {
          in: [OrderStatus.SUBMITTED, OrderStatus.PENDING],
        },
      },
      data: {
        orderStatus: OrderStatus.CANCELLED,
      },
    }),
    (error) => {
      logger.warn(`Error cancelling order ${orderNumber} for ${orgId} `, error)

      return ERRORS.DATABASE_ERROR
    }
  ).andThen(({ count }) => {
    if (count === 1) {
      logger.info(`Order ${orderNumber} for ${orgId} cancelled`)
      return ok({ orderStatus: OrderStatus.CANCELLED })
    } else {
      logger.warn(
        `Error updating order ${orderNumber} for ${orgId} to CANCELLED`
      )
      return err({
        code: ERRORS.NOT_FOUND.code,
        message: `Error updating order ${orderNumber} for ${orgId} to CANCELLED`,
      })
    }
  })
}

export function calculateFeeDiscount({
  orgDiscount,
  globalDiscount,
}: {
  orgDiscount: number | null
  globalDiscount: number
}) {
  const discount = orgDiscount ?? 0
  if (discount > globalDiscount) {
    return discount
  } else {
    return globalDiscount
  }
}

export function cancelOrderWithNote({
  orderId,
  noteMessage,
  noteTitle,
  userId,
}: {
  orderId: string
  noteMessage: string
  noteTitle?: string
  userId: string
}) {
  return updateProductOrderById(orderId, {
    orderStatus: OrderStatus.CANCELLED,
    cancelledById: userId,
    cancelledByDate: new Date(),
  }).andThen(() => {
    return createProductOrderNote({
      productOrderId: orderId,
      message: noteMessage,
      status: ProductOrderNoteStatus.CANCELLED,
      title: noteTitle,
      userId,
    })
  })
}

export function holdOrderWithNote({
  orderId,
  noteMessage,
  noteTitle,
  userId,
}: {
  orderId: string
  noteMessage: string
  noteTitle?: string
  userId: string
}) {
  return updateProductOrderById(orderId, {
    orderStatus: OrderStatus.ON_HOLD,
  }).andThen(() => {
    return createProductOrderNote({
      productOrderId: orderId,
      message: noteMessage,
      status: ProductOrderNoteStatus.ON_HOLD,
      title: noteTitle,
      userId,
    })
  })
}

export function resumeOrder(orderId: string) {
  // Admin can only hold/resume orders when user has submitted their order
  return updateOrderStatus(orderId, OrderStatus.SUBMITTED)
}

export function updateProductOrderItemsWithoutRecipients(
  productOrderItems: ProductOrderItem[]
) {
  const filtered = productOrderItems.filter((item) => !item.recipientName)
  const batchNameMap = productOrderItems
    .filter((item) => item.deliveryRecipient)
    .reduce((acc, item) => {
      acc[item.deliveryBatchId!] = item.recipientName
      return acc
    }, {} as Record<string, string>)

  const promises = filtered.reduce((acc, item) => {
    const batchName = batchNameMap[item.deliveryBatchId!]

    if (batchName) {
      acc.push(
        prisma.productOrderItem.update({
          where: { id: item.id },
          data: {
            recipientName: batchName,
          },
        })
      )
    }

    return acc
  }, [] as Promise<ProductOrderItem>[])

  return fromPromise(Promise.all(promises), (error) => {
    logger.warn(`Error updating product order items without recipients`, error)
    return ERRORS.DATABASE_ERROR
  })
}

export function getLatestProductOrderDetailsAndSendToEpay(orderNumber: string) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        orderNumber,
      },
      include: {
        productOrderItems: true,
      },
    }),
    (error) => {
      logger.warn(
        `failed to find product order with order number [${orderNumber}]`,
        error
      )
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      return err(ERRORS.NOT_FOUND)
    }

    return sendOrderDetail(productOrder).map(() => {
      return {
        releaseDate: productOrder.submittedAt,
        orderStatus: productOrder.orderStatus,
        releasedBy: productOrder.releasedBy,
        paymentDate: productOrder.paymentDate,
      }
    })
  })
}

export function tryCreateStockCardOrder({
  userId,
  stockCardOrderItems,
  orgId,
}: {
  userId: string
  stockCardOrderItems: StockCardOrder
  orgId: string
}) {
  //find products for stock card order items
  const productCodeList = stockCardOrderItems.map((item) => item.productCode)

  return createStockProductMap({
    stockCardOrderItems: productCodeList,
    orgId,
  })
    .andThen((productMap) => {
      const stockDeliveryMap = accumulateStockDeliveries(stockCardOrderItems)
      return calcStockShippingTotal(stockDeliveryMap).map((shippingTotal) => ({
        productMap: productMap.productMap,
        stockDeliveryMap,
        shippingTotal,
      }))
    })
    .andThen((data) => {
      return getGlobalDiscounts().map((globalDiscount) => ({
        ...data,
        globalDiscount,
      }))
    })
    .andThen(
      ({ productMap, stockDeliveryMap, shippingTotal, globalDiscount }) => {
        const productOrderItems = createStockOrderItems(
          stockCardOrderItems,
          productMap,
          stockDeliveryMap,
          globalDiscount
        )

        const orderTotals = calculateOrderTotals(
          productOrderItems,
          shippingTotal
        )

        return createStockCardProductOrder(
          userId,
          orgId,
          orderTotals,
          productOrderItems
        )
      }
    )
    .andThen((productOrder) => {
      const orderNumber = productOrder.orderIncrementNumber.toString()

      return createUniqueProductOrderNumber({
        orderNumber,
        id: productOrder.id,
      }).map((order) => {
        return {
          orderNumber: order.orderNumber,
        }
      })
    })
}

function calculateOrderTotals(
  productOrderItems: Omit<Prisma.ProductOrderItemCreateInput, 'productOrder'>[],
  shippingTotal: { shippingTotal: number; shippingFeeDiscountTotal: number }
) {
  const subTotal = productOrderItems.reduce(
    (total, item) => total + (item.itemFee || 0) * item.quantity,
    0
  )
  const totalQuantity = productOrderItems.reduce(
    (total, item) => total + item.quantity,
    0
  )

  const totalItemDiscount = productOrderItems.reduce(
    (total, item) => total + (item?.discount || 0),
    0
  )

  const discountTotal =
    totalItemDiscount + shippingTotal.shippingFeeDiscountTotal
  // Calculate GST
  const shippingGst = getGst(
    shippingTotal.shippingTotal - shippingTotal.shippingFeeDiscountTotal
  )

  const itemsGst = getGst(subTotal - totalItemDiscount)

  const totalGst = Math.round(shippingGst + itemsGst)

  // Calculate final order total including GST
  const baseTotal = subTotal + shippingTotal.shippingTotal - discountTotal

  const creditCardFee = Math.round(baseTotal * CREDIT_CARD_SURCHARGE) // This surcharge gets subtracted at the end when order is finalised

  const orderTotal = baseTotal + creditCardFee

  return {
    subTotal,
    totalQuantity,
    shippingTotal: shippingTotal.shippingTotal,
    gstAmount: totalGst,
    orderTotal,
    discountTotal,
    creditCardFee,
    shippingFeeDiscountTotal: shippingTotal.shippingFeeDiscountTotal,
  }
}

export type StockProduct = Product & {
  design: Pick<CardDesign, 'externalCardDesignId'>
  extraDesign: Pick<CardDesign, 'externalCardDesignId'> | null
  logo: Pick<CardLogo, 'logoUrl' | 'name'> | null
}

export function createStockProductMap({
  stockCardOrderItems,
  orgId,
}: {
  stockCardOrderItems: number[]
  orgId: string
}) {
  logger.info(`Creating product map for stock card order items`)

  const productCodes = stockCardOrderItems.reduce<Set<number>>((acc, item) => {
    acc.add(item)
    return acc
  }, new Set())

  return fromPromise(
    prisma.product.findMany({
      where: {
        AND: [
          {
            productCode: {
              in: Array.from(productCodes),
            },
          },
          {
            type: ProductType.STOCK,
          },
          {
            OR: [{ organizationId: null }, { organizationId: orgId }],
          },
        ],
      },
      include: {
        design: {
          select: {
            externalCardDesignId: true,
          },
        },
        extraDesign: {
          select: {
            externalCardDesignId: true,
          },
        },
        logo: {
          select: {
            logoUrl: true,
            name: true, // Added the name field here
          },
        },
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(
        `Failed to find stock products with codes [${Array.from(
          productCodes
        ).join(', ')}]`,
        e
      )
      return 'DATABASE_ERROR'
    }
  ).andThen((products) => {
    if (products.length === 0) {
      logger.warn(
        `No stock products found with codes [${Array.from(productCodes).join(
          ', '
        )}]`
      )
      return err({
        errorType: 'INVALID_PRODUCT_ORDER' as const,
        data: 'Invalid product codes for stock cards',
      })
    }

    const productMap = products.reduce<Record<number, StockProduct>>(
      (acc, product) => {
        acc[product.productCode] = product as StockProduct
        return acc
      },
      {}
    )

    // Validate that all required products are found
    const missingProducts = Array.from(productCodes).filter(
      (code) => !productMap[code]
    )
    if (missingProducts.length > 0) {
      logger.warn(
        `Some stock products not found: [${missingProducts.join(', ')}]`
      )
      return err({
        errorType: 'INVALID_PRODUCT_ORDER' as const,
        data: `Missing stock products: ${missingProducts.join(', ')}`,
      })
    }

    return ok({ productMap })
  })
}

// temporary function to get the courier key try and use existing function in the future
function accumulateStockDeliveries(
  stockOrderItems: StockCardOrder
): CourierDeliveryMap {
  let counter = 1
  const result = stockOrderItems.reduce<CourierDeliveryMap>((acc, item) => {
    const key = getCourierKey(item.deliveryMethod)
    if (acc[key]) {
      acc[key].quantity += item.quantity
    } else {
      acc[key] = {
        deliveryMethod: item.deliveryMethod,
        quantity: item.quantity,
        deliveryBatchId: `${counter}`,
      }
      counter += 1
    }
    return acc
  }, {})

  logger.debug('accumulateStockDeliveries', result)
  return result
}

function calcStockShippingTotal(courierDeliveryMap: CourierDeliveryMap) {
  logger.info('Calculating shipping total for stock card order')

  const shippingCostsPromises = Object.values(courierDeliveryMap).map(
    ({ deliveryMethod, quantity }) => {
      return getShippingCosts(deliveryMethod).map((cost) => {
        const multiplier = Math.ceil(quantity / 60) // Assuming 200 cards per package for stock orders
        return {
          cost: multiplier * cost,
          discount: 0, // No discount for stock orders, but we keep the structure for consistency
        }
      })
    }
  )

  return ResultAsync.combineWithAllErrors(shippingCostsPromises).map(
    (results) => {
      const totalShippingFee = results.reduce((acc, { cost }) => acc + cost, 0)
      const totalShippingFeeDiscount = results.reduce(
        (acc, { discount }) => acc + discount,
        0
      )

      return {
        shippingTotal: totalShippingFee,
        shippingFeeDiscountTotal: totalShippingFeeDiscount,
      }
    }
  )
}

function createStockOrderItems(
  stockCardOrderItems: StockCardOrder,
  productMap: Record<number, StockProduct>,
  courierDeliveryMap: CourierDeliveryMap,
  globalDiscount: GlobalDiscount
): Omit<Prisma.ProductOrderItemCreateInput, 'productOrder'>[] {
  return stockCardOrderItems.map((item) => {
    const product = productMap[item.productCode]
    const deliveryMethod = item.deliveryMethod
    const key = getCourierKey(deliveryMethod)

    const isLowResolution = item.resolution === 'LOW'
    const itemFee = isLowResolution ? product.option1Fee : product.option2Fee

    const options = {
      resolution: item.resolution,
      externalCardDesignId: isLowResolution
        ? product.design.externalCardDesignId
        : product.extraDesign?.externalCardDesignId,
    }

    const orderItemsDiscount = calculateFeeDiscount({
      globalDiscount:
        item.resolution === 'LOW'
          ? globalDiscount.lowResStockCard
          : globalDiscount.highResStockCard,
      orgDiscount: 0,
    })

    const orderItemCreateData: Omit<
      Prisma.ProductOrderItemCreateInput,
      'productOrder'
    > = {
      productCode: product.productCode,
      deliveryMethod: 'COURIER',

      quantity: item.quantity,
      unitPrice: 0, // Always 0 for stock cards

      itemFee,
      options,
      discount: orderItemsDiscount * item.quantity,
      recipientName: '',
      recipientAddress: deliveryMethod.address,
      recipientSuburb: deliveryMethod.suburb,
      recipientPostCode: deliveryMethod.postcode,
      recipientCity: deliveryMethod.city,
      recipientCountry: 'NZ',

      deliveryBatchId: courierDeliveryMap[key]?.deliveryBatchId,

      product: {
        connect: {
          id: product.id,
        },
      },
    }

    return orderItemCreateData
  })
}

function createStockCardProductOrder(
  userId: string,
  orgId: string,
  orderTotals: ReturnType<typeof calculateOrderTotals>,
  productOrderItems: Omit<Prisma.ProductOrderItemCreateInput, 'productOrder'>[]
): ResultAsync<ProductOrder, ErrorResponse> {
  logger.debug('createStockCardProductOrder', productOrderItems)
  return fromPromise(
    prisma.productOrder.create({
      data: {
        orderType: OrderType.STOCK,
        orderStatus: OrderStatus.PENDING,
        userId,
        organizationId: orgId,
        subTotal: orderTotals.subTotal,
        totalQuantity: orderTotals.totalQuantity,
        shippingTotal: orderTotals.shippingTotal,
        gstAmount: orderTotals.gstAmount,
        orderTotal: orderTotals.orderTotal,
        discountTotal: orderTotals.discountTotal, // Explicitly map discountTotal
        shippingFeeDiscountTotal: orderTotals.shippingFeeDiscountTotal,
        creditCardFee: orderTotals.creditCardFee,
        productOrderItems: {
          create: productOrderItems,
        },
      },
      include: {
        productOrderItems: true,
      },
    }),
    (error) => {
      logger.error(error, 'Failed to create stock card order')
      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: 'Failed to create stock card order',
      }
    }
  )
}

type StockOrderItem = {
  productCode: number
  productName: string
  quantity: number
  resolution: 'LOW' | 'HIGH'
  discount: number
  printItemPrice: number
}

export function getStockCardOrderPaymentSummary({
  orderNumber,
}: {
  orderNumber: string
}) {
  return getProductOrder({ orderNumber }).andThen((productOrder) => {
    if (!productOrder) {
      return errAsync(ERRORS.NOT_FOUND)
    }

    const productOrderItems: StockOrderItem[] =
      productOrder.productOrderItems.map((item) => {
        return {
          productCode: item.productCode,
          quantity: item.quantity,
          productName: item.product?.name ?? '',
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore - options object is not typed
          resolution: item?.options?.resolution,
          discount: centsToDollars(item.discount),
          printItemPrice: centsToDollars(item.itemFee),
        }
      })

    const recipientNamesByDeliveryBatchId = getRecipientNamesForBatchDeliveryId(
      {
        productOrderItems: productOrder.productOrderItems,
      }
    )

    return okAsync({
      orderType: productOrder.orderType,
      orderNumber: productOrder.orderNumber,
      subTotal: centsToDollars(productOrder.subTotal),
      shippingTotal: centsToDollars(productOrder.shippingTotal),
      digitalFeeDiscountTotal: centsToDollars(
        productOrder.digitalFeeDiscountTotal
      ),
      loadingFeeDiscountTotal: centsToDollars(
        productOrder.loadingFeeDiscountTotal
      ),
      shippingFeeDiscountTotal: centsToDollars(
        productOrder.shippingFeeDiscountTotal
      ),
      discountTotal: centsToDollars(productOrder.discountTotal),
      gstAmount: centsToDollars(productOrder.gstAmount),
      orderTotal: centsToDollars(productOrder.orderTotal),
      totalQuantity: productOrder.totalQuantity,
      recipientNamesByDeliveryBatchId,
      stockCardInvoiceItems: productOrderItems,
      creditCardFee: centsToDollars(productOrder.creditCardFee),
      cardDesignUrl:
        productOrder.productOrderItems[0].product?.design.cardDesignUrl,
      logoUrl: productOrder.productOrderItems[0].product?.logo?.logoUrl,
    })
  })
}

export function submitPaymentForStockCardOrder({
  billingAddress,
  city,
  country,
  orderNumber,
  paymentMethod,
  postcode,
  purchaseOrderNumber,
  selectedDeliveryRecipients,
  isLiveAgent,
  authorizedByEmail,
  authorizedByName,
}: SubmitStockCardOrderProps) {
  return getProductOrder({ orderNumber })
    .andThen((productOrder) => {
      if (!productOrder) {
        return errAsync(ERRORS.NOT_FOUND)
      }

      return setWhoParcelIsAddressedTo({ selectedDeliveryRecipients })
        .andThen(() =>
          updateProductOrderItemsWithoutRecipients(
            productOrder.productOrderItems
          )
        )
        .andThen(() =>
          updateOrderWithPaymentDetails({
            orgId: productOrder.organizationId,
            orderNumber,
            billingAddress,
            city,
            country,
            paymentMethod,
            postcode,
            purchaseOrderNumber,
            lockCode: NO_LOCK_CODE,
            creditCardFee: productOrder.creditCardFee,
            orderTotal: productOrder.orderTotal!,
            isLiveAgent,
            authorizedByEmail,
            authorizedByName,
          })
        )
        .map(() => productOrder)
    })
    .andThen((productOrder) => {
      if (paymentMethod === PaymentMethod.CREDIT_CARD) {
        return createPaymentSession({ orderNumber })
      } else {
        // Send email notifications for bank transfer
        sendEmailWithTemplate({
          email: productOrder.user.email,
          templateId: CUSTOMER_DIRECT_CREDIT_ORDER_SUBMITTED,
          templateModel: {
            orderNumber,
            firstName: productOrder.user.firstName,
            orderTotal: centsToDollars(productOrder.orderTotal),
          },
        })

        sendEmailWithTemplate({
          email: EPAY_ADMIN_EMAIL,
          templateId: EPAY_ORDER_DIRECT_CREDIT,
          templateModel: {
            orderNumber,
            orderTotal: centsToDollars(productOrder.orderTotal),
            companyName: productOrder.organization.name,
          },
        })

        return okAsync({
          orderStatus: OrderStatus.SUBMITTED,
          redirect: undefined,
        })
      }
    })
}
