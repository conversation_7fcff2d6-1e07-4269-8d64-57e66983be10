import {
  CardType,
  DeliveryMethod,
  DesignType,
  OrderStatus,
  Prisma,
  Product,
  ProductStatus,
  ProductType,
} from '@prisma/client'
import {
  ResultAsync,
  err,
  errAsync,
  fromPromise,
  ok,
  okAsync,
} from 'neverthrow'
import type { ProductListProps } from 'routes/product.route'

import { ERRORS, ErrorObject, PickErrorCodeKey } from 'utils/error'
import logger from 'utils/logger'
import { centsToDollars } from 'utils/numeric'
import { prisma } from 'utils/prisma'

import {
  createCardLogo,
  createNewProductWithLogo,
} from './custom-product.service'

import { CUSTOMER_LOGO_APPROVED } from '../helpers/email-templates'
import { sendEmailWithTemplate } from './postmark.service'

import {
  CONDITIONS,
  LOAD_FEE,
  MAX_ORDER_ITEM_QUANTITY,
  MAX_VALUE,
  MIN_VALUE,
  OPTION_1_FEE,
  OPTION_2_FEE,
  STOCK_DETAILS,
  TESTING_MIN_VALUE,
} from 'constants/prezzy'
import { getCustomerOrderReferenceForOrg } from 'data/organization.data'
import { IS_LOCAL, IS_PROD } from 'utils/config'
import { findCardDesign, getHighAndLowResDesignIds } from './card.service'
import { calculateFeeDiscount, getOrgDiscount } from './product-order.service'
import { getGlobalDiscounts } from 'data/discount.data'
import { createBatchOrder } from 'external-apis/ren/card-batch-order.api'
import {
  getCardDesignList,
  getHighAndLowResDesigns,
} from 'data/card-design.data'
import { orderCardsFromSupplierByWindcaveSessionId } from './product-order/order-cards.service'
import {
  UpdatedProductStatusResult,
  updateProductToApproved,
} from 'data/product.data'
import { errorCodes, SimpleError } from 'types/simple-error'
import {
  updateCustomProductOrderError,
  updateCustomProductOrderWithExternalBatchId,
} from 'data/custom-product-order.data'

export function findProductsForOrg({
  filter,
  orgId,
  page,
  pageSize,
  search,
}: ProductListProps & { orgId?: string }) {
  let filters: CardType[] = ['PHYSICAL', 'VIRTUAL']
  if (filter === 'Physical') {
    filters = ['PHYSICAL']
  } else if (filter === 'Virtual') {
    filters = ['VIRTUAL']
  }

  const whereBase: Prisma.ProductWhereInput = {
    name: {
      contains: search,
      mode: 'insensitive',
    },
    cardTypes: {
      hasSome: filters,
    },
    type: {
      not: ProductType.STOCK,
    },
  }

  const where: Prisma.ProductWhereInput =
    filter === 'Personalized'
      ? {
          ...whereBase,
          organizationId: orgId,
          status: ProductStatus.APPROVED,
        }
      : {
          OR: [
            {
              ...whereBase,
              organizationId: orgId,
              status: ProductStatus.APPROVED,
            },
            {
              ...whereBase,
              organizationId: null,
              status: ProductStatus.APPROVED,
            },
          ],
        }

  const query = prisma.product.findMany({
    where,
    skip: (page - 1) * pageSize,
    take: pageSize,
    include: {
      design: true,
      logo: true,
    },
  })

  const countAll = prisma.product.count({
    where: {
      OR: [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
          organizationId: orgId,
          status: ProductStatus.APPROVED,
          type: { not: ProductType.STOCK },
        },
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
          organizationId: null,
          status: ProductStatus.APPROVED,
          type: { not: ProductType.STOCK },
        },
      ],
    },
  })

  const countCustom = prisma.product.count({
    where: {
      name: {
        contains: search,
        mode: 'insensitive',
      },
      organizationId: orgId,
      status: ProductStatus.APPROVED,
      type: { not: ProductType.STOCK },
    },
  })

  const countPhysical = prisma.product.count({
    where: {
      OR: [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
          organizationId: orgId,
          cardTypes: {
            hasSome: 'PHYSICAL',
          },
          status: ProductStatus.APPROVED,
          type: { not: ProductType.STOCK },
        },
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
          organizationId: null,
          status: ProductStatus.APPROVED,
          cardTypes: {
            hasSome: 'PHYSICAL',
          },
          type: { not: ProductType.STOCK },
        },
      ],
    },
  })
  const countVirtual = prisma.product.count({
    where: {
      name: {
        contains: search,
        mode: 'insensitive',
      },
      cardTypes: {
        hasSome: 'VIRTUAL',
      },
      type: { not: ProductType.STOCK },
    },
  })

  return fromPromise(
    prisma.$transaction([
      query,
      countAll,
      countCustom,
      countPhysical,
      countVirtual,
    ]),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to find products for org with id [${orgId}]`, e)

      return 'DATABASE_ERROR'
    }
  ).map(([products, all, personalized, physical, virtual]) => {
    return {
      products: products.map((product) => {
        return {
          id: product.id,
          name: product.name,
          productCode: product.productCode,
          designUrl: product.design.cardDesignUrl,
          logoUrl: product.logo?.logoUrl,
          displayLogoUrl: product.displayLogoUrl,
          cardTypes: product.cardTypes,
          fixedValues: product.fixedValues.map((v) => centsToDollars(v)),
          min: centsToDollars(IS_PROD ? product.minValue : TESTING_MIN_VALUE),
          max: centsToDollars(product.maxValue),
          details: product.details,
          conditions: product.conditions,
          redeem: product.redeem,
          deliveryMethods: product.deliveryMethods,
          organizationId: product.organizationId,
          loadingFee: centsToDollars(product.loadingFee),
          digitalFee: centsToDollars(product.digitalFee),
          discount: orgId ? centsToDollars(45) : 0,
          isAvailable: product.design.available,
          ribbonColor: product.design.ribbonColor,
        }
      }),
      all,
      personalized,
      physical,
      virtual,
    }
  })
}

export function findProductsForOrgCSV({ orgId }: { orgId: string }) {
  return fromPromise(
    prisma.product.findMany({
      where: {
        OR: [
          {
            organizationId: orgId,
            status: ProductStatus.APPROVED,
            type: { not: ProductType.STOCK },
          },
          {
            organizationId: null,
            status: ProductStatus.APPROVED,
            type: { not: ProductType.STOCK },
          },
        ],
      },
      select: {
        productCode: true,
        name: true,
        deliveryMethods: true,
        minValue: true,
        maxValue: true,
        fixedValues: true,
        organizationId: true,
      },
      orderBy: {
        productCode: 'asc',
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to find products for org with id [${orgId}]`, e)

      return 'DATABASE_ERROR'
    }
  )
}

function findProductForOrg({
  productCode,
  orgId,
}: {
  productCode: number
  orgId?: string
}) {
  return fromPromise(
    prisma.product.findFirst({
      where: {
        OR: [
          {
            productCode,
            organizationId: null,
          },
          {
            productCode,
            organizationId: orgId,
          },
        ],
      },
      include: {
        design: true,
        logo: true,
      },
    }),
    (e) => {
      logger.warn(
        `failed to query product with code [${productCode}] for org [${orgId}]`
      )

      return 'DATABASE_ERROR'
    }
  ).andThen((product) => {
    if (!product) {
      return err({
        code: ERRORS.NOT_FOUND.code,
        message: `Product with code ${productCode} not found for org ${orgId}`,
      })
    }

    return ok(product)
  })
}

export function findProductWithDiscountForOrg({
  productCode,
  orgId,
}: {
  productCode: number
  orgId: string
}) {
  return ResultAsync.combine([
    getOrgDiscount(orgId),
    findProductForOrg({ productCode, orgId }),
    getCustomerOrderReferenceForOrg(orgId),
    getGlobalDiscounts(),
  ]).map(
    ([discount, product, hasCustomerOrderReferenceEnabled, globalDiscount]) => {
      logger.debug('org product', product)
      const {
        id,
        name,
        design,
        logo,
        cardTypes,
        fixedValues,
        minValue,
        maxValue,
        details,
        conditions,
        redeem,
        deliveryMethods,
        organizationId,
        loadingFee,
        digitalFee,
        displayLogoUrl,
      } = product

      const loadingFeeDiscount = calculateFeeDiscount({
        orgDiscount: discount.loadingFee,
        globalDiscount: globalDiscount.loadingFee,
      })

      return {
        id,
        name,
        productCode,
        designUrl: design.cardDesignUrl,
        orientation: design.cardDirection,
        logoUrl: displayLogoUrl ? displayLogoUrl : logo?.logoUrl,
        cardTypes,
        fixedValues: fixedValues.map(centsToDollars),
        min: centsToDollars(IS_PROD ? minValue : TESTING_MIN_VALUE),
        max: centsToDollars(maxValue),
        details,
        conditions,
        redeem,
        deliveryMethods,
        organizationId,
        loadingFee: centsToDollars(loadingFee),
        digitalFee: centsToDollars(digitalFee),
        discount: centsToDollars(loadingFeeDiscount),
        hasCustomerOrderReferenceEnabled,
      }
    }
  )
}

export function productApproved({ logoFilename }: { logoFilename: string }) {
  const logoId = logoFilename.split('.')[0]

  return fromPromise(
    prisma.product.updateMany({
      where: {
        logo: {
          id: logoId,
        },
      },
      data: {
        status: 'APPROVED',
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(
        `failed to update product to approved with logo filename [${logoFilename}]`,
        e
      )

      return 'DATABASE_ERROR'
    }
  ).andThen(({ count }) => {
    if (count === 1) {
      return ok({ message: 'acknowledged' })
    }

    return err('NOT_FOUND' as const)
  })
}

function findOrgInCardLogoJoinTable({
  cPartnerLogoId,
  orgId,
}: {
  cPartnerLogoId: string
  orgId: string
}) {
  logger.info(
    `finding org in card logo join table with cPartnerLogoId: ${cPartnerLogoId} and orgId: ${orgId}`
  )

  return fromPromise(
    prisma.organizationCPartnerLogoJoinTable.findFirst({
      where: {
        cPartnerLogoId,
        organizationId: orgId,
        created: false,
      },
      include: {
        cPartnerLogo: true,
      },
    }),
    (e) => {
      logger.warn(
        `Failed to query join table orgId [${orgId}] cPartnerLogoId [${cPartnerLogoId}]`,
        e
      )

      return ERRORS.DATABASE_ERROR
    }
  )
}

export async function cPartnerCreateProduct({
  cPartnerLogoId,
  orgId,
  productName,
  designId,
}: {
  cPartnerLogoId: string
  orgId: string
  productName: string
  designId: string
}) {
  logger.info(`Starting cPartnerCreateProduct process`, {
    cPartnerLogoId,
    orgId,
    productName,
    designId,
  })

  const checkIfProductExists = await findCardDesign(designId)

  if (checkIfProductExists.isErr()) {
    logger.warn(`Design not found`, { designId })
    return errAsync({
      code: ERRORS.NOT_FOUND.code,
      message: `Design with id ${designId} not found`,
    })
  }

  const cPartnerLogoRes = await findOrgInCardLogoJoinTable({
    cPartnerLogoId,
    orgId,
  })

  if (cPartnerLogoRes.isErr()) {
    logger.warn(`CPartnerLogo not found for organization`, {
      cPartnerLogoId,
      orgId,
    })
    return errAsync({
      code: ERRORS.NOT_FOUND.code,
      message: `CPartnerLogo with id ${cPartnerLogoId} not found for org ${orgId}`,
    })
  }

  const cPartnerLogo = cPartnerLogoRes.value

  if (
    !cPartnerLogo?.cPartnerLogo.logoUrl ||
    !cPartnerLogo?.cPartnerLogo.externalCardLogoId
  ) {
    logger.warn(`Invalid CPartnerLogo data`, { cPartnerLogoId, orgId })
    return errAsync({
      code: ERRORS.NOT_FOUND.code,
      message: `CPartnerLogo with id ${cPartnerLogoId} not found for org ${orgId}`,
    })
  }

  // fixes CPartnerLogo logoUrl encoding
  let logoUrl = cPartnerLogo?.cPartnerLogo.logoUrl
  if (logoUrl) {
    logoUrl = encodeURI(logoUrl)
  }

  const cardLogo = await createCardLogo({
    logoUrl,
    orgId,
    logoFileName: `${cPartnerLogo?.cPartnerLogo.externalCardLogoId}`,
  })

  if (cardLogo.isErr()) {
    logger.error({ error: cardLogo.error }, `Failed to create card logo`)
    return errAsync({
      code: ERRORS.NOT_FOUND.code,
      message: `Failed to create card logo`,
    })
  }

  const product = await createNewProductWithLogo({
    designId,
    logoId: cardLogo.value.id,
    name: productName,
    orgId,
    cardStatus: ProductStatus.APPROVED,
  })

  if (product.isErr()) {
    logger.error({ error: product.error }, `Failed to create product`)
    return errAsync({
      code: ERRORS.NOT_FOUND.code,
      message: `Failed to create product`,
    })
  }

  logger.info(`Updating CPartnerLogo`, { cPartnerLogoId, orgId })
  const updateCPartnerLogoRes = await updateCPartnerLogo({
    cPartnerLogoId,
    orgId,
  })

  if (updateCPartnerLogoRes.isErr()) {
    logger.error(
      {
        error: updateCPartnerLogoRes.error,
      },
      `Failed to update cPartnerLogo`
    )
    return errAsync({
      code: ERRORS.NOT_FOUND.code,
      message: `Failed to update cPartnerLogo`,
    })
  }

  logger.info(`cPartnerCreateProduct process completed successfully`, {
    cPartnerLogoId,
    orgId,
    productName,
    designId,
  })
  return okAsync({ updateCPartnerLogoRes })
}

function updateCPartnerLogo({
  cPartnerLogoId,
  orgId,
}: {
  cPartnerLogoId: string
  orgId: string
}) {
  return fromPromise(
    prisma.organizationCPartnerLogoJoinTable.updateMany({
      where: {
        cPartnerLogoId,
        organizationId: orgId,
      },
      data: {
        created: true,
      },
    }),

    (err) => {
      logger.warn(
        `failed to update cPartnerLogo with id [${cPartnerLogoId}] for org [${orgId}]`,
        err
      )

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function updateProductStatusAfterSendingToPlacard(id: string) {
  return fromPromise(
    prisma.product.update({
      where: {
        id,
      },
      data: {
        status: 'SENT',
      },
    }),
    (error) => {
      logger.error(error, 'failed update product status to SENT')
      return {
        code: 'DB-100',
        message: 'Database operation failed',
      }
    }
  )
}

export function declineCustomProduct({
  productId,
  orderNumber,
}: {
  productId: string
  orderNumber: string
}) {
  return fromPromise(
    prisma.customProductOrder.findFirst({
      where: {
        orderNumber,
        productId,
      },
    }),
    (error) => {
      logger.warn('couldn"t find orderNumber with matching product ID', error)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((order) => {
    if (!order) {
      return err(ERRORS.NOT_FOUND)
    }

    return fromPromise(
      prisma.product.update({
        where: {
          id: productId,
        },
        data: {
          status: 'DECLINED',
        },
      }),
      (error) => {
        logger.warn('failed to decline custom product', error)
        return ERRORS.DATABASE_ERROR
      }
    )
  })
}

export function updateProductLogoUrl({
  productId,
  logoUrl,
  fileName,
}: {
  productId: string
  logoUrl: string
  fileName: string
}) {
  return fromPromise(
    prisma.product.update({
      where: {
        id: productId,
      },
      data: {
        logo: {
          update: {
            logoUrl,
            name: fileName,
          },
        },
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn('failed to update product logo url', error)
      return 'DATABASE_ERROR'
    }
  )
}

type UpdateCustomProductNameError = ErrorObject<'DATABASE_ERROR' | 'NOT_FOUND'>

export function updateCustomProductName({
  productId,
  productName,
}: {
  productId: string
  productName: string
}): ResultAsync<Product, UpdateCustomProductNameError> {
  // logger.infog(`updating name of product  with id: ${productId}`)

  return fromPromise<Product, UpdateCustomProductNameError>(
    prisma.product.update({
      where: {
        id: productId,
      },
      data: {
        name: productName,
      },
    }),

    (err) => {
      logger.warn('failed to update custom product name', err)
      return ERRORS.DATABASE_ERROR
    }
  )
}

function findLogoWithProduct({ logoName }: { logoName: string }) {
  return fromPromise(
    prisma.cardLogo.findUnique({
      where: {
        name: logoName,
      },
      include: {
        product: true,
      },
    }),
    (err) => {
      logger.error(err, `failed to find logo with name ${logoName}`)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `failed to find logo with name ${logoName}`,
      } as SimpleError
    }
  ).andThen((result) => {
    if (!result) {
      logger.warn(`logo not found with name: ${logoName}`)
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `logo not found with name: ${logoName}`,
      })
    }

    if (!result.product) {
      logger.warn(`product not found for logo with name: ${logoName}`)
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `product not found for logo with name: ${logoName}`,
      } as SimpleError)
    }

    return ok(result)
  })
}

function handleStockCardApproval(product: UpdatedProductStatusResult) {
  const order = product.customProductOrder[0]

  return getHighAndLowResDesigns().andThen((cardDesigns) => {
    if (IS_LOCAL) {
      logger.info('Skipping createBatchOrder in local environment')
      logger.inspect(cardDesigns)
      return ok(product)
    }

    return createBatchOrder({
      cardType: cardDesigns.highResDesign.externalCardType!,
      cardSubType: cardDesigns.highResDesign.externalCardDesignId!,
      recipientName: order.deliveryRecipientName ?? '',
      addressLine1: order.deliveryAddressLine1 ?? '',
      addressLine2: order.deliveryAddressLine2 ?? '',
      addressLine3: order.deliverySuburb ?? '',
      city: order.deliveryCity ?? '',
      postCode: order.deliveryPostcode ?? '',
      email: order.user.email ?? '',
      embossLine1: 'HI-RES SAMPLE', // placard uses to identify sample card
      embossLine2: '',
      quantity: 1,
      deliveryOrderId: order.orderNumber ?? '',
      logoFilename: product.logo?.name ?? '',
      shippingMethod: '2',
      denomination: '0',
    })
      .andThen((response) => {
        return updateCustomProductOrderWithExternalBatchId({
          id: order.id,
          externalBatchId1:
            response.instCrdOrderDetailResponse.response.uniqueId,
        })
      })
      .andThen(() => {
        return createBatchOrder({
          cardType: cardDesigns.lowResDesign.externalCardType!,
          cardSubType: cardDesigns.lowResDesign.externalCardDesignId!,
          recipientName: order.deliveryRecipientName ?? '',
          addressLine1: order.deliveryAddressLine1 ?? '',
          addressLine2: order.deliveryAddressLine2 ?? '',
          addressLine3: order.deliverySuburb ?? '',
          city: order.deliveryCity ?? '',
          postCode: order.deliveryPostcode ?? '',
          email: order.user.email ?? '',
          embossLine1: 'LOW-RES SAMPLE', // placard uses to identify sample card
          embossLine2: '',
          quantity: 1,
          deliveryOrderId: order.orderNumber ?? '',
          logoFilename: product.logo?.name ?? '',
          shippingMethod: '2',
          denomination: '0', // used for the leter to detail the unit price
        })
      })
      .andThen((response) => {
        return updateCustomProductOrderWithExternalBatchId({
          id: order.id,
          externalBatchId2:
            response.instCrdOrderDetailResponse.response.uniqueId,
        })
      })
      .map(() => product)
      .orElse((error) => {
        logger.error(error, 'failed to create batch order for samples')

        updateCustomProductOrderError({
          id: order.id,
          error: JSON.stringify(error),
        })

        return err({
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'failed to create batch order for samples',
        } as SimpleError)
      })
  })
}

export function approveCustomProduct({ logo }: { logo: string }) {
  logger.info(`approving custom product with logo name: ${logo}`)

  return findLogoWithProduct({ logoName: logo })
    .andThen((logo) => {
      return updateProductToApproved({ id: logo.product!.id! })
    })
    .andThen((product) => {
      if (product.customProductOrder.length === 0) {
        const logRef = logger.error(
          `No custom product order found for product ID: ${product.id}`
        )
        return err({
          code: errorCodes.UNEXPECTED,
          message: `No custom product order found for product ID: ${product.id}`,
          logRef,
        } as SimpleError)
      }

      if (product.design.designType === DesignType.STOCK) {
        return handleStockCardApproval(product)
      } else {
        return ok(product)
      }
    })
    .andThen((product) => {
      const order = product.customProductOrder[0]
      logger.info(
        `sending email to user with email: ${order.user.email} using template id ${CUSTOMER_LOGO_APPROVED}`
      )

      const email = order.user.email

      sendEmailWithTemplate({
        email,
        templateId: CUSTOMER_LOGO_APPROVED,
        templateModel: {
          name: order.user.firstName,
          customProductName: product.name,
        },
      })

      return ok({
        logo: product.logoId,
        logoName: product.logo?.name,
        productName: product.name,
        logoUrl: product.logo?.logoUrl,
      })
    })
}

export function confirmWindcaveSessionForProductOrder(
  windcaveSessionId: string
) {
  return fromPromise(
    prisma.productOrder.updateMany({
      where: {
        windcaveSessionId,
        windcaveSessionConfirmed: false,
      },
      data: {
        windcaveSessionConfirmed: true,
        orderStatus: OrderStatus.RELEASING,
        submittedAt: new Date().toISOString(),
        releasedBy: 'Windcave Payment',
      },
    }),
    (e) => {
      logger.warn(
        `Failed to update product order with windcave sessionId: ${windcaveSessionId}`,
        e
      )
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function validateHighValueProductOrder(
  windcaveSessionId: string,
  amount: number
) {
  return fromPromise(
    prisma.productOrder.updateMany({
      where: {
        windcaveSessionId,
        orderTotal: amount,
      },
      data: {
        orderStatus: 'VALIDATING',
      },
    }),
    (e) => {
      logger.warn(
        `failed to update product order with session id [${windcaveSessionId}] amount [${amount}]`,
        e
      )
      return ERRORS.DATABASE_ERROR
    }
  )
}

type StockCardProduct = {
  name: string
  productCode: number
  designUrl: string
  logoUrl?: string
  orientation: string
  type: string[]
  minValue: number
  maxValue: number
}

export function getAllStockProductsForOrg(orgId: string) {
  return fromPromise(
    prisma.product.findMany({
      where: {
        organizationId: orgId,
        type: ProductType.STOCK,
        status: ProductStatus.APPROVED,
      },
      include: {
        design: true,
        logo: true,
      },
    }),
    (e) => {
      logger.warn(`failed to find stock products for org with id [${orgId}]`, e)

      return 'DATABASE_ERROR'
    }
  ).andThen((products) => {
    if (!products) {
      return errAsync(ERRORS.NOT_FOUND)
    }

    const stockProducts: StockCardProduct[] = products.map((product) => {
      return {
        name: product.name,
        productCode: product.productCode,
        designUrl: product.design.cardDesignUrl,
        logoUrl: product.logo?.logoUrl,
        orientation: product.design.cardDirection,
        type: product.cardTypes,
        minValue: IS_PROD
          ? centsToDollars(product.minValue)
          : centsToDollars(TESTING_MIN_VALUE),
        maxValue: centsToDollars(product.maxValue),
      }
    })

    return okAsync(stockProducts)
  })
}

export function getStockCardDetails({
  productCode,
  orgId,
}: {
  productCode: number
  orgId: string
}) {
  return findProductForOrg({ productCode, orgId }).map(
    ({
      id,
      name,
      design,
      logo,
      cardTypes,
      details,
      conditions,
      redeem,
      organizationId,
      option1Fee,
      option2Fee,
    }) => ({
      id,
      name,
      productCode,
      designUrl: design.cardDesignUrl,
      orientation: design.cardDirection,
      logoUrl: logo?.logoUrl ?? null,
      cardTypes,
      details,
      conditions,
      redeem,
      organizationId,
      highResFee: centsToDollars(option2Fee),
      lowResFee: centsToDollars(option1Fee),
      minQuantity: IS_PROD ? 50 : 1,
      maxQuantity: MAX_ORDER_ITEM_QUANTITY,
    })
  )
}

function createStockCardProduct({
  orgId,
  logoId,
  highResDesignId,
  lowResDesignId,
  name,
}: {
  orgId: string
  logoId: string
  highResDesignId: string
  lowResDesignId: string
  name: string
}) {
  logger.info(`creating stock card product for org with id [${orgId}]`)

  return fromPromise(
    prisma.product.create({
      data: {
        name,
        organizationId: orgId,
        logoId,
        designId: lowResDesignId,
        extraDesignId: highResDesignId,
        type: ProductType.STOCK,
        status: ProductStatus.PENDING,
        details: STOCK_DETAILS,
        conditions: CONDITIONS,
        option1Fee: OPTION_1_FEE,
        option2Fee: OPTION_2_FEE,
        cardTypes: {
          set: [CardType.PHYSICAL],
        },
        categories: ['PERSONALISED'],
        minValue: MIN_VALUE,
        maxValue: MAX_VALUE,
        loadingFee: LOAD_FEE,
        deliveryMethods: [DeliveryMethod.COURIER],
      },
    }),
    (e) => {
      logger.warn(
        `failed to create stock card product for org with id [${orgId}]`,
        e
      )

      return 'DATABASE_ERROR'
    }
  ).andThen((product) => {
    if (!product) {
      logger.warn(
        `failed to create stock card product for org with id [${orgId}]`
      )
      return errAsync({
        code: ERRORS.NOT_FOUND.code,
        message: 'Failed to create stock card product',
      })
    }

    return okAsync(product)
  })
}

export function createLogoAndProductForStockCard({
  logoUrl,
  designUrl,
  name,
  orgId,
  logoFileName,
}: {
  logoUrl: string
  designUrl: string
  name: string
  orgId: string
  logoFileName: string
}) {
  return createCardLogo({
    logoUrl,
    orgId,
    logoFileName: decodeURIComponent(logoFileName),
  }).andThen((logo) => {
    if (!logo) {
      return errAsync({
        code: ERRORS.NOT_FOUND.code,
        message: 'Failed to create card logo',
      })
    }
    return getHighAndLowResDesignIds(designUrl).andThen(
      ({ highResDesignId, lowResDesignId }) => {
        return createStockCardProduct({
          orgId,
          logoId: logo.id,
          highResDesignId,
          lowResDesignId,
          name,
        })
      }
    )
  })
}
