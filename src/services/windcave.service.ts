import { Prisma, ProductType } from '@prisma/client'
import type { Session } from 'types/windcave'

import { err, errAsync, fromPromise, ok, okAsync } from 'neverthrow'

import logger from 'utils/logger'

import {
  WINDCAVE_URL,
  WINDCAVE_USER,
  WINDCAVE_PASSWORD,
  USE_MOCKING,
} from 'utils/config'

import { ERRORS } from 'utils/error'
import axios, { AxiosResponse } from 'axios'
import { dollarsToCents } from 'utils/numeric'
import { confirmWindcaveSessionForCustomProductOrder } from './custom-product.service'
import {
  confirmWindcaveSessionForProductOrder,
  validateHighValueProductOrder,
} from './product.service'
import { releaseCustomOrderByOrderNumber } from './admin-organization.service'
import { orderCardsFromSupplierByWindcaveSessionId } from './product-order/order-cards.service'
import { errorCodes, SimpleError } from 'types/simple-error'
import { epayUpdateProductOrderStatusByOrder } from 'epay-data/epay-product-order.data'
import { processWindcaveOrder } from './product-order/process-windcave-order.service'

const token = Buffer.from(`${WINDCAVE_USER}:${WINDCAVE_PASSWORD}`).toString(
  'base64'
)

const TRANSACTION_APPROVED_RESPONSE_CODE = '00'

const isCustomOrder = (metaData: string[] = []) =>
  metaData?.includes(ProductType.CUSTOM)

export function createWindcaveSession({
  amount,
  callbackUrls,
  notificationUrl,
  metaData,
}: {
  amount: string
  callbackUrls: {
    approved: string
    declined: string
    cancelled: string
  }
  notificationUrl: string
  metaData?: string[]
}) {
  const orderNumber = getOrderNumberFromCallbackUrl(callbackUrls.approved)

  const data = {
    type: 'purchase',
    amount,
    currency: 'NZD',
    merchantReference: orderNumber,
    callbackUrls,
    notificationUrl,
    methods: ['card'],
    metaData,
  }

  logger.info('WINDCAVE_URL', WINDCAVE_URL)
  logger.info('data', data)

  return fromPromise(
    axios.post<Session>(WINDCAVE_URL, data, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    }),
    (error) => {
      logger.error(error, '[Windcave] Failed to create windcave session')
      return ERRORS.EXTERNAL_API
    }
  ).andThen((res) => {
    if (res.status === 202) {
      return okAsync({
        id: res.data.id,
        redirect: res.data.links.find((link) => {
          return link.method === 'REDIRECT'
        })?.href,
      })
    }

    logger.error(
      `[Windcave] Unexpected response: ${res.status} - ${res.data?.id}`
    )
    return errAsync(ERRORS.EXTERNAL_API)
  })
}

function fetchWindcaveSession(id: string) {
  return fromPromise(
    axios.get<Session>(`${WINDCAVE_URL}/${id}`, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    }),
    (error) => {
      logger.error(
        error,
        `[Windcave] Failed to retrieve windcave session ${id}`
      )
      return ERRORS.EXTERNAL_API
    }
  )
}

function checkCompletedSession(res: AxiosResponse<Session>) {
  // 202 is pending session
  if (res.status === 200) {
    if (res.data.state === 'complete') {
      const isTransactionApproved = res.data.transactions?.find(
        (tr: any) => tr.reCo === TRANSACTION_APPROVED_RESPONSE_CODE
      )

      logger.info(
        `[Windcave] Transaction status: ${res.data.transactions[0]?.responseText}`
      )

      if (isTransactionApproved) {
        return ok(res)
      }

      return err(ERRORS.WINDCAVE_DECLINED_TRANSACTION)
    } else if (res.data.state === 'cancelled') {
      return err(ERRORS.WINDCAVE_CANCELLED_TRANSACTION)
    }
  }

  logger.error(
    `[Windcave] Session incomplete: ${res.status} - ${res.data?.state}`
  )
  return err(ERRORS.WINDCAVE_SESSION_INCOMPLETE)
}

function checkDuplicateNotification(
  res: AxiosResponse<Session>,
  batchPayload: Prisma.BatchPayload
) {
  if (batchPayload.count === 1) {
    return ok(res)
  }

  logger.warn(`[Windcave] Order has already been confirmed ${res.data.id}`)
  return err(ERRORS.WINDCAVE_DUPLICATE_NOTIFICATION)
}

function orderCards(res: AxiosResponse<Session>) {
  const amount = Number(res.data.amount) * 100

  if (amount > 5000000) {
    return validateHighValueProductOrder(res.data.id, amount).andThen(() => {
      return okAsync(true)
    })
  } else {
    fromPromise(
      processWindcaveOrder({
        sessionId: res.data.id,
        amount: dollarsToCents(Number(res.data.amount)),
        useRen: !USE_MOCKING,
      }),
      (error) => {
        const logRef = logger.error(
          error,
          `orderCards.. error processing async orderCardsFromSupplierByWindcaveSessionId`
        )
        return {
          code: errorCodes.order.PROCESSING_ERROR,
          message: 'Background releasing order process failed for windcave',
          logRef,
        } as SimpleError
      }
    )
    return okAsync(true)
  }
}

function getOrderNumberFromCallbackUrl(url: string) {
  // Might have query string
  return url.split('/').pop()?.split('?')[0]
}

function releaseCustomCards(res: AxiosResponse<Session>) {
  const orderNumber = getOrderNumberFromCallbackUrl(
    res.data.callbackUrls.approved
  )
  return releaseCustomOrderByOrderNumber(String(orderNumber))
}

function validateCustomProductOrders(res: AxiosResponse<Session>) {
  return confirmWindcaveSessionForCustomProductOrder(res.data.id)
    .andThen((batchPayload) => checkDuplicateNotification(res, batchPayload))
    .andThen(releaseCustomCards)
}

function validateProductOrders(res: AxiosResponse<Session>) {
  return confirmWindcaveSessionForProductOrder(res.data.id)
    .andThen((batchPayload) => checkDuplicateNotification(res, batchPayload)) // return after this step
    .andThen(orderCards) // process this async
}

function validateProductByType(res: AxiosResponse<Session>) {
  if (isCustomOrder(res.data.metaData)) {
    return validateCustomProductOrders(res)
  }
  return validateProductOrders(res)
}

export function validateProductOrderSession({ id }: { id: string }) {
  return fetchWindcaveSession(id)
    .andThen(checkCompletedSession)
    .andThen(validateProductByType)
}
