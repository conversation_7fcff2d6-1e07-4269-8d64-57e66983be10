import { Prisma, UserLoginNotification, UserRole } from '@prisma/client'
import { err, errAsync, fromPromise, ok, okAsync } from 'neverthrow'

import { CreateOrganization } from 'routes/organization.route'
import { DISCOUNT_MAP } from 'types/discount'
import { ERRORS, PickErrorCodeKey } from 'utils/error'
import { getEntity } from 'utils/infolog'
import logger from 'utils/logger'
import { centsToDollars } from 'utils/numeric'
import { prisma } from 'utils/prisma'
import { sendOrganizationCreatedEmail } from './corporate-application.service'
import { checkEmailIsInSkipKyc } from './onboarding.service'
import { updateUser } from './user.service'

export function getOrganizationUsersFromOrgId({ orgId }: { orgId: string }) {
  return fromPromise(
    prisma.organization
      .findFirst({
        where: {
          id: orgId,
        },
        include: {
          users: true,
        },
      })
      .then((organization) => {
        return organization?.users ?? []
      }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'NOT_FOUND'> => {
      logger.error(error)

      return 'DATABASE_ERROR'
    }
  )
}

export function getOrgFromUserId(userId: string) {
  return fromPromise(
    prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        organization: {
          select: {
            id: true,
            name: true,
            gstNumber: true,
          },
        },
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`error getting orgId for user [${userId}]`, error)

      return 'DATABASE_ERROR'
    }
  ).andThen((user) => {
    if (!user || !user.organization) {
      return errAsync('NOT_FOUND' as const)
    }

    return okAsync({
      orgId: user.organization.id,
      orgname: user.organization.name,
      gstNumber: user.organization.gstNumber,
    })
  })
}

export async function addCPartnerId({
  orgId,
  cPartnerId,
}: {
  orgId: string
  cPartnerId: string
}) {
  return fromPromise(
    prisma.organization
      .update({
        where: {
          id: orgId,
        },
        data: {
          cPartnerIds: {
            push: cPartnerId,
          },
        },
        select: {
          cPartnerIds: true,
        },
      })
      .then((organization) => {
        return organization.cPartnerIds
      }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.error(error)
      return 'DATABASE_ERROR'
    }
  )
}

export function updateCPartnerIds(params: {
  orgId: string
  cPartnerIds: string[]
}) {
  const { orgId, cPartnerIds } = params

  return fromPromise(
    prisma.organization
      .update({
        where: {
          id: orgId,
        },
        data: {
          cPartnerIds,
        },
        select: {
          cPartnerIds: true,
        },
      })
      .then((organization) => {
        return organization.cPartnerIds
      }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.error(error)
      return 'DATABASE_ERROR'
    }
  )
}

export function getCPartnerInfo({ orgId }: { orgId: string }) {
  const cPartnerAccountQuery = prisma.organizationCPartnerJoinTable.findMany({
    where: {
      organizationId: orgId,
    },
    select: {
      cPartnerAccount: {
        select: {
          email: true,
          userId: true,
        },
      },
    },
  })
  const cPartnerLogoQuery = prisma.organizationCPartnerLogoJoinTable.findMany({
    where: {
      organizationId: orgId,
    },
    select: {
      cPartnerLogo: {
        select: {
          id: true,
          externalCardLogoId: true,
          logoUrl: true,
          organizationCPartnerLogoJoinTable: {
            select: {
              created: true,
            },
          },
        },
      },
    },
  })

  return fromPromise(
    prisma.$transaction([cPartnerAccountQuery, cPartnerLogoQuery]),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn('Failed to query cPartner info')

      return 'DATABASE_ERROR'
    }
  ).map(([cPartnerAccountRes, cPartnerLogoRes]) => {
    const cPartnerAccounts = cPartnerAccountRes.map(({ cPartnerAccount }) => {
      return cPartnerAccount
    })
    const cPartnerLogos = cPartnerLogoRes.map(({ cPartnerLogo }) => {
      return {
        externalCardLogoId: cPartnerLogo.externalCardLogoId,
        id: cPartnerLogo.id,
        logoUrl: cPartnerLogo.logoUrl,
        created: cPartnerLogo.organizationCPartnerLogoJoinTable[0].created,
      }
    })

    return { cPartnerAccounts, cPartnerLogos }
  })
}

export function addCPartnerInfoToOrg({
  orgId,
  userId,
  email,
}: {
  orgId: string
  userId: string
  email: string
}) {
  return fromPromise(
    prisma.cPartnerAccount.findFirst({
      where: {
        OR: [{ email }, { userId }],
      },
      include: {
        cPartnerOrg: {
          include: {
            cPartnerLogos: true,
          },
        },
      },
    }),
    (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(
        `Failed to query c partner user email [${email}] userId [${userId}]`,
        e
      )

      return 'DATABASE_ERROR'
    }
  )
    .andThen((cPartnerAccount) => {
      console.log(cPartnerAccount)
      if (!cPartnerAccount) {
        return errAsync('NOT_FOUND' as const)
      }

      return fromPromise(
        prisma.organizationCPartnerJoinTable.create({
          data: {
            cPartnerAccountId: cPartnerAccount.id,
            organizationId: orgId,
          },
        }),
        (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
          logger.warn(
            `Failed to create joined table org [${orgId}] cPartnerAccount ${cPartnerAccount.id}`,
            e
          )

          return 'DATABASE_ERROR'
        }
      ).map(() => {
        return cPartnerAccount
      })
    })
    .andThen((cPartnerAccount) => {
      const joinList = cPartnerAccount.cPartnerOrg.cPartnerLogos.map(
        (cPartnerLogo) => {
          return prisma.organizationCPartnerLogoJoinTable.create({
            data: {
              cPartnerLogoId: cPartnerLogo.id,
              organizationId: orgId,
            },
            include: {
              cPartnerLogo: true,
            },
          })
        }
      )

      return fromPromise(
        prisma.$transaction(joinList),
        (e): PickErrorCodeKey<'DATABASE_ERROR'> => {
          logger.warn(`Failed to create joined table org [${orgId}]`, e)

          return 'DATABASE_ERROR'
        }
      ).map((logoJoins) => {
        return {
          cPartnerLogos: logoJoins.map((logoJoin) => {
            return {
              id: logoJoin.cPartnerLogo.id,
              created: logoJoin.created,
              externalCardLogoId: logoJoin.cPartnerLogo.externalCardLogoId,
              logoUrl: logoJoin.cPartnerLogo.logoUrl,
            }
          }),
          cPartnerAccount: {
            userId: cPartnerAccount.userId,
            email: cPartnerAccount.email,
          },
        }
      })
    })
}

export function checkOrgAlreadyExists({ nzbn }: { nzbn: string }) {
  return fromPromise(
    prisma.organization.findFirst({
      where: {
        nzbn,
      },
    }),
    (error) => {
      logger.error(error, 'Failed to check if organization already exists')

      return ERRORS.DATABASE_ERROR
    }
  ).andThen((organization) => {
    return ok({ exists: !organization ? false : true })
  })
}

export function getRoleCountForOrg({
  orgId,
  role,
}: {
  orgId: string
  role: UserRole
}) {
  return fromPromise(
    prisma.user.count({
      where: {
        AND: {
          role,
          organizationId: orgId,
        },
      },
    }),
    (error) => {
      logger.warn(
        `Error getting role count for org ${orgId} and role ${role}`,
        error
      )

      return err(ERRORS.DATABASE_ERROR)
    }
  )
}

export async function getCompanyNameFromInfolog({ nzbn }: { nzbn: string }) {
  logger.info(`Getting entity from infolog with NZBN ${nzbn}`)

  const checkNZBNRes = await checkOrgAlreadyExists({ nzbn })

  if (checkNZBNRes.isErr()) {
    return err(checkNZBNRes.error)
  }

  if (checkNZBNRes.value.exists) {
    logger.debug('Organization already exists', checkNZBNRes.value)

    return okAsync(checkNZBNRes.value)
  } else {
    try {
      logger.info(`Getting entity from infolog with NZBN ${nzbn}`)

      const getEntityResponse = await getEntity({
        NZBN: nzbn,
      })

      const entityName = getEntityResponse.data.Entity.EntityName

      return okAsync({ nzbnCompanyName: entityName, exists: false })
    } catch (error) {
      logger.warn(`Error getting entity from infolog with NZBN ${nzbn}`, error)

      return errAsync({
        code: ERRORS.EXTERNAL_API.code,
        message: `Error getting entity from infolog with NZBN ${nzbn} with error: ${error}`,
      })
    }
  }
}

export function createSimpleOrganization({
  orgData,
  userId,
  applicationId,
}: {
  orgData: Prisma.OrganizationCreateInput
  userId: string
  applicationId?: string
}) {
  const organizationData: Prisma.OrganizationCreateInput = {
    ...orgData,
    users: {
      connect: {
        id: userId,
      },
    },
  }

  if (applicationId) {
    organizationData.corporateApplications = {
      connect: {
        id: applicationId,
      },
    }
  }

  return fromPromise(
    prisma.organization.create({
      data: organizationData,
    }),
    (error) => {
      logger.error(error, 'Failed to create organization')

      return ERRORS.DATABASE_ERROR
    }
  ).andThen((organization) => {
    if (!organization) {
      return err(ERRORS.DATABASE_ERROR)
    }
    return generateCustomerCodeForOrg(organization.id).andThen(
      (customerCode) => {
        if (!customerCode) {
          return err(ERRORS.DATABASE_ERROR)
        }
        return updateCustomerCodeForOrg(customerCode).andThen(
          (organization) => {
            if (!organization) {
              return err(ERRORS.DATABASE_ERROR)
            }
            return ok(organization)
          }
        )
      }
    )
  })
}

export async function createOrganisationForSkipKycUser({
  nzbn,
  orgName,
  userId,
  organizationType,
  firstName,
  lastName,
  email,
}: CreateOrganization & { userId: string; email: string }) {
  logger.info(
    `creating organization with no application, nzbn: ${nzbn}, orgName: ${orgName}, userId: ${userId}, orgType:  ${organizationType}`
  )

  const skipKycResult = await checkEmailIsInSkipKyc({ email: email })

  if (skipKycResult.isErr()) {
    return errAsync(skipKycResult.error)
  }

  if (!skipKycResult.value) {
    return errAsync(ERRORS.UNAUTHORIZED)
  }

  const createOrganisation = await createSimpleOrganization({
    orgData: {
      name: orgName,
      nzbn,
      type: organizationType,
    },
    userId,
  })

  if (createOrganisation.isErr()) {
    return errAsync(createOrganisation.error)
  }

  const updateUserRes = await updateUser({
    id: userId,
    data: {
      firstName,
      lastName,
      loginNotification: UserLoginNotification.APPLICATION_CELEBRATION,
    },
  })

  await createDiscountsForOrg({
    orgId: createOrganisation.value.id,
    email,
    nzbn,
  })

  if (updateUserRes.isErr()) {
    return errAsync(updateUserRes.error)
  }

  sendOrganizationCreatedEmail({
    email,
    nzbn,
    name: orgName,
  })

  return okAsync(updateUserRes.value)
}

async function createDiscountsForOrg({
  orgId,
  nzbn,
  email,
}: {
  orgId: string
  email: string
  nzbn: string
}) {
  const emailLowerCase = email.toLowerCase()
  const discount = DISCOUNT_MAP[nzbn] || DISCOUNT_MAP[emailLowerCase]

  logger.info(`createDiscountsForOrg, nzbn: ${nzbn}, email: ${email}`)

  const hasEmail = discount?.email?.find(
    (e) => e.toLowerCase() === emailLowerCase.toLowerCase()
  )

  if (hasEmail) {
    const existingDiscount = await prisma.discount.findFirst({
      where: {
        organizationId: orgId,
      },
    })

    if (!existingDiscount) {
      logger.info(`creating discount: ${JSON.stringify(discount)}`)

      return fromPromise(
        prisma.discount.create({
          data: {
            loadingFee: discount.loadingFee,
            digitalFee: discount.digitalFee,
            organizationId: orgId,
          },
        }),
        (error) => {
          logger.error(error, `Failed to create discount for ${orgId}`)
          return ERRORS.DATABASE_ERROR
        }
      )
    }

    logger.info(`discount exists: ${JSON.stringify(discount)}`)
  }

  return okAsync(null)
}

export function deleteOrganizationById(orgId: string) {
  return fromPromise(
    prisma.organization.delete({
      where: {
        id: orgId,
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function getOrganizationsWithDiscounts({
  page,
  pageSize,
  orgName,
}: {
  page: number
  pageSize: number
  orgName?: string
}) {
  const where: Prisma.OrganizationWhereInput = orgName
    ? { name: { contains: orgName, mode: 'insensitive' } }
    : {}

  return fromPromise(
    prisma.$transaction([
      prisma.organization.findMany({
        where,
        include: {
          discounts: true,
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      prisma.organization.count({ where }),
    ]),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  ).map(([organizations, count]) => {
    return {
      count,
      organizations: organizations.map((organization) => {
        return {
          id: organization.id,
          name: organization.name,
          loadingFee: centsToDollars(organization.discounts?.loadingFee ?? 0),
          digitalFee: centsToDollars(organization.discounts?.digitalFee ?? 0),
          shippingFee: centsToDollars(
            organization.discounts?.shippingFeeDiscountCents ?? 0
          ),
        }
      }),
    }
  })
}

export function updateOrgDiscounts({
  orgId,
  loadingFee,
  digitalFee,
  shippingFee,
}: {
  orgId: string
  loadingFee: number
  digitalFee: number
  shippingFee: number
}) {
  const data = {
    loadingFee,
    digitalFee,
    shippingFeeDiscountCents: shippingFee,
  }

  return fromPromise(
    prisma.discount.upsert({
      where: {
        organizationId: orgId,
      },
      create: {
        ...data,
        organizationId: orgId,
      },
      update: data,
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

type CustomerCode = {
  orgId: string
  customerCode: string
}

export function generateCustomerCodeForOrg(orgId: string) {
  return fromPromise(
    prisma.organization.findFirst({
      where: {
        id: orgId,
      },
      select: {
        increment: true,
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((organization) => {
    if (!organization) {
      return errAsync({
        code: ERRORS.NOT_FOUND.code,
        message: `Organization not found for id: ${orgId}`,
      })
    }

    const customerCode = `C-${organization.increment
      .toString()
      .padStart(7, '0')}`

    return okAsync({ orgId, customerCode } as CustomerCode)
  })
}

export function updateCustomerCodeForOrg(customerCode: CustomerCode) {
  return fromPromise(
    prisma.organization.update({
      where: {
        id: customerCode.orgId,
      },
      data: {
        customerCode: customerCode.customerCode,
      },
      include: {
        users: true,
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  )
}
