import {
  CardType,
  CustomProductOrderItem,
  DeliveryMethod,
  FloatFundsTransactionType,
  OrderStatus,
  PaymentMethod,
  Prisma,
  Product,
  ProductStatus,
  ProductType,
  RibbonColor,
} from '@prisma/client'

import {
  err,
  errAsync,
  from<PERSON>rom<PERSON>,
  ok,
  okAsync,
  ResultAsync,
} from 'neverthrow'

import type {
  CustomProductSchema,
  SubmitCustomProductProps,
} from 'routes/product.custom.route'

import {
  API_URL_BASE,
  CLIENT_URL_BASE,
  CREDIT_CARD_SURCHARGE,
  EPAY_ADMIN_EMAIL,
} from 'utils/config'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { centsToDollars, generateRandomNumbers, getGst } from 'utils/numeric'
import { prisma } from 'utils/prisma'

import {
  CUSTOMER_LOGO_SUBMITTED,
  EPAY_REVIEW_LOGO,
} from '../helpers/email-templates'

import { sendEmailWithTemplate } from './postmark.service'

import {
  EPAY_GST_NUMBER,
  EPAY_LIVE_AGENT,
  EPAY_SUPPORT_EMAIL,
} from 'constants/admin'
import {
  CONDITIONS,
  DETAILS,
  FIXED_VALUES,
  LOAD_FEE,
  MAX_VALUE,
  MIN_VALUE,
} from 'constants/prezzy'
import { CreateStockCardProductArgs } from 'routes/stock.route'
import { uploadLogoToSftp } from './placard.service'
import { calculateFeeDiscount } from './product-order.service'
import {
  createLogoAndProductForStockCard,
  updateProductStatusAfterSendingToPlacard,
} from './product.service'
import { createWindcaveSession } from './windcave.service'
import { getGlobalDiscounts } from 'data/discount.data'
import { createFloatFundsTransaction } from 'data/float-funds.data'
import { releaseCustomOrder } from './admin-organization.service'

const CREATE_CARD_PRODUCT_CODE = 'LOGO'
export const CREATE_STOCK_PRODUCT_CODE = 'STOCK_LOGO'

export function createCustomPaymentSession({
  orderNumber,
}: {
  orderNumber: string
}) {
  return findCustomProductByOrderNumber(orderNumber)
    .andThen((productOrder) => {
      if (!productOrder) {
        return errAsync(ERRORS.NOT_FOUND)
      }

      return createWindcaveSession({
        amount: centsToDollars(productOrder.orderTotal).toFixed(2),
        callbackUrls: {
          approved: `${CLIENT_URL_BASE}/order-history/create-card/invoice/${productOrder.orderNumber}`,
          declined: `${CLIENT_URL_BASE}/settings`,
          cancelled: `${CLIENT_URL_BASE}/settings`,
        },
        metaData: [ProductType.CUSTOM],
        notificationUrl: `${API_URL_BASE}/notification/product/order/payment`,
      })
    })
    .andThen(({ id, redirect }) => {
      return updateCustomProductOrderStatus(
        { orderNumber, orderStatus: OrderStatus.SUBMITTED },
        { windcaveSessionId: id }
      ).map(() => {
        return { redirect, orderStatus: OrderStatus.SUBMITTED }
      })
    })
}

export function createCardLogo({
  logoFileName,
  logoUrl,
  orgId,
}: {
  logoFileName: string
  logoUrl: string
  orgId: string
}) {
  logger.info(`creating card logo with name: ${logoFileName} for org: ${orgId}`)

  return fromPromise(
    prisma.cardLogo.create({
      data: {
        name: logoFileName,
        logoUrl,
        organizationId: orgId,
      },
    }),
    (error: unknown) => {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        logger.error(error, 'card logo name already exists')
        return ERRORS.UNIQUE_CONSTRAINT
      } else {
        logger.error(error, 'failed to create card logo')
        return ERRORS.DATABASE_ERROR
      }
    }
  )
}

export function createNewProductWithLogo({
  name,
  logoId,
  designId,
  orgId,
  cardStatus,
  displayLogoUrl,
}: {
  name: string
  logoId: string
  orgId: string
  designId: string
  cardStatus: 'PENDING' | 'APPROVED'
  displayLogoUrl?: string
}) {
  logger.info(`creating new product with name: ${name} for org: ${orgId} `)
  return fromPromise(
    prisma.product.create({
      data: {
        name,
        designId,
        logoId,
        organizationId: orgId,
        loadingFee: LOAD_FEE,
        minValue: MIN_VALUE,
        maxValue: MAX_VALUE,
        fixedValues: FIXED_VALUES,
        deliveryMethods: [DeliveryMethod.COURIER],
        categories: ['PERSONALISED'],
        details: DETAILS,
        conditions: CONDITIONS,
        status: cardStatus,
        type: ProductType.CUSTOM,
        cardTypes: {
          set: [CardType.PHYSICAL],
        },
        displayLogoUrl,
      },
    }),
    (error) => {
      logger.error(error, 'failed to create custom product')
      return {
        code: 'DB-100',
        message: 'Database operation failed',
      }
    }
  )
}

export function createCustomProductAndOrder({
  name,
  designId,
  logoUrl,
  orgId,
  userId,
  logoFileName,
  displayLogoUrl,
}: { orgId: string; userId: string } & CustomProductSchema) {
  return createCardLogo({ logoFileName, logoUrl, orgId })
    .andThen((logo) => {
      return createNewProductWithLogo({
        designId,
        logoId: logo.id,
        name,
        orgId,
        cardStatus: ProductStatus.PENDING,
        displayLogoUrl,
      })
    })
    .andThen((product) => {
      logger.debug('product created', product)

      return getCustomProductOrderItem({
        productCode: CREATE_CARD_PRODUCT_CODE,
      }).map((cardProduct) => {
        return { cardProduct, product }
      })
    })
    .andThen(({ cardProduct, product }) => {
      if (!cardProduct) {
        return err({
          code: 'DB-101',
          message: 'failed to find create card order item',
        })
      }
      return createCustomProductOrder({
        cardProduct,
        product,
        orgId,
        userId,
      })
    })
}

function createCustomProductOrder({
  cardProduct,
  product,
  orgId,
  userId,
  termsAndConditionsVersion,
  termsAndConditionsAcceptedBy,
  cardType = 'DEFAULT',
  deliveryRecipientName,
  deliveryAddressLine1,
  deliveryAddressLine2,
  deliverySuburb,
  deliveryCity,
  deliveryPostcode,
}: {
  cardProduct: Pick<CustomProductOrderItem, 'unitPrice' | 'discount'>
  product: Product
  orgId: string
  userId: string
  termsAndConditionsVersion?: string
  termsAndConditionsAcceptedBy?: string
  cardType?: 'STOCK' | 'DEFAULT'
  deliveryRecipientName?: string
  deliveryAddressLine1?: string
  deliveryAddressLine2?: string
  deliverySuburb?: string
  deliveryCity?: string
  deliveryPostcode?: string
}) {
  logger.info(`creating custom product order for product: ${product.id}`)

  return getGlobalDiscounts().andThen((globalDiscount) => {
    const createProductDiscount =
      cardType === 'STOCK'
        ? globalDiscount.stockCard
        : globalDiscount.createCard

    const baseTotal =
      cardProduct.unitPrice -
      calculateFeeDiscount({
        orgDiscount: cardProduct.discount,
        globalDiscount: createProductDiscount,
      })
    const gstAmount = Math.round(getGst(baseTotal))
    const creditCardFee = Math.round(baseTotal * CREDIT_CARD_SURCHARGE)
    const orderTotal = baseTotal + creditCardFee

    return createOrder({
      userId,
      orgId,
      product,
      cardProduct,
      creditCardFee,
      orderTotal,
      gstAmount,
      createProductDiscount,
      termsAndConditionsVersion,
      termsAndConditionsAcceptedBy,
      deliveryRecipientName,
      deliveryAddressLine1,
      deliveryAddressLine2,
      deliverySuburb,
      deliveryCity,
      deliveryPostcode,
    }).andThen((order) => {
      const orderNumber = `LOGO${generateRandomNumbers(4)}${
        order.orderIncrementNumber
      }`
      return createUniqueCustomProductOrderNumber({
        id: order.id,
        orderNumber,
      }).map((order) => {
        return {
          orderNumber: order.orderNumber,
          subtotal: centsToDollars(order.logoUploadFee),
          discount: centsToDollars(order.discountTotal),
          gst: centsToDollars(order.gstAmount),
          creditCardFee: centsToDollars(order.creditCardFee),
          total: centsToDollars(order.orderTotal),
        }
      })
    })
  })
}

function submitCustomProductOrderWithCreditCard({
  orgId,
  orderNumber,
  billingAddress,
  city,
  country,
  paymentMethod,
  postCode,
  purchaseOrderNumber,
  isLiveAgent,
  authorizedByName,
  authorizedByEmail,
}: SubmitCustomProductProps) {
  return updateCustomProductOrderWithPaymentDetails({
    orgId,
    orderNumber,
    billingAddress,
    city,
    country,
    paymentMethod,
    postCode,
    purchaseOrderNumber,
    isLiveAgent,
    authorizedByName,
    authorizedByEmail,
  }).andThen((customProductOrder) => {
    return createCustomPaymentSession({
      orderNumber: customProductOrder.orderNumber!,
    })
  })
}

function submitCustomProductOrderWithBankTransfer(
  data: SubmitCustomProductProps
) {
  return updateNonCreditCardPaymentDetails(data).andThen(
    (customProductOrder) => {
      sendEmailWithTemplate({
        email: customProductOrder.user.email,
        templateId: CUSTOMER_LOGO_SUBMITTED,
        templateModel: {
          name: customProductOrder.user.firstName,
          orderNumber: customProductOrder.orderNumber,
          cardName: customProductOrder.product?.name,
        },
      })

      sendEmailWithTemplate({
        email: EPAY_ADMIN_EMAIL,
        templateId: EPAY_REVIEW_LOGO,
        templateModel: {
          companyName: customProductOrder.organization?.name,
          orderNumber: customProductOrder.orderNumber,
          cardName: customProductOrder.product?.name,
        },
      })

      return ok({
        orderStatus: OrderStatus.SUBMITTED,
        redirect: undefined,
      })
    }
  )
}

function updateNonCreditCardPaymentDetails({
  orgId,
  orderNumber,
  billingAddress,
  city,
  country,
  paymentMethod,
  postCode,
  purchaseOrderNumber,
  isLiveAgent,
  authorizedByName,
  authorizedByEmail,
}: SubmitCustomProductProps) {
  return findCustomProductOrderWithOrderNumber(orderNumber).andThen(
    (customProductOrder) => {
      // Remove credit card fee and subtract it from total
      const creditCardFee = 0
      const orderTotal =
        customProductOrder.orderTotal! - customProductOrder.creditCardFee!

      return updateCustomProductOrderWithPaymentDetails({
        orgId,
        orderNumber,
        billingAddress,
        city,
        country,
        paymentMethod,
        postCode,
        purchaseOrderNumber,
        creditCardFee,
        orderTotal,
        isLiveAgent,
        authorizedByName,
        authorizedByEmail,
      })
    }
  )
}

function submitCustomProductOrderWithFloatFunds(
  data: SubmitCustomProductProps
) {
  logger.info(
    '(A) submitCustomProductOrderWithFloatFunds: updating custom product order payment details'
  )
  return updateNonCreditCardPaymentDetails(data).andThen(
    (customProductOrder) => {
      logger.info(
        '(B) submitCustomProductOrderWithFloatFunds: creating float funds transaction'
      )
      const orderNumber = customProductOrder.orderNumber!

      return createFloatFundsTransaction({
        orgId: data.orgId,
        amount: customProductOrder.orderTotal!,
        transactionType: FloatFundsTransactionType.DEBIT,
        createdByUserId: customProductOrder.user.id,
        orderNumber,
        customProductOrderId: customProductOrder.id,
        description: `Custom logo order #${orderNumber}`,
      })
        .andThen((floatFundsTransaction) => {
          logger.info(
            '(C) submitCustomProductOrderWithFloatFunds: releasing product order'
          )
          return releaseCustomOrder({
            orderId: customProductOrder.id,
            paymentDate: new Date(floatFundsTransaction.transactionDate),
            userId: customProductOrder.user.id,
          })
        })
        .andThen(() => {
          logger.info(
            '(D) submitCustomProductOrderWithFloatFunds: order released - status completed'
          )
          return okAsync({
            orderStatus: OrderStatus.PROCESSING,
            redirect: undefined,
          })
        })
        .orElse((error) => {
          logger.error(
            error,
            `failed to create float funds transaction for custom product order [${orderNumber}]. Resetting order status to PENDING`
          )
          updateCustomProductOrderStatus({
            orderNumber: customProductOrder.orderNumber!,
            orderStatus: OrderStatus.PENDING,
          })
          return err(error)
        })
    }
  )
}

export function submitCustomProductOrder(data: SubmitCustomProductProps) {
  const { orderNumber, paymentMethod } = data
  logger.info(`submitting order [${orderNumber}]`)

  if (paymentMethod === PaymentMethod.CREDIT_CARD) {
    return submitCustomProductOrderWithCreditCard(data)
  }

  if (paymentMethod === PaymentMethod.FLOAT_FUNDS) {
    return submitCustomProductOrderWithFloatFunds(data)
  }

  return submitCustomProductOrderWithBankTransfer(data)
}

export function getCustomProductOrderListForOrg({ orgId }: { orgId: string }) {
  return fromPromise(
    prisma.customProductOrder.findMany({
      where: {
        organizationId: orgId,
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        id: true,
        orderNumber: true,
        orderStatus: true,
        createdAt: true,
        product: {
          select: {
            name: true,
            displayLogoUrl: true,
            logo: {
              select: {
                logoUrl: true,
              },
            },
            design: {
              select: {
                cardDesignUrl: true,
                cardDirection: true,
                ribbonColor: true,
              },
            },
          },
        },
        user: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    }),
    (error) => {
      logger.error(error, 'Error searching orders')

      return ERRORS.DATABASE_ERROR
    }
  ).map((customProductOrders) => {
    return {
      productOrders: customProductOrders.map((productOrder) => {
        return {
          id: productOrder.id,
          orderNumber: productOrder.orderNumber,
          orderStatus: productOrder.orderStatus,
          createdAt: productOrder.createdAt,
          cardName: productOrder.product?.name,
          cardDesignUrl: productOrder.product?.design?.cardDesignUrl,
          logoUrl: productOrder.product?.displayLogoUrl
            ? productOrder.product?.displayLogoUrl
            : productOrder.product?.logo?.logoUrl,
          cardDirection: productOrder.product?.design?.cardDirection,
          ribbonColor: productOrder.product?.design?.ribbonColor,

          user: `${
            productOrder.user.firstName
          } ${productOrder.user.lastName?.charAt(0)}`,
        }
      }),
    }
  })
}

export function getCustomProductOrderInvoiceForOrg(
  orderNumber: string,
  orgId: string
) {
  return fromPromise(
    prisma.customProductOrder.findFirst({
      where: {
        orderNumber,
        organizationId: orgId,
      },
      include: {
        organization: true,
        user: true,
      },
    }),
    (error) => {
      logger.error(error, 'Error fetching Custom product order invoice ')

      return ERRORS.DATABASE_ERROR
    }
  ).map((customProductOrder) => {
    if (!customProductOrder) {
      logger.error(
        `Custom product order not found with orderNumber: ${orderNumber}`
      )

      return {
        code: ERRORS.NOT_FOUND.code,
        message: 'Custom product order not found',
      }
    }

    return {
      orderNumber: customProductOrder.orderNumber,
      organizationName: customProductOrder.organization?.name,
      billingAddress: customProductOrder.billingAddress,
      city: customProductOrder.city,
      country: customProductOrder.country,
      postcode: customProductOrder.postCode,
      paymentMethod: customProductOrder.paymentMethod,
      purchaseOrderNumber: customProductOrder.purchaseOrderNumber,
      invoiceDate: customProductOrder.createdAt,
      contactPerson: customProductOrder.createdByLiveAgent
        ? EPAY_LIVE_AGENT
        : `${customProductOrder.user.firstName} ${customProductOrder.user.lastName}`,
      emailAddress: customProductOrder.createdByLiveAgent
        ? EPAY_SUPPORT_EMAIL
        : customProductOrder.user.email,
      discountTotal: centsToDollars(customProductOrder.discountTotal),
      orderTotal: centsToDollars(customProductOrder.orderTotal),
      gstAmount: centsToDollars(customProductOrder.gstAmount),
      gstNumber: EPAY_GST_NUMBER,
      unitPrice: centsToDollars(customProductOrder.logoUploadFee),
      discount: centsToDollars(customProductOrder.discountTotal),
      creditCardFee: centsToDollars(customProductOrder.creditCardFee),
      authorizedByName: customProductOrder.authorizedByName,
      authorizedByEmail: customProductOrder.authorizedByEmail,
      termsAndConditionsVersion: customProductOrder.termsAndConditionsVersion,
    }
  })
}

export function getAllApprovedCustomProductsForOrg(orgId: string) {
  return fromPromise(
    prisma.product.findMany({
      where: {
        organizationId: orgId,
        status: ProductStatus.APPROVED,
        type: ProductType.CUSTOM,
      },
      include: {
        design: true,
        logo: true,
      },
    }),
    (err) => {
      logger.warn('failed to update product logo url', err)
      return ERRORS.DATABASE_ERROR
    }
  ).map((products) => {
    return {
      products: products.map((product) => {
        return {
          id: product.id,
          name: product.name,
          productCode: product.productCode,
          designUrl: product.design.cardDesignUrl,
          logoUrl: product.displayLogoUrl
            ? product.displayLogoUrl
            : product.logo?.logoUrl,
          cardTypes: product.cardTypes,
          fixedValues: product.fixedValues.map((v) => centsToDollars(v)),
          min: centsToDollars(product.minValue),
          max: centsToDollars(product.maxValue),
          details: product.details,
          conditions: product.conditions,
          redeem: product.redeem,
          deliveryMethods: product.deliveryMethods,
          organizationId: product.organizationId,
          loadingFee: centsToDollars(product.loadingFee),
          digitalFee: centsToDollars(product.digitalFee),
          discount: orgId ? centsToDollars(45) : 0,
          ribbonColor: product.design.ribbonColor,
        }
      }),
    }
  })
}

function getCustomProductOrderItem({ productCode }: { productCode: string }) {
  return fromPromise(
    prisma.customProductOrderItem.findFirst({
      where: {
        productCode,
      },
      select: {
        unitPrice: true,
        discount: true,
      },
    }),
    (error) => {
      logger.warn('failed to  find create card order item', error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

function createOrder({
  userId,
  orgId,
  product,
  cardProduct,
  creditCardFee,
  orderTotal,
  gstAmount,
  createProductDiscount,
  termsAndConditionsVersion,
  termsAndConditionsAcceptedBy,
  deliveryRecipientName,
  deliveryAddressLine1,
  deliveryAddressLine2,
  deliverySuburb,
  deliveryCity,
  deliveryPostcode,
}: {
  userId: string
  orgId: string
  product: Product
  cardProduct: Pick<CustomProductOrderItem, 'unitPrice' | 'discount'>
  creditCardFee: number
  orderTotal: number
  gstAmount: number
  createProductDiscount: number
  termsAndConditionsVersion?: string
  termsAndConditionsAcceptedBy?: string
  deliveryRecipientName?: string
  deliveryAddressLine1?: string
  deliveryAddressLine2?: string
  deliverySuburb?: string
  deliveryCity?: string
  deliveryPostcode?: string
}) {
  return fromPromise(
    prisma.customProductOrder.create({
      data: {
        userId,
        organizationId: orgId,
        productId: product.id,
        logoUploadFee: cardProduct.unitPrice,
        discountTotal: calculateFeeDiscount({
          orgDiscount: cardProduct.discount,
          globalDiscount: createProductDiscount,
        }),
        orderTotal,
        creditCardFee,
        gstAmount,
        termsAndConditionsVersion,
        termsAndConditionsAcceptedBy,
        deliveryRecipientName,
        deliveryAddressLine1,
        deliveryAddressLine2,
        deliverySuburb,
        deliveryCity,
        deliveryPostcode,
      },
    }),
    (error) => {
      logger.warn('failed to create create card order', error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

function createUniqueCustomProductOrderNumber({
  id,
  orderNumber,
}: {
  id: string
  orderNumber: string
}) {
  return fromPromise(
    prisma.customProductOrder.update({
      where: {
        id,
      },
      data: {
        orderNumber,
      },
    }),
    (error) => {
      logger.warn('failed to update create card order', error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

function updateCustomProductOrderWithPaymentDetails({
  orgId,
  orderNumber,
  billingAddress,
  city,
  country,
  paymentMethod,
  postCode,
  purchaseOrderNumber,
  creditCardFee,
  orderTotal,
  isLiveAgent,
  authorizedByName,
  authorizedByEmail,
}: SubmitCustomProductProps & {
  creditCardFee?: number
  orderTotal?: number
}) {
  return fromPromise(
    prisma.customProductOrder.updateMany({
      where: {
        orderNumber: orderNumber,
        orderStatus: OrderStatus.PENDING,
        organizationId: orgId,
      },
      data: {
        paymentMethod,
        billingAddress: billingAddress,
        city: city,
        country: country,
        postCode: postCode,
        purchaseOrderNumber: purchaseOrderNumber,
        orderStatus: OrderStatus.SUBMITTED,
        creditCardFee,
        orderTotal,
        createdByLiveAgent: isLiveAgent,
        authorizedByName: authorizedByName ? authorizedByName : null,
        authorizedByEmail: authorizedByEmail ? authorizedByEmail : null,
      },
    }),
    (error) => {
      logger.error(
        error,
        `error updating custom product order with payment details, order number [${orderNumber}]`
      )

      return ERRORS.DATABASE_ERROR
    }
  ).andThen(({ count }) => {
    if (count !== 1) {
      logger.error(
        `failed to update custom product order with payment details with order number [${orderNumber}]`
      )
      return err({
        code: ERRORS.NOT_FOUND.code,
        message: 'failed to update custom product order with payment details',
      })
    }
    return findCustomProductOrderWithOrderNumber(orderNumber)
  })
}

function findCustomProductOrderWithOrderNumber(orderNumber: string) {
  return fromPromise(
    prisma.customProductOrder.findFirst({
      where: {
        orderNumber,
      },
      select: {
        id: true,
        orderNumber: true,
        orderTotal: true,
        creditCardFee: true,
        product: {
          select: {
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            email: true,
          },
        },
        organization: {
          select: {
            name: true,
          },
        },
      },
    }),
    (err) => {
      logger.error(
        err,
        `error finding custom product order, order number [${orderNumber}]`
      )

      return ERRORS.DATABASE_ERROR
    }
  ).andThen((customProductOrder) => {
    if (!customProductOrder) {
      const message = `failed to find custom product with order number [${orderNumber}]`
      logger.error(message)
      return errAsync({
        code: ERRORS.NOT_FOUND.code,
        message,
      })
    }
    return ok(customProductOrder)
  })
}

export function sendCustomProductToPlacard({
  productId,
  orderNumber,
}: {
  productId: string
  orderNumber: string
}) {
  return findCustomProductOrderWithOrderNumberAndProductId({
    productId,
    orderNumber,
  })
    .andThen((order) => {
      if (!order?.product?.logo?.name) {
        logger.error(
          `[sendCustomProductToPlacard]: Failed to find custom order ${orderNumber} with product id ${productId}`
        )
        return err({
          code: ERRORS.NOT_FOUND.code,
          message: 'failed to find custom product order',
        })
      }

      const key = `logo/${order.product.logo.name}`
      logger.info(`uploading logo to sftp with key: ${key}`)

      // Assume the logo's key is stored in order.product.logo.key
      return ResultAsync.fromPromise(
        uploadLogoToSftp({
          key,
        }),
        (error) => {
          logger.error(
            error,
            '[sendCustomProductToPlacard] failed to upload logo to sftp'
          )
          return ERRORS.SFTP_ERROR
        }
      )
    })
    .andThen((result: any) => {
      // For some reason a aysncResult is not propogating up from uploadLogoToSftp
      // so we need to check the error here
      if (result.error) {
        logger.error(
          result.error,
          '[sendCustomProductToPlacard] failed to upload logo to sftp'
        )
        return errAsync(result)
      }
      logger.debug(`uploadLogoToSftp result: ${JSON.stringify(result)}`)
      logger.info(`updating product status to SENT with id: ${productId}`)
      return updateProductStatusAfterSendingToPlacard(productId)
    })
    .andThen(() => {
      logger.info(
        `updating custom product order status to COMPLETED with orderNumber: ${orderNumber}`
      )
      return updateCustomProductOrderStatus({
        orderNumber,
        orderStatus: OrderStatus.COMPLETED,
      })
    })
}

function updateCustomProductOrderStatus(
  {
    orderNumber,
    orderStatus,
  }: {
    orderNumber: string
    orderStatus: OrderStatus
  },
  data?: Prisma.CustomProductOrderUpdateInput
) {
  return fromPromise(
    prisma.customProductOrder.update({
      where: {
        orderNumber,
      },
      data: {
        orderStatus,
        ...data,
      },
    }),
    (error) => {
      logger.error(error, 'failed to update custom product order status')
      return ERRORS.DATABASE_ERROR
    }
  )
}

function findCustomProductOrderWithOrderNumberAndProductId({
  orderNumber,
  productId,
}: {
  orderNumber: string
  productId: string
}) {
  return fromPromise(
    prisma.customProductOrder.findFirst({
      where: {
        orderNumber,
        productId,
      },
      include: {
        product: {
          include: {
            logo: true,
          },
        },
      },
    }),
    (error) => {
      logger.error(error)
      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: ERRORS.DATABASE_ERROR.message,
      }
    }
  )
}

export function confirmWindcaveSessionForCustomProductOrder(
  windcaveSessionId: string
) {
  return fromPromise(
    prisma.customProductOrder.updateMany({
      where: {
        windcaveSessionId,
        windcaveSessionConfirmed: false,
      },
      data: {
        windcaveSessionConfirmed: true,
      },
    }),
    (error) => {
      logger.error(
        error,
        `[Custom Product] Failed to update product order with windcave sessionId: ${windcaveSessionId}`
      )
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function findCustomProductByOrderNumber(orderNumber: string) {
  return fromPromise(
    prisma.customProductOrder.findFirst({
      where: {
        orderNumber,
      },
      include: {
        organization: true,
        user: true,
      },
    }),
    (error) => {
      logger.warn(`Error fetching Custom product order ${orderNumber} `, error)

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function createStockCardProductAndOrder({
  designUrl,
  logoUrl,
  name,
  logoFileName,
  orgId,
  userId,
  termsAndConditionsVersion,
  deliveryAddress,
}: {
  orgId: string
  userId: string
} & CreateStockCardProductArgs) {
  return createLogoAndProductForStockCard({
    designUrl,
    logoUrl,
    name,
    logoFileName,
    orgId,
  })
    .andThen((product) => {
      return getCustomProductOrderItem({
        productCode: CREATE_STOCK_PRODUCT_CODE,
      }).map((cardProduct) => {
        return { cardProduct, product }
      })
    })
    .andThen(({ cardProduct, product }) => {
      if (!cardProduct) {
        return err({
          code: 'DB-101',
          message:
            'createStockCardProductAndOrder..failed to find create card order item',
        })
      }
      return createCustomProductOrder({
        cardProduct,
        product,
        orgId,
        userId,
        termsAndConditionsVersion,
        termsAndConditionsAcceptedBy: userId,
        cardType: 'STOCK',
        deliveryRecipientName: deliveryAddress.recipientName,
        deliveryAddressLine1: deliveryAddress.addressLine1,
        deliveryAddressLine2: deliveryAddress.addressLine2,
        deliverySuburb: deliveryAddress.suburb,
        deliveryCity: deliveryAddress.city,
        deliveryPostcode: deliveryAddress.postcode,
      })
    })
}

// product-order.service.ts

export function getCustomProductOrderPaymentSummary({
  orderNumber,
  orgId,
}: {
  orderNumber: string
  orgId: string
}) {
  return fromPromise(
    prisma.customProductOrder.findFirst({
      where: {
        orderNumber,
        organizationId: orgId,
      },
      include: {
        product: {
          include: {
            design: {
              select: {
                cardDesignUrl: true,
                ribbonColor: true,
              },
            },
            logo: {
              select: {
                logoUrl: true,
              },
            },
          },
        },
      },
    }),
    (err) => {
      logger.warn('Error finding stock card custom order', err)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((customProductOrder) => {
    // First check if order exists
    if (!customProductOrder) {
      logger.warn(
        `No custom product order found for orderNumber: ${orderNumber}`
      )
      return err({
        code: ERRORS.NOT_FOUND.code,
        message: 'Custom product order not found',
      })
    }

    // Check each required field individually for better error logging
    const missingFields = []
    if (customProductOrder.logoUploadFee === null)
      missingFields.push('logoUploadFee')
    if (customProductOrder.discountTotal === null)
      missingFields.push('discountTotal')
    if (customProductOrder.orderTotal === null) missingFields.push('orderTotal')
    if (customProductOrder.gstAmount === null) missingFields.push('gstAmount')
    if (customProductOrder.creditCardFee === null)
      missingFields.push('creditCardFee')

    if (missingFields.length > 0) {
      logger.warn(
        `Missing payment summary values for order ${orderNumber}. Missing fields: ${missingFields.join(
          ', '
        )}`
      )
      return err({
        code: ERRORS.MISSING_REQUIRED_FIELD.code,
        message: `Custom product order is missing required payment fields: ${missingFields.join(
          ', '
        )}`,
      })
    }

    // All required fields exist, return payment summary
    return ok({
      orderType: 'CUSTOM',
      orderNumber: customProductOrder.orderNumber,
      orderTotal: centsToDollars(customProductOrder.orderTotal),
      discountTotal: centsToDollars(customProductOrder.discountTotal),
      gstAmount: centsToDollars(customProductOrder.gstAmount),
      subTotal: centsToDollars(customProductOrder.logoUploadFee),
      creditCardFee: centsToDollars(customProductOrder.creditCardFee || 0), // Provide default of 0 if null
      cardDesignUrl: customProductOrder.product?.design?.cardDesignUrl,
      logoUrl: customProductOrder.product?.displayLogoUrl
        ? customProductOrder.product?.displayLogoUrl
        : customProductOrder.product?.logo?.logoUrl,
      ribbonColor: customProductOrder.product?.design?.ribbonColor,
    })
  })
}
