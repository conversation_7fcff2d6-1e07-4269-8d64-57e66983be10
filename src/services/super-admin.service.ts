import { Prisma } from '@prisma/client'
import logger from 'utils/logger'
import { ResultAsync, err, ok, okAsync } from 'neverthrow'
import {
  countOrganizationsWithSearch,
  searchOrganizations,
} from 'epay-data/epay-organization.data'
import { updateUserMetadata } from 'external-apis/auth0/update-user-metadata.api'
import {
  getUserInfoByEmail,
  UserInfo,
  UserMetadata,
} from 'external-apis/auth0/users-by-email.api'
import { errorCodes } from 'types/simple-error'
import { appendEmailSuffix } from 'helpers/add-email-suffix'

const SUPERADMIN_MINUTES_TO_EXPIRE = 30 * 60 * 1000 // 30 minutes

const SUPERADMIN_EMAIL_SUFFIX = 'super'

export function getCompanyListForSuperAdminWithFilter({
  searchTerm,
  page,
  pageSize,
}: {
  searchTerm?: string
  page: number
  pageSize: number
}) {
  logger.info(
    `getCompanyListForSuperAdminWithFilter...[searchSearch: ${searchTerm}]`
  )

  const whereClause: Prisma.OrganizationWhereInput = searchTerm
    ? {
        OR: [
          {
            name: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            customerCode: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
        ],
      }
    : {}

  return ResultAsync.combine([
    countOrganizationsWithSearch(whereClause),
    searchOrganizations({
      where: whereClause,
      page,
      pageSize,
    }),
  ]).map(([count, organizations]) => ({
    count,
    organizations: organizations.map((org) => ({
      id: org.id,
      name: org.name,
      customerCode: org.customerCode,
    })),
  }))
}

type EmailParts = {
  localPart: string
  domain: string
}

export function addSuperAdminToOrg({
  userId,
  userEmail,
  targetOrgId,
}: {
  userId: string
  userEmail: string
  targetOrgId: string
}) {
  logger.debug(`addSuperAdminToOrg..userEmail [${userEmail}]`)
  return getSuperAdminUserInfo({ userEmail, userId })
    .andThen((userInfo) => {
      const { localPart, domain, fullEmail } = appendEmailSuffix(
        userEmail,
        SUPERADMIN_EMAIL_SUFFIX
      )
      const superAdminEmail = fullEmail

      if (!userInfo) {
        logger.info(
          `User [${userId}] [${userEmail}] admin portal user has no corresponding corporate portal user with the email [${superAdminEmail}].`
        )

        return err({
          code: errorCodes.external.api.BAD_REQUEST,
          message: `You need to have a login to the corporate portal with the email ${localPart} @${domain}`,
        })
      }
      const expires = new Date(
        new Date().getTime() + SUPERADMIN_MINUTES_TO_EXPIRE
      ).toISOString()

      const updatedInfo = userInfo

      if (!updatedInfo?.user_metadata) {
        updatedInfo.user_metadata = {} as UserMetadata
      }

      updatedInfo.user_metadata.super_admin_config = {
        org_id: targetOrgId,
        is_live_agent: true,
        expires_at: expires,
      }

      return ok(updatedInfo as UserInfo)
    })
    .andThen((userInfo) => {
      return updateUserMetadata({ userInfo, isAdminPortalUser: false })
    })
}

function validateUserIdMatches(userInfo: UserInfo, userId: string) {
  logger.debug('validateUserIdMatches', {
    userInfoId: userInfo.user_id,
    userId,
  })
  if (userId.length > 0 && userInfo.user_id.indexOf(userId) !== -1) {
    return ok(userInfo)
  }

  return err({
    code: errorCodes.UNEXPECTED,
    message: 'addSuperAdminToOrg..userId mismatch',
  })
}

export function revokeSuperAdmin({
  userEmail,
  userId,
}: {
  userEmail: string
  userId: string
}) {
  return getSuperAdminUserInfo({ userEmail, userId })
    .andThen((userInfo) => {
      const updatedInfo = userInfo

      if (updatedInfo.user_metadata?.super_admin_config) {
        updatedInfo.user_metadata.super_admin_config = null
        return updateUserMetadata({
          userInfo: updatedInfo,
          isAdminPortalUser: false,
        })
      }

      return okAsync(null)
    })
    .map(() => {
      return { success: true }
    })
}

/// Get the corporate portal user info for an admin portal user
export function getSuperAdminUserInfo({
  userEmail,
  userId,
}: {
  userEmail: string
  userId: string
}) {
  logger.debug('getSuperAdminUserInfo', { userEmail, userId })
  // Get the user info for the admin portal user
  return getUserInfoByEmail({ email: userEmail, isAdminPortalUser: true })
    .andThen((userInfo) => {
      // Validate that the user id matches the userId in the request token
      return validateUserIdMatches(userInfo, userId)
    })
    .andThen(() => {
      const { fullEmail } = appendEmailSuffix(
        userEmail,
        SUPERADMIN_EMAIL_SUFFIX
      )
      const superAdminEmail = fullEmail
      // Get the user info for the corporate portal user with the same email
      logger.debug(
        `getSuperAdminUserInfo..getUserInfoByEmail ${superAdminEmail}`
      )
      return getUserInfoByEmail({
        email: superAdminEmail,
        isAdminPortalUser: false,
      })
    })
}
