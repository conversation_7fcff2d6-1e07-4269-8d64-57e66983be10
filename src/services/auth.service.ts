import { UserRole } from '@prisma/client'
import { errAsync, okAsync } from 'neverthrow'
import { getUserById, upsertUser } from './user.service'
import { CORPORATE_PORTAL_APP_ID } from 'utils/config'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { getOrganizationById } from 'data/organization.data'
import { z } from 'zod'
import { getSuperAdminUserInfo } from './super-admin.service'

export const signInSchema = z.object({
  epayAppId: z.string(),
  userId: z.string().transform((id) => id.replace('auth0|', '')),
  userEmail: z.string().email('Invalid email format'),
  isLiveAgent: z.boolean().default(false),
  targetOrgId: z.string().optional(),
  expiresAt: z.string().optional(),
})

type SignInSchema = z.infer<typeof signInSchema>

type GetUserResult = {
  userId: string
  userEmail: string
  userRole: UserRole | null
  orgId: string | null
  orgName: string | null
  expiresAt: string | null
  isLiveAgent: boolean
  targetOrgId?: string
}

/**
 * Used in Auth0 Post Login action (Onboarding - Login) to enrich the access token with user data.
 * If user does not exist, it creates it and returns the user data.
 *
 * @param {SignInSchema} data - The user data received from Auth0.
 * @returns {Promise<ResultAsync>} A promise that resolves to an object containing the user's information.
 */
export function getUserForAuth0(data: SignInSchema) {
  logger.debug('getUserForAuth0', data)
  if (data.epayAppId !== CORPORATE_PORTAL_APP_ID) {
    return getAdminUser(data)
  }

  if (data.isLiveAgent && !isSuperAdminSessionExpired(data.expiresAt)) {
    return getSuperAdminUser({
      expiresAt: data.expiresAt!,
      targetOrgId: data.targetOrgId!,
      userId: data.userId,
    }).orElse(() => getRegularUser(data))
  }

  return getRegularUser(data)
}

function isSuperAdminSessionExpired(expiryDate?: string) {
  if (!expiryDate) {
    return true
  }

  return new Date().getTime() > new Date(expiryDate).getTime()
}

function getRegularUser({
  userId,
  userEmail,
  epayAppId,
}: {
  userId: string
  userEmail: string
  epayAppId: string
}) {
  return getUserById({ id: userId })
    .andThen((user) => {
      if (user) {
        return okAsync(user)
      }

      return upsertUser({
        id: userId,
        email: userEmail,
        role: UserRole.ORG_ADMIN,
      })
    })
    .map(
      (user) =>
        ({
          userId: user.id,
          userEmail: user.email,
          userRole: user.role,
          orgId: user.organization?.id ?? null,
          orgName: user.organization?.name ?? null,
          isLiveAgent: false,
          expiresAt: null,
        } as GetUserResult)
    )
}

function getSuperAdminUser({
  expiresAt,
  targetOrgId,
  userId,
}: {
  expiresAt: string
  targetOrgId: string
  userId: string
}) {
  return getUserById({ id: userId })
    .andThen((user) => {
      if (user) {
        return okAsync(user)
      }

      logger.warn(`User [${userId}] not in EPAY`)
      return errAsync(ERRORS.UNAUTHORIZED)
    })
    .andThen((user) => {
      return getOrganizationById(targetOrgId).andThen((org) => {
        if (org) {
          return okAsync({
            userId: user.id,
            userEmail: user.email,
            userRole: user.role,
            orgId: org?.id ?? null,
            orgName: org?.name ?? null,
            isLiveAgent: true,
            expiresAt,
          } as GetUserResult)
        }

        logger.warn(`Organization [${targetOrgId}] not found in EPAY`)
        return errAsync(ERRORS.UNAUTHORIZED)
      })
    })
}

function getAdminUser({
  userId,
  userEmail,
}: {
  userId: string
  userEmail: string
}) {
  logger.debug('getAdminUser')
  return getUserById({ id: userId })
    .andThen((user) => {
      logger.debug('getUserById', user)
      if (user) {
        return okAsync(user)
      }

      logger.warn(`User [${userId}] not found in EPAY`)
      return errAsync(ERRORS.UNAUTHORIZED)
    })
    .andThen((user) => {
      return getSuperAdminUserInfo({
        userId,
        userEmail,
      }).map((superAdminData) => {
        logger.debug('superAdminConfig', superAdminData)

        const adminUser = {
          userId: user.id,
          userEmail: user.email,
          userRole: user.role,
          orgId: user.organization?.id ?? null,
          orgName: user.organization?.name ?? null,
          isLiveAgent: false,
          expiresAt: null,
        } as GetUserResult

        const superAdminConfig =
          superAdminData?.user_metadata?.super_admin_config

        if (
          superAdminConfig &&
          superAdminConfig.is_live_agent &&
          !isSuperAdminSessionExpired(superAdminConfig.expires_at)
        ) {
          adminUser.isLiveAgent = true
          adminUser.expiresAt = superAdminConfig.expires_at
          adminUser.targetOrgId = superAdminConfig.org_id
        }

        logger.debug('Admin user', adminUser)
        return adminUser
      })
    })
}
