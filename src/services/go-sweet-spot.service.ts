import { GO_SWEET_SPOT_API_KEY } from 'utils/config'
import axios from 'axios'
import { fromPromise } from 'neverthrow'
import { handleAxiosError } from 'utils/axios-error'

export const DELIVERY_TYPE = {
  URBAN: 'URBAN',
  RURAL: 'RURAL',
  WAIHEKE: 'WAIHEKE',
}

export type GoSweetSpotAddressValidationRequest = {
  BuildingName?: string
  StreetAddress: string
  Suburb: string
  City?: string
  PostCode?: string
  CountryCode: string
}

export function getAddressType({
  BuildingName,
  StreetAddress,
  Suburb,
  City,
  PostCode,
  CountryCode,
}: GoSweetSpotAddressValidationRequest) {
  const data = JSON.stringify({
    Address: {
      BuildingName,
      StreetAddress,
      Suburb,
      City,
      PostCode,
      CountryCode,
    },
  })

  // fromPromise nested to make sure that this can never error, if it errors we give default urban cost
  return fromPromise(
    fromPromise(
      axios('https://api.gosweetspot.com/v2/addressvalidation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          access_key: `${GO_SWEET_SPOT_API_KEY}`,
          site_id: 'epay-corporate-portal',
          supportemail: '<EMAIL>',
        },
        data: data,
      }),
      (error) => {
        handleAxiosError(error, 'GoSweetSpot address validation')
        return {
          error: 'EXTERNAL_API' as const,
          data: { error },
        }
      }
    )
      .map(({ data, status }) => {
        const isRural = data.Address.Address.IsRural

        return isRural ? DELIVERY_TYPE.RURAL : DELIVERY_TYPE.URBAN
      })
      .unwrapOr(DELIVERY_TYPE.URBAN),
    () => {
      return undefined
    }
  )
}
