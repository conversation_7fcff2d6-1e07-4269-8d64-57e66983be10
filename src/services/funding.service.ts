import {
  FundingProductOrderItem,
  OrderStatus,
  OrderType,
  Prisma,
  Product,
  ProductOrder,
} from '@prisma/client'
import { TESTING_MIN_VALUE } from 'constants/prezzy'
import { fromPromise, okAsync, ResultAsync } from 'neverthrow'
import { AvailableCardsListProps, FundingOrderArgs } from 'routes/funding.route'
import { CREDIT_CARD_SURCHARGE, IS_PROD } from 'utils/config'
import { ErrorResponse, ERRORS } from 'utils/error'
import logger from 'utils/logger'
import {
  centsToDollars,
  dollarsToCents,
  generateRandomNumbers,
  getGst,
} from 'utils/numeric'
import { prisma } from 'utils/prisma'
import {
  AggregateInvoiceLines,
  calculateFeeDiscount,
  createStockProductMap,
  getOrgDiscount,
  getProductOrder,
  StockProduct,
} from './product-order.service'
import { getProductOrderItemCardIds } from 'data/product-order-item-card.data'
import { NO_LOCK_CODE } from './product-order/product-order-util'
import { getGlobalDiscounts } from 'data/discount.data'
import { getProductOrderWithFundingOrderItems } from 'data/product-order.data'
import { createFundingProductOrderItems } from 'data/funding-product-order-item.data'

export function getAvailableCards({
  orgId,
  props,
}: {
  orgId: string
  props: AvailableCardsListProps
}) {
  const whereClause: Prisma.ProductOrderItemCardWhereInput = {
    unitPriceInCents: 0,
    usedInFundingOrder: false,
    productOrderItem: {
      productOrder: {
        organizationId: orgId,
      },
    },
  }

  if (props.searchTerm) {
    whereClause.OR = [
      {
        productOrderItem: {
          productOrder: {
            orderNumber: { contains: props.searchTerm, mode: 'insensitive' },
          },
        },
      },
      {
        productOrderItem: {
          product: {
            name: { contains: props.searchTerm, mode: 'insensitive' },
          },
        },
      },
      {
        externalCardReferenceNumber: {
          contains: props.searchTerm,
          mode: 'insensitive',
        },
      },
    ]
  }

  return ResultAsync.combine([
    countProductOrderItemsWithSearch(whereClause),
    searchProductOrderItems({
      where: whereClause,
      page: props.page,
      pageSize: props.pageSize,
    }),
  ]).map(([count, cards]) => ({
    count,
    cardItems: cards.map((card) => ({
      orderNumber: card.productOrderItem.productOrder.orderNumber,
      crn: card.externalCardReferenceNumber,
      designUrl: card.productOrderItem.product.design.cardDesignUrl,
      logoUrl: card.productOrderItem.product.logo?.logoUrl,
      productName: card.productOrderItem.product.name,
      storeValue: card.productOrderItem.unitPrice,
      productCode: card.productOrderItem.product.productCode,
      maxValue: centsToDollars(card.productOrderItem.product.maxValue),
      minValue: centsToDollars(
        IS_PROD ? card.productOrderItem.product.minValue : TESTING_MIN_VALUE
      ),
    })),
  }))
}

function countProductOrderItemsWithSearch(
  where: Prisma.ProductOrderItemCardWhereInput
) {
  return fromPromise(prisma.productOrderItemCard.count({ where }), (error) => {
    logger.error(error, 'Failed to count available cards')
    return { code: 'DB-100', message: 'Database operation failed' }
  })
}

function searchProductOrderItems({
  where,
  pageSize,
  page,
}: {
  where: Prisma.ProductOrderItemCardWhereInput
  pageSize: number
  page: number
}) {
  return fromPromise(
    prisma.productOrderItemCard.findMany({
      where,
      select: {
        externalCardReferenceNumber: true,
        productOrderItem: {
          select: {
            unitPrice: true,
            product: {
              select: {
                name: true,
                id: true,
                productCode: true,
                minValue: true,
                maxValue: true,
                design: { select: { cardDesignUrl: true } },
                logo: { select: { logoUrl: true } },
              },
            },
            productOrder: {
              select: {
                orderNumber: true,
              },
            },
          },
        },
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    }),
    (error) => {
      logger.error(error, 'Failed to fetch available cards')
      return { code: 'DB-100', message: 'Database operation failed' }
    }
  )
}

export function updateCardsUsedInFundingOrder(crns: string[]) {
  return ResultAsync.fromPromise(
    prisma.$transaction(async (tx) => {
      // Check if any of the CRNs are already used in a funding order
      const alreadyUsedCards = await tx.productOrderItemCard.findMany({
        where: {
          externalCardReferenceNumber: { in: crns },
          usedInFundingOrder: true,
        },
        select: {
          externalCardReferenceNumber: true,
        },
      })

      if (alreadyUsedCards.length > 0) {
        // If any cards are already used, return their CRNs without updating anything
        return {
          success: false,
          alreadyUsedCRNs: alreadyUsedCards.map(
            (card) => card.externalCardReferenceNumber
          ),
        }
      }

      // If no cards are already used, update all the specified cards
      await tx.productOrderItemCard.updateMany({
        where: {
          externalCardReferenceNumber: { in: crns },
        },
        data: {
          usedInFundingOrder: true,
        },
      })

      // Return success
      return {
        success: true,
        alreadyUsedCRNs: [],
      }
    }),
    (error) => {
      logger.error(error, 'Failed to update cards used in funding order')
      return {
        code: 'DB-100',
        message: 'Database operation failed',
      }
    }
  )
}

export function saveFundingOrder({
  orgId,
  fundingOrderItems,
  userId,
  orderTotals,
}: {
  orgId: string
  fundingOrderItems: Omit<
    Prisma.FundingProductOrderItemCreateInput,
    'productOrder'
  >[]
  userId: string
  orderTotals: FundingOrderTotals
}): ResultAsync<ProductOrder, ErrorResponse> {
  return fromPromise(
    prisma.productOrder.create({
      data: {
        organizationId: orgId,
        orderType: OrderType.SINGLE_FUNDS_LOAD,
        orderStatus: OrderStatus.PENDING,
        userId: userId,

        ...orderTotals,
        FundingProductOrderItem: {
          create: fundingOrderItems,
        },
      },
      include: {
        FundingProductOrderItem: true,
      },
    }),
    (error) => {
      logger.error(error, 'Failed to create funding product order')
      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: 'Failed to create funding productorder',
      }
    }
  )
}

export function createEmptyFundingOrder({
  orgId,
  userId,
}: {
  orgId: string
  userId: string
}): ResultAsync<ProductOrder, ErrorResponse> {
  return fromPromise(
    prisma.productOrder.create({
      data: {
        organizationId: orgId,
        orderType: OrderType.SINGLE_FUNDS_LOAD,
        orderStatus: OrderStatus.PENDING,
        userId: userId,
      },
    }),
    (error) => {
      logger.error(error, 'Failed to create funding product order')
      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: 'Failed to create funding productorder',
      }
    }
  )
}

export function createFundingProductOrderItemsForProductOrder({
  orgId,
  productOrderId,
  ...fundingOrderArgs
}: FundingOrderArgs & {
  orgId: string
  productOrderId: string
}) {
  const productCodeList = fundingOrderArgs.cards.map((item) => item.productCode)

  return ResultAsync.combine([
    getOrgDiscount(orgId),
    createStockProductMap({ stockCardOrderItems: productCodeList, orgId }),
    getProductOrderItemCardIds(fundingOrderArgs),
    getGlobalDiscounts(),
  ]).andThen(
    ([
      orgDiscount,
      { productMap },
      { productOrderItemCards },
      globalDiscount,
    ]) => {
      const fundingOrderItems = getFundingOrderItemData({
        fundingCardItems: fundingOrderArgs,
        orgDiscount: orgDiscount.loadingFee,
        productMap,
        productOrderItemCards,
        globalDiscount: globalDiscount.loadingFee,
        productOrderId,
      })

      return createFundingProductOrderItems(fundingOrderItems)
    }
  )
}

export function completeFundingProductOrder({
  productOrderId,
}: {
  productOrderId: string
}) {
  return getProductOrderWithFundingOrderItems(productOrderId)
    .andThen((productOrder) => {
      const totals = calcOrderTotalFromOrderItems(
        productOrder.FundingProductOrderItem || []
      )
      return okAsync({
        ...totals,
        orderNumber: productOrder.orderIncrementNumber.toString(),
      })
    })
    .andThen((totals) => {
      return fromPromise(
        prisma.productOrder.update({
          where: { id: productOrderId },
          data: {
            orderNumber: totals.orderNumber,
            subTotal: totals.subTotal,
            totalQuantity: totals.totalQuantity,
            gstAmount: totals.gstAmount,
            orderTotal: totals.orderTotal,
            loadingFeeTotal: totals.loadingFeeTotal,
            loadingFeeDiscountTotal: totals.loadingFeeDiscountTotal,
            discountTotal: totals.discountTotal,
            creditCardFee: totals.creditCardFee,
          },
        }),
        (e) => {
          logger.warn(`failed to getFundingProductOrder [${productOrderId}]`, e)
          return ERRORS.DATABASE_ERROR
        }
      )
    })
}

export function getFundingOrderLockCode({
  lockCodeType,
  orderLockCode,
}: {
  lockCodeType: string
  orderLockCode: string
}) {
  if (lockCodeType === NO_LOCK_CODE) {
    return NO_LOCK_CODE
  } else if (lockCodeType === 'unique_for_each_card') {
    return generateRandomNumbers(4)
  }

  return orderLockCode
}

type FundingOrderTotals = {
  subTotal: number
  totalQuantity: number
  shippingTotal: number
  gstAmount: number
  orderTotal: number
  loadingFeeTotal: number
  discountTotal: number
  loadingFeeDiscountTotal: number
  creditCardFee: number
}

export function aggregateFundingInvoiceLines({
  productOrderItems,
}: {
  productOrderItems: (FundingProductOrderItem & { product: Product })[]
}) {
  const lineItems: AggregateInvoiceLines = {}

  const quantity = 1

  productOrderItems.forEach((item) => {
    const key = `${item.productId}_${item.unitPriceInCents}`
    const loadingFee = item.loadingFee ?? 0
    const digitalFee = item.digitalFee ?? 0

    if (lineItems.hasOwnProperty(key)) {
      lineItems[key].quantity += quantity
      lineItems[key].subTotal += quantity * item.unitPriceInCents
      lineItems[key].loadingFee += loadingFee
      lineItems[key].discount += item.discount ?? 0
      lineItems[key].digitalFee += digitalFee
    } else {
      lineItems[key] = {
        productId: item.productId,
        productCode: item.product.productCode,
        quantity,
        unitPrice: item.unitPriceInCents,
        productName: item.product.name,
        subTotal: quantity * item.unitPriceInCents,
        discount: item.discount ?? 0,
        loadingFee,
        digitalFee,
      }
    }
  })

  const invoiceItems = Object.values(lineItems).map((item) => ({
    productId: item.productId,
    productCode: item.productCode,
    quantity: item.quantity,
    unitPrice: centsToDollars(item.unitPrice),
    productName: item.productName,
    subTotal: centsToDollars(item.subTotal),
    loadingFee: centsToDollars(item.loadingFee),
    discount: centsToDollars(item.discount),
    digitalFee: centsToDollars(item.digitalFee),
  }))

  return invoiceItems
}

function calcOrderTotalFromOrderItems(
  fundingOrderItems: FundingProductOrderItem[]
) {
  const subTotal = fundingOrderItems.reduce(
    (total, item) => total + item.unitPriceInCents,
    0
  )
  const totalQuantity = fundingOrderItems.length

  const loadingFeeTotal = fundingOrderItems.reduce(
    (total, item) => total + item.loadingFee!,
    0
  )

  const loadingFeeDiscountTotal = fundingOrderItems.reduce(
    (total, item) => total + item.loadingFeeDiscount!,
    0
  )

  const gstAmount = Math.round(getGst(subTotal - loadingFeeDiscountTotal))

  const baseTotal = subTotal + loadingFeeTotal - loadingFeeDiscountTotal

  const creditCardFee = Math.round(baseTotal * CREDIT_CARD_SURCHARGE)

  const orderTotal = baseTotal + creditCardFee

  return {
    subTotal,
    totalQuantity,
    gstAmount,
    orderTotal,
    loadingFeeTotal,
    loadingFeeDiscountTotal,
    discountTotal: loadingFeeDiscountTotal,
    creditCardFee,
  } as FundingOrderTotals
}

export function getFundingOrderPaymentSummary(orderNumber: string) {
  logger.debug('order', orderNumber)
  return getProductOrder({ orderNumber }).andThen((productOrder) => {
    const fundingOrderItems = aggregateFundingInvoiceLines({
      productOrderItems: productOrder.FundingProductOrderItem,
    })

    const cardReferenceNumbers = productOrder.FundingProductOrderItem.map(
      (item) => item.externalCardReferenceNumber
    )

    logger.info('funding', cardReferenceNumbers)

    return okAsync({
      orderNumber: productOrder.orderNumber,
      orderType: productOrder.orderType,
      orderTotal: centsToDollars(productOrder.orderTotal),
      shippingTotal: centsToDollars(productOrder.shippingTotal),
      gstNumber: productOrder.organization?.gstNumber,
      loadingFeeTotal: centsToDollars(productOrder.loadingFeeTotal),
      digitalFeeDiscountTotal: centsToDollars(
        productOrder.digitalFeeDiscountTotal
      ),
      loadingFeeDiscountTotal: centsToDollars(
        productOrder.loadingFeeDiscountTotal
      ),
      shippingFeeDiscountTotal: centsToDollars(
        productOrder.shippingFeeDiscountTotal
      ),
      discountTotal: centsToDollars(productOrder.discountTotal),
      digitalFeeTotal: centsToDollars(productOrder.digitalFeeTotal),
      creditCardFee: centsToDollars(productOrder.creditCardFee),
      subTotal: centsToDollars(productOrder.subTotal),
      gstAmount: centsToDollars(productOrder.gstAmount),
      totalQuantity: productOrder.totalQuantity,
      fundingOrderItems,
      cardReferenceNumbers,
      cardDesignUrl:
        productOrder.FundingProductOrderItem[0].product.design.cardDesignUrl,
      logoUrl: productOrder.FundingProductOrderItem[0].product.logo?.logoUrl,
    })
  })
}

function getFundingOrderItemData({
  fundingCardItems,
  productMap,
  orgDiscount,
  productOrderItemCards,
  globalDiscount,
  productOrderId,
}: {
  fundingCardItems: FundingOrderArgs
  productMap: Record<number, StockProduct>
  orgDiscount: number | null
  productOrderItemCards: { id: string; crn: string }[]
  globalDiscount: number
  productOrderId: string
}) {
  const now = new Date().toISOString()
  const orderLockCode = `${fundingCardItems.orderLockCode}`
  return fundingCardItems.cards.map((item) => {
    const product = productMap[item.productCode]

    const orderItemDiscount = calculateFeeDiscount({
      globalDiscount,
      orgDiscount,
    })

    const productOrderItemCard = productOrderItemCards.find(
      (card) => card.crn === item.crn
    )

    const fundingProductOrderItem = {
      externalCardReferenceNumber: item.crn,
      lockCode: getFundingOrderLockCode({
        lockCodeType: fundingCardItems.lockCodeType,
        orderLockCode,
      }),
      createdAt: now,

      unitPriceInCents: dollarsToCents(item.unitPriceInDollars),
      loadingFee: product.loadingFee,
      loadingFeeDiscount: orderItemDiscount,
      discount: orderItemDiscount,

      recipientEmail: item.recipientEmail,

      productId: product.id,

      productOrderItemCardId: productOrderItemCard?.id,

      productOrderId,
    }

    return fundingProductOrderItem
  })
}
