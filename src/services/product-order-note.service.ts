import { Prisma, ProductOrderNoteStatus } from '@prisma/client'
import { fromPromise } from 'neverthrow'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function createProductOrderNote({
  productOrderId,
  message,
  status,
  title,
  userId,
}: {
  productOrderId: string
  message: string
  status?: ProductOrderNoteStatus
  title?: string
  userId: string
}) {
  return fromPromise(
    prisma.productOrderNote.create({
      data: {
        userId,
        productOrderId,
        status,
        title,
        message,
      },
    }),
    (error) => {
      logger.error(
        error,
        `failed to create productOrderNote for ${productOrderId}`
      )

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2003'
      ) {
        return ERRORS.FOREIGN_KEY_CONSTRAINT
      }

      return ERRORS.DATABASE_ERROR
    }
  )
}
