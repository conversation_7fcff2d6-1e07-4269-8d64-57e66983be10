import { Address, Prisma } from '@prisma/client'
import { fromPromise, okAsync } from 'neverthrow'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function getAddressesByUserId({
  page,
  pageSize,
  query = '',
  userId,
}: {
  page: number
  pageSize: number
  query?: string
  userId: string
}) {
  const where = { userId } as Prisma.AddressWhereInput

  const trimmed = query?.trim()
  if (trimmed) {
    where.OR = [
      {
        address: {
          contains: trimmed,
          mode: 'insensitive',
        },
      },
      {
        city: {
          contains: trimmed,
          mode: 'insensitive',
        },
      },
      {
        suburb: {
          contains: trimmed,
          mode: 'insensitive',
        },
      },
      {
        postCode: {
          contains: trimmed,
          mode: 'insensitive',
        },
      },
    ]
  }

  return fromPromise(
    prisma.$transaction([
      prisma.address.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: {
          isDefault: 'desc',
        },
      }),
      prisma.address.count({ where }),
    ]),
    (error) => {
      logger.warn(`failed to query addresses [${userId}]`, error)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen(([items, count]) =>
    okAsync({
      items,
      count,
    })
  )
}

export function createAddress(data: Omit<Address, 'id'>) {
  return fromPromise(
    prisma.address.create({ data }).then((address) => {
      if (address.isDefault) {
        return unsetDefaultAddresses(address).then(() => address)
      }
      return address
    }),
    (error) => {
      logger.warn(`failed to create address [${JSON.stringify(data)}]`, error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function deleteAddressById(id: string) {
  return fromPromise(
    prisma.address.delete({
      where: { id },
    }),
    (error) => {
      logger.warn(`failed to delete address [${id}]`, error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

export function updateAddressById(data: Address) {
  return fromPromise(
    prisma.address
      .update({
        where: { id: data.id },
        data,
      })
      .then((address) => {
        if (address.isDefault) {
          return unsetDefaultAddresses(address).then(() => address)
        }
        return address
      }),
    (error) => {
      logger.warn(`failed to update address [${JSON.stringify(data)}]`, error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

/**
 * Unset the default address for all addresses of a user except the specified one.
 */
function unsetDefaultAddresses({ id, userId }: { id: string; userId: string }) {
  return prisma.address.updateMany({
    where: {
      id: { not: id },
      userId,
    },
    data: {
      isDefault: false,
    },
  })
}
