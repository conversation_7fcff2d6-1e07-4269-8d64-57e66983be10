import {
  ResultAsync,
  err,
  errAsync,
  from<PERSON>rom<PERSON>,
  ok,
  okAsync,
} from 'neverthrow'
import { createObjectCsvStringifier } from 'csv-writer'
import { centsToDollars, generateRandomNumbers } from 'utils/numeric'
import logger from 'utils/logger'
import * as fs from 'fs'
import { prisma } from 'utils/prisma'
import { ERRORS, PickErrorCodeKey } from 'utils/error'
import { sendEmailWithTemplate } from './postmark.service'
import {
  CardDirection,
  OrderStatus,
  Organization,
  Prisma,
  Product,
  ProductOrder,
  ProductOrderItem,
  ProductOrderItemCard,
  ProductStatus,
  ProductType,
  User,
} from '@prisma/client'

import { getCountryCode } from 'utils/country-code'
import {
  AWS_S3_BUCKET_REPORT,
  DISABLE_REPORTED_TO_EPAY_UPDATE,
  EPAY_REPORT_PROD_TEST,
  EPAY_SFTP_HOST,
  EPAY_SFTP_PASSPHRASE,
  EPAY_SFTP_PASSWORD,
  EPAY_SFTP_PORT,
  EPAY_SFTP_PRIVATE_KEY,
  EPAY_SFTP_REMOTE_PATH,
  EPAY_SFTP_USERNAME,
  IS_LOCAL,
} from 'utils/config'
import { s3Move, s3Put } from 'utils/aws'

import { getFilestreamFromS3, uploadToSftp } from 'utils/sftp'
import sftp from 'ssh2-sftp-client'
import { ensureTrailingSlash, timestampToFilename } from 'utils/format'
import {
  LOCKCODE_LIST_BY_ADDRESS_EMAIL,
  SINGLE_LOCK_CODE,
  STOCK_CARD_ORDER,
} from 'helpers/email-templates'
import { EpayLogosListProps } from 'types/epay.logos'
import { errorCodes, SimpleError } from 'types/simple-error'
import {
  getProductOrderLockCodeEmailData,
  ProductOrderLockCodeEmailData,
} from 'data/product-order.data'

export type EpayBatchResponse = {
  batchId: string
  cards: [
    {
      cardReferenceId: string
    }
  ]
}

export function getCardReferenceNumbersMocked(quantity: number) {
  return new Array(quantity).fill(null).map(() => {
    return `mock-${generateRandomNumbers(5)}`
  })
}

// You get a batch Id per order item
export async function getBatchIdsForOrder(orderNumber: string) {
  const result = await prisma.productOrder.findUnique({
    where: {
      orderNumber: orderNumber,
    },
    select: {
      orderNumber: true,
      productOrderItems: {
        select: {
          id: true,
          externalBatchId: true,
          quantity: true,
          unitPrice: true,
          cardItems: true,
          recipientName: true,
          recipientEmail: true, // Assuming this is the face value
        },
      },
    },
  })

  if (!result) {
    return errAsync({ code: 'NOT FOUND', message: 'Order not found' })
  }

  const batchIds = {
    orderNumber: result.orderNumber,
    productOrderItems: result.productOrderItems.map((item) => ({
      id: item.id,
      externalBatchId: item.externalBatchId,
      quantity: item.quantity,
      faceValue: item.unitPrice,
      recipientName: item.recipientName,
      recipientEmail: item.recipientEmail,
      cardItems: item.cardItems, // Assuming unitPrice is face value
    })),
  }

  return okAsync(batchIds)
}

export function sendLockCodeEmailsByOrderNumber(orderNumber: string) {
  logger.debug(
    'retrieving orders and productItems list to generate CRN and lockcode list'
  )
  return getProductOrderLockCodeEmailData(orderNumber).andThen(
    (productOrder) => {
      logger.debug(
        `found product order [${productOrder?.orderNumber}] with no lock code email sent`
      )

      if (productOrder.orderType === 'STOCK') {
        return sendStockCardOrderTypeEmail(productOrder)
      }

      return sendProductOrderTypeEmails(productOrder)
    }
  )
}

function sendProductOrderTypeEmails(
  productOrder: ProductOrderLockCodeEmailData
) {
  const emailContent = {
    productOrderId: productOrder.id,
    orderNumber: productOrder.orderNumber!,
    email: productOrder.user.email,
    name: productOrder.user.firstName ?? 'cardholder',
    sections: productOrder.productOrderItems
      .map((productOrderItem) => {
        return {
          address: productOrderItem.recipientAddress ?? '',
          referenceWithLockCodes: productOrderItem.cardItems.map((cardItem) => {
            return {
              reference: cardItem.externalCardReferenceNumber ?? '',
              lockCode: cardItem.lockCode ?? '',
            }
          }),
        }
      })
      .filter((section) => {
        return section.referenceWithLockCodes.length > 0
      }),
  }

  if (emailContent.sections.length === 0) {
    return sendSingleLockCodeEmail({
      name: emailContent.name,
      orderNumber: emailContent.orderNumber,
      lockCode: emailContent.sections[0].referenceWithLockCodes[0].lockCode,
      email: emailContent.email,
      productOrderId: productOrder.id,
    })
  } else {
    return sendOrderEmail(emailContent)
  }
}

function sendStockCardOrderTypeEmail(
  productOrder: ProductOrderLockCodeEmailData
) {
  const emailContent = {
    email: productOrder.user.email,
    productOrderId: productOrder.id,
    name: productOrder.user.firstName ?? 'cardholder',
    orderNumber: productOrder.orderNumber ?? '',
    sections: productOrder.productOrderItems.map((productOrderItem) => {
      const options = productOrderItem.options as unknown

      return {
        quantity: productOrderItem.quantity,
        productName: productOrderItem.product.name,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore - options object is not typed
        printResolution: options.resolution,
      }
    }),
  }

  return sendStockCardEmail(emailContent)
}

function sendStockCardEmail({
  productOrderId,
  email,
  name,
  orderNumber,
  sections,
}: {
  productOrderId: string
  email: string
  name: string
  orderNumber: string
  sections: {
    quantity: number
    productName: string
    printResolution: string
  }[]
}) {
  return sendEmailWithTemplate({
    email,
    templateModel: {
      name,
      orderNumber,
      sections,
    },
    templateId: STOCK_CARD_ORDER,
  }).andThen(() => {
    return updateProductOrderLockCodeEmailSent(productOrderId)
  })
}

function sendSingleLockCodeEmail({
  name,
  lockCode,
  email,
  orderNumber,
  productOrderId,
}: {
  name: string
  lockCode: string
  email: string
  orderNumber: string
  productOrderId: string
}) {
  return sendEmailWithTemplate({
    email,
    templateModel: {
      name,
      lockCode,
      orderNumber,
    },
    templateId: SINGLE_LOCK_CODE,
  }).andThen(() => {
    return updateProductOrderLockCodeEmailSent(productOrderId)
  })
}

function sendOrderEmail({
  productOrderId,
  email,
  name,
  sections,
}: {
  productOrderId: string
  email: string
  name: string
  sections: {
    address: string
    referenceWithLockCodes: {
      reference: string
      lockCode: string
    }[]
  }[]
}) {
  if (sections.length > 0) {
    return sendEmailWithTemplate({
      email,
      templateModel: {
        name,
        sections,
      },
      templateId: LOCKCODE_LIST_BY_ADDRESS_EMAIL,
    }).andThen(() => {
      return updateProductOrderLockCodeEmailSent(productOrderId)
    })
  }

  return updateProductOrderLockCodeEmailSent(productOrderId)
}

function updateProductOrderLockCodeEmailSent(id: string) {
  return fromPromise(
    prisma.productOrder.update({
      where: {
        id,
      },
      data: {
        lockCodeEmailSent: true,
      },
    }),
    (error) => {
      logger.warn(`failed to update product order with id [${id}]`, error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}

type CustomOrder = {
  createdAt: Date
  orderNumber: string | null
  email: string
  organizationName: string
  status: ProductStatus // Assuming ProductStatus is a defined type
  cardDesignUrl: string
  logoUrl: string
  orientation: CardDirection
  type: ProductType
}

export function getAllCustomProductOrdersForReview({
  filterText,
  status,
  page,
  pageSize,
  startDate,
  endDate,
}: EpayLogosListProps) {
  logger.debug(
    `Fetching custom product orders for review. Page: ${page}, PageSize: ${pageSize}`
  )

  const where: Prisma.CustomProductOrderWhereInput = {
    OR: [
      {
        orderNumber: {
          contains: filterText,
          mode: 'insensitive',
        },
      },
      {
        organization: {
          name: {
            contains: filterText,
            mode: 'insensitive',
          },
        },
      },
    ],

    product: {
      status: status,
    },

    orderStatus: {
      notIn: [OrderStatus.PENDING, OrderStatus.SUBMITTED],
    },

    createdAt: {
      gte: startDate,
      lte: endDate,
    },
  }

  const query = prisma.customProductOrder.findMany({
    where,
    select: {
      createdAt: true,
      orderNumber: true,
      user: {
        select: {
          email: true,
        },
      },
      organization: {
        select: {
          name: true,
        },
      },
      product: {
        select: {
          status: true,
          type: true,
          design: {
            select: {
              cardDesignUrl: true,
              cardDirection: true,
            },
          },
          logo: {
            select: {
              logoUrl: true,
            },
          },
        },
      },
    },
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
      createdAt: 'desc',
    },
  })

  const count = prisma.customProductOrder.count({ where })

  return fromPromise(prisma.$transaction([query, count]), (error) => {
    logger.error(error, 'failed to query custom product orders')
    return ERRORS.DATABASE_ERROR
  }).map(([customProductOrders, count]) => {
    logger.debug('customProductOrders', customProductOrders)
    logger.debug('count', count)

    const sortedOrders = customProductOrders.sort((a, b) => {
      const statusA = a.product.status === ProductStatus.PENDING ? 0 : 1
      const statusB = b.product.status === ProductStatus.PENDING ? 0 : 1

      if (statusA !== statusB) {
        return statusA - statusB
      }

      return b.createdAt.getTime() - a.createdAt.getTime()
    })

    const items = sortedOrders.reduce<CustomOrder[]>(
      (acc, customProductOrder) => {
        if (
          customProductOrder.product.logo !== null &&
          customProductOrder.product.logo.logoUrl !== null
        ) {
          acc.push({
            createdAt: customProductOrder.createdAt,
            orderNumber: customProductOrder.orderNumber,
            email: customProductOrder.user.email,
            organizationName: customProductOrder.organization.name,
            status: customProductOrder.product.status,
            cardDesignUrl: customProductOrder.product.design.cardDesignUrl,
            logoUrl: customProductOrder.product.logo.logoUrl,
            orientation: customProductOrder.product.design.cardDirection,
            type: customProductOrder.product.type,
          })
        }
        return acc
      },
      []
    )

    return {
      count,
      items,
    }
  })
}

export function getCustomProductOrderForApproval({
  orderNumber,
}: {
  orderNumber: string
}) {
  return fromPromise(
    prisma.customProductOrder.findUnique({
      where: {
        orderNumber: orderNumber,
      },
      select: {
        createdAt: true,
        orderNumber: true,
        deliveryRecipientName: true,
        deliveryAddressLine1: true,
        deliveryAddressLine2: true,
        deliverySuburb: true,
        deliveryCity: true,
        deliveryPostcode: true,
        user: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        organization: {
          select: {
            name: true,
          },
        },
        product: {
          select: {
            id: true,
            status: true,
            name: true,
            type: true,
            design: {
              select: {
                cardDesignUrl: true,
                cardDirection: true,
              },
            },
            logo: {
              select: {
                logoUrl: true,
                name: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.error(error, 'failed to query custom product orders')
      return ERRORS.DATABASE_ERROR
    }
  ).map((customProductOrder) => {
    if (customProductOrder && customProductOrder.product.logo !== null) {
      return {
        createdAt: customProductOrder.createdAt,
        orderNumber: customProductOrder.orderNumber,
        email: customProductOrder.user.email,
        name: `${customProductOrder.user.firstName} ${customProductOrder.user.lastName}`,
        organizationName: customProductOrder.organization.name,
        status: customProductOrder.product.status,
        cardDesignUrl: customProductOrder.product.design.cardDesignUrl,
        logoUrl: customProductOrder.product.logo.logoUrl,
        productName: customProductOrder.product.name,
        productId: customProductOrder.product.id,
        logoFileName: customProductOrder.product.logo.name,
        type: customProductOrder.product.type,
        orientation: customProductOrder.product.design.cardDirection,
        deliveryRecipient: customProductOrder.deliveryRecipientName,
        deliveryAddressLine1: customProductOrder.deliveryAddressLine1,
        deliveryAddressLine2: customProductOrder.deliveryAddressLine2,
        deliverySuburb: customProductOrder.deliverySuburb,
        deliveryCity: customProductOrder.deliveryCity,
        deliveryPostcode: customProductOrder.deliveryPostcode,
      }
    }
  })
}

async function getEpaySmtpPrivateKey() {
  if (IS_LOCAL) {
    return fs.readFileSync('./sftp_private_key').toString()
  }

  return EPAY_SFTP_PRIVATE_KEY
}

export async function sendReleasedOrderReport() {
  const reportResult = await getReleasedProductOrdersReport()
  if (reportResult.isErr()) {
    return errAsync(reportResult.error)
  }
  const { csvContent, orderIds } = reportResult.value

  logger.debug(`csvReportContent:\n${csvContent}`)

  const test = EPAY_REPORT_PROD_TEST ? 'TEST_' : ''

  //TODO - remove TEST from filename when ready to go live
  const filename = `${test}${timestampToFilename(
    Date.now()
  )}-released-orders.txt`
  const readyKey = `orders/ready/${filename}`
  const processedKey = `orders/processed/${filename}`
  const readyBody = Buffer.from(csvContent, 'utf-8')

  const uplaodResult = await s3Put({
    bucket: AWS_S3_BUCKET_REPORT,
    key: readyKey,
    body: readyBody,
    contentType: 'text/csv',
  })
  if (uplaodResult.isErr()) {
    logger.error(
      uplaodResult.error,
      'failed to upload released orders report to S3'
    )
    return errAsync(uplaodResult.error)
  }

  const readyFstream = await getFilestreamFromS3({
    bucket: AWS_S3_BUCKET_REPORT,
    key: readyKey,
  })
  if (readyFstream.isErr()) {
    logger.error(
      readyFstream.error,
      'failed to get released report file from s3 to send to Epay SFTP'
    )
    return errAsync(readyFstream.error)
  }
  logger.info(
    'Successfully fetched released orders report from S3 to send to Epay SFTP'
  )

  const options = {
    host: EPAY_SFTP_HOST,
    port: Number(EPAY_SFTP_PORT),
    username: EPAY_SFTP_USERNAME,
    privateKey: await getEpaySmtpPrivateKey(),
    passphrase: EPAY_SFTP_PASSPHRASE,
    password: EPAY_SFTP_PASSWORD,
  } as sftp.ConnectOptions

  const sftpResult = await uploadToSftp({
    fileStream: readyFstream.value.filestream,
    remotePath: `${ensureTrailingSlash(EPAY_SFTP_REMOTE_PATH)}${
      readyFstream.value.filename
    }`,
    options,
  })

  if (sftpResult.isErr()) {
    logger.error(
      sftpResult.error,
      'failed to upload released orders report to Epay SFTP'
    )
    return errAsync(sftpResult.error)
  }
  logger.info('Successfully uploaded released orders report to Epay SFTP')

  // mark as reported to epay
  if (!DISABLE_REPORTED_TO_EPAY_UPDATE) {
    const validOrderIds = orderIds.filter((id): id is string => id !== null)
    try {
      await prisma.productOrder.updateMany({
        where: { id: { in: validOrderIds } },
        data: { reportedToEpay: true },
      })
    } catch (error) {
      logger.error(
        error,
        'failed to update product orders as reported to epay after sending the report'
      )
      // don't return error here, as the report has been sent
    }
  }

  // move to processed folder
  const moveResult = await s3Move({
    sourceBucket: AWS_S3_BUCKET_REPORT,
    sourceKey: readyKey,
    destinationBucket: AWS_S3_BUCKET_REPORT,
    destinationKey: processedKey,
  })

  if (moveResult.isErr()) {
    return errAsync({
      code: ERRORS.S3_ERROR.code,
      message: `failed to move released orders csv file to processed folder. Error: ${moveResult.error}`,
    })
  }
  logger.info('Successfully moved released orders report to processed folder')

  return okAsync({ message: `some records were uploaded to S3` })
}

async function getReleasedProductOrdersReport() {
  let csvContent = ''
  let orderIds: (string | null)[] = []
  const ordersResult = await getReleasedProductOrders()
  if (ordersResult.isOk()) {
    const flattenedOrders = ordersResult.value.flatMap((order) => {
      return order.productOrderItems.flatMap((item) => {
        return {
          OrderNumber: `EP${order.orderNumber}`,
          SkuProductId: item.productCode,
          SkuProductCurrency: 'NZD',
          AmountLoadEventAt: 'A',
          PreActivated: 'Y',
          Quantity: item.quantity.toString(),
          PurchaseDate: order.submittedAt?.toISOString() ?? '',
          ShippingMethod: item.deliveryMethod === 'COURIER' ? '2' : '', // null for virtual
          CardPostageAndHandlingFee: centsToDollars(order.shippingTotal),
          CardLoadAmountFaceValue: centsToDollars(item.unitPrice),
          UnitPricePaid: 0.0,
          PartnersOrderNumber: order.purchaseOrderNumber ?? '',
          BillingName: order.organization.name,
          BillingAddressLine1: order.billingAddress?.substring(0, 50), // 50 char limit
          BillingAddressLine2: '',
          BillingCity: order.city, //todo: add billing city
          BillingState: '', //N/A
          BillingZipPostalCode: order.postCode ?? '',
          BillingCountryCode: getCountryCode(order.country ?? ''),
          EmailAddress: item.recipientEmail ?? '',
          LineNumber: item.lineNumber?.toString() ?? '', // order item number - for epay purposes only
          PaymentMethodChannel:
            order.paymentMethod === 'CREDIT_CARD' ? 'C' : 'B', // 1 for credit card, 2 for invoice
          CreditCardConvenienceFee: order.creditCardFee ?? 0, // what is this? probably credit card fee
          GoodsAndServicesTax: centsToDollars(order.gstAmount),
          CardPurchaseFee:
            item.deliveryMethod === 'COURIER'
              ? centsToDollars((item.loadingFee ?? 0) - (item.discount ?? 0))
              : centsToDollars(order.digitalFeeTotal),
          CardBatchNumber: item.externalBatchId ?? '',
          OrderStatus: 'A', // A = approved, Null = pending
          PurchaseTime: order.submittedAt?.toISOString() ?? '',
          TotalOrderAmount: centsToDollars(order.orderTotal),
          AdditionalField1: '',
          AdditionalField2: '', // card design id
        }
      })
    })

    const flattenedOrdersString = flattenedOrders
      .map((order) => {
        return Object.values(order).join('|')
      })
      .join('\n')

    const header = 'HEADER | NCS |' + flattenedOrders.length + '\n'

    const footer = '\nEOF-' + flattenedOrders.length

    const flattenedOrdersStringWithHeaderFooter =
      header + flattenedOrdersString + footer

    orderIds = ordersResult.value.map((order) => order.id)
    csvContent = flattenedOrdersStringWithHeaderFooter
  }

  return okAsync({ csvContent, orderIds: orderIds })
}

function getReleasedProductOrders() {
  return fromPromise(
    prisma.productOrder.findMany({
      where: {
        orderStatus: OrderStatus.PROCESSING,
        reportedToEpay: false,
      },
      include: {
        organization: true,
        productOrderItems: true,
      },
      orderBy: { createdAt: 'asc' },
    }),
    (error) => {
      logger.error(error, 'failed to fetch released orders to report on')
      return err(ERRORS.DATABASE_ERROR)
    }
  ).andThen((orders) => {
    if (orders.length === 0) {
      return err(ERRORS.NOT_FOUND)
    }

    logger.info(`Fetched ${orders.length} orders`)
    logger.debug(`Fetched orders: ${JSON.stringify(orders)}`)

    return ok(orders)
  })
}

export async function getCrnOrderReportAsCsv({
  startDate,
  endDate,
}: {
  startDate: Date
  endDate: Date
}) {
  logger.debug(`Fetching CRN order report for ${startDate} to ${endDate}`)

  const ordersResult = await getProductOrderCardsByDateRange({
    startDate,
    endDate,
  })

  if (ordersResult.isErr()) {
    return errAsync(ordersResult.error)
  }

  logger.debug(`ordersResult: ${JSON.stringify(ordersResult.value)}`)
  const csvContent = cardOrdersToCsvString(ordersResult.value)
  return okAsync({ csvContent, count: ordersResult.value.length })
}

export async function getProductOrderReportAsCsv({
  startDate,
  endDate,
}: {
  startDate: Date
  endDate: Date
}) {
  logger.debug(`Fetching product rder report for ${startDate} to ${endDate}`)

  const ordersResult = await getProductOrdersByDateRange({
    startDate,
    endDate,
  })

  if (ordersResult.isErr()) {
    return errAsync(ordersResult.error)
  }

  logger.debug(`ordersResult: ${JSON.stringify(ordersResult.value)}`)
  const csvContent = productOrdersToCsvString(ordersResult.value)
  return okAsync({ csvContent, count: ordersResult.value.length })
}

function getProductOrdersByDateRange({
  startDate,
  endDate,
}: {
  startDate: Date
  endDate: Date
}) {
  return fromPromise(
    prisma.productOrder.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        organization: true,
        user: true,
      },
      orderBy: { createdAt: 'asc' },
    }),
    (error) => {
      logger.error(error, 'failed to fetch released orders to report on')
      return err(ERRORS.DATABASE_ERROR)
    }
  ).andThen((orders) => {
    if (orders.length === 0) {
      return err(ERRORS.NOT_FOUND)
    }

    logger.info(`Fetched ${orders.length} orders`)
    logger.debug(`Fetched orders: ${JSON.stringify(orders)}`)

    return ok(orders)
  })
}

function getProductOrderCardsByDateRange({
  startDate,
  endDate,
}: {
  startDate: Date
  endDate: Date
}) {
  logger.debug('Fetching product order cards by date range')
  return fromPromise(
    prisma.productOrderItemCard.findMany({
      where: {
        productOrderItem: {
          productOrder: {
            submittedAt: {
              // This is actually the released date
              gte: startDate,
              lte: endDate,
            },
          },
        },
      },
      include: {
        productOrderItem: {
          include: {
            product: true,
            productOrder: {
              include: {
                organization: true,
                user: true,
              },
            },
          },
        },
      },
      orderBy: {
        productOrderItem: {
          productOrder: {
            submittedAt: 'asc', // or 'desc' for descending order
          },
        },
      },
    }),
    (error) => {
      logger.error(error, 'failed to fetch released orders to report on')
      return err(ERRORS.DATABASE_ERROR)
    }
  ).andThen((orderItemCards) => {
    if (orderItemCards.length === 0) {
      logger.info('No orderItemCards found')
      return err(ERRORS.NOT_FOUND)
    }

    logger.info(`Fetched ${orderItemCards.length} orderItemCards`)

    return ok(orderItemCards)
  })
}

type CardOrderReportItem = ProductOrderItemCard & {
  productOrderItem: ProductOrderItem & {
    product: Product
    productOrder: ProductOrder & {
      user: User
      organization: Organization
    }
  }
}

type ProductOrderReportItem = ProductOrder & {
  organization: Organization
  user: User
}

function productOrdersToCsvString(productOrders: ProductOrderReportItem[]) {
  const orders = productOrders.map((productOrder) => {
    const discount = centsToDollars(productOrder.discountTotal)
    const orderTotal = centsToDollars(productOrder.orderTotal)
    return {
      purchaseDate: productOrder.createdAt.toISOString(),
      orderNumber: productOrder.orderNumber,
      customerId: productOrder.organization.customerCode,
      customerName: productOrder.organization.name,
      totalOrderValue: orderTotal + discount,
      totalPaymentValue: orderTotal,
      totalFaceValue: centsToDollars(productOrder.subTotal),
      totalLoadFee: centsToDollars(productOrder.loadingFeeTotal),
      discount,
      quantity: productOrder.totalQuantity,
      totalShipmentFee: centsToDollars(productOrder.shippingTotal),
      totalConvenienceFee: centsToDollars(productOrder.creditCardFee),
      totalDigitalDeliveryFee: centsToDollars(productOrder.digitalFeeTotal),
      totalGst: centsToDollars(productOrder.gstAmount),
      paymentMethod: productOrder.paymentMethod,
      releaseDate: productOrder.submittedAt?.toISOString(),
      releasedBy: productOrder.releasedBy,
      comments: '',
      orderStatus: productOrder.orderStatus,
      orgId: productOrder.organization.id,
    }
  })

  const csvStringifier = createObjectCsvStringifier({
    header: [
      { id: 'purchaseDate', title: 'Purchase Date' },
      { id: 'orderNumber', title: 'Order Number' },
      { id: 'customerId', title: 'Partner ID (Customer ID)' },
      { id: 'customerName', title: 'Customer/Stakeholder Name' },
      { id: 'totalOrderValue', title: 'Total order value' },
      { id: 'totalPaymentValue', title: 'Total payment value' },
      { id: 'totalFaceValue', title: 'Total Face Value' },
      { id: 'totalLoadFee', title: 'Total Load Fee' },
      { id: 'discount', title: 'Discount' },
      { id: 'quantity', title: 'Cards quantity' },
      { id: 'totalShipmentFee', title: 'Total shipment Fee' },
      {
        id: 'totalConvenienceFee',
        title: 'Total Convenience Fee (credit card fee)',
      },
      { id: 'totalDigitalDeliveryFee', title: 'Total Digital Delivery Fee' },
      { id: 'totalGst', title: 'Total Fees GST' },
      { id: 'paymentMethod', title: 'Payment Method' },
      { id: 'releaseDate', title: 'Order Released Date' },
      { id: 'releasedBy', title: 'Released by' },
      { id: 'comments', title: 'Comments' },
      { id: 'orderStatus', title: 'Order Status' },
      { id: 'orgId', title: 'Org Id' },
    ],
  })

  return (
    csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(orders)
  )
}

function cardOrdersToCsvString(items: CardOrderReportItem[]) {
  const orders = items.map((item) => {
    const {
      loadingFee,
      discount,
      digitalFee,
      deliveryMethod,
      externalBatchId,
      productOrder,
      quantity,
    } = item.productOrderItem

    const { gstAmount, orderNumber, organization, submittedAt } = productOrder

    return {
      purchaseDate: submittedAt?.toISOString(),
      orderNumber: orderNumber,
      customerId: organization.customerCode,
      customerName: organization.name,
      crn: item.externalCardReferenceNumber,
      faceValue: centsToDollars(item.unitPriceInCents),
      loadFee: centsToDollars(loadingFee) / quantity,
      discount: centsToDollars(discount) / quantity,
      digitalFee: centsToDollars(digitalFee) / quantity,
      gst: centsToDollars(gstAmount),
      cardProgramName: deliveryMethod === 'COURIER' ? 'PHYSICAL' : 'VIRTUAL',
      cardBatchNumber: externalBatchId,
      orgId: organization.id,
    }
  })

  const csvStringifier = createObjectCsvStringifier({
    header: [
      { id: 'purchaseDate', title: 'Purchase Date' },
      { id: 'orderNumber', title: 'Order Number' },
      { id: 'customerId', title: 'Partner ID (Customer ID)' },
      { id: 'customerName', title: 'Customer/Stakeholder Name' },
      { id: 'crn', title: 'Card Reference Number' },
      { id: 'faceValue', title: 'Face Value' },
      { id: 'loadFee', title: 'Load Fee' },
      { id: 'discount', title: 'Discount' },
      { id: 'digitalFee', title: 'Digital fee' },
      { id: 'gst', title: 'GST' },
      { id: 'cardProgramName', title: 'Card Program Name' },
      { id: 'cardBatchNumber', title: 'Card batch number' },
      { id: 'orgId', title: 'Org Id' },
    ],
  })

  return (
    csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(orders)
  )
}
