import logger from 'utils/logger'

import axios from 'axios'
import { err, errAsync, fromPromise } from 'neverthrow'
import { handleAxiosError } from 'utils/axios-error'
import {
  ENVIRONMENT,
  POSTMARK_API_KEY,
  SENDER_EMAIL,
  TEAM_NOTIFICATION_EMAIL,
} from 'utils/config'
import { ok } from 'assert'

type Email = {
  to: string
  subject: string
  html: string
}

const getPostmarkHeaders = () => ({
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'X-Postmark-Server-Token': `${POSTMARK_API_KEY}`,
})

export function sendEmail({ to, subject, html }: Email) {
  return fromPromise(
    axios.post(
      'https://api.postmarkapp.com/email',
      {
        From: `${SENDER_EMAIL}`,
        To: `${to}`,
        Subject: `${subject}`,
        HtmlBody: `${html}`,
        MessageStream: 'outbound',
      },
      { headers: getPostmarkHeaders() }
    ),
    (error) => {
      handleAxiosError(error, 'Postmark sending email failed')
      return {
        error: 'EXTERNAL_API' as const,
        data: { error },
      }
    }
  )
}

type SendEmailWithTemplate = {
  email: string
  templateModel: { [key: string]: any }
  templateId: number
}

export function sendEmailWithTemplate({
  email,
  templateModel,
  templateId,
}: SendEmailWithTemplate) {
  logger.debug(`Sending email with template id: ${templateId} to ${email}`)
  const url = 'https://api.postmarkapp.com/email/withTemplate'
  return fromPromise(
    axios.post(
      'https://api.postmarkapp.com/email/withTemplate',
      {
        To: email,
        From: SENDER_EMAIL,
        TemplateId: templateId,
        TemplateModel: templateModel,
      },
      { headers: getPostmarkHeaders() }
    ),
    (error) => {
      return handleAxiosError(error, url)
    }
  )
}
/*** Fire and forget
 *** Sends an email to the team. In staging and prod, sends a notification to slack channel #epay-prod-notifications
 *** Summary should prefix with area in the format [logo] Ordered, description have the details you want read from slack
 ***/
export async function notifyTeam({
  summary,
  description,
}: {
  summary: string
  description: string
}) {
  const sendTo = TEAM_NOTIFICATION_EMAIL
  if (!sendTo) {
    logger.warn('No team notification email configured')
    return
  }
  try {
    axios.post(
      'https://api.postmarkapp.com/email',
      {
        To: sendTo,
        From: SENDER_EMAIL,
        Subject: `[${ENVIRONMENT}] ${summary}`,
        textBody: description,
      },
      { headers: getPostmarkHeaders() }
    )
  } catch (error) {
    // raises error in sentry
    logger.error(error, 'Failed to send email to team')
  }
}
