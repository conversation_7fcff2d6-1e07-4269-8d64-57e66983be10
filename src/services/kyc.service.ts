import type {
  CorporateApplicationStatus,
  KycStatus,
  Prisma,
} from '@prisma/client'
import {
  findCorporateApplicationWithNotes,
  updateCorporateApplication,
} from '../models/corporate-application'
import {
  createCorporateApplicationNote as createCorporateApplicationNoteModel,
  updateCorporateApplicationNote as updateCorporateApplicationNoteModel,
} from '../models/corporate-application-note'
import { createOrganisationAndWelcomeUser } from './corporate-application.service'

import { APPROVED_APPLICATION } from 'helpers/email-templates'
import { err, fromPromise, ok, okAsync } from 'neverthrow'
import { getS3SignedUrlGet } from 'utils/aws'
import { ERRORS, PickErrorCodeKey } from 'utils/error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'
import { sendEmailWithTemplate } from './postmark.service'

export function listCorporateApplicationsWithStatusCount({
  search,
  status,
  page,
  pageSize,
}: {
  search: string
  status?: CorporateApplicationStatus
  page: number
  pageSize: number
}) {
  let whereCondition: Prisma.CorporateApplicationWhereInput = {
    name: {
      contains: search,
      mode: 'insensitive',
    },
    status: { not: 'DRAFT' },
  }

  if (status === 'PENDING') {
    whereCondition = {
      ...whereCondition,
      OR: [
        { status: 'PENDING' },
        {
          AND: [{ status: 'APPROVED' }, { kycStatus: 'NKYC' }],
        },
      ],
    }
  } else if (status === 'APPROVED') {
    whereCondition = {
      ...whereCondition,
      status: 'APPROVED',
      OR: [{ kycStatus: { not: 'NKYC' } }, { kycStatus: null }],
    }
  } else if (status) {
    whereCondition.status = status
  }

  const corporateApplications = prisma.corporateApplication.findMany({
    where: whereCondition,
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
      updatedAt: 'desc',
    },
  })

  const count = prisma.corporateApplication.groupBy({
    by: ['status', 'kycStatus'],
    _count: {
      status: true,
    },
    where: {
      status: { not: 'DRAFT' },
    },
  })

  return fromPromise(
    prisma.$transaction([corporateApplications, count]),
    (error) => {
      logger.warn(
        `failed to fetch applications with status: [${status}]`,
        error
      )
      return ERRORS.DATABASE_ERROR
    }
  ).map(([corporateApplications, countList]) => {
    type CountsType = {
      PENDING: number
      REVIEWING: number
      DECLINED: number
      APPROVED: number
      PRE_APPROVED: number
      REQUIRES_INFO: number
      ALL: number
    }

    type StatusType = keyof Omit<CountsType, 'ALL'>

    type FilterStatus = Exclude<CorporateApplicationStatus, 'DRAFT'>

    const statusMapping: Record<FilterStatus, StatusType> = {
      PENDING: 'PENDING',
      REVIEWING: 'REVIEWING',
      DECLINED: 'DECLINED',
      APPROVED: 'APPROVED',
      PRE_APPROVED: 'PRE_APPROVED',
      REQUIRES_INFO: 'REQUIRES_INFO',
    }

    const counts = countList.reduce<CountsType>(
      (acc, current) => {
        const count = current._count.status
        acc.ALL += count

        const status = statusMapping[current.status as FilterStatus]

        if (status) {
          if (current.status === 'APPROVED' && current.kycStatus === 'NKYC') {
            acc.PENDING += count
          } else {
            acc[status] += count
          }
        }

        return acc
      },
      {
        PENDING: 0,
        REVIEWING: 0,
        DECLINED: 0,
        APPROVED: 0,
        PRE_APPROVED: 0,
        REQUIRES_INFO: 0,
        ALL: 0,
      }
    )

    return { corporateApplications, counts }
  })
}

export async function getCorporateApplication(id: string) {
  const applicationRes = await findCorporateApplicationWithNotes(id)

  if (applicationRes.isErr()) {
    return err(applicationRes.error)
  }

  const application = applicationRes.value

  if (application.status === 'PENDING') {
    const updateRes = await updateCorporateApplication({
      id: application.id,
      data: {
        status: 'REVIEWING',
      },
    })

    if (updateRes.isErr()) {
      return err(updateRes.error)
    }

    application.status = 'REVIEWING'
  }

  return ok(application)
}

export function createCorporateApplicationNote({
  userId,
  corporateApplicationId,
  title,
  message,
}: {
  userId: string
  corporateApplicationId: string
  title?: string
  message: string
}) {
  return createCorporateApplicationNoteModel({
    userId,
    corporateApplicationId,
    title,
    message,
  })
}

export function updateCorporateApplicationNote({
  id,
  title,
  message,
}: {
  id: string
  title?: string
  message: string
}) {
  return updateCorporateApplicationNoteModel({ id, data: { title, message } })
}

export function deleteCorporateApplicationNote({ id }: { id: string }) {
  return fromPromise(
    prisma.corporateApplicationNote.delete({
      where: {
        id,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn(`failed to delete note with id [${id}]`, error)

      return 'DATABASE_ERROR'
    }
  )
}

export function processCorporateApplication({
  corporateApplicationId,
  status,
  kycStatus,
}: {
  corporateApplicationId: string
  status: Extract<
    CorporateApplicationStatus,
    'APPROVED' | 'DECLINED' | 'REQUIRES_INFO' | 'PRE_APPROVED'
  >
  kycStatus?: KycStatus
}) {
  return updateCorporateApplication({
    id: corporateApplicationId,
    data: {
      status,
      kycStatus,
    },
  }).andThen((corporateApplication) => {
    if (!corporateApplication) {
      logger.warn(
        'failed to update status for application',
        corporateApplicationId
      )
      return err(ERRORS.NOT_FOUND)
    }

    if (
      corporateApplication.status === 'APPROVED' &&
      !corporateApplication.organization?.id
    ) {
      logger.info(
        'creatign new organization and welcoming user for approved application: ',
        corporateApplication.id
      )
      sendEmailWithTemplate({
        email: corporateApplication?.user.email,
        templateId: APPROVED_APPLICATION,
        templateModel: {
          orgName: corporateApplication.name,
          userName: corporateApplication.user.firstName,
        },
      })

      return createOrganisationAndWelcomeUser({
        corporateApplication,
      }).map(() => corporateApplication)
    }

    return okAsync(corporateApplication)
  })
}

export function addSkipKycEmail({ email }: { email: string }) {
  return fromPromise(
    prisma.skipKyc.findFirst({
      where: {
        email,
      },
    }),
    (error) => {
      logger.warn(`failed to check skip kyc email [${email}]`, error)

      return ERRORS.DATABASE_ERROR
    }
  ).andThen((emailExists) => {
    if (emailExists) {
      return err({
        code: ERRORS.ALREADY_EXIST.code,
        message: `Email [${email}] is already in the skip kyc list`,
      })
    }

    return fromPromise(
      prisma.skipKyc.create({
        data: {
          email,
        },
      }),
      (error) => {
        logger.warn(`failed to add skip kyc email [${email}]`, error)

        return ERRORS.DATABASE_ERROR
      }
    )
  })
}

export function getSignedUrlForImage(key: string) {
  return fromPromise(getS3SignedUrlGet({ key }), (error) => {
    logger.warn(`failed to get signed url for image [${key}]`, error)
    return ERRORS.S3_SIGNED
  })
}
