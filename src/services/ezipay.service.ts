import {
  epayGetProcessedCardsByOrderNumber,
  type ProcessedCardsByOrderNumberData,
} from 'epay-data/epay-product-order.data'
import { fromPromise, okAsync, err, ResultAsync } from 'neverthrow'

import RequestWithBackoff from 'helpers/request-with-backoff'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { SimpleError } from 'types/simple-error'

const EZIPAY_API_URL_1 = `update with your EZIPAY_API_URL_1`
const EZIPAY_API_URL_2 = 'update with your EZIPAY_API_URL_2'
const EZIPAY_API_USERNAME = 'update with your EZIPAY_API_USERNAME'
const EZIPAY_API_PASSWORD = 'update'

// First, let's define the output structure
interface EzipayCardItemData {
  crn: string
  loadValue: number
  loadFee: number
  discountOnLoadFee: number
  digitalFee: number
  discountOnDigitalFee: number
  gstAmount: number
  orderNumber: string
  orderId: string
}

export function buildRequestBodyForEzipaySystem({
  processedOrder,
}: {
  processedOrder: ProcessedCardsByOrderNumberData
}): EzipayCardItemData[] {
  const { id, orderNumber, productOrderItems } = processedOrder

  if (!productOrderItems || productOrderItems.length === 0) {
    return []
  }

  // Flatten all card items
  const cardItems: EzipayCardItemData[] = []

  for (const orderItem of productOrderItems) {
    // Calculate per-card values
    const quantity = orderItem.quantity || 1 // Default to 1 to avoid division by zero
    const loadFeePerCard = orderItem.loadingFee
      ? orderItem.loadingFee / quantity
      : 0
    const discountOnLoadFeePerCard = orderItem.loadingFeeDiscount
      ? orderItem.loadingFeeDiscount / quantity
      : 0
    const digitalFeePerCard = orderItem.digitalFee
      ? orderItem.digitalFee / quantity
      : 0
    const discountOnDigitalFeePerCard = orderItem.digitalFeeDiscount
      ? orderItem.digitalFeeDiscount / quantity
      : 0

    // Calculate GST amount per card (excluding loadValue)
    const feesBeforeGst =
      loadFeePerCard -
      discountOnLoadFeePerCard +
      digitalFeePerCard -
      discountOnDigitalFeePerCard
    const gstAmount = Math.round(feesBeforeGst * 0.15) // Assuming 15% GST

    // Map each card item to the required format
    for (const card of orderItem.cardItems || []) {
      cardItems.push({
        crn: card.externalCardReferenceNumber,
        loadValue: orderItem.unitPrice || 0,
        loadFee: loadFeePerCard,
        discountOnLoadFee: discountOnLoadFeePerCard,
        digitalFee: digitalFeePerCard,
        discountOnDigitalFee: discountOnDigitalFeePerCard,
        gstAmount,
        orderNumber: orderNumber || '',
        orderId: id,
      })
    }
  }

  return cardItems
}

// Batch size for sending cards
const BATCH_SIZE = 100

export function sendProcessedCardsToEzipaySystem(orderNumber: string) {
  return epayGetProcessedCardsByOrderNumber({ orderNumber }).andThen(
    (processedOrder) => {
      if (!processedOrder) {
        logger.warn(
          `sendProcessedCardsToEzipaySystem... No processed order found for orderNumber: ${orderNumber}`
        )
        return err({
          code: ERRORS.EXTERNAL_API_NOT_FOUND.code,
          message: 'No processed order found',
        } as SimpleError)
      }

      const cardItems = buildRequestBodyForEzipaySystem({ processedOrder })

      if (cardItems.length === 0) {
        logger.warn(
          `sendProcessedCardsToEzipaySystem... No card items to process for orderNumber: ${orderNumber}`
        )
        return err({
          code: ERRORS.EXTERNAL_API_NOT_FOUND.code,
          message: 'No card items to process',
        } as SimpleError)
      }

      logger.info(
        `sendProcessedCardsToEzipaySystem... Processing ${cardItems.length} cards for orderNumber: ${orderNumber}`
      )

      // Create batches of card items
      const batches: Array<typeof cardItems> = []
      for (let i = 0; i < cardItems.length; i += BATCH_SIZE) {
        batches.push(cardItems.slice(i, i + BATCH_SIZE))
      }

      // Set up API client
      const apiUrls: string[] = [EZIPAY_API_URL_1, EZIPAY_API_URL_2].filter(
        Boolean
      )

      if (apiUrls.length === 0) {
        const message =
          'EZIPAY_API_URL_1 OR EZIPAY_API_URL_2 are null. You need at least one specified'
        logger.error(message)
        return err({
          code: ERRORS.CONFIG.code,
          message,
        } as SimpleError)
      }

      const ezipayApi = new RequestWithBackoff({
        apiUrls,
        initialTimeout: 10000,
      })

      // Auth token for Ezipay API
      const username = EZIPAY_API_USERNAME
      const password = EZIPAY_API_PASSWORD
      const token = Buffer.from(`${username}:${password}`).toString('base64')

      // Send each batch
      const batchPromises = batches.map((batch, index) => {
        return fromPromise(
          ezipayApi.request({
            url: '/api/processed-cards',
            method: 'post',
            headers: {
              Authorization: `Basic ${token}`,
              'Content-Type': 'application/json',
            },
            data: batch,
          }),
          (error) => {
            logger.error(
              error,
              `Failed to send batch ${index + 1}/${
                batches.length
              } to Ezipay system`
            )
            return {
              code: ERRORS.EXTERNAL_API.code,
              message: `Failed to send batch ${index + 1}/${batches.length}`,
              batchIndex: index,
            } as SimpleError
          }
        )
      })

      return ResultAsync.combineWithAllErrors(batchPromises)
        .map((responses) => {
          logger.info(
            `Successfully sent all ${batches.length} batches to Ezipay system for orderNumber: ${orderNumber}`
          )
          return {
            success: true,
            message: `Successfully processed ${cardItems.length} cards in ${batches.length} batches`,
            batchCount: batches.length,
            cardCount: cardItems.length,
          }
        })
        .mapErr((errors) => {
          // Convert array of errors to a single error with combined message
          const errorMessage = errors.map((e) => e.message).join('; ')
          return {
            code: ERRORS.EXTERNAL_API.code,
            message: `Failed to process batches: ${errorMessage}`,
          } as SimpleError
        })
    }
  )
}
