import {
  CardDesign,
  CardDirection,
  DesignType,
  OrderStatus,
  Prisma,
  ProductOrderItemCard,
  Resolution,
  RibbonColor,
} from '@prisma/client'
import { ResultAsync, errAsync, fromPromise, okAsync } from 'neverthrow'
import { v4 as uuid } from 'uuid'

import { UploadLogoSchema } from 'routes/product.custom.route'

import { getS3SignedUrlPut } from 'utils/aws'
import { AWS_S3_BUCKET_CARD } from 'utils/config'
import { ERRORS, PickErrorCodeKey } from 'utils/error'
import logger from 'utils/logger'

import { prisma } from 'utils/prisma'

export function getApprovedCardsForUser({ orgId }: { orgId?: string }) {
  return fromPromise(
    prisma.product.findMany({
      where: {
        AND: [
          { isArchived: false },
          { status: 'APPROVED' },
          {
            OR: [{ organizationId: null }, { organizationId: orgId }],
          },
        ],
      },
      select: {
        id: true,
        name: true,
        productCode: true,

        design: {
          select: {
            cardDesignUrl: true,
          },
        },
        logo: {
          select: {
            logoUrl: true,
          },
        },
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn('failed to list products', error)

      return 'DATABASE_ERROR'
    }
  ).map((products) => {
    return {
      products: products.map((product) => {
        return {
          id: product.id,
          name: product.name,
          productCode: product.productCode.toString(),
          designUrl: product.design.cardDesignUrl,
          logoUrl: product.logo ? product.logo.logoUrl : null,
        }
      }),
    }
  })
}

export function getAllCardDesigns() {
  return fromPromise(
    prisma.cardDesign.findMany({
      where: {
        isPrezzyDesign: true,
        designType: DesignType.PREZZY,
        available: true,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn('failed to list card designs', error)

      return 'DATABASE_ERROR'
    }
  ).map((cardDesign) => {
    return cardDesign.map((design) => {
      return {
        id: design.id,
        designUrl: design.cardDesignUrl,
        ribbonColor: design.ribbonColor,
      }
    })
  })
}

export function uploadLogo({
  fileExtension,
  contentType,
  productName,
}: UploadLogoSchema) {
  const transformProductName = productName.replace(/\s+/g, '_').toLowerCase()

  const generatedId = transformProductName + '_' + uuid()

  logger.debug('generated id', generatedId)

  return getS3SignedUrlPut({
    id: generatedId,
    fileExtension,
    contentType,
    path: 'logo',
    bucket: AWS_S3_BUCKET_CARD,
  }).map(({ signedUrl, url }) => {
    return { signedUrl, url }
  })
}

export function listCardsWithFilters({
  orgName,
  orgId,
  orderId,
  page,
  referenceId,
  pageSize,
}: {
  orgId?: string
  orderId?: string
  referenceId?: string
  orgName?: string
  page: number
  pageSize: number
}) {
  const where: Prisma.ProductOrderItemCardWhereInput = {
    // usedInFundingOrder: false,
  }

  if (referenceId || orderId || orgName) {
    where.OR = []

    if (referenceId) {
      where.OR.push({ externalCardReferenceNumber: referenceId })
    }

    if (orderId) {
      where.OR.push({
        productOrderItem: {
          productOrder: {
            orderNumber: orderId,
          },
        },
      })
    }

    if (orgName) {
      where.OR.push({
        productOrderItem: {
          productOrder: {
            organization: {
              name: {
                contains: orgName,
                mode: 'insensitive',
              },
            },
          },
        },
      })
    }
  }

  if (orgId) {
    where.productOrderItem = {
      productOrder: {
        organizationId: orgId,
      },
    }
  }

  const count = prisma.productOrderItemCard.count({
    where,
  })

  const query = prisma.productOrderItemCard.findMany({
    where,
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
      createdAt: 'desc',
    },
    select: {
      externalCardReferenceNumber: true,
      lockCode: true,
      productOrderItem: {
        select: {
          recipientEmail: true,
          customerReference: true,
          productOrder: {
            select: {
              orderNumber: true,
              organization: {
                select: {
                  name: true,
                },
              },
            },
          },
          product: {
            select: {
              cardTypes: true,
            },
          },
        },
      },
    },
  })

  return fromPromise(prisma.$transaction([count, query]), (error) => {
    logger.warn(
      `failed to query cards, orgId [${orgId}] orgName [${orgName}] orderId [${orderId}] referenceId [${referenceId}] page [${page}]`,
      error
    )

    return ERRORS.DATABASE_ERROR
  }).map(([count, cards]) => {
    return {
      count,
      cards: cards.map((card) => {
        return {
          referenceId: card.externalCardReferenceNumber,
          lockCode: card.lockCode,
          orderNumber: card.productOrderItem.productOrder.orderNumber,
          cardType: card.productOrderItem.product.cardTypes,
          orgName: card.productOrderItem.productOrder.organization.name,
          email: card.productOrderItem.recipientEmail,
          customerReference: card.productOrderItem.customerReference,
        }
      }),
    }
  })
}

// export function activateCards({
//   cardReferenceIds,
//   orgId,
// }: {
//   cardReferenceIds: string[]
//   orgId?: string
// }) {
//   const where: Prisma.ProductOrderItemCardWhereInput = {
//     externalCardReferenceNumber: {
//       in: cardReferenceIds,
//     },
//   }

//   if (orgId) {
//     where.productOrderItem = {
//       productOrder: {
//         organizationId: orgId,
//       },
//     }
//   }

//   return fromPromise(prisma.productOrderItemCard.findMany({ where }), (e) => {
//     logger.warn(
//       `failed to query cards with reference ids [${cardReferenceIds}]`,
//       e
//     )

//     return ERRORS.DATABASE_ERROR
//   })
//     .andThen((cards) => {
//       return ResultAsync.combineWithAllErrors(
//         cards.map((card) => {
//           if (!card.lockCode) {
//             logger.error(
//               `missing lock code for card [${card.externalCardReferenceNumber}]`
//             )
//             return errAsync(ERRORS.CARD_WITHOUT_LOCK_CODE)
//           } else {
//             return unblockCardAndUpdateDb(card)
//           }
//         })
//       )
//     })
//     .andThen((data) => {
//       const productOrderIds = Array.from(
//         data.reduce<Set<string>>((acc, item) => {
//           acc.add(item.productOrderItem.productOrderId)
//           return acc
//         }, new Set())
//       )

//       if (productOrderIds.length > 0) {
//         return activateOrderIfAllCardsActivated(productOrderIds).andThen(() =>
//           okAsync(data)
//         )
//       }

//       return okAsync(data)
//     })
// }

// function activateOrderIfAllCardsActivated(productOrderIds: string[]) {
//   return fromPromise(
//     prisma.productOrder.updateMany({
//       where: {
//         id: {
//           in: productOrderIds,
//         },
//         orderStatus: { not: OrderStatus.ACTIVATED },
//         // Only activate order when all card items are NOT blocked (ie activated)
//         productOrderItems: {
//           every: {
//             cardItems: {
//               none: {
//                 blocked: true,
//               },
//             },
//           },
//         },
//       },
//       data: {
//         orderStatus: OrderStatus.ACTIVATED,
//       },
//     }),
//     (e) => {
//       logger.warn(`failed to find productOrderIds [${productOrderIds}]`, e)
//       return ERRORS.DATABASE_ERROR
//     }
//   )
// }

// export function blockCards({
//   cardReferenceIds,
//   orgId,
// }: {
//   cardReferenceIds: string[]
//   orgId?: string
// }) {
//   const where: Prisma.ProductOrderItemCardWhereInput = {
//     externalCardReferenceNumber: {
//       in: cardReferenceIds,
//     },
//   }

//   if (orgId) {
//     where.productOrderItem = {
//       productOrder: {
//         organizationId: orgId,
//       },
//     }
//   }

//   return fromPromise(prisma.productOrderItemCard.findMany({ where }), (e) => {
//     logger.warn(
//       `failed to query cards with reference ids [${cardReferenceIds}]`,
//       e
//     )

//     return ERRORS.DATABASE_ERROR
//   }).andThen((cards) => {
//     return ResultAsync.combineWithAllErrors(
//       cards.map((card) => {
//         if (!card.lockCode) {
//           logger.error(
//             `missing lock code for card [${card.externalCardReferenceNumber}]`
//           )
//           return errAsync('CARD_WITHOUT_LOCK_CODE' as const)
//         } else {
//           return blockCard({
//             referenceId: card.externalCardReferenceNumber,
//             lockCode: card.lockCode,
//           })
//         }
//       })
//     )
//   })
// }

export function findCardDesign(designId: string) {
  logger.debug('find card design=', designId)

  return fromPromise(
    prisma.cardDesign.findFirst({
      where: {
        id: designId,
        isPrezzyDesign: true,
      },
    }),
    (e) => {
      logger.warn(`Failed to query prezzy cardDesign with id [${designId}]`, e)

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function findCardByCrn(crn: string) {
  return fromPromise(
    prisma.productOrderItemCard.findFirst({
      where: {
        externalCardReferenceNumber: crn,
      },
      select: {
        activated: true,
        blocked: true,
        externalCardReferenceNumber: true,
        lockCode: true,
        productOrderItem: {
          select: {
            recipientEmail: true,
            productOrder: {
              select: {
                orderNumber: true,
                organization: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            product: {
              select: {
                cardTypes: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.warn(
        `failed to query product order item card with crn [${crn}]`,
        error
      )

      return ERRORS.DATABASE_ERROR
    }
  ).map((card) => {
    if (!card) {
      return errAsync(ERRORS.NOT_FOUND)
    }

    return {
      referenceId: card.externalCardReferenceNumber,
      lockCode: card.lockCode,
      orderNumber: card.productOrderItem.productOrder.orderNumber,
      cardType: card.productOrderItem.product.cardTypes,
      orgName: card.productOrderItem.productOrder.organization.name,
      email: card.productOrderItem.recipientEmail,
      isActivated: card.activated,
      isBlocked: card.blocked,
    }
  })
}

// export function updateCardBlocked({
//   productOrderItemCardId,
//   blocked,
//   blockError,
//   crn,
// }: {
//   productOrderItemCardId: string
//   blocked: boolean
//   blockError?: string
//   crn?: string
// }) {
//   const blockedAt = blocked ? new Date() : null
//   return fromPromise(
//     prisma.productOrderItemCard.update({
//       where: {
//         id: productOrderItemCardId,
//       },
//       data: {
//         blockedAt,
//         blocked,
//         blockError,
//       },
//       include: { productOrderItem: true },
//     }),
//     (error) => {
//       const message = `Failed to update card BLOCKED for [${productOrderItemCardId}], CRN: [${crn}]`
//       logger.error(error, message)

//       return {
//         ...ERRORS.DATABASE_ERROR,
//         message,
//       }
//     }
//   )
// }

// // Unblock card from I2C then set blocked to false in our DB
// export function unblockCardAndUpdateDb(card: ProductOrderItemCard) {
//   logger.info(`Unblocking card in I2C: ${card.id}`)
//   return unblockCard({
//     referenceId: card.externalCardReferenceNumber,
//     lockCode: card.lockCode!,
//   }).andThen(() => {
//     logger.info(`Unblocking card in DB: ${card.id}`)
//     return updateCardBlocked({
//       productOrderItemCardId: card.id,
//       blocked: false,
//       crn: card.externalCardReferenceNumber,
//     })
//   })
// }

type StockCardDesign = {
  id: string
  designUrl: string
  cardDirection: CardDirection
}

export function getAllStockCardDesigns() {
  return fromPromise(
    prisma.cardDesign.findMany({
      where: {
        isPrezzyDesign: true,
        designType: DesignType.STOCK,
      },
      select: {
        id: true,
        cardDesignUrl: true,
        cardDirection: true,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.warn('failed to list card designs', error)
      return 'DATABASE_ERROR'
    }
  ).map((cardDesigns) => {
    const uniqueDesigns: { [key: string]: StockCardDesign } = {}

    cardDesigns.forEach((design) => {
      uniqueDesigns[design.cardDesignUrl] = {
        id: design.id,
        designUrl: design.cardDesignUrl,
        cardDirection: design.cardDirection,
      }
    })

    return Object.values(uniqueDesigns)
  })
}

export function getHighAndLowResDesignIds(designUrl: string) {
  logger.info(`Getting high and low res design ids for designUrl: ${designUrl}`)

  return fromPromise(
    prisma.cardDesign.findMany({
      where: {
        cardDesignUrl: designUrl,
      },
    }),
    (error) => {
      logger.warn('failed to list card designs', error)
      return ERRORS.DATABASE_ERROR
    }
  ).andThen((designs) => {
    if (designs.length === 0) {
      logger.warn(`Designs not found for designUrl: ${designUrl}`)
      return errAsync(ERRORS.NOT_FOUND)
    }

    logger.debug(`Found ${designs.length} designs for designUrl: ${designUrl}`)

    const highResDesign = designs.find(
      (design) => design.resolution === Resolution.HIGH
    )

    const lowResDesign = designs.find(
      (design) => design.resolution === Resolution.LOW
    )

    if (!highResDesign || !lowResDesign) {
      return errAsync(ERRORS.NOT_FOUND)
    }

    return okAsync({
      highResDesignId: highResDesign.id,
      lowResDesignId: lowResDesign.id,
    })
  })
}
