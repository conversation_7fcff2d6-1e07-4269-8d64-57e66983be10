import { Prisma } from '@prisma/client'

import { errAsync, fromPromise, okAsync } from 'neverthrow'

import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

function getUserByIdWithApplication({ userId }: { userId: string }) {
  return fromPromise(
    prisma.user.findUnique({
      where: {
        id: userId,
      },
      include: {
        corporateApplications: true,
        organization: true,
      },
    }),
    () => {
      return ERRORS.DATABASE_ERROR
    }
  )
}

export async function onboard({ userId }: { userId: string }) {
  try {
    const userResult = await getUserByIdWithApplication({ userId })

    if (userResult.isErr()) {
      return errAsync(userResult.error)
    }

    const user = userResult.value

    logger.debug(`user: ${userId}`, user)

    if (!user) {
      return errAsync(ERRORS.NOT_FOUND)
    }

    if (user.organization) {
      return errAsync({
        code: ERRORS.ALREADY_EXIST.code,
        message: 'Organization already exists',
      })
    }

    if (user.corporateApplications) {
      return errAsync({
        code: ERRORS.ALREADY_EXIST.code,
        message: `user: ${userId} already has an application`,
      })
    }

    const skipKyc = await checkEmailIsInSkipKyc({ email: user.email })

    if (skipKyc.isErr()) {
      return errAsync(skipKyc.error)
    }

    logger.debug('skipKyc=', skipKyc.value)

    if (skipKyc.value) {
      return okAsync({ skipKyc: true })
    } else {
      const applicationResult = await createApplication({ userId })

      if (applicationResult.isErr()) {
        return errAsync(applicationResult.error)
      }

      const applicationId = applicationResult.value.id

      return okAsync({ id: applicationId, skipKyc: false })
    }
  } catch (error) {
    logger.error(`error onboarding user: ${userId}, error: ${error}`)

    return errAsync({ code: ERRORS.DATABASE_ERROR.code, message: error })
  }
}

function createApplication({ userId }: { userId: string }) {
  logger.info(`creating application for user: ${userId}`)

  return fromPromise(
    prisma.corporateApplication.create({
      data: {
        userId,
      },
      select: {
        id: true,
      },
    }),
    (error) => {
      logger.error(
        `error creating application for user: ${userId}, error:${error}`
      )

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2003'
      ) {
        logger.error(`user: ${userId} already has an application`)

        return {
          code: ERRORS.FOREIGN_KEY_CONSTRAINT.code,
          message: 'User already has an application',
        }
      }

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function checkEmailIsInSkipKyc({ email }: { email: string }) {
  logger.debug(`checking skipKyc table using email ${email}`)
  return fromPromise(
    prisma.skipKyc.findUnique({
      where: {
        email,
      },
    }),
    (error) => {
      logger.error(
        `error checking skipKyc table for email ${email}, error:${error}`
      )

      return ERRORS.DATABASE_ERROR
    }
  )
}
