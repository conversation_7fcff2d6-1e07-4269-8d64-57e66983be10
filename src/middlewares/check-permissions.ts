import { Request, Response, NextFunction } from 'express'
import { unauthorized } from 'utils/error'
import logger from 'utils/logger'

export const checkPermissions = (permissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    logger.debug('Checking Epay Permissions...', req.permissions)
    const hasAccess = req.permissions?.some((permission: string) => {
      return permissions.includes(permission)
    })

    if (!hasAccess) {
      return unauthorized(res)
    } else {
      next()
    }
  }
}
