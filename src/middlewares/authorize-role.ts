import type { UserRole } from '@prisma/client'

import { NextFunction, Request, Response } from 'express'

import { unauthorized } from 'utils/error'

//TODO: authorizeR<PERSON> should check against the company id
export const authorizeRole = (roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (roles.length < 1) {
      throw new Error(
        'At least 1 required role must exist to check authorization'
      )
    }

    let hasAccess = false

    if (req.userId && req.role) {
      hasAccess = roles.some((role) => {
        return role === req.role
      })
    }

    if (!hasAccess) {
      return unauthorized(res)
    } else {
      next()
    }
  }
}
