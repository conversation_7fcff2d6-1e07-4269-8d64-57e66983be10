import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import {
  AUTH0_AUDIENCE,
  AUTH0_REDIRECT_SECRET,
  AUTH0_SIGNING_SECRET,
  ENVIRONMENT,
} from 'utils/config'

import * as logger from 'utils/logger'

export const checkJwt = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  //logger.debug('Checking Epay JWT...')
  const authToken = req.headers.authorization?.split(' ')[1]

  if (!authToken) {
    //logger.debug('Missing token')
    return res.status(401).send('unauthorized or missing token')
  }

  try {
    const decoded = jwt.verify(
      authToken,
      AUTH0_SIGNING_SECRET
    ) as jwt.JwtPayload

    if (decoded) {
      logger.logRequest(req)

      req.userId = decoded[`${AUTH0_AUDIENCE}/userId`]
      req.role = decoded[`${AUTH0_AUDIENCE}/userRole`]
      req.orgId = decoded[`${AUTH0_AUDIENCE}/orgId`]
      req.userEmail = decoded[`${AUTH0_AUDIENCE}/userEmail`]
      req.permissions = decoded.permissions || []

      next()
    } else {
      logger.debug('Invalid token')
      return res.status(401).send('unauthorized or missing token')
    }
  } catch (error) {
    logger.info('error verifying token', error)
    return res.status(401).send('unauthorized or missing token')
  }
}

export const checkAuth0RedirectJwt = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.debug('Checking Auth0 Redirect JWT...')
  const authToken = req.headers.authorization?.split(' ')[1]

  if (!authToken) {
    logger.debug('Missing token')
    return res.status(401).send('unauthorized or missing token')
  }

  try {
    const decoded = jwt.verify(
      authToken,
      AUTH0_REDIRECT_SECRET
    ) as jwt.JwtPayload

    if (decoded) {
      req.userId = decoded.userId
      req.phoneNumber = decoded.phoneNumber

      next()
    } else {
      logger.debug('Invalid token')
      return res.status(401).send('unauthorized or missing token')
    }
  } catch (error) {
    logger.info('error verifying token', error)
    return res.status(401).send('unauthorized or missing token')
  }
}
