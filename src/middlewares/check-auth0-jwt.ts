import { NextFunction, Request, Response } from 'express'
import { auth } from 'express-oauth2-jwt-bearer'
import {
  AUTH0_AUDIENCE,
  AUTH0_CLIENT_SECRET,
  AUTH0_ISSUER_BASE_URL,
  AUTH0_SIGNING_SECRET,
} from 'utils/config'
import logger from 'utils/logger'

const auth0Check = auth({
  secret: `${AUTH0_SIGNING_SECRET}`,
  audience: `${AUTH0_AUDIENCE}`,
  issuerBaseURL: `${AUTH0_ISSUER_BASE_URL}`,
  tokenSigningAlg: 'HS256',
})

export const checkAuth0Jwt = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    logger.debug('Checking Auth0 JWT')
    auth0Check(req, res, (error) => {
      if (error) {
        //logger.info('Failed to authenticate JWT', error)
        return res.status(401).json({ error: 'Unauthorized: Invalid token' })
      }
      logger.logRequest(req)
      next()
    })
  } catch (error) {
    logger.info('Failed to authenticate JWT', error)
    return res.status(401).json({ error: 'Unauthorized: Invalid token' })
  }
}
