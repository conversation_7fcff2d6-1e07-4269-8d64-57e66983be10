import { err, from<PERSON>rom<PERSON>, ok } from 'neverthrow'
import { SimpleError } from 'types/simple-error'
import { REN_PART_ID, REN_VCARD_BRANCH } from 'utils/config'
import logger from 'utils/logger'
import { truncateString } from 'utils/truncate-string'
import { processRenApiError } from './process-ren-api-error'
import renAxiosInstance from './ren-axios-instance'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
} from './ren-utils'

import { encodeNewlines } from 'utils/encode-newline'
import internal from 'stream'
interface RenResponse {
  cardIssuanceResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

function cardIssueRequest(body: unknown) {
  const url = `/PREZZY/RenCms/V2.1.0.0/card/issue`
  return fromPromise(
    renAxiosInstance({ method: 'post', url, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'cardIssueRequest failed')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as SimpleError
    }
  )
}

type DebitCardIssuanceResponse = {
  debitCardIssuanceResponse: {
    response: {
      responseCode: string
      description: string
    }
    cardInfo: {
      cardSeqNumber: string
      proxyNumber: string
      hashCardNumber: string
      encryptedPAN: string
      expiryDate: string
      embossLine1: string
    }
    accountInfo: any[]
  }
}

export type ResendVirtualCardArgs = {
  crn: string
  cardType: string
  cardSubType: string
  recipientEmail: string
  recipientName: string // we put fullname into firstname
  customerId: string
  embossLine1: string
  embossLine2: string
}

export function reissueVirtualCard(args: ResendVirtualCardArgs) {
  const body = createCardResendRequestBody({ args })

  logger.warn(
    `resendVirtualCard.. reqID [${body.debitCardIssuanceRequest.header.reqID}]`
  )
  return cardIssueRequest(body).andThen((responseData) => {
    const renResponse = (responseData.data as DebitCardIssuanceResponse)
      .debitCardIssuanceResponse.response

    logger.debug(
      `reissueVirtualCard for card crn [${args.crn}]: ${JSON.stringify(
        renResponse
      )}`
    )

    if (renResponse.responseCode !== '00') {
      const logRef = logger.error(
        `reissueVirtualCard failed with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      logger.inspect(body, 'reissueVirtualCard body')

      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }

    return ok(responseData.data as DebitCardIssuanceResponse)
  })
}

function createCardResendRequestBody({
  args,
}: {
  args: ResendVirtualCardArgs
}) {
  const body = {
    debitCardIssuanceRequest: {
      header: {
        partID: `${REN_PART_ID}`,
        serviceName: 'RPLNEWCRD',
        channelID: 'RENCMS',
        userId: REN_USER_ID,
        reqID: getNewRequestId(),
        reqDateTime: getNewRequestTimestamp(),
      },
      cardInfo: {
        cardType: args.cardType,
        cardSubType: args.cardSubType,
        cardIssuanceType: 'V',
        embossingType: 'N', // this is B in the example but not with our current virtual cards
        embossLine1: args.embossLine1 ?? 'PREZZY CARDHOLDER',
        embossLine2: args.embossLine2 ?? '',
        proxyCardNumber: args.crn,
        cardProductType: 'P',
        cardSeqNumber: '',
      },
      customerInfo: {
        customerType: 'I',
        firstName: truncateString(args.recipientName, 80),
        customerID: args.customerId,
      },
      addressInfo: [],
      phoneInfo: [],
      emailInfo: [
        {
          emailType: 'PER',
          emailId: args.recipientEmail.trim(),
          primaryFlag: 'Y',
        },
      ],

      notes: [
        {
          notesData: 'Reissue New Virtual Card',
          addedBy: 'corp-portal',
          addedDateTime: getNewRequestTimestamp(),
        },
      ],
      dispatchDetails: {},
      userInfo: null,
    },
  }

  return body
}
