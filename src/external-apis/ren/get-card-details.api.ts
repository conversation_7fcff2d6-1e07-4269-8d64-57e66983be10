import { err, fromPromise, ok } from 'neverthrow'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
} from './ren-utils'
import renAxiosInstance from './ren-axios-instance'
import { processRenApiError } from './process-ren-api-error'
import { SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { REN_PART_ID } from 'utils/config'

interface RenResponse {
  getCardDetailsResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

function getCardDetailsRequest(body: unknown) {
  const url = '/PREZZY/RenCms/V2.1.0.0/card/getCardDetails'
  return fromPromise(
    renAxiosInstance({ method: 'get', url, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'getCardDetailsRequest failed')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as SimpleError
    }
  )
}

export function getCardDetails(proxyNumber: string) {
  const body = getCardDetailsRequestbody(proxyNumber)
  return getCardDetailsRequest(body).andThen((res) => {
    const renResponse = (res.data as RenResponse).getCardDetailsResponse
      .response

    if (renResponse.responseCode !== '00') {
      const logRef = logger.error(
        `getCardDetailsRequest failed with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }

    const fullResponse = (res.data as GetCardDetailsResponse)
      .getCardDetailsResponse

    const cardDetail: CardDetail = {
      proxyNumber: fullResponse.cardInfo.proxyCardNumber,
      //cardNumber: fullResponse.cardInfo.cardNumber,
      cardIssuanceType: fullResponse.cardInfo.cardIssuanceType,
      embossLine1: fullResponse.cardInfo.embossLine1,
      embossLine2: fullResponse.cardInfo.embossLine2,
      expiryDate: fullResponse.cardInfo.expiryDate,
      issueDate: fullResponse.cardInfo.issueDate,
      productCode: fullResponse.cardInfo.productCode,
      cardType: fullResponse.cardInfo.cardType,
      cardSubType: fullResponse.cardInfo.cardSubType,
      feeProfile: fullResponse.cardInfo.feeProfile,
      encodeFirstName: fullResponse.cardInfo.encodeFirstName,
      encodeSecondName: fullResponse.cardInfo.encodeSecondName,
      encodeLastName: fullResponse.cardInfo.encodeLastName,
      embossingType: fullResponse.cardInfo.embossingType,
      cardseqNo: fullResponse.cardInfo.cardseqNo,
      issueCardMailer: fullResponse.cardInfo.issueCardMailer,
      customerId: fullResponse.customerInfo.customerID,
      recipientEmail:
        fullResponse.emailInfo && fullResponse.emailInfo.length > 0
          ? fullResponse.emailInfo[0].emailId
          : '',
      recipientFirstName: fullResponse.customerInfo.firstName,
    }

    return ok(cardDetail)
  })
}

function getCardDetailsRequestbody(proxyNumber: string) {
  const body = {
    getCardDetailsRequest: {
      header: {
        partID: `${REN_PART_ID}`,
        serviceName: 'GETCARDDTL',
        channelID: 'DCMSUI',
        userId: REN_USER_ID,
        reqID: getNewRequestId(),
        reqDateTime: getNewRequestTimestamp(),
      },
      cardParams: {
        tokenCardNumber: '',
        proxyCardNumber: `${proxyNumber}`,
        cardSeqNumber: '1',
        cardValueIndicator: '2',
      },
    },
  }

  return body
}

// Never log out this object. Use invidiual properties to avoid cardNumber befing logged
export type CardDetail = {
  proxyNumber: string
  //cardNumber: string // Never save this value. Logger(not console.log) should mask don't
  embossLine1?: string
  embossLine2?: string
  expiryDate: string // in format yyyyMMDD
  issueDate: string // in format yyyyMMDD
  productCode: string // cardType
  cardType: string // yes it is the same as productCode for Epay (for now)
  cardSubType: string // cardSubType -> externalCardDesignId
  feeProfile: string
  encodeFirstName: string
  encodeSecondName?: string
  encodeLastName?: string
  embossingType: string
  cardIssuanceType: string
  cardseqNo: string
  issueCardMailer: string
  customerId: string
  recipientEmail: string
  recipientFirstName: string
}

export interface GetCardDetailsResponse {
  getCardDetailsResponse: {
    header: {
      partID: string
      serviceName: string
      channelID: string
      userId: string
      reqID: string
      reqDateTime: string
    }
    response: {
      responseCode: string
      description: string
    }
    cardInfo: {
      productCode: string
      cardType: string
      cardSubType: string
      binNumber: string
      cardIssuanceType: string
      //cardNumber: string
      cardseqNo: string
      proxyCardNumber: string
      encryptedPAN: string
      parentProxyNumber: string
      parentCardNumber: string
      parentCardSeqNumber: number
      embossLine1: string
      embossLine2: string
      encodeTitle: string
      encodeFirstName: string
      encodeSecondName: string
      encodeLastName: string
      embossingType: string
      expiryDate: string
      issueDate: string
      issueCardMailer: string
      cardMailerIssueDate: string
      cardHolderSinceDate: string
      limitProfile: string
      supplementaryCardCount: number
      cardHolderFlag: string
      productLimitProfile: string
      languageCode: string
      cardProductType: string
      feeProfile: string
      productFeeProfile: string
      schemeTokenEnabled: string
      additionalInfo: {
        internationalUsageFlag: string
      }
      pinInfo: {
        pinMailerFlag: string
        dailyPINRetryLimit: string
        dailyPINRetryCount: string
        consecutivePINRetryLimit: string
        consecutivePINRetryCount: string
        lastPinErrorDate: string
        dailyPinErrorDate: string
        pinMailerIssueDate: string
        pinCreationDate: string
        pinFlag: string
      }
      statusInfo: {
        statusCode: string
        reasonCode: string
        cardStatus: string
        memo: string
        makerID: string
        checkerID: string
      }
      statusChangeDateTime: string
      dispatchDetails: {
        dispatchType: string
        dispatchLocation: string
        shippingMethod: string
      }
      UserInfo: {
        makerID: string
        checkerID: string
        makerRemarks: string
        checkerRemarks: string
      }
    }
    customerInfo: {
      customerType: string
      customerID: string
      internalCustomerId: string
      parentCustomerId: string
      parentCustomerInternalId: string
      title: string
      firstName: string
      middleName: string
      lastName: string
      gender: string
      dateOfBirth: string
      motherMaidenName: string
      localResident: string
      verificationFlag: string
      employeeFlag: string
      maritalStatus: string
      occupation: string
      militaryStatus: string
      income: number
      incomeCurrency: string
      otherIncome: number
      additionalInfo: {
        segmentCode: string
        customerSegmentCode: string
        armCode: string
        residentialStatus: string
        relationshipToParent: string
      }
    }
    addressInfo: Array<{
      addressType: string
      addressLine1: string
      addressLine2: string
      addressLine3: string
      city: string
      state: string
      countryCode: string
      zipCode: string
      postBox: string
      primaryFlag: string
    }>
    phoneInfo: Array<{
      phoneType: string
      phoneNumber: string
      primaryFlag: string
    }>
    emailInfo: Array<{
      emailType: string
      emailId: string
      primaryFlag: string
    }>
    identificationInfo: Array<{
      identificationType: string
      identificationTypeNo: string
      identificationNo: string
    }>
    accountInfo: Array<{
      accountType: string
      accountNumber: string
      currencyCode: string
      accountBranch: string
      unspecifiedFlag: string
      primaryFlag: string
      accountStatus: string
      overrideFlag: string
      iBan: string
      additionalInfo: {
        productCode: string
        operatingInstitutionId: string
        segmentCode: string
        institutionClassificationCode: string
        payrollIndicator: string
        multiCurrencyIndicator: string
        riskCodes: Array<{
          riskCode: string
        }>
        linkTypes: {
          linkType: string
        }
      }
    }>
    iBANInfo: Array<any> // This array is empty in the provided JSON
  }
}
