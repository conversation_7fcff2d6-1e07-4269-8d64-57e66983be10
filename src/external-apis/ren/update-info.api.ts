import { err, from<PERSON>rom<PERSON>, ok } from 'neverthrow'
import { handleAxiosError } from 'utils/axios-error'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
} from './ren-utils'
import { get } from 'http'
import renAxiosInstance from './ren-axios-instance'
import { processRenApiError } from './process-ren-api-error'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { CardDetail } from './get-card-details.api'
import { REN_PART_ID } from 'utils/config'

interface RenResponse {
  cardInfoUpdateResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

type CardInfoUpdateResponse = {
  cardInfoUpdateResponse: {
    header: {
      partID: string
      reqDateTime: string
      serviceName: string
      userId: string
      channelID: string
      reqID: string
    }
    response: {
      responseCode: string
      description: string
    }
  }
}

function setActivationCodeRequest(body: unknown) {
  const url = '/PREZZY/RenCms/V2.1.0.0/card/updInfo'
  return fromPromise(
    renAxiosInstance({ method: 'post', url, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'setActivationCodeRequest failed')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as SimpleError
    }
  )
}

export function setActivationCode({
  cardDetail,
  activationCode,
}: {
  cardDetail: CardDetail
  activationCode: string
}) {
  // TODO: Get the card details from the proxy number

  const body = buildSetActivationCodeBody({
    cardDetail,
    activationCode: activationCode,
  })

  return setActivationCodeRequest(body).andThen((responseData) => {
    const renResponse = (responseData.data as RenResponse)
      .cardInfoUpdateResponse.response

    logger.debug(
      `setActivationCode for card [${cardDetail.proxyNumber}]: ${JSON.stringify(
        renResponse
      )}`
    )

    if (renResponse.responseCode !== '00') {
      const logRef = logger.error(
        `setActivationCode failed with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }

    logger.debug('setActivationCodeRequest completed successfully')
    return ok(cardDetail) // better chaining
  })
}

function buildSetActivationCodeBody({
  cardDetail,
  activationCode,
}: {
  cardDetail: CardDetail
  activationCode: string
}) {
  return {
    cardInfoUpdateRequest: {
      header: {
        partID: `${REN_PART_ID}`,
        reqDateTime: getNewRequestTimestamp(),
        serviceName: 'UPDCARDDTL',
        userId: REN_USER_ID,
        channelID: 'DCMSUI',
        reqID: getNewRequestId(),
      },
      cardParams: {
        proxyCardNumber: `${cardDetail.proxyNumber}`,
        cardIssuanceType: `${cardDetail.cardIssuanceType}`,
        embossLine1: `${cardDetail.embossLine1}`,
        embossLine2: `${cardDetail.embossLine2}`,
        encodeFirstName: `${cardDetail.encodeFirstName}`,
        encodeSecondName: `${cardDetail.encodeSecondName}`,
        encodeLastName: `${cardDetail.encodeLastName}`,
        embossingType: `${cardDetail.embossingType}`,
        expiryDate: `${cardDetail.expiryDate}`, // get from card details
        issueDate: `${cardDetail.issueDate}`,
        feeProfile: `${cardDetail.feeProfile}`,
        cardSubType: `${cardDetail.cardSubType}`,
        activationCode: null,
        newActivationCode: `${activationCode}`,
        supportCSTFreedomFlag: null,
        supportCSTRequiredFlag: null,
        cardActivationCode: '',
        productCode: `${cardDetail.productCode}`, // cardType
        shippingMethod: null,
        additionalAttribute: [
          {
            attrName: 'ACTIVATION_CODE',
            attrValue: `${activationCode}`,
          },
        ],
      },
    },
  }
}
