import { err, from<PERSON>rom<PERSON>, ok } from 'neverthrow'
import { SimpleError } from 'types/simple-error'
import { REN_PART_ID, REN_VCARD_BRANCH } from 'utils/config'
import logger from 'utils/logger'
import { truncateString } from 'utils/truncate-string'
import { processRenApiError } from './process-ren-api-error'
import renAxiosInstance from './ren-axios-instance'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
} from './ren-utils'

import { encodeNewlines } from 'utils/encode-newline'
interface RenResponse {
  cardIssuanceResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

function cardIssueRequest(body: unknown) {
  const url = `/PREZZY/RenCms/V2.1.0.0/card/issue`
  return fromPromise(
    renAxiosInstance({ method: 'post', url, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'cardIssueRequest failed')
      logger.inspect(body, 'cardIssueRequest body')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as SimpleError
    }
  )
}

type CardIssuanceResponse = {
  cardIssuanceResponse: {
    header: {
      partID: string
      serviceName: string
      channelID: string
      userId: string
      reqID: string
      reqDateTime: string
    }
    response: {
      responseCode: string
      description: string
    }
    cardInfo: {
      cardNumber: string
      cardSeqNumber: string
      proxyNumber: string
      hashCardNumber: string
      encryptedPAN: string
      expiryDate: string
      embossLine1: string
    }
    accountInfo: Array<{
      accountNumber: string
      accountType: string
      currencyCode: string
    }>
  }
}

export type CreateVirtualCardArgs = {
  orderNumber: string
  recipientEmail: string
  recipientName: string
  cardType: string
  cardSubType: string
  productCode: string
  productBin: string
  customMessage?: string
}

export function createVirtualCard(args: CreateVirtualCardArgs) {
  const body = createCardIssueRequestBody({ args })

  logger.warn(
    `createVirtualCard.. reqID [${body.cardIssuanceRequest.header.reqID}]`
  )
  return cardIssueRequest(body).andThen((responseData) => {
    const renResponse = (responseData.data as RenResponse).cardIssuanceResponse
      .response

    logger.debug(
      `createVirtualCard for order [${args.orderNumber}]: ${JSON.stringify(
        renResponse
      )}`
    )

    if (renResponse.responseCode !== '00') {
      const logRef = logger.error(
        `createVirtualCard failed with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      logger.inspect(body, `${logRef} body`)

      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }

    return ok(responseData.data as CardIssuanceResponse)
  })
}

function createCardIssueRequestBody({ args }: { args: CreateVirtualCardArgs }) {
  const body = {
    cardIssuanceRequest: {
      header: {
        partID: `${REN_PART_ID}`,
        serviceName: 'ISSNEWCRD',
        channelID: 'RENCMS',
        userId: REN_USER_ID,
        reqID: getNewRequestId(),
        reqDateTime: getNewRequestTimestamp(),
      },
      cardInfo: {
        productCode: args.productCode,
        cardType: args.cardType,
        cardProductType: 'P',
        productBIN: args.productBin,
        cardSubType: args.cardSubType,
        cardIssuanceType: 'V',
        cardNumber: '',
        cardSeqNumber: '',
        parentCardNumber: '',
        parentCardSeqNumber: '',
        embossLine1: 'PREZZY CARDHOLDER',
        embossLine2: '',
        encodeFirstName: truncateString(args.recipientName, 80),
        encodeSecondName: '',
        encodeLastName: '',
        pinMailerFlag: 'N',
        embossingType: 'N',
        languageCode: '01',
        limitProfile: '',
        additionalInfo: {
          customMsg: args.customMessage
            ? encodeNewlines(args.customMessage)
            : '',
        },
      },
      customerInfo: {
        customerType: 'I',
        customerID: '',
        parentCustomerId: '',
        title: '',
        firstName: truncateString(args.recipientName, 80),
        middleName: '',
        lastName: '',
        gender: '',
        dateOfBirth: '',
        motherMaidenName: '',
      },
      addressInfo: [
        {
          addressType: 'HOM',
          addressLine1: '108 Placeholder Street',
          addressLine2: 'Placeholder Suburb',
          addressLine3: 'Nearest placeholder',
          city: 'MUMBAI',
          state: 'MH',
          countryCode: 'NZ',
          zipCode: '544441',
          postBox: '',
          primaryFlag: 'Y',
        },
      ],
      phoneInfo: [],
      emailInfo: [
        {
          emailType: 'PER',
          emailId: args.recipientEmail.trim(),
          primaryFlag: 'Y',
        },
      ],

      notes: [
        {
          notesData: 'New Virtual Card',
          addedBy: 'corp-portal',
          addedDateTime: getNewRequestTimestamp(),
        },
      ],
      accountInfo: [
        {
          accountType: '',
          accountNumber: '',
          currencyCode: '554',
          accountBranch: `${REN_VCARD_BRANCH}`,
          unspecifiedFlag: 'Y',
          primaryFlag: 'Y',
          accountStatus: 'A',
        },
      ],
    },
  }

  return body
}
