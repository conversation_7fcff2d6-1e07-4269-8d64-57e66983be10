import { randomUUID } from 'crypto'

export const RenCurrencyCode = {
  NZD: '554',
}

/*** The userID can be any string to identify caller */
export const REN_USER_ID = 'epay-c-portal'

/***
 * A unique request ID is required for each request.
 *
 * A request that faile can be retried with the same request ID without any issues.
 *
 *  ***/
export const getNewRequestId = () => randomUUID()

/***
 * A timestamp is required for each request.
 */
export const getNewRequestTimestamp = () =>
  new Date().toISOString().replace('T', ' ').slice(0, 19)
