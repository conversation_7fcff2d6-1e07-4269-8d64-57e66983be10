import { err, from<PERSON>rom<PERSON>, ok } from 'neverthrow'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
} from './ren-utils'
import { processRenApiError } from './process-ren-api-error'
import { SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import renAxiosInstance from './ren-axios-instance'
import { REN_PART_ID } from 'utils/config'

function cardStatusChangeRequest(body: unknown) {
  const route = `/PREZZY/RenCms/V2.1.0.0/card/cardStatusChange`
  return fromPromise(
    renAxiosInstance({ method: 'post', url: route, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'cardStatusChangeRequest failed')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as Simple<PERSON>rro<PERSON>
    }
  )
}

interface RenResponse {
  cardStatusChangeResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

type CardStatusChangeArgs = {
  proxyNumber: string
  status: 'block' | 'unblock'
}

export function cardStatusChange(args: CardStatusChangeArgs) {
  const body = createCardStatusRequestBody({
    proxyNumber: args.proxyNumber,
    status: args.status,
  })

  return cardStatusChangeRequest(body).andThen((res) => {
    const renResponse = (res.data as RenResponse).cardStatusChangeResponse
      .response

    logger.debug(
      `cardStatusChange for card [${args.proxyNumber}] to status [${
        args.status
      }]: ${JSON.stringify(renResponse)}`
    )

    // TODO: return true if already in the status you want even though it is an error
    if (renResponse.responseCode !== '00') {
      const logRef = logger.error(
        `cardStatusChangeRequest failed with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }

    return ok({
      renResponse,
      proxyNumber: args.proxyNumber,
      status: args.status,
    })
  })
}

function createCardStatusRequestBody(args: CardStatusChangeArgs) {
  let statusInfoBlock
  if (args.status === 'unblock') {
    statusInfoBlock = {
      statusCode: 'A',
      reasonCode: 'AC',
      cardStatus: '',
    }
  } else {
    statusInfoBlock = {
      statusCode: 'A',
      reasonCode: 'WM',
      cardStatus: '',
    }
  }

  const body = {
    cardStatusChangeRequest: {
      header: {
        partID: `${REN_PART_ID}`,
        serviceName: 'CRDSTSCHG',
        channelID: 'DCMSUI',
        userId: REN_USER_ID,
        reqID: getNewRequestId(),
        reqDateTime: getNewRequestTimestamp(),
      },
      cardStatusDetails: {
        hashCardNumber: '',
        proxyNumber: `${args.proxyNumber}`,
        cardSeqNumber: '1',
        memo: `${args.status} card`,
        statusInfo: statusInfoBlock,
        userRemarks: '',
      },
    },
  }

  return body
}
