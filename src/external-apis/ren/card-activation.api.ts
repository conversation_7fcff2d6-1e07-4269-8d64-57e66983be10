import { err, from<PERSON>rom<PERSON>, ok } from 'neverthrow'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
} from './ren-utils'
import renAxiosInstance from './ren-axios-instance'
import { processRenApiError } from './process-ren-api-error'
import { SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { CardDetail } from './get-card-details.api'
import { REN_PART_ID } from 'utils/config'

interface RenResponse {
  cardActivationResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

export type CardActivationResponse = {
  cardActivationResponse: {
    header: {
      partID: string
      serviceName: string
      channelID: string
      userId: string
      reqID: string
      reqDateTime: string
    }
    response: {
      uniqueId: string
      orderingBranch: string
      responseCode: string
      description: string
    }
  }
}

function cardActivationRequest(body: unknown) {
  const url = '/PREZZY/RenCms/V2.1.0.0/card/cardActivation'
  return fromPromise(
    renAxiosInstance({ method: 'post', url, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'cardActivationRequest failed')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as SimpleError
    }
  )
}

export function cardActivation({
  cardDetail,
  activationCode,
}: {
  cardDetail: CardDetail
  activationCode: string
}) {
  const body = buildRequestBody({
    proxyNumber: cardDetail.proxyNumber,
    activationCode: activationCode,
  })

  return cardActivationRequest(body).andThen((responseData) => {
    const renResponse = (responseData.data as RenResponse)
      .cardActivationResponse.response

    logger.debug(
      `cardActivation for card [${cardDetail.proxyNumber}]: ${JSON.stringify(
        renResponse
      )}`
    )

    // 99 is card already activated so we can ignore it
    if (
      renResponse.responseCode !== '00' &&
      renResponse.responseCode !== '99'
    ) {
      const logRef = logger.error(
        `cardActivationRequest failed with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }

    return ok(cardDetail) // better chaining
  })
}

function buildRequestBody({
  proxyNumber,
  activationCode,
}: {
  proxyNumber: string
  activationCode: string
}) {
  return {
    cardActivationRequest: {
      appHeader: {
        partID: `${REN_PART_ID}`,
        serviceName: 'CARDACTIVE',
        channelID: 'DCMSUI',
        userId: REN_USER_ID,
        reqID: getNewRequestId(),
        reqDateTime: getNewRequestTimestamp(),
      },
      cardParams: {
        proxyNumber: proxyNumber,
        hashCardNumber: '',
        cardSeqNumber: '1',
      },
      activationParam: {
        activationCode: `${activationCode}`,
        dateOfBirth: '',
        expiryDate: '',
      },
    },
  }
}
