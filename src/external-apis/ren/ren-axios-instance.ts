import axios, { AxiosRequestConfig } from 'axios'
import axiosRetry from 'axios-retry'
import rateLimit from 'axios-rate-limit'
import {
  IS_DEV,
  IS_STAGING,
  REN_CA_CERT,
  REN_DOMAIN_CERT,
  REN_INTERMIDIATE_CERT as REN_INTERMEDIATE_CERT,
  REN_URL,
  REN_URL2,
} from 'utils/config'
import https from 'https'
import dns from 'dns'
import logger from 'utils/logger'
import { request } from 'http'

declare module 'axios' {
  interface AxiosRequestConfig {
    currentEndpointIndex?: number
  }
}

/***
 * Create axios instance for REN API
 *
 * This ensures all calls have an exponential backoff and retry mechanism
 *
 * REN APIs ensure that requests with the same requestID are not processed more than once
 */

// Custom DNS resolution mapping for specific hostnames
const hostnameToIpMap: { [hostname: string]: string } = {
  // production
  'prezzynzprepaid.eftapme.com': '*************',
  'dr-prezzynzprepaid.eftapme.com': '*************',
  // dev / REN UAT
  'prezzy_nz_uat.eftapme.com': '************',
  'prezzynzuat.eftapme.com': '************',
}

const endpoints = [REN_URL, REN_URL2]

const authenticateWithCertificates = IS_STAGING
let customHttpsAgent: https.Agent

if (authenticateWithCertificates) {
  console.log('authenticateWithCertificates = true') // logger is not initialized yet
  // Create a custom HTTPS agent with DNS resolution override
  customHttpsAgent = new https.Agent({
    rejectUnauthorized: authenticateWithCertificates,
    cert: REN_DOMAIN_CERT,
    ca: [REN_INTERMEDIATE_CERT, REN_CA_CERT],
    lookup(
      hostname: string,
      options: dns.LookupOptions,
      callback: (
        err: NodeJS.ErrnoException | null,
        address: string | dns.LookupAddress[],
        family: number
      ) => void
    ): void {
      const ip = hostnameToIpMap[hostname]
      //console.log(`Resolving hostname: ${hostname} to IP: ${ip}`)

      if (ip) {
        if (options.all) {
          // When options.all is true, return an array of addresses
          const addresses: dns.LookupAddress[] = [{ address: ip, family: 4 }]
          return callback(null, addresses, 4)
        } else {
          // When options.all is false or undefined, return a single address
          return callback(null, ip, 4)
        }
      }

      // Fallback to default DNS lookup for all other hostnames
      dns.lookup(hostname, options, callback)
    },
  })
} else {
  customHttpsAgent = new https.Agent({
    rejectUnauthorized: false,
    lookup(
      hostname: string,
      options: dns.LookupOptions,
      callback: (
        err: NodeJS.ErrnoException | null,
        address: string | dns.LookupAddress[],
        family: number
      ) => void
    ): void {
      const ip = hostnameToIpMap[hostname]
      //console.log(`Resolving hostname: ${hostname} to IP: ${ip}`)

      if (ip) {
        if (options.all) {
          // When options.all is true, return an array of addresses
          const addresses: dns.LookupAddress[] = [{ address: ip, family: 4 }]
          return callback(null, addresses, 4)
        } else {
          // When options.all is false or undefined, return a single address
          return callback(null, ip, 4)
        }
      }

      // Fallback to default DNS lookup for all other hostnames
      dns.lookup(hostname, options, callback)
    },
  })
}

const axiosInstance = axios.create({
  // baseURL: REN_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  httpsAgent: customHttpsAgent,
})

// Apply rate limiting
const rateLimitedInstance = rateLimit(axiosInstance, {
  maxRequests: IS_DEV ? 2 : 5, // max number of requests per interval
  perMilliseconds: 1000, // interval in milliseconds
  maxRPS: IS_DEV ? 2 : 5, // optional: max requests per second
})

// **3. Attach interceptors to rateLimitedInstance BEFORE applying axios-retry**
rateLimitedInstance.interceptors.request.use((config) => {
  if (config.currentEndpointIndex === undefined) {
    config.currentEndpointIndex = 0 // Start with the first endpoint
  }

  config.baseURL = endpoints[config.currentEndpointIndex]
  // logger.debug(
  //   `rateLimitedInstance.interceptors.request.. Request to ${config.baseURL}${config.url}`
  // )
  if (config.url?.includes(`/card/issue`)) {
    //logger.inspect(config.data, `interceptors.request..`)
  }
  return config
})

rateLimitedInstance.interceptors.response.use(
  (response) => {
    // Optional: Log the response for troubleshooting
    // logger.inspect(response.data, `Response from ${response.config.url}`)
    return response
  },
  (error) => {
    // **This error handler will now be called only after retries are exhausted**
    const config = error.config || {}
    const retries = config['axios-retry'] ? config['axios-retry'].retryCount : 0
    const maxRetries = config['axios-retry'] ? config['axios-retry'].retries : 0

    // logger.debug(
    //   `rateLimitedInstance.interceptors.response.. Response from ${error.config.baseURL}${error.config.url}`
    // )

    if (retries >= maxRetries) {
      // Retries are exhausted, log the error
      logger.error(error, 'REN Response Error:')
    }

    return Promise.reject(error)
  }
)

function customExponentialDelay(retryNumber = 0) {
  const baseDelay = 100 // Starting delay in milliseconds
  const delay = Math.pow(2, retryNumber) * baseDelay
  const randomSum = delay * 0.2 * Math.random() // Add up to 20% jitter
  return delay + randomSum
}

axiosRetry(rateLimitedInstance, {
  retries: 6,
  retryDelay: customExponentialDelay,
  retryCondition: (error) => {
    logger.debug(`axiosRetry.. retryCondition: ${error}`)
    // Network errors should be retried
    if (!error.response) {
      return true
    }

    // Retry on specific status codes
    const retryStatusCodes = [429, 500, 502, 503, 504]
    if (retryStatusCodes.includes(error.response.status)) {
      return true
    }

    return false
  },
  onRetry: (retryCount, error, requestConfig) => {
    const config = error.config || {}
    let currentIndex =
      config.currentEndpointIndex !== undefined
        ? config.currentEndpointIndex
        : 0
    currentIndex = (currentIndex + 1) % endpoints.length // Cycle through endpoints
    config.currentEndpointIndex = currentIndex
    config.baseURL = endpoints[currentIndex]

    logger.error(
      error,
      `axiosRetry.. (attempt ${retryCount}) to URL:[${config.baseURL}${requestConfig.url}]`
    )

    const maskedData = JSON.parse(
      JSON.stringify(config.data, (key, value) => {
        if (key === 'PAN') {
          return '****'
        }
        return value
      })
    )

    logger.inspect(maskedData, 'axios onRetry.. request body:')
  },
})

// RENs API's are non standard requiring body to be sent to GET requests
// That is why we have to transform the request
const renAxiosInstance = (config: AxiosRequestConfig) => {
  if (config.method?.toLowerCase() === 'get' && config.data) {
    return rateLimitedInstance({
      ...config,
      method: 'get', // Ensure it's still a GET request
      transformRequest: [
        (data, headers = {}) => {
          // Ensure the data is properly serialized as JSON
          headers['Content-Type'] = 'application/json'
          const seralizedData = JSON.stringify(data)

          //logger.inspect(seralizedData, 'renAxiosInstance.. request data')

          return seralizedData
        },
      ],
      headers: {
        ...config.headers, // Ensure any existing headers are retained
      },
      data: config.data, // Add the body to the GET request
    })
  }

  return rateLimitedInstance(config)
}

export default renAxiosInstance
