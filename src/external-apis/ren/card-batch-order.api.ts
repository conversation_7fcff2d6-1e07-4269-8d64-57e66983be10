import { err, from<PERSON>rom<PERSON>, ok } from 'neverthrow'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
  RenCurrencyCode,
} from './ren-utils'
import { processRenApiError } from './process-ren-api-error'
import renAxiosInstance from './ren-axios-instance'
import logger from 'utils/logger'
import { errorCodes, SimpleError } from 'types/simple-error'
import { truncateString } from 'utils/truncate-string'
import { REN_PART_ID } from 'utils/config'

type CreateBatchOrderResponse = {
  instCrdOrderDetailResponse: {
    header: {
      partID: string
      serviceName: string
      channelID: string
      userId: string
      reqID: string
      reqDateTime: string
    }
    response: {
      uniqueId: string
      responseCode: string
      description: string
    }
  }
}

export type CreateBatchOrderArgs = {
  recipientName: string
  addressLine1: string
  addressLine2: string
  addressLine3: string
  city: string
  postCode: string
  email: string
  embossLine1?: string
  embossLine2: string
  quantity: number
  deliveryOrderId?: string
  logoFilename?: string
  shippingMethod: '2' | '6' | ''
  cardType: string
  cardSubType: string
  denomination: string
}

interface RenResponse {
  instCrdOrderDetailResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

function cardBatchOrderRequest(body: unknown) {
  const url = `/PREZZY/RenCms/V2.1.0.0/card/orderInstaCardWithDetails`
  return fromPromise(
    renAxiosInstance({ method: 'post', url, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'instCrdOrderDetailRequest failed')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as SimpleError
    }
  )
}

export function createBatchOrder(args: CreateBatchOrderArgs) {
  const body = createBatchOrderRequestBody(args)

  logger.inspect(
    body,
    `createBatchOrder.. body for order ${args.deliveryOrderId}, recipeient, ${args.recipientName}`
  )

  return cardBatchOrderRequest(body).andThen((res) => {
    // Guard against empty response
    if (!res?.data?.instCrdOrderDetailResponse?.response) {
      const logRef = logger.error(
        'createBatchOrder.. did not have expected instCrdOrderDetailResponse and response nodes'
      )
      logger.inspect(res.data, 'createBatchOrder.. response data')
      return err({
        code: errorCodes.external.ren.REQUEST_FAILED,
        message: 'Failed to create batch order',
        logRef,
      } as SimpleError)
    }

    const renResponse = (res.data as RenResponse).instCrdOrderDetailResponse
      .response

    if (!renResponse.responseCode || !renResponse.description) {
      const logRef = logger.error(
        'createBatchOrder.. did not have responseCode or description'
      )
      logger.inspect(res.data, 'createBatchOrder.. response data')
    }

    logger.debug(
      `createBatchOrder for order [${args.deliveryOrderId}], recipient [${
        args.recipientName
      }], response:: ${JSON.stringify(renResponse)}`
    )

    if (renResponse.responseCode !== '00') {
      const logRef = logger.error(
        `createBatchOrder failed with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      logger.inspect(body, 'createBatchOrder body')
      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }
    return ok(res.data as CreateBatchOrderResponse)
  })
}

function createBatchOrderRequestBody(args: CreateBatchOrderArgs) {
  return {
    instCrdOrderDetailRequest: {
      header: {
        partID: `${REN_PART_ID}`,
        serviceName: 'ORDINCRDTL',
        channelID: 'DCMSUI',
        userId: REN_USER_ID,
        reqID: getNewRequestId(),
        reqDateTime: getNewRequestTimestamp(),
      },
      orderParam: {
        cardType: args.cardType,
        cardSubType: args.cardSubType,
        currencyCode: RenCurrencyCode.NZD,
        orderingBranch: '',
        orderingBranchInfo: {
          branchCode: '',
          branchName: '',
          firstName: truncateString(args.recipientName, 40),
          lastName: '',
          status: '2',
          branchType: '',
          addressLine1: truncateString(args.addressLine1, 40),
          addressLine2: truncateString(args.addressLine2, 40),
          city: args.city,
          state: '',
          countryCode: 'NZ',
          zipCode: truncateString(args.postCode, 80),
          postBox: 'placeholder', // mandatory but not used so 'placeholder' hardcoded
          phone: '',
          //email: '', removed because it's not the cardholder email and stops OTP at merchant
        },
        embossLine1: args.embossLine1 ?? 'PREZZY CARDHOLDER', // Cardholder Name
        embossLine2: truncateString(args.embossLine2, 26),
        orderQty: `${args.quantity}`,
        orderId: args.deliveryOrderId, // memberId (deliveryBatchId)
        shippingMethod: args.shippingMethod,
        packageName: '',
        logoId: truncateString(args.logoFilename, 255),
        memo1: `${args.addressLine3}`,
        memo2: args.denomination,
      },
    },
  }
}
