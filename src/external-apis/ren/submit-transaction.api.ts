import { err, from<PERSON><PERSON><PERSON>, ok } from 'neverthrow'
import { getNewRequestTimestamp, RenCurrencyCode } from './ren-utils'
import { CardDetail } from './get-card-details.api'
import renAxiosInstance from './ren-axios-instance'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { cardExpiryToYYMM } from 'helpers/data-helper'

function submitTransactionRequest(body: unknown) {
  const url = '/PREZZY/RenSbmt/V2.1.0.0/V2.0.1.0'
  return fromPromise(
    renAxiosInstance({ method: 'post', url, data: body }),
    (error) => {
      const logRef = logger.error(error, 'cardStatusChangeRequest failed')
      return {
        code: errorCodes.external.ren.REQUEST_FAILED,
        message: 'Ren Submit Transaction API request failed',
        logRef,
      } as SimpleError
    }
  )
}

interface TransactionResponse {
  Document: {
    AccptrAuthstnRspn: {
      Hdr: {
        MsgFctn: string
        PrtcolVrsn: string
        XchgId: string
        CreDtTm: string
        Chnl: string
      }
      AuthstnRspn: {
        Envt: {
          AcqrrId: {
            Id: string
          }
          MrchntId: {
            Id: string
          }
          POIId: {
            Id: string
          }
          Card: {
            PlainCardData: {
              XpryDt: string
              PAN: string //masked
            }
            CardCtryCd: string
            CardCcyCd: string
          }
        }
        Tx: {
          TxId: {
            TxDtTm: string
            TxRef: string
          }
          TxDtls: {
            Ccy: string
            TtlAmt: string
          }
        }
        TxRspn?: {
          AuthstnRslt: {
            RspnToAuthstn: {
              Rspn: 'DECL' | string
              RspnRsn?: string
            }
          }
        }
      }
    }
  }
}

export function loadFunds({
  cardDetail,
  amountInCents,
  orderNumber,
}: {
  cardDetail: CardDetail
  amountInCents: number
  orderNumber: string
}) {
  const body = buildLoadFundsBody({
    proxyNumber: cardDetail.proxyNumber,
    amountInCents: amountInCents,
    //cardNumber: cardDetail.cardNumber,
    expiryDate: cardDetail.expiryDate, // convert to year month
    orderNumber: orderNumber,
  })

  return submitTransactionRequest(body).andThen((res) => {
    const tsxResponse = (res.data as TransactionResponse).Document
      .AccptrAuthstnRspn.AuthstnRspn.TxRspn

    const maskedResponse = JSON.parse(
      JSON.stringify(tsxResponse, (key, value) => {
        if (key === 'PAN') {
          return '****'
        }
        return value
      })
    )

    logger.debug(
      `loadFunds.. submitTransactionResponse for card proxy [${
        cardDetail.proxyNumber
      }] amount [${amountInCents}]: ${JSON.stringify(maskedResponse)}`
    )

    // TODO: return true if already in the status you want even though it is an error
    if (tsxResponse?.AuthstnRslt?.RspnToAuthstn?.Rspn === 'DECL') {
      const logRef = logger.error(
        `loadFunds.. failed with ren response ${JSON.stringify(maskedResponse)}`
      )
      return err({
        code: errorCodes.external.ren.TSX_FAILED,
        message:
          tsxResponse?.AuthstnRslt.RspnToAuthstn.RspnRsn ?? 'Unknown error',
        logRef,
      } as SimpleError)
    }

    return ok(cardDetail) // better chaining
  })
}

function buildLoadFundsBody({
  //cardNumber,
  expiryDate,
  orderNumber,
  amountInCents,
  proxyNumber,
}: {
  //cardNumber: string
  expiryDate: string
  orderNumber: string
  amountInCents: number
  proxyNumber: string
}) {
  return {
    Document: {
      AccptrAuthstnReq: {
        Hdr: {
          MsgFctn: 'AUTQ',
          PrtcolVrsn: '2.0',
          CreDtTm: getNewRequestTimestamp(),
          Chnl: 'WEB',
        },
        AuthstnReq: {
          Envt: {
            Acqrr: {
              Id: {
                Id: '50009', // ???
              },
            },
            Mrchnt: {
              Id: 'SUBMITTXN',
              LctnAndCtct: {
                PstlAdr: {
                  TwnNm: 'Auckland', // Sakshi ???
                  CtrySubDvsn: 'Mt Wellington', // Shedge ???
                  CtryCd: 'NZ', // ???
                  PstCd: '1060', // 10571 ???
                },
              },
              CmonNm: 'Prezzy Card Corporate', // Avenue d Rue
            },
            POI: {
              Id: 'SbmtTrn',
            },
            Card: {
              Id: {
                Ccy: '554', // NZD
              },
              PlainCardData: {
                ProxyCardNumber: `${proxyNumber}`,
                //PAN: `${cardNumber}`,
                XpryDt: `${cardExpiryToYYMM(expiryDate)}`,
              },
              PrxyInd: '1', // need to get sequence number
              PrxyData: {
                PrxyId: `${proxyNumber}`,
              },
            },
          },
          Cntxt: {
            PmtCntxt: {
              TxEnvt: 'ECOM',
            },
          },
          Tx: {
            MrchntCtgyCd: '7298',
            TxCaptr: 'false',
            TxTp: 'LD',
            TxId: {
              TxDtTm: getNewRequestTimestamp(),
              TxRef: `${orderNumber}`,
            },
            TxDtls: {
              TtlAmt: `${amountInCents}`,
              Ccy: RenCurrencyCode.NZD,
            },
          },
        },
      },
    },
  }
}
