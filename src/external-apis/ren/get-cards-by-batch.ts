import { err, from<PERSON>rom<PERSON>, ok } from 'neverthrow'
import {
  getNewRequestId,
  getNewRequestTimestamp,
  REN_USER_ID,
} from './ren-utils'
import renAxiosInstance from './ren-axios-instance'
import { processRenApiError } from './process-ren-api-error'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { REN_PART_ID } from 'utils/config'

interface RenResponse {
  searchCardListResponse: {
    response: {
      responseCode: string
      description: string
    }
  }
}

type SearchCardListResponse = {
  searchCardListResponse: {
    header: {
      partID: string
      serviceName: string
      channelID: string
      userId: string
      reqID: string
      reqDateTime: string
    }
    response: {
      responseCode: string
      description: string
    }
    cardInfo: Array<{
      cardNumber: string
      cardSeqNumber: number
      accountNumber: string
      customerID: string
      internalCustomerID: number
      customerName: string
      mobileNumber: string
      cardIssueDate: string
      cardExpiryDate: string
      cardIssueTime: string
      cardExpiryTime: string
      embossName: string
      proxyCardNumber: string
      orderingBranch: string
      embossType: string
      cardProductType: string
      cardType: string
      statusCode: string
      reasonCode: string
      emailId: string
      cardStatus: number
      embossTrackingId: string
      cardHolderFlag: string
    }>
    totalRecords: number
    pendingRecords: number
  }
}
function getCardsByBatchRequest(body: unknown) {
  const url = `/PREZZY/RenCms/V2.1.0.0/card/searchCardListRequest`

  return fromPromise(
    renAxiosInstance({ method: 'get', url, data: body }),
    (error) => {
      const renApiError = processRenApiError(error)
      const logRef = logger.error(error, 'getCardsByBatchRequest failed')
      return {
        code: renApiError.code,
        message: 'Ren API request failed',
        logRef,
      } as SimpleError
    }
  )
}

function createGetCardsByBatchBody({
  batchId,
  index,
  limit,
}: {
  batchId: string
  index: number
  limit: number
}) {
  return {
    searchCardListRequest: {
      header: {
        partID: `${REN_PART_ID}`,
        serviceName: 'SRCHINSCRD',
        channelID: 'DCMSUI',
        userId: REN_USER_ID,
        reqID: getNewRequestId(),
        reqDateTime: getNewRequestTimestamp(),
      },
      searchParams: {
        hashCardNumber: '',
        accountNumber: '',
        customerID: '',
        mobileNumber: '',
        customerName: '',
        tokenCardNumber: '',
        uniqueId: batchId,
        cardValueIndicator: '4',
        index,
        limit,
      },
    },
  }
}

export function getCardsByBatch({
  batchId,
  index,
  limit,
}: {
  batchId: string
  index: number
  limit: number
}) {
  const body = createGetCardsByBatchBody({ batchId, index, limit })

  return getCardsByBatchRequest(body).andThen((res) => {
    const renResponse = (res.data as RenResponse).searchCardListResponse
      .response

    logger.debug(
      `getCardsByBatch for batch [${batchId}]: ${JSON.stringify(renResponse)}`
    )

    if (renResponse.responseCode !== '00') {
      const logRef = logger.error(
        `getCardsByBatch.. failed to get cards for batch [${batchId}] with ren response ${JSON.stringify(
          renResponse
        )}`
      )
      return err({
        code: renResponse.responseCode,
        message: renResponse.description,
        logRef,
      } as SimpleError)
    }

    const fullResponse = res.data as SearchCardListResponse

    return ok(fullResponse)
  })
}
