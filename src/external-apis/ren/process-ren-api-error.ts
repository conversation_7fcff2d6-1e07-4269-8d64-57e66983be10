import { AxiosError } from 'axios'
import { get } from 'http'
import { errorCodes, SimpleError } from 'types/simple-error'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'

function extractApiErrorResponse(data: unknown): SimpleError | null {
  if (typeof data === 'object' && data !== null) {
    const responseObj = Object.values(data)[0] as any
    if (
      responseObj &&
      typeof responseObj === 'object' &&
      'response' in responseObj
    ) {
      const { responseCode, description } = responseObj.response
      if (typeof responseCode === 'string' && typeof description === 'string') {
        return { code: responseCode, message: description }
      }
    }
  }
  return null
}

function convertToSimpleError(error: AxiosError): SimpleError | null {
  if (error.response && error.response.data) {
    return extractApiErrorResponse(error.response.data)
  }
  return null
}

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  } else if (typeof error === 'string') {
    return error
  } else if (
    typeof error === 'object' &&
    error !== null &&
    'message' in error
  ) {
    return String((error as { message: unknown }).message)
  }
  return 'Unhandled REN api response error'
}

/***
 * This function is used to process the error response from the REN API
 * It will return a SimpleError object with the error code and message
 * @param error - The error object returned from the REN API
 *
 * @returns A SimpleError object with the error code and message
 * It turns the responseCode from the REN API as the code and the description as the message
 *
 * NOTE: This function will automatically log an error with Sentry if
 * It doesn't recognise the format. Specific errors will have to be
 * handled in the calling function.
 */
export function processRenApiError(error: unknown): SimpleError {
  if (error instanceof AxiosError) {
    const errorResponse = convertToSimpleError(error)
    if (errorResponse) {
      return errorResponse
    }
  }

  const logRef = logger.error(error, 'Unhandled REN api response error')
  return {
    code: errorCodes.external.api.UNKNOWN_ERROR,
    message: getErrorMessage(error),
    logRef,
  } as SimpleError
}
