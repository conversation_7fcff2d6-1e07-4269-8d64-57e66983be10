import { fromPromise } from 'neverthrow'
import auth0AxiosInstance from './auth0-axios-instance'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { getAuth0ManagementToken } from './oauth-token.api'
import { UserInfo } from './users-by-email.api'
import { AUTH0_ADMIN_CONNECTION, AUTH0_CONNECTION } from 'utils/config'

function updateUserMetadataRequest({
  userInfo,
  token,
  isAdminPortalUser,
}: {
  userInfo: UserInfo
  token: string
  isAdminPortalUser: boolean
}) {
  const url = `/api/v2/users/${userInfo.user_id}`
  return fromPromise(
    auth0AxiosInstance({
      method: 'PATCH',
      url,
      headers: {
        authorization: `Bearer ${token}`,
      },
      data: {
        connection: isAdminPortalUser
          ? AUTH0_ADMIN_CONNECTION
          : AUTH0_CONNECTION,
        user_metadata: userInfo.user_metadata,
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `Failed to get user info [${userInfo.user_id}]`
      )
      return {
        code: errorCodes.external.api.UNKNOWN_ERROR,
        message: `Failed to get user info`,
        logRef,
      } as SimpleError
    }
  )
}

export function updateUserMetadata({
  userInfo,
  isAdminPortalUser,
}: {
  userInfo: UserInfo
  isAdminPortalUser: boolean
}) {
  return getAuth0ManagementToken().andThen((token) => {
    return updateUserMetadataRequest({
      userInfo,
      token,
      isAdminPortalUser,
    }).map((res) => {
      logger.inspect(res.data, 'upateUserMetadata..response')
      return {
        expiresAt: userInfo.user_metadata.super_admin_config?.expires_at,
        targetOrgId: userInfo.user_metadata.super_admin_config?.org_id,
      }
    })
  })
}
