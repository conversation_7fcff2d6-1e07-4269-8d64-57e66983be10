import logger from 'utils/logger'
import { from<PERSON>romise, okAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import {
  AUTH0_CLIENT_ID,
  AUTH0_CLIENT_SECRET,
  AUTH0_ISSUER_BASE_URL,
} from 'utils/config'
import auth0AxiosInstance from './auth0-axios-instance'

let cachedToken: string | null = null
let tokenExpiry: Date | null = null

function isTokenExpired(): boolean {
  if (!cachedToken || !tokenExpiry) return true
  const now = new Date()
  return now >= tokenExpiry
}

interface TokenResponse {
  access_token: string
  expires_in: number
}

export function getAuth0ManagementToken() {
  if (!isTokenExpired()) {
    return okAsync(cachedToken!)
  }

  const requestData = {
    grant_type: 'client_credentials',
    client_id: AUTH0_CLIENT_ID,
    client_secret: AUTH0_CLIENT_SECRET,
    audience: `${AUTH0_ISSUER_BASE_URL}/api/v2/`,
  }

  return from<PERSON>romise(
    auth0AxiosInstance({
      method: 'POST',
      url: '/oauth/token',
      data: requestData,
    }),
    (error) => {
      const logRef = logger.error(
        error,
        'getAuth0ManagementToken..Failed to get auth0 management token'
      )

      return {
        code: errorCodes.external.auth0.FAILED_TO_GET_MANAGEMENT_TOKEN,
        message:
          'getAuth0ManagementToken..Failed to get auth0 management token',
        logRef,
      } as SimpleError
    }
  ).map((response) => {
    const tokenData = response.data as TokenResponse

    // Update cache
    cachedToken = tokenData.access_token
    tokenExpiry = new Date(new Date().getTime() + tokenData.expires_in * 1000)

    logger.debug('getAuth0ManagementToken..Token cache updated', {
      expiresAt: tokenExpiry.toISOString(),
    })

    return tokenData.access_token
  })
}
