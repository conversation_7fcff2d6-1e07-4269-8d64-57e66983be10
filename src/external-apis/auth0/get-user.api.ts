import { fromPromise } from 'neverthrow'
import auth0AxiosInstance from './auth0-axios-instance'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { getAuth0ManagementToken } from './oauth-token.api'
import { AUTH0_ADMIN_CONNECTION, AUTH0_CONNECTION } from 'utils/config'

function getUserInfoRequest({
  userId,
  token,
  isAdminPortalUser,
}: {
  userId: string
  token: string
  isAdminPortalUser: boolean
}) {
  const url = `/api/v2/users/${userId}`
  return fromPromise(
    auth0AxiosInstance({
      method: 'get',
      url,
      params: {
        connection: isAdminPortalUser
          ? AUTH0_ADMIN_CONNECTION
          : AUTH0_CONNECTION,
      },
      headers: {
        authorization: `Bearer ${token}`,
      },
    }),
    (error) => {
      const logRef = logger.error(error, `Failed to get user info [${userId}]`)
      return {
        code: errorCodes.external.api.UNKNOWN_ERROR,
        message: `Failed to get user info`,
        logRef,
      } as SimpleError
    }
  )
}

export async function getUserInfoById({
  userId,
  isAdminPortalUser,
}: {
  userId: string
  isAdminPortalUser: boolean
}) {
  return getAuth0ManagementToken().andThen((token) => {
    return getUserInfoRequest({ userId, token, isAdminPortalUser }).map(
      (res) => {
        return res.data
      }
    )
  })
}
