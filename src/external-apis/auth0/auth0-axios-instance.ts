import axios, { AxiosRequestConfig, AxiosInstance, AxiosError } from 'axios'
import axiosRetry, { isNetworkOrIdempotentRequestError } from 'axios-retry'
import rateLimit from 'axios-rate-limit'
import { AUTH0_ISSUER_BASE_URL } from 'utils/config'
import logger from 'utils/logger'

const MANAGEMENT_API = `${AUTH0_ISSUER_BASE_URL}`

// Create the base axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: MANAGEMENT_API,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Apply rate limiting with type assertion
const rateLimitedInstance = rateLimit(axiosInstance, {
  maxRequests: 20,
  perMilliseconds: 1000,
  maxRPS: 20,
}) as AxiosInstance

// Configure retry behavior
axiosRetry(rateLimitedInstance, {
  retries: 5,
  retryDelay: axiosRetry.exponentialDelay, // Add exponential backoff
  shouldResetTimeout: true,
  retryCondition: (error: AxiosError) => {
    logger.debug(`auth0 axiosRetry.. retryCondition: ${error.message}`)

    // Check for network errors or no response
    if (isNetworkOrIdempotentRequestError(error)) {
      return true
    }

    // Check for rate limit errors
    if (error.response?.status === 429) {
      return true
    }

    // Don't retry on 500s
    if (error.response?.status && error.response.status >= 500) {
      return false
    }

    return false
  },
  onRetry: (
    retryCount: number,
    error: AxiosError,
    requestConfig: AxiosRequestConfig
  ) => {
    logger.error(
      {
        error: error.message,
        status: error.response?.status,
        url: `${requestConfig.baseURL}${requestConfig.url}`,
        retryCount,
      },
      `Auth0 API request failed, retrying...`
    )
  },
})

// Create a function that returns a configured axios instance
const auth0AxiosInstance = (config: AxiosRequestConfig) => {
  return rateLimitedInstance({
    ...config,
    validateStatus: (status: number) => {
      return status >= 200 && status < 300
    },
  })
}

export default auth0AxiosInstance
