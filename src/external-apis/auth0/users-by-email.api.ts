import { fromPromise, ok } from 'neverthrow'
import auth0AxiosInstance from './auth0-axios-instance'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { getAuth0ManagementToken } from './oauth-token.api'
import { get } from 'http'
import { connect } from 'http2'
import { AUTH0_ADMIN_CONNECTION, AUTH0_CONNECTION } from 'utils/config'

export type UserMetadata = {
  phone_number: string
  super_admin_config:
    | {
        org_id: string
        is_live_agent: boolean
        expires_at: string
      }
    | null
    | undefined
}
export interface UserInfo {
  user_id: string
  user_metadata: UserMetadata
}

function getUserInfoByEmailRequest({
  email,
  token,
  isAdminPortalUser,
}: {
  email: string
  token: string
  isAdminPortalUser: boolean
}) {
  return fromPromise(
    auth0AxiosInstance({
      method: 'get',
      url: `/api/v2/users-by-email`,
      params: {
        email,
        connection: isAdminPortalUser
          ? AUTH0_ADMIN_CONNECTION
          : AUTH0_CONNECTION,
      },
      headers: {
        authorization: `Bearer ${token}`,
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `getUserInfoByEmailRequest..Failed to get user info [${email}]`
      )
      return {
        code: errorCodes.external.api.UNKNOWN_ERROR,
        message: `Failed to get user info`,
        logRef,
      } as SimpleError
    }
  )
}

export function getUserInfoByEmail({
  email,
  isAdminPortalUser,
}: {
  email: string
  isAdminPortalUser: boolean
}) {
  logger.debug('getUserInfoByEmail', {
    email,
    isAdminPortalUser,
  })
  return getAuth0ManagementToken().andThen((token) => {
    return getUserInfoByEmailRequest({ email, token, isAdminPortalUser }).map(
      (res) => {
        logger.inspect(res.data, 'getUserInfoByEmail..')
        return res.data[0] as UserInfo
      }
    )
  })
}
