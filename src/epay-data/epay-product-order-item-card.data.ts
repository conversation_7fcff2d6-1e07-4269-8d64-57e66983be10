import { from<PERSON>romise } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function epayUpdateLockCodeInPOIC({
  id,
  lockCode,
}: {
  id: string
  lockCode: string
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: { id },
      data: { lockCode },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to update POIC lock code')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to update POIC lock code',
        logRef,
      } as SimpleError
    }
  )
}

export function epayUpdateUnitPriceInPOIC({
  id,
  unitPriceInCents,
}: {
  id: string
  unitPriceInCents: number
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: { id },
      data: { unitPriceInCents },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to update POIC unit price')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to update POIC unit price',
        logRef,
      } as SimpleError
    }
  )
}

