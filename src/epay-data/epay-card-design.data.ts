import { fromPromise } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function getCardDesignByExternalCardDesignId(externalCardDesignId: string) {
  {
    return fromPromise(
      prisma.cardDesign.findFirst({
        where: { externalCardDesignId },
      }),
      (error) => {
        const logRef = logger.error(error, `Failed to get card design [${externalCardDesignId}]`)
        return {
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Failed to get card design',
          logRef,
        } as SimpleError
      }
    )
  }
}
