import { OrderProcessingStatus, Prisma } from '@prisma/client'
import { fromPromise, ResultAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import { DEFERRED_DELAY_BEFORE_RETRY_IN_MINUTES } from 'utils/config'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export type OrderQueueItemWithProductOrder =
  Prisma.OrderProcessingQueueGetPayload<{
    include: {
      productOrder: true
    }
  }>

export function epayProcessNextQueueItem(orderAgeInMinutes = 0) {
  let sanitisedOrderAgeInMinutes = orderAgeInMinutes
  if (isNaN(orderAgeInMinutes)) {
    sanitisedOrderAgeInMinutes = 0
    logger.error(
      'epayProcessNextQueueItem.. orderInMinutes is not a number, defaulting to 0'
    )
  }
  const MINUTES_TO_MS = 60 * 1000
  const now = new Date().toISOString()
  const nowDate = new Date(now)

  const agedDate = new Date(
    nowDate.getTime() - sanitisedOrderAgeInMinutes * MINUTES_TO_MS
  )
  logger.debug(`epayProcessNextQueueItem.. agedDate: ${agedDate.toISOString()}`)

  const deferredRetryDelay = new Date(
    nowDate.getTime() - DEFERRED_DELAY_BEFORE_RETRY_IN_MINUTES * MINUTES_TO_MS
  )

  return fromPromise(
    // Claim next order in transaction
    prisma.$transaction(async (tx) => {
      // Define the time filter if applicable

      const timeFilter =
        sanitisedOrderAgeInMinutes === 0
          ? {}
          : {
              dateCreated: {
                lt: agedDate.toISOString(),
              },
            }

      const scheduledDateFilter = {
        scheduledDate: {
          lt:
            sanitisedOrderAgeInMinutes === 0
              ? nowDate.toISOString()
              : agedDate.toISOString(),
        },
      }

      // Find and lock next pending order
      const queueItem = await tx.orderProcessingQueue.findFirst({
        where: {
          OR: [
            {
              status: OrderProcessingStatus.DEFERRED,
              lastUpdated: {
                lt: deferredRetryDelay.toISOString(),
              },
              error: null,
            },
            // Use `dateCreated` for orders without scheduledDate
            {
              status: OrderProcessingStatus.PENDING,
              productOrder: {
                scheduledDate: null,
              },
              ...timeFilter, // only include time filter if orderAgeInMinutes is not 0
            },
            // Get next scheduled order
            {
              status: OrderProcessingStatus.PENDING,
              productOrder: {
                ...scheduledDateFilter,
              },
            },
          ],
        },
        orderBy: { queueId: 'asc' },
        include: {
          productOrder: true,
        },
      })
      if (!queueItem) {
        return null // no PENDING queue items available
      }

      // Mark as processing while we have the lock
      const updated = await tx.orderProcessingQueue.updateMany({
        where: {
          queueId: queueItem.queueId,
          status: {
            in: [OrderProcessingStatus.PENDING, OrderProcessingStatus.DEFERRED],
          },
        },
        data: {
          status: OrderProcessingStatus.PROCESSING,
          dateStarted: now,
          lastUpdated: now,
        },
      })

      if (updated.count === 0) {
        // Another process has claimed this queue item
        return null
      }

      return queueItem
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to claim next queue item')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to claim next queue item',
        logRef,
      } as SimpleError
    }
  )
}

export function epayUpdateQueueItemStatusCompleted({
  queueId,
  errorMessage,
}: {
  queueId: number
  errorMessage: string | null
}) {
  return fromPromise(
    prisma.orderProcessingQueue.update({
      where: { queueId: queueId },
      data: {
        status: OrderProcessingStatus.COMPLETED,
        dateCompleted: new Date().toISOString(),
        error: errorMessage,
        lastUpdated: new Date().toISOString(),
      },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to complete queue item')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to complete queue item',
        logRef,
      } as SimpleError
    }
  )
}

export function epayUpdateQueueItemStatusDeferred({
  queueId,
}: {
  queueId: number
}) {
  return fromPromise(
    prisma.orderProcessingQueue.update({
      where: { queueId: queueId },
      data: {
        status: OrderProcessingStatus.DEFERRED,
        lastUpdated: new Date().toISOString(),
      },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to complete queue item')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to complete queue item',
        logRef,
      } as SimpleError
    }
  )
}

// In epay-data/order-processing-queue.data.ts

export function epayAddOrderToProcessQueue({
  orderNumber,
  productOrderId,
}: {
  orderNumber: string
  productOrderId: string
}): ResultAsync<void, SimpleError> {
  return fromPromise(
    prisma.orderProcessingQueue.create({
      data: {
        orderNumber,
        productOrderId,
        status: OrderProcessingStatus.PENDING,
        dateCreated: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `Failed to add order [${orderNumber}] to processing queue`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to add order to processing queue',
        logRef,
      } as SimpleError
    }
  ).map(() => {
    return undefined
  })
}
