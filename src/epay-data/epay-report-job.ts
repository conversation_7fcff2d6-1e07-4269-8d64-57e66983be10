import { ReportJobStatus } from '@prisma/client'
import { errAsync, fromPromise, okAsync } from 'neverthrow'
import { ReportDefinition } from 'types/reports'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function updateReportJobStatus({
  jobId,
  status,
  error,
}: {
  jobId: string
  status: ReportJobStatus
  error?: string
}) {
  logger.info(`updateJobStatus... for job [${jobId}]`)
  return fromPromise(
    prisma.reportJob.update({
      where: { id: jobId },
      data: {
        status,
        error,
      },
    }),
    (error) => {
      logger.warn(`failed to update job status for job [${jobId}]`, error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}

export function updateReportJobProgress({
  jobId,
  progress,
}: {
  jobId: string
  progress: number
}) {
  logger.info(`updateJobProgress... for job [${jobId}]`)
  return fromPromise(
    prisma.reportJob.update({
      where: { id: jobId },
      data: { progress },
    }),
    (error) => {
      logger.warn(`failed to update job progress for job [${jobId}]`, error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  ).map(() => undefined)
}

export function getReportJobStatus({
  jobId,
  userId,
}: {
  jobId: string
  userId: string
}) {
  logger.info(`getJobStatus... for job [${jobId}]`)

  return fromPromise(
    prisma.reportJob.findFirst({
      where: { id: jobId, userId },
    }),
    (error) => {
      logger.warn(
        `getJobStatus...failed to get job status for job [${jobId}]`,
        error
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  ).andThen((job) => {
    if (!job) {
      logger.warn(`getJobStatus... Job [${jobId}] not found`)
      return errAsync({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Job [${jobId}] not found`,
      } as SimpleError)
    }
    return okAsync(job)
  })
}

export function createReportJob({
  reportDef,
  startDate,
  endDate,
  userId,
  filters,
}: {
  reportDef: ReportDefinition
  startDate: Date
  endDate: Date
  userId: string
  filters?: Record<string, any>
}) {
  return fromPromise(
    prisma.reportJob.create({
      data: {
        type: reportDef.type,
        status: ReportJobStatus.PENDING,
        startDate,
        endDate,
        userId,
        filters: filters || {},
      },
    }),
    (error) => {
      logger.warn(`failed to create report job`, error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}

export function finalizeReportJob({
  jobId,
  finalKey,
}: {
  jobId: string
  finalKey: string
}) {
  return fromPromise(
    prisma.reportJob.update({
      where: { id: jobId },
      data: {
        status: ReportJobStatus.COMPLETED,
        progress: 100,
        completedAt: new Date(),
        filename: finalKey,
      },
    }),
    (error) => {
      logger.warn('Failed to update report job status', error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to update report job status',
      } as SimpleError
    }
  )
}
