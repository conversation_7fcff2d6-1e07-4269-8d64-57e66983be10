import { fromPromise, ResultAsync } from 'neverthrow'
import { prisma } from 'utils/prisma'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { ORDER_LOCK_TIMEOUT_IN_MINUTES } from 'utils/config'

export const ORDER_PROCESSING_LOCK_ID = 'ORDER_PROCESSING'

const LOCK_TIMEOUT_IN_MS = ORDER_LOCK_TIMEOUT_IN_MINUTES * 60 * 1000

export function epayTryAcquireProcessingLock(
  key: string
): ResultAsync<boolean, SimpleError> {
  return fromPromise(
    prisma.$transaction(async (tx) => {
      // Ensure we're using UTC time consistently
      const now = new Date().toISOString()
      const nowDate = new Date(now)

      const lock = await tx.processingLock.findUnique({
        where: { id: key },
      })

      if (!lock) {
        await tx.processingLock.create({
          data: {
            id: key,
            isProcessing: true,
            lastUpdated: now,
          },
        })

        // Lock was created
        logger.debug(`Successfully created processing lock [${key}]`)

        return true
      } else if (lock.isProcessing) {
        const lastUpdated = new Date(lock.lastUpdated)
        logger.debug(
          `epayTryAcquireProcessingLock.. Comparing times:` +
          `\nNow UTC: ${nowDate.toISOString()}` +
          `\nLast Updated UTC: ${lastUpdated.toISOString()}` +
          `\nDifference in MS: ${nowDate.getTime() - lastUpdated.getTime()}` +
          `\nTimeout threshold: ${LOCK_TIMEOUT_IN_MS}`
        )
        if (nowDate.getTime() - lastUpdated.getTime() < LOCK_TIMEOUT_IN_MS) {
          // Lock is still valid
          logger.debug(`Processing lock [${key}] is still valid`)
          return false
        }
        // Lock is stale; proceed to acquire it. Log error so the team is alerted they need to investigate
        logger.error(
          `Lock [${key}] is stale (last updated at ${lock.lastUpdated}). Acquiring stale lock.`
        )
      }

      const acquired = await tx.processingLock.updateMany({
        where: { id: key, lastUpdated: lock.lastUpdated },
        data: { isProcessing: true, lastUpdated: now },
      })

      if (acquired.count > 0) {
        logger.debug(`Successfully acquired processing lock [${key}]`)
        return true
      } else {
        logger.debug(
          `Failed to acquire processing lock [${key}] due to losing race condition`
        )
        return false
      }
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to update processing lock')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to update processing lock',
        logRef,
      } as SimpleError
    }
  )
}

// Function to release the processing lock
export function epayReleaseProcessingLock(
  key: string
): ResultAsync<boolean, SimpleError> {
  return fromPromise(
    prisma.processingLock
      .update({
        where: { id: key },
        data: { isProcessing: false, lastUpdated: new Date().toISOString() },
      })
      .then(() => {
        logger.debug(`Successfully released processing lock [${key}]`)
        return true
      }),
    (error) => {
      const logRef = logger.error(error, 'Failed to release processing lock')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to release processing lock',
        logRef,
      } as SimpleError
    }
  )
}

export function epayUpdateProcessingLockLastUpdated(
  key: string
): ResultAsync<boolean, SimpleError> {
  return fromPromise(
    prisma.processingLock
      .update({
        where: { id: key },
        data: { lastUpdated: new Date().toISOString() },
      })
      .then(() => {
        logger.debug(
          `Successfully REFRESHED processing lock [${key}] to lastUpdated time`
        )
        return true
      }),
    (error) => {
      const logRef = logger.error(
        error,
        'Failed to update processing lock lastUpdated time'
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to update processing lock lastUpdated time',
        logRef,
      } as SimpleError
    }
  )
}
