import { err, errAsync, from<PERSON><PERSON><PERSON>, ok, Result } from 'neverthrow'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'
import { errorCodes, SimpleError } from 'types/simple-error'
import { UpdatedProductOrderData } from 'data/product-order.data'
import { DeliveryMethod, OrderStatus, OrderType, Prisma } from '@prisma/client'
import { EpayOrdersListProps } from 'types/epay.order'
import { centsToDollars } from 'utils/numeric'

export type UnprocessedProductOrderData = Prisma.ProductOrderGetPayload<{
  include: {
    user: true
    organization: true
    productOrderItems: {
      include: {
        product: {
          include: {
            design: {
              select: {
                externalCardDesignId: true
                externalCardType: true
                designType: true
                externalBinNumber: true
                externalProductCode: true
              }
            }
            logo: {
              select: {
                logoUrl: true
                name: true
              }
            }
          }
        }
      }
    }
  }
}>

export type EpayOrderForProcessing = Prisma.ProductOrderGetPayload<{
  include: {
    user: true
    organization: true
    productOrderItems: {
      include: {
        product: {
          include: {
            design: true
            logo: true
          }
        }
      }
    }
  }
}>

export function epayGetOrderForProcessing(orderId: string) {
  return fromPromise(
    prisma.productOrder.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        organization: true,
        productOrderItems: {
          where: {
            OR: [
              // Physical cards should have externalBatchId
              {
                AND: [
                  { productOrder: { scheduledDate: null } },
                  { externalBatchId: { not: null } },
                  { externalBatchId: { not: '' } },
                  { cardItems: { none: {} } },
                ],
              },
              // Scheduled virtual cards should not have externalBatchId yet
              {
                AND: [
                  {
                    OR: [{ externalBatchId: null }, { externalBatchId: '' }],
                  },
                  { cardItems: { none: {} } },
                  { productOrder: { scheduledDate: { not: null } } },
                ],
              },
            ],
          },
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        'epayGetReleasedOrder.. Failed to get productOrder from DB'
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `epayGetReleasedOrder.. ProductOrder with id [${orderId}] not found`,
      } as SimpleError)
    }

    return ok(productOrder as UnprocessedProductOrderData)
  })
}

export function epayUpdateProductOrderStatusByOrder({
  orderStatus,
  releasedAt,
  orderNumber,
  releasedBy,
  paymentDate,
  orderId,
}: {
  orderStatus: OrderStatus
  releasedAt: Date
  orderNumber?: string
  releasedBy?: string
  paymentDate: Date
  orderId?: string
}) {
  if (!orderNumber && !orderId) {
    return errAsync({
      code: errorCodes.db.BAD_INPUT,
      message: 'orderNumber or orderId must be provided',
    } as SimpleError)
  }

  let where: Prisma.ProductOrderWhereUniqueInput
  if (orderId) {
    where = {
      id: orderId,
    }
  } else {
    where = {
      orderNumber,
    }
  }
  return fromPromise(
    prisma.productOrder.update({
      where,
      data: {
        orderStatus,
        submittedAt: releasedAt.toISOString(),
        releasedBy,
        paymentDate: paymentDate.toISOString(),
      },
      include: {
        user: true,
        organization: true,
        FundingProductOrderItem: true,
        productOrderItems: {
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `epayUpdateProductOrderStatusByOrderNumber.. error updating product order [${orderNumber}] to [${orderStatus}]`
      )

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Database operation failed',
        logRef,
      } as SimpleError
    }
  ).map((productOrder) => {
    return productOrder as UpdatedProductOrderData
  })
}

export type ProductOrderValidationResult = {
  id: string
  orderNumber: string
  productOrderItems: Array<{
    id: string
    quantity: number
    _count: {
      cardItems: number
    }
  }>
} | null

export function epayValidateVirtualOnlyOrder(orderNumber: string) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        orderNumber,
        productOrderItems: {
          every: {
            // Condition 3: No errors on items
            OR: [{ error: null }, { error: '' }],
            // Condition 2: All cards must be valid
            cardItems: {
              none: {
                OR: [
                  { fundsLoaded: false },
                  {
                    AND: [{ error: { not: null } }, { error: { not: '' } }],
                  },
                ],
              },
            },
          },
        },
      },
      select: {
        id: true,
        orderNumber: true,
        productOrderItems: {
          select: {
            id: true,
            quantity: true,
            _count: {
              select: {
                cardItems: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.warn(`Failed to validate ProductOrder`, error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to validate order items and cards',
      } as SimpleError
    }
  ).map((result) => {
    if (!result) {
      return null
    }

    return result as ProductOrderValidationResult
  })
}

export function epayValidateOrderFundedInFull({
  orderId,
}: {
  orderId: string
}) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        id: orderId,
        orderType: OrderType.SINGLE_FUNDS_LOAD,
        FundingProductOrderItem: {
          some: {},
          every: {
            AND: [
              {
                OR: [{ error: null }, { error: '' }],
              },
              {
                fundsLoaded: true,
              },
            ],
          },
        },
      },
      select: {
        id: true,
        orderNumber: true,
        FundingProductOrderItem: {
          select: {
            id: true,
            lockCode: true,
            unitPriceInCents: true,
            externalCardReferenceNumber: true,
            product: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.warn(`Failed to validate ProductOrder`, error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to validate order items and cards',
      } as SimpleError
    }
  ).map((result) => {
    if (!result) {
      return null
    }

    return result
  })
}

type ProductOrderData = Prisma.ProductOrderGetPayload<{
  include: {
    user: true
    organization: true
    productOrderItems: {
      include: {
        product: {
          include: {
            design: true
            logo: true
          }
        }
      }
    }
    FundingProductOrderItem: {
      include: {
        product: {
          include: {
            design: true
            logo: true
          }
        }
      }
    }
  }
}>
export function epayGetProductOrder({
  orderId,
  orderNumber,
  windcaveSessionId,
}: {
  orderId?: string
  orderNumber?: string
  windcaveSessionId?: string
}) {
  const where = orderNumber
    ? { orderNumber }
    : windcaveSessionId
    ? { windcaveSessionId }
    : { id: orderId } // failsafe: will return no result if all args are missing

  return fromPromise(
    prisma.productOrder.findFirst({
      where,
      include: {
        user: true,
        organization: true,
        productOrderItems: {
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
        FundingProductOrderItem: {
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to get product order')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Product order with id [${orderNumber}] not found`,
      } as SimpleError)
    }

    return ok(productOrder as ProductOrderData)
  })
}

export function epayGetUnprocessedOrderByWindcaveSessionId({
  windcaveSessionId,
  amount,
}: {
  windcaveSessionId: string
  amount: number
}) {
  return epayGetProductOrderWithUnprocessedItems({
    orderStatus: {
      in: ['RELEASING'],
    },
    windcaveSessionId: windcaveSessionId,
    orderTotal: amount,
  }).andThen((productOrder) => {
    if (productOrder.productOrderItems.length === 0) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Order with windcave session [${windcaveSessionId}] has no unprocessed product order items.`,
      } as SimpleError)
    }

    logger.info(`Retrieved order by Windcave session: ${windcaveSessionId}`)
    return ok(productOrder as UnprocessedProductOrderData)
  })
}

export function epayGetUnprocessedOrder(orderId: string) {
  return epayGetProductOrderWithUnprocessedItems({
    orderStatus: {
      in: ['RELEASING'],
    },
    id: orderId,
  }).andThen((productOrder) => {
    if (productOrder.productOrderItems.length === 0) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Order with orderId [${orderId}], orderNumber [${productOrder.orderNumber}] has no unprocessed product order items.`,
      } as SimpleError)
    }

    return ok(productOrder as UnprocessedProductOrderData)
  })
}

function epayGetProductOrderWithUnprocessedItems(
  whereClause: Prisma.ProductOrderWhereInput
) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: whereClause,
      include: {
        user: true,
        organization: true,
        productOrderItems: {
          where: {
            OR: [{ externalBatchId: null }, { externalBatchId: '' }],
            //virtual cards don't have a batch id but have a card item id, this avoids cards being released twice
            AND: {
              cardItems: { none: {} },
            },
          },
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to get product order')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: 'Product order not found',
      } as SimpleError)
    }
    return ok(productOrder as UnprocessedProductOrderData)
  })
}

export enum ProductOrderContent {
  CONTAINS_PHYSICAL = 'CONTAINS_PHYSICAL',
  VIRTUAL_ONLY_ORDER = 'VIRTUAL_ONLY_ORDER',
  FUNDING_ONLY_ORDER = 'FUNDING_ONLY_ORDER',
}

export function epayGetProductOrderContentType(orderNumber: string) {
  return epayGetProductOrder({
    orderNumber,
  }).andThen((productOrder) => {
    return fromPromise(
      prisma.productOrder.count({
        where: {
          orderNumber: orderNumber,
          productOrderItems: {
            every: {
              deliveryMethod: DeliveryMethod.EMAIL,
            },
          },
        },
      }),
      (error) => {
        logger.warn(`Failed to validate ProductOrder email delivery`, error)
        return {
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Failed to validate order delivery method',
        } as SimpleError
      }
    ).map((count) => {
      if (productOrder.orderType === OrderType.SINGLE_FUNDS_LOAD) {
        return ProductOrderContent.FUNDING_ONLY_ORDER
      }

      if (count === 0) {
        return ProductOrderContent.CONTAINS_PHYSICAL
      }

      return ProductOrderContent.VIRTUAL_ONLY_ORDER
    })
  })
}

export type ProductOrderWithAdminRelations = Prisma.ProductOrderGetPayload<{
  include: {
    productOrderItems: {
      include: {
        product: true
      }
    }
    FundingProductOrderItem: {
      include: {
        product: true
      }
    }
    productOrderNotes: {
      include: {
        user: true
      }
    }
    organization: true
    user: true
  }
}>

export function epayFetchInvoiceDataForProductOrder(orderNumber: string) {
  return fromPromise(
    prisma.productOrder.findUnique({
      where: { orderNumber },
      include: {
        productOrderItems: {
          include: {
            product: true,
          },
        },
        FundingProductOrderItem: {
          include: {
            product: true,
          },
        },
        productOrderNotes: {
          orderBy: {
            updatedAt: 'desc',
          },
          include: {
            user: true,
          },
        },
        organization: true,
        user: true,
      },
    }),
    (error) => {
      logger.warn(`Error getting order [${orderNumber}] for invoice`, error)
      return {
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Error getting order [${orderNumber}] for invoice`,
      } as SimpleError
    }
  )
}

export function epyGetProductOrderListWithFilter({
  filterText,
  endDate,
  startDate,
  status,
  page,
  pageSize,
  useReleaseDate,
}: EpayOrdersListProps) {
  logger.info(`epayGetProductOrderListWithFilter: ${filterText}`)

  const dateField = useReleaseDate ? 'submittedAt' : 'createdAt'

  const where: Prisma.ProductOrderWhereInput = {
    OR: [
      {
        orderNumber: {
          contains: filterText,
          mode: 'insensitive',
        },
      },
      {
        organization: {
          name: {
            contains: filterText,
            mode: 'insensitive',
          },
        },
      },
    ],

    orderStatus: status ?? {
      notIn: [OrderStatus.CANCELLED, OrderStatus.PENDING],
    },

    [dateField]: {
      gte: startDate,
      lte: endDate,
    },
  }

  const query = prisma.productOrder.findMany({
    where,
    select: {
      id: true,
      orderTotal: true,
      orderNumber: true,
      orderStatus: true,
      createdAt: true,
      totalQuantity: true,
      orderType: true,
      organization: {
        select: {
          name: true,
        },
      },
    },
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
      [dateField]: 'desc',
    },
  })

  const count = prisma.productOrder.count({
    where,
  })

  return fromPromise(prisma.$transaction([count, query]), (error) => {
    logger.warn(
      `epyGetProductOrderListWithFilter: failed to query product orders with [${filterText}]`,
      error
    )
    return {
      code: errorCodes.db.ITEM_NOT_FOUND,
      message: `epyGetProductOrderListWithFilter: failed to query product orders with [${filterText}]`,
    } as SimpleError
  }).map(([count, productOrdersList]) => {
    const items = productOrdersList.map((productOrder) => {
      return {
        id: productOrder.id,
        orderNumber: productOrder.orderNumber,
        orderTotal: centsToDollars(productOrder.orderTotal),
        orderType: productOrder.orderType,
        orderStatus: productOrder.orderStatus,
        createdAt: productOrder.createdAt,
        organizationName: productOrder.organization?.name,
        totalQuantity: productOrder.totalQuantity,
      }
    })

    return { count, items }
  })
}

export type ProcessedCardsByOrderNumberData = Prisma.ProductOrderGetPayload<{
  select: {
    orderNumber: true
    id: true
    productOrderItems: {
      select: {
        loadingFee: true
        loadingFeeDiscount: true
        quantity: true
        digitalFee: true
        digitalFeeDiscount: true
        unitPrice: true
        cardItems: {
          select: {
            externalCardReferenceNumber: true
          }
        }
      }
    }
  }
}>

export function epayGetProcessedCardsByOrderNumber({
  orderNumber,
}: {
  orderNumber: string
}) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        orderNumber,
      },
      select: {
        orderNumber: true,
        id: true,
        productOrderItems: {
          select: {
            loadingFee: true,
            loadingFeeDiscount: true,
            quantity: true,
            digitalFee: true,
            digitalFeeDiscount: true,
            unitPrice: true,
            cardItems: {
              select: {
                externalCardReferenceNumber: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.warn(
        `Error getting processed cards for order [${orderNumber}]`,
        error
      )
      return {
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Error getting processed cards for order [${orderNumber}]`,
      } as SimpleError
    }
  ).andThen((result) => {
    if (!result) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Error getting processed cards for order [${orderNumber}]`,
      } as SimpleError)
    }
    return ok(result)
  })
}

export function epayGetUnprocessedScheduledOrder(orderId: string) {
  return epayGetProductOrderWithUnprocessedItems({
    orderStatus: {
      in: ['RELEASING'],
    },
    id: orderId,
    scheduledDate: {
      not: null,
    },
    productOrderItems: {
      every: {
        deliveryMethod: DeliveryMethod.EMAIL,
      },
    },
  }).andThen((productOrder) => {
    if (productOrder.productOrderItems.length === 0) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Order with orderId [${orderId}], orderNumber [${productOrder.orderNumber}] has no unprocessed product order items.`,
      } as SimpleError)
    }

    return ok(productOrder as UnprocessedProductOrderData)
  })
}
