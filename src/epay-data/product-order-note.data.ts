import { Prisma, ProductOrderNoteStatus } from '@prisma/client'
import { fromPromise } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import { prisma } from 'utils/prisma'

type CreateProductOrderNoteInput = {
  title?: string
  message: string
  status?: ProductOrderNoteStatus
  userId?: string
  productOrderId: string
}
export function epayAdminCreateProductOrderNote({
  title,
  message,
  status,
  userId,
  productOrderId,
}: CreateProductOrderNoteInput) {
  const data: Prisma.ProductOrderNoteCreateInput = {
    title,
    message,
    status,
    productOrder: {
      connect: { id: productOrderId },
    },
  }

  if (userId) {
    data.user = {
      connect: { id: userId },
    }
  }

  return fromPromise(
    prisma.productOrderNote.create({
      data,
    }),
    (error) =>
      ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to create a note for product product order note',
      } as SimpleError)
  ).map(() => {
    return
  })
}
