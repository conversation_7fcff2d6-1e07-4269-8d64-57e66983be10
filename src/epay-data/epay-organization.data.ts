import { Prisma } from '@prisma/client'
import { fromPromise } from 'neverthrow'

import { errorCodes, SimpleError } from 'types/simple-error'

import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function countOrganizationsWithSearch(
  where: Prisma.OrganizationWhereInput
) {
  return fromPromise(prisma.organization.count({ where }), (error) => {
    logger.error(error, 'countOrganizationsWithSearch... failed to count orgs')
    return {
      code: errorCodes.db.UNKNOWN_ERROR,
      message: 'Database operation failed',
    } as SimpleError
  })
}

export function searchOrganizations({
  where,
  pageSize,
  page,
}: {
  where: Prisma.OrganizationWhereInput
  pageSize: number
  page: number
}) {
  return fromPromise(
    prisma.organization.findMany({
      where,
      include: {
        users: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    }),
    (error) => {
      logger.error(error, 'searchOrganizations... failed to search orgs')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Database operation failed',
      } as SimpleError
    }
  )
}
