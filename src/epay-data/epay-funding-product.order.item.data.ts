import { OrderType, Prisma } from '@prisma/client'
import { err, fromPromise, ok, ResultAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

const UnprocessedFundingOrderSelect = {
  id: true,
  orderNumber: true,
  FundingProductOrderItem: {
    select: {
      id: true,
      lockCode: true,
      unitPriceInCents: true,
      externalCardReferenceNumber: true,
      productOrder: {
        select: {
          orderNumber: true,
          lockCode: true,
        },
      },
      product: {
        select: {
          id: true,
          name: true,
          productCode: true,
          design: {
            select: {
              externalCardDesignId: true,
              cardDirection: true,
              resolution: true,
              externalCardType: true,
              externalProductCode: true,
            },
          },
        },
      },
    },
  },
} as const

export type UnprocessedFundingOrderData = Prisma.ProductOrderGetPayload<{
  select: typeof UnprocessedFundingOrderSelect
}>

export function epayGetUnprocessedFundingOrder({
  orderId,
}: {
  orderId: string
}): ResultAsync<UnprocessedFundingOrderData | null, SimpleError> {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        id: orderId,
        orderStatus: 'RELEASING',
        orderType: OrderType.SINGLE_FUNDS_LOAD,
        FundingProductOrderItem: {
          some: {},
          every: {
            AND: [
              {
                OR: [{ error: null }, { error: '' }],
              },
              {
                fundsLoaded: false,
              },
            ],
          },
        },
      },
      select: UnprocessedFundingOrderSelect,
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `Failed to get unprocessed funding order 
        [${orderId}]`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to get unprocessed funding order',
        logRef,
      } as SimpleError
    }
  ).map((result) => {
    if (!result) {
      return null
    }

    return result as UnprocessedFundingOrderData
  })
}

export function epayUpdateFundingOrderItemLockCodeSet({
  fundingProductOrderItemId,
}: {
  fundingProductOrderItemId: string
}) {
  return fromPromise(
    prisma.fundingProductOrderItem.update({
      where: {
        id: fundingProductOrderItemId,
      },
      data: {
        lockCodeSet: true,
        lockCodeSetAt: new Date().toISOString(),
      },
    }),
    (error) => {
      const message = `epayUpdateFundingOrderItemLockCodeSet..Failed to update card LOCKCODESET for [${fundingProductOrderItemId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}

export function epayUpdateFundingProductOrderItemFundsLoaded({
  fundingProductOrderItemId,
}: {
  fundingProductOrderItemId: string
}) {
  return fromPromise(
    prisma.fundingProductOrderItem.update({
      where: {
        id: fundingProductOrderItemId,
      },
      data: {
        fundsLoaded: true,
      },
    }),
    (error) => {
      const message = `epayUpdateFundingProductOrderItemFundsLoaded..Failed to update card FUNDS_LOADED for [${fundingProductOrderItemId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}

export function epayUpdateFundingProductOrderItemError({
  id,
  errorString,
}: {
  id: string
  errorString: string | null
}) {
  return fromPromise(
    prisma.fundingProductOrderItem.update({
      where: { id },
      data: { error: errorString },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `epayUpdateFundingProductOrderItemError.. Failed to update error for ProductOrderItemCard with id ${id}`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `Failed to update error for ProductOrderItemCard with id ${id}`,
        logRef,
      } as SimpleError
    }
  )
}

export function epayGetProductOrderItemCardByCrn({ crn }: { crn: string }) {
  return fromPromise(
    prisma.productOrderItemCard.findFirst({
      where: { externalCardReferenceNumber: crn },
    }),
    (error) => {
      const message = `epayGetProductOrderItemCardByCrn..Failed to get crn [${crn}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  ).andThen((productOrderItemCard) => {
    if (!productOrderItemCard) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `epayGetProductOrderItemCardByCrn..ProductOrderItemCard not found with crn [${crn}]`,
      } as SimpleError)
    }

    return ok(productOrderItemCard)
  })
}

export function epayUpdateProductOrderItemCardLockCodeSet({
  productOrderItemCardId,
  crn,
  lockCode,
}: {
  productOrderItemCardId?: string
  crn?: string
  lockCode: string
}) {
  const whereClause = productOrderItemCardId
    ? { id: productOrderItemCardId }
    : { externalCardReferenceNumber: crn }

  return fromPromise(
    prisma.productOrderItemCard.update({
      where: whereClause,
      data: {
        lockCode,
      },
    }),
    (error) => {
      const message = `updateProductOrderItemCardFundsLoaded..Failed to update card FUNDS_LOADED for [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}
