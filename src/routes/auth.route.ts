import express from 'express'
import 'express-async-errors' // required in all router files

import { checkAuth0Jwt } from 'middlewares/check-auth0-jwt'
import { getUserForAuth0, signInSchema } from 'services/auth.service'
import logger from 'utils/logger'
import { validateInput } from 'utils/validation'

export const authRouter = express.Router()

authRouter.post('/signin', checkAuth0Jwt, async (req, res) => {
  logger.debug(`Logging in from auth0: ${JSON.stringify(req.body)}`)

  const input = await validateInput({
    schema: signInSchema,
    data: req.body,
  })

  if (input.isErr()) {
    logger.debug(`${JSON.stringify(input.error)}`)
    return res.status(400).json(input.error)
  }

  return getUserForAuth0(input.value).match(
    (user) => res.status(200).json(user),
    (error) => res.status(400).json(error)
  )
})
