import { UserRole } from '@prisma/client'
import { getOrganizationById } from 'data/organization.data'
import express from 'express'
import 'express-async-errors' // required in all router files
import { authorizeRole } from 'middlewares/authorize-role'
import { validate } from 'utils/validation'
import { z } from 'zod'

export const epayOrganizationRouter = express.Router()

epayOrganizationRouter.get(
  '/:orgId',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validate({
      schema: z.string(),
      data: req.params.orgId,
    })
      .andThen((id) => getOrganizationById(id))
      .match(
        (organization) => res.status(200).json(organization),
        (error) => res.status(400).json(error)
      )
  }
)
