import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { PaymentMethod, UserRole } from '@prisma/client'
import { authorizeRole } from 'middlewares/authorize-role'
import { getAllStockCardDesigns } from 'services/card.service'
import {
  createStockCardProductAndOrder,
  getCustomProductOrderPaymentSummary,
} from 'services/custom-product.service'
import {
  getStockCardOrderPaymentSummary,
  submitPaymentForStockCardOrder,
  tryCreateStockCardOrder,
} from 'services/product-order.service'
import {
  getAllStockProductsForOrg,
  getStockCardDetails,
} from 'services/product.service'
import {
  transformStringToNumber,
  validateWithErrorData,
} from 'utils/validation'
import { courierSchema } from './product.order.route'
import { fundingOrderItemListWithFilters } from 'data/funding-product-order-item.data'

export const stockRouter = express.Router()

stockRouter.get(
  '/card-design/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return getAllStockCardDesigns().match(
      (cardDesigns) => {
        return res.status(200).json(cardDesigns)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
  }
)

stockRouter.get('/product/list', async (req, res) => {
  return getAllStockProductsForOrg(req.orgId!).match(
    (products) => {
      return res.status(200).json(products)
    },
    (error) => {
      return res.status(400).json(error)
    }
  )
})

const productSchema = z.object({
  productCode: z.string().transform((productCode) => {
    const number = Number(productCode)

    if (!number) {
      throw new Error('Invalid number format')
    }

    return number
  }),
})

stockRouter.get(`/product/:productCode`, async (req, res) => {
  return validateWithErrorData({
    schema: productSchema,
    data: req.params,
  })
    .andThen((data) => {
      return getStockCardDetails({
        orgId: req.orgId!,
        productCode: data.productCode,
      })
    })
    .match(
      (product) => {
        return res.status(200).json(product)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
})

const ResolutionEnum = z.enum(['HIGH', 'LOW'])

// Create a schema for the individual stock card order item
const StockCardOrderItemSchema = z.object({
  productCode: z.number(),
  quantity: z.number().int().positive(),
  resolution: ResolutionEnum,
  deliveryMethod: courierSchema,
})

// Create a schema for the array of stock card order items
export const StockCardOrderSchema = z.array(StockCardOrderItemSchema)

// Export types
export type StockCardOrderItem = z.infer<typeof StockCardOrderItemSchema>
export type StockCardOrder = z.infer<typeof StockCardOrderSchema>

stockRouter.post(
  '/card-order/create',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: StockCardOrderSchema,
      data: req.body,
    })
      .andThen((data) => {
        return tryCreateStockCardOrder({
          orgId: req.orgId!,
          stockCardOrderItems: data,
          userId: req.userId!,
        })
      })
      .match(
        (stockCardOrder) => {
          return res.status(200).json(stockCardOrder)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

stockRouter.get(
  '/card-order/:stockCardOrderNumber/payment-summary',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        stockCardOrderNumber: z.string(),
      }),
      data: { stockCardOrderNumber: req.params.stockCardOrderNumber },
    })
      .andThen((data) => {
        return getStockCardOrderPaymentSummary({
          // orgId: req.orgId!,
          orderNumber: data.stockCardOrderNumber,
        })
      })
      .match(
        (paymentSummary) => {
          return res.status(200).json(paymentSummary)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

const submitStockCardOrderSchema = z.object({
  params: z.object({
    orderNumber: z.string(),
  }),
  body: z.object({
    paymentMethod: z.nativeEnum(PaymentMethod),
    billingAddress: z.string(),
    city: z.string(),
    country: z.string(),
    postcode: z.string(),
    purchaseOrderNumber: z.string().max(30).nullable().optional(),
    selectedDeliveryRecipients: z.array(
      z.object({
        id: z.string(),
        value: z.string(),
      })
    ),
    isLiveAgent: z.boolean().optional().default(false),
    authorizedByName: z.string().optional(),
    authorizedByEmail: z.string().optional(),
  }),
})

type SubmitStockCardOrderSchema = z.infer<typeof submitStockCardOrderSchema>
export type SubmitStockCardOrderProps = SubmitStockCardOrderSchema['params'] &
  SubmitStockCardOrderSchema['body']

stockRouter.put('/:orderNumber/submit', async (req, res) => {
  return validateWithErrorData({
    data: {
      params: req.params,
      body: req.body,
    },
    schema: submitStockCardOrderSchema,
  })
    .andThen((data) => {
      return submitPaymentForStockCardOrder({
        paymentMethod: data.body.paymentMethod,
        orderNumber: data.params.orderNumber,
        billingAddress: data.body.billingAddress,
        city: data.body.city,
        country: data.body.country,
        postcode: data.body.postcode,
        purchaseOrderNumber: data.body.purchaseOrderNumber,
        selectedDeliveryRecipients: data.body.selectedDeliveryRecipients,
        isLiveAgent: data.body.isLiveAgent,
        authorizedByName: data.body.authorizedByName,
        authorizedByEmail: data.body.authorizedByEmail,
      })
    })
    .match(
      (orderStatus) => {
        return res.status(200).json(orderStatus)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
})

const CreateStockCardProductSchema = z.object({
  designUrl: z.string(),
  logoUrl: z.string(),
  name: z.string(),
  logoFileName: z.string(),
  termsAndConditionsVersion: z.string(),
  deliveryAddress: z.object({
    recipientName: z.string().max(40),
    addressLine1: z.string().max(40),
    addressLine2: z.string().max(40).optional(),
    suburb: z.string().max(40),
    city: z.string().max(40),
    postcode: z.string().max(10),
  }),
})

export type CreateStockCardProductArgs = z.infer<
  typeof CreateStockCardProductSchema
>

stockRouter.post(
  '/product/create',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: CreateStockCardProductSchema,
      data: req.body,
    })
      .andThen((data: CreateStockCardProductArgs) => {
        return createStockCardProductAndOrder({
          orgId: req.orgId!,
          userId: req.userId!,
          ...data,
        })
      })
      .match(
        (customProductOrder) => {
          return res
            .status(200)
            .json({ orderNumber: customProductOrder.orderNumber })
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

const listCardsFilterSchema = z.object({
  filter: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

stockRouter.get('/cards/list', async (req, res) => {
  return validateWithErrorData({
    data: req.query,
    schema: listCardsFilterSchema,
  })
    .andThen((input) => {
      return fundingOrderItemListWithFilters({
        page: input.page,
        filter: input.filter,
        pageSize: input.pageSize,
        orgId: req.orgId!,
      })
    })
    .match(
      (cardsList) => {
        return res.status(200).json(cardsList)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
})
