import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { UserRole } from '@prisma/client'
import { authorizeRole } from 'middlewares/authorize-role'

import { error } from 'utils/error'
import { transformStringToNumber, validate } from 'utils/validation'

import {
  findCardByCrn,
  getAllCardDesigns,
  listCardsWithFilters,
} from 'services/card.service'

import { PRODUCTS } from 'utils/dummy-data'
import { blockManyCardsByCrnV2 } from 'services/product-order/block-cards.service'
import { activateCardsByCrns } from 'services/product-order/activate-cards.service'
import { manageCardsListCardsWithFiltersForOrg } from 'data/product-order-item-card.data'

export const cardRouter = express.Router()

const listCardsFilterSchema = z.object({
  filter: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

cardRouter.get(
  '/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    const input = await validate({
      data: req.query,
      schema: listCardsFilterSchema,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const cardsListWithFilter = await manageCardsListCardsWithFiltersForOrg({
      orgId: req.orgId,
      filter: input.value.filter,
      page: input.value.page,
      pageSize: input.value.pageSize,
    })

    if (cardsListWithFilter.isErr()) {
      return res.status(400).json(cardsListWithFilter.error)
    }

    return res.status(200).json(cardsListWithFilter.value)
  }
)

const getProductSchema = z.object({
  id: z.string(),
})

cardRouter.get(
  '/product/:id',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER']),
  async (req, res) => {
    return validate({ data: req.params, schema: getProductSchema }).match(
      ({ id }) => {
        return res.json(PRODUCTS.find((p) => p.productCode === id) ?? null)
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

const activateAndBlockSchema = z.object({
  cardReferenceIds: z.array(z.string()),
})

cardRouter.post(
  '/activate',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER']),
  async (req, res) => {
    return validate({
      schema: activateAndBlockSchema,
      data: req.body,
    })
      .andThen((data) => {
        return activateCardsByCrns({
          crns: data.cardReferenceIds,
          orgId: req.orgId,
        })
      })
      .match(
        (data) => {
          return res.json({ activated: data.totalActivated })
        },
        (e) => {
          return res.status(400).json(e)
        }
      )
  }
)

cardRouter.post(
  '/block',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER']),
  async (req, res) => {
    return validate({
      schema: activateAndBlockSchema,
      data: req.body,
    })
      .andThen((data) => {
        return blockManyCardsByCrnV2({
          crns: data.cardReferenceIds,
          orgId: req.orgId!,
        })
      })
      .match(
        (data) => {
          return res.json({ blocked: data.length })
        },
        (e) => {
          return res.status(400).json(e)
        }
      )
  }
)

cardRouter.get(
  '/design/list',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER', 'ORG_BASIC']),
  (req, res) => {
    return getAllCardDesigns().match(
      (cardDesigns) => {
        return res.json(cardDesigns)
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

cardRouter.get(
  '/crn/:id',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER', 'ORG_BASIC']),
  async (req, res) => {
    return validate({
      data: req.params,
      schema: z.object({ id: z.string() }),
    })
      .andThen((data) => findCardByCrn(data.id))
      .match(
        (data) => res.status(200).json(data),
        (e) => res.status(400).json(e)
      )
  }
)
