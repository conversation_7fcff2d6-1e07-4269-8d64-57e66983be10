import express from 'express'
import 'express-async-errors' // required in all router files

import {
  FloatFundsTransactionType,
  PaymentMethod,
  UserRole,
} from '@prisma/client'
import { authorizeRole } from 'middlewares/authorize-role'
import {
  transformStringToNumber,
  validateWithErrorData,
} from 'utils/validation'
import { z } from 'zod'
import {
  getTransactions,
  getBalance,
  createTransaction,
  createOrder,
} from 'services/float-funds.service'

export const floatFundsRouter = express.Router()

const FloatFundListSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
  transactionType: z.nativeEnum(FloatFundsTransactionType).optional(),
  orderNumber: z.string().optional(),
})

export type FloatFundListArgs = z.infer<typeof FloatFundListSchema>

const CreateFloatFundOrderSchema = z.object({
  orgId: z.string(),
  userId: z.string(),
  amount: z.number(),
  billingAddress: z.string(),
  city: z.string(),
  country: z.string(),
  postCode: z.string(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  purchaseOrderNumber: z.string().optional(),
})

export type CreateFloatFundOrderSchemaArgs = z.infer<
  typeof CreateFloatFundOrderSchema
>

floatFundsRouter.get(
  '/transactions/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    return validateWithErrorData({
      schema: FloatFundListSchema,
      data: req.query,
    })
      .andThen((data) => {
        return getTransactions({
          requstedByUserId: req.userId!,
          orgId: req.orgId!,
          startDate: data.startDate ? new Date(data.startDate) : undefined,
          endDate: data.endDate ? new Date(data.endDate) : undefined,
          transactionType: data.transactionType,
          page: data.page,
          pageSize: data.pageSize,
          orderNumber: data.orderNumber,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

const FloatFundsTransactionSchema = z.object({
  amount: z.number().int(),
  transactionType: z.enum([
    FloatFundsTransactionType.DEBIT,
    FloatFundsTransactionType.CREDIT,
  ]),
  orderId: z.string().optional(),
  orderNumber: z.string().optional(),
  description: z.string(),
  note: z.string().optional(),
})

export type FloatFundTransactionArgs = z.infer<
  typeof FloatFundsTransactionSchema
>

floatFundsRouter.post(
  '/transactions/create',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    return validateWithErrorData({
      schema: FloatFundsTransactionSchema,
      data: req.body,
    })
      .andThen((data: FloatFundTransactionArgs) => {
        return createTransaction({
          orgId: req.orgId!,
          amount: data.amount,
          transactionType: data.transactionType,
          createdByUserId: req.userId!,
          orderNumber: data.orderNumber,
          orderId: data.orderId,
          description: data.description,
          note: data.note,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

floatFundsRouter.get(
  '/balance',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    return getBalance({
      requstedByUserId: req.userId!,
      orgId: req.orgId!,
    }).match(
      (result) => {
        return res.status(200).json(result)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
  }
)

floatFundsRouter.post(
  '/order',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    return validateWithErrorData({
      schema: CreateFloatFundOrderSchema,
      data: {
        ...req.body,
        userId: req.userId,
        orgId: req.orgId,
      },
    })
      .andThen((data) => createOrder(data))
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)
