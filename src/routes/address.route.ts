import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'
import { authorizeRole } from 'middlewares/authorize-role'
import {
  createAddress,
  deleteAddressById,
  getAddressesByUserId,
  updateAddressById,
} from 'services/address.service'
import { transformStringToNumber, validateInput } from 'utils/validation'
import { UserRole } from '@prisma/client'

export const addressRouter = express.Router()

const createAddressSchema = z.object({
  address: z.string(),
  addressLine2: z.string(),
  city: z.string(),
  suburb: z.string(),
  postCode: z.string(),
  userId: z.string(),
  isDefault: z.boolean(),
  country: z
    .string()
    .nullable()
    .optional()
    .transform((value) => value || null),
})

const updateAddressSchema = createAddressSchema.extend({
  id: z.string(),
})

const listSchema = z.object({
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
  query: z.string().optional(),
  userId: z.string(),
})

addressRouter.get(
  '/user',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateInput({
      schema: listSchema,
      data: {
        ...req.query,
        userId: req.userId,
      },
    })
      .andThen((data) => getAddressesByUserId(data))
      .match(
        (data) => res.status(200).json(data),
        (error) => res.status(400).json(error)
      )
  }
)

addressRouter.post(
  '/',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateInput({
      schema: createAddressSchema,
      data: {
        ...req.body,
        userId: req.userId,
      },
    })
      .andThen((address) => createAddress(address))
      .match(
        (data) => res.status(200).json({ data }),
        (error) => res.status(400).json(error)
      )
  }
)

addressRouter.delete(
  '/:id',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateInput({
      schema: z.string(),
      data: req.params.id,
    })
      .andThen((id) => deleteAddressById(id))
      .match(
        (address) => res.status(200).json({ data: address.id }),
        (error) => res.status(400).json(error)
      )
  }
)

addressRouter.put(
  '/:id',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateInput({
      schema: updateAddressSchema,
      data: {
        ...req.body,
        userId: req.userId,
      },
    })
      .andThen((address) => updateAddressById(address))
      .match(
        (data) => res.status(200).json({ data }),
        (error) => res.status(400).json(error)
      )
  }
)
