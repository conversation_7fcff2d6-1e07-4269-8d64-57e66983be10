import 'express-async-errors' // required in all router files
import express from 'express'
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'

import { error } from 'utils/error'

import {
  addCPartnerInfoToOrg,
  getCPartnerInfo,
} from 'services/organization.service'
import { validate } from 'utils/validation'
import { cPartnerCreateProduct } from 'services/product.service'

export const organizationCPartnerRouter = express.Router()

organizationCPartnerRouter.get(
  '/',
  authorizeRole(['ORG_ADMIN']),
  (req, res) => {
    return getCPartnerInfo({
      orgId: req.orgId!,
    }).match(
      (data) => {
        return res.json(data)
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

const addSchema = z.object({
  userId: z.string(),
  email: z.string(),
})

organizationCPartnerRouter.post(
  '/add',
  authorizeRole(['ORG_ADMIN']),
  (req, res) => {
    return validate({ schema: addSchema, data: req.body })
      .andThen((data) => {
        return addCPartnerInfoToOrg({
          orgId: req.orgId!,
          email: data.email,
          userId: data.userId,
        })
      })
      .match(
        (data) => {
          return res.json(data)
        },
        (e) => {
          return error({ res, errorType: e })
        }
      )
  }
)

const createCardSchema = z.object({
  cPartnerLogoId: z.string(),
  productName: z.string(),
  designId: z.string(),
})

organizationCPartnerRouter.post(
  '/create-card',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const input = await validate({ schema: createCardSchema, data: req.body })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const result = await cPartnerCreateProduct({
      orgId: req.orgId!,
      cPartnerLogoId: input.value.cPartnerLogoId,
      designId: input.value.designId,
      productName: input.value.productName,
    })

    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    return res.json({
      created: true,
    })
  }
)
