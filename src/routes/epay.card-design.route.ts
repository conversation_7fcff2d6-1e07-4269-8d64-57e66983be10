import express from 'express'
import 'express-async-errors' // required in all router files
import { authorizeRole } from 'middlewares/authorize-role'
import { ADMIN_ROLES } from 'constants/admin'
import {
  getCardDesignList,
  updateCardDesignAvailability,
} from 'data/card-design.data'
import { validateWithErrorData } from 'utils/validation'
import { z } from 'zod'

export const epayCardDesignRouter = express.Router()

epayCardDesignRouter.get(
  '/list',
  authorizeRole(ADMIN_ROLES),
  async (_, res) => {
    return getCardDesignList().match(
      (cardDesigns) => res.json({ cardDesigns }),
      (err) => res.status(400).json(err)
    )
  }
)

epayCardDesignRouter.put(
  '/available',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        id: z.string(),
        available: z.boolean(),
      }),
      data: req.body,
    })
      .andThen((data) => updateCardDesignAvailability(data.id, data.available))
      .match(
        (updated) => res.json({ updated }),
        (err) => res.status(400).json(err)
      )
  }
)
