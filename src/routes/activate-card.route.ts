import 'express-async-errors' // required in all router files
import express from 'express'

import { z } from 'zod'

import { error } from 'utils/error'

export const activateCardRoute = express.Router()

const validationSchema = z.object({
  reference: z.string(),
  code: z.string(),
  disableMockKey: z.string().optional(),
})

export type ActivateCardProps = z.infer<typeof validationSchema>

// This was a hold over from when we issued our own lock codes
// This is now handled by the prezzy card website
// To implment this we wold need to limit the attempts to activate a card
// and call the ren endpoint to activate
activateCardRoute.post('/', async (req, res) => {
  const validation = validationSchema.safeParse(req.body)

  if (!validation.success) {
    return error({ res, errorType: 'BAD_INPUT' })
  }

  return res
    .status(400)
    .json({ isUnblocked: false, message: 'not implemented' })
})
