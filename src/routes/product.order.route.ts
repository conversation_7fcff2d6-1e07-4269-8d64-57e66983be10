import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'

import { error } from 'utils/error'
import {
  transformStringToNumber,
  validate,
  validateInput,
  validateWithErrorData,
} from 'utils/validation'
import {
  cancelProductOrder,
  getInvoice,
  getPaymentSummary,
  getShippingDetails,
  submitProductOrder,
  verifyCsvProductOrder,
} from 'services/product-order.service'
import { validateEmail } from 'utils/email'
import { OrderStatus, PaymentMethod, UserRole } from '@prisma/client'
import { activateOrder } from 'services/product-order/activate-cards.service'
import { createEmptyProductOrder } from 'data/product-order.data'
import {
  calculateAndCompleteProductOrder,
  createProductOrderItemsFromOrderItems,
} from 'services/product-order/checkout-order.service'
import { searchOrdersForOrg } from 'services/product-order/order-history.service'

export const productOrderRouter = express.Router()

export const courierSchema = z.object({
  type: z.literal('COURIER'),
  address: z.string().min(1).max(40),
  addressLine2: z.string().max(40).optional(),
  suburb: z.string().max(40).optional(),
  city: z.string().min(1),
  postcode: z.string().min(1),
})
export type CourierDelivery = z.infer<typeof courierSchema>

const emailSchema = z.object({
  type: z.literal('EMAIL'),
  email: z.string().refine(
    (email) => {
      return validateEmail(email)
    },
    {
      message: 'invlid email',
    }
  ),
})
export type EmailDelivery = z.infer<typeof emailSchema>

// const smsSchema = z.object({
//   type: z.literal('SMS'),
//   mobile: z.string(),
// })

const pickupDeliverySchema = z.object({
  type: z.literal('PICKUP'),
})

export const deliverySchema = z.union([
  courierSchema,
  emailSchema,
  //   smsSchema,
  pickupDeliverySchema,
])

const createSchema = z.array(
  z.object({
    productCode: z.number(),
    recipientName: z.string(),
    quantity: z.number().positive(),
    value: z.number().positive(),
    deliveryMethod: deliverySchema,
    message: z.string().optional(),
    customerReference: z.string().optional().nullable(),
    lineNumber: z.number().optional(),
    recipientEmail: z
      .string()
      .refine(
        (recipientEmail) => {
          if (!recipientEmail) {
            return true
          }
          return validateEmail(recipientEmail)
        },
        {
          message: 'invalid email',
        }
      )
      .optional(),
  })
)

export type CreateProductOrderProps = z.infer<typeof createSchema>

productOrderRouter.post(
  '/create-empty-order',
  authorizeRole([UserRole.ORG_BASIC, UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
        userId: z.string(),
      }),
      data: {
        orgId: req.orgId,
        userId: req.userId,
      },
    })
      .andThen(createEmptyProductOrder)
      .match(
        (data) => res.json(data),
        (e) => res.status(400).json(e)
      )
  }
)

productOrderRouter.post(
  '/create-product-order-items',
  authorizeRole([UserRole.ORG_BASIC, UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orderItems: createSchema,
        productOrderId: z.string(),
        orgId: z.string(),
      }),
      data: {
        ...req.body,
        orgId: req.orgId,
      },
    })
      .andThen(createProductOrderItemsFromOrderItems)
      .match(
        (data) => res.json(data),
        (e) => res.status(400).json(e)
      )
  }
)

productOrderRouter.post(
  '/complete-order',
  authorizeRole([UserRole.ORG_BASIC, UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
        productOrderId: z.string(),
      }),
      data: {
        ...req.body,
        orgId: req.orgId,
      },
    })
      .andThen(calculateAndCompleteProductOrder)
      .match(
        (data) => res.json({ id: data.id, orderNumber: data.orderNumber }),
        (e) => res.status(400).json(e)
      )
  }
)

productOrderRouter.get(
  '/:orderNumber/payment-summary',
  authorizeRole([UserRole.ORG_BASIC, UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    const input = await validateInput({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: req.params,
    })

    if (input.isErr()) {
      return res.status(400).send(input.error)
    }

    const paymentSummaryResult = await getPaymentSummary({
      orderNumber: input.value.orderNumber,
      orgId: req.orgId!,
    })

    if (paymentSummaryResult.isErr()) {
      return res.status(400).send(paymentSummaryResult.error)
    }

    return res.status(200).json(paymentSummaryResult.value)
  }
)

const submitSchema = z.object({
  params: z.object({
    orderNumber: z.string(),
  }),
  body: z.object({
    paymentMethod: z.nativeEnum(PaymentMethod),
    billingAddress: z.string(),
    city: z.string(),
    country: z.string(),
    postcode: z.string(),
    purchaseOrderNumber: z.string().max(30).nullable().optional(),
    secureDelivery: z.boolean().optional().default(false),
    lockCode: z
      .string()
      .refine(
        (data) => {
          if (!data) {
            return true
          }
          return !isNaN(Number(data)) && data.length > 3
        },
        {
          message:
            'Lock code must be a numeric value and at least 4 digits long',
        }
      )
      .optional(),
    selectedDeliveryRecipients: z
      .array(
        z.object({
          id: z.string(),
          value: z.string(),
        })
      )
      .optional(),
    isLiveAgent: z.boolean().optional().default(false),
    authorizedByName: z.string().optional(),
    authorizedByEmail: z.string().optional(),
    scheduledDate: z.string().optional(),
  }),
})

type SubmitSchema = z.infer<typeof submitSchema>
export type SubmitProductOrderProps = SubmitSchema['params'] &
  SubmitSchema['body']

productOrderRouter.post(
  '/:orderNumber/submit',
  authorizeRole([UserRole.ORG_BASIC, UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: submitSchema,
      data: req,
    })

    if (input.isErr()) {
      return res.status(400).send(input.error)
    }

    const sumbitOrder = await submitProductOrder({
      orgId: req.orgId!,
      orderNumber: input.value.params.orderNumber,
      billingAddress: input.value.body.billingAddress,
      city: input.value.body.city,
      country: input.value.body.country,
      paymentMethod: input.value.body.paymentMethod,
      postcode: input.value.body.postcode,
      purchaseOrderNumber: input.value.body.purchaseOrderNumber,
      secureDelivery: input.value.body.secureDelivery,
      lockCode: input.value.body.lockCode,
      selectedDeliveryRecipients: input.value.body.selectedDeliveryRecipients,
      isLiveAgent: input.value.body.isLiveAgent,
      authorizedByName: input.value.body.authorizedByName,
      authorizedByEmail: input.value.body.authorizedByEmail,
      scheduledDate: input.value.body.scheduledDate,
    })

    if (sumbitOrder.isErr()) {
      return res.status(400).send(sumbitOrder.error)
    }

    return res.status(200).json(sumbitOrder.value)
  }
)

const listSchema = z.object({
  orderNumber: z.string().optional(),
  status: z.nativeEnum(OrderStatus).optional(),
  endDate: z.coerce.date().optional(),
  startDate: z.coerce.date().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

export type ListProps = z.infer<typeof listSchema>

productOrderRouter.get(
  '/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validate({ schema: listSchema, data: req.query })
      .andThen((data) => {
        return searchOrdersForOrg({
          orgId: req.orgId!,
          status: data.status,
          orderNumber: data.orderNumber,
          endDate: data.endDate,
          startDate: data.startDate,
          page: data.page,
          pageSize: data.pageSize,
          userRole: req.role!,
          userId: req.userId!,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

productOrderRouter.get(
  '/:orderNumber/invoice',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER, UserRole.ORG_BASIC]),
  async (req, res) => {
    const validationSchema = z.object({
      orderNumber: z.string(),
    })

    const validation = validationSchema.safeParse(req.params)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    if (!req.orgId) {
      return error({ res, errorType: 'UNAUTHORIZED' })
    }

    return getInvoice(validation.data.orderNumber, req.orgId).match(
      (invoice) => {
        return res.json({ invoice })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

productOrderRouter.get(
  '/:orderNumber/shipping/list',
  authorizeRole([
    UserRole.EPAY_ACCOUNTS,
    UserRole.ORG_ADMIN,
    UserRole.ORG_MANAGER,
    UserRole.ORG_BASIC,
  ]),
  async (req, res) => {
    return validate({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: req.params,
    })
      .andThen((data) => {
        return getShippingDetails(data.orderNumber, req.orgId!)
      })
      .match(
        (data) => res.json(data),
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

productOrderRouter.post(
  '/:orderNumber/activate',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validate({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: req.params,
    })
      .andThen((data) => {
        return activateOrder({
          orderNumber: data.orderNumber,
          orgId: req.orgId!,
        })
      })
      .match(
        (data) => {
          return res.status(200).json(data)
        },
        (e) => {
          return res.status(400).json(e)
        }
      )
  }
)
const recipientSettingsSchema = z.union([
  z.literal('NO_RECIPIENT'),
  z.literal('HAS_RECIPIENT'),
])

const verifySchema = z.object({
  recipientSettings: recipientSettingsSchema,
  items: z.array(
    z.object({
      productCode: z.number().optional(),
      recipientName: z.string().optional(),
      quantity: z.number().optional(),
      storeValue: z.number().optional(),
      deliveryMethod: z.string().optional(),
      address: z.string().optional(),
      addressLine2: z.string().optional(),
      suburb: z.string().optional(),
      city: z.string().optional(),
      postcode: z.string().optional(),
      email: z.string().optional(),
      message: z.string().optional(),
      customerReference: z.string().optional(),
      rowNumber: z.number(),
    })
  ),
})

export type VerifyProps = z.infer<typeof verifySchema>

productOrderRouter.post(
  '/verify',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER, UserRole.ORG_BASIC]),
  (req, res) => {
    return validate({ schema: verifySchema, data: req.body })
      .andThen((data) => {
        return verifyCsvProductOrder({
          recipientSettings: data.recipientSettings,
          items: data.items,
          orgId: req.orgId!,
        })
      })
      .match(
        (data) => {
          return res.json(data)
        },
        (e) => {
          return error({ res, errorType: e })
        }
      )
  }
)

productOrderRouter.post(
  '/:orderNumber/cancel',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER, UserRole.ORG_BASIC]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: req.params,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const cancelOrderResult = await cancelProductOrder({
      orgId: req.orgId!,
      orderNumber: input.value.orderNumber,
    })

    if (cancelOrderResult.isErr()) {
      return res.status(400).json(cancelOrderResult.error)
    }

    return res.status(200).json(cancelOrderResult.value)
  }
)
