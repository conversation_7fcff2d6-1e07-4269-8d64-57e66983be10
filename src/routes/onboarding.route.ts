import express from 'express'
import 'express-async-errors'

import { authorizeRole } from 'middlewares/authorize-role'

import { onboard } from 'services/onboarding.service'

export const onboardingRouter = express.Router()

onboardingRouter.post(
  '/start',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const result = await onboard({ userId: req.userId! })

    if (result.isErr()) {
      return res.status(400).send(result.error)
    }

    return res.status(200).json(result.value)
  }
)
