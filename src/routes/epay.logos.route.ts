import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'
import {
  getAllCustomProductOrdersForReview,
  getCustomProductOrderForApproval,
} from 'services/epay.service'
import {
  declineCustomProduct,
  updateProductLogoUrl,
} from 'services/product.service'

import { sendCustomProductToPlacard } from 'services/custom-product.service'

import { ProductStatus, UserRole } from '@prisma/client'
import { error, ErrorResponse } from 'utils/error'
import logger from 'utils/logger'
import { validate, validateWithErrorData } from 'utils/validation'
import { epayLogosListSchema } from 'types/epay.logos'

export const epayLogosRouter = express.Router()

epayLogosRouter.get(
  '/list',
  authorizeRole(['EPAY_ADMIN', 'EPAY_COSTUMER_SERVICE']),
  async (req, res) => {
    return validateWithErrorData({
      schema: epayLogosListSchema,
      data: req.query,
    })
      .andThen((input) => getAllCustomProductOrdersForReview(input))
      .match(
        (orders) => {
          return res.status(200).json(orders)
        },
        (err: ErrorResponse) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayLogosRouter.get(
  '/:orderNumber',
  authorizeRole([UserRole.EPAY_COSTUMER_SERVICE, UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({ orderNumber: z.string() }),
      data: req.params,
    })
      .andThen((data) => {
        return getCustomProductOrderForApproval({
          orderNumber: data.orderNumber,
        })
      })
      .match(
        (order) => {
          return res.status(200).json(order)
        },
        (err: ErrorResponse) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayLogosRouter.post(
  '/approve',
  authorizeRole(['EPAY_ADMIN', 'EPAY_COSTUMER_SERVICE']),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orderNumber: z.string(),
        productId: z.string(),
      }),
      data: req.body,
    })
      .andThen((data) => {
        return sendCustomProductToPlacard({
          orderNumber: data.orderNumber,
          productId: data.productId,
        })
      })
      .match(
        () => {
          return res.status(200).json({ status: ProductStatus.SENT })
        },
        (err: ErrorResponse) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayLogosRouter.post(
  '/decline',
  authorizeRole(['EPAY_ADMIN', 'EPAY_COSTUMER_SERVICE']),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orderNumber: z.string(),
        productId: z.string(),
      }),
      data: req.body,
    })
      .andThen((data) => {
        return declineCustomProduct({
          orderNumber: data.orderNumber,
          productId: data.productId,
        })
      })
      .match(
        () => {
          return res.json({ status: 'DECLINED' })
        },
        (err: ErrorResponse) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayLogosRouter.post(
  '/update',
  authorizeRole(['EPAY_ADMIN', 'EPAY_COSTUMER_SERVICE']),
  async (req, res) => {
    return validate({
      schema: z.object({
        productId: z.string(),
        logoUrl: z.string(),
        fileName: z.string(),
      }),
      data: req.body,
    })
      .andThen((data) => {
        return updateProductLogoUrl({
          productId: data.productId,
          logoUrl: data.logoUrl,
          fileName: data.fileName,
        })
      })
      .match(
        (order) => {
          logger.debug('logoUrl', order.logoId)
          return res.status(200).json({ logoUrl: order.logoId })
        },
        (err) => {
          return error({ res, errorType: err })
        }
      )
  }
)
