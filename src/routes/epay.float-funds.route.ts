import express from 'express'
import 'express-async-errors'

import { FloatFundsTransactionType, UserRole } from '@prisma/client'
import { authorizeRole } from 'middlewares/authorize-role'
import {
  transformStringToNumber,
  validateWithErrorData,
} from 'utils/validation'
import { z } from 'zod'
import {
  getTransactions,
  getBalance,
  createTransaction,
  activateFloatFundsForOrganization,
  deactivateFloatFundsForOrganization,
  getTransactionById,
} from 'services/float-funds.service'

export const epayFloatFundsRouter = express.Router()

const FloatFundListSchema = z.object({
  orgId: z.string(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 50 }),
  transactionType: z.nativeEnum(FloatFundsTransactionType).optional(),
  orderNumber: z.string().optional(),
})

epayFloatFundsRouter.get(
  '/:orgId/transactions/list',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: FloatFundListSchema,
      data: {
        ...req.query,
        orgId: req.params.orgId,
      },
    })
      .andThen((data) => {
        return getTransactions({
          requstedByUserId: req.userId!,
          orgId: data.orgId,
          startDate: data.startDate ? new Date(data.startDate) : undefined,
          endDate: data.endDate ? new Date(data.endDate) : undefined,
          transactionType: data.transactionType,
          page: data.page,
          pageSize: data.pageSize,
          orderNumber: data.orderNumber,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

epayFloatFundsRouter.get(
  '/:orgId/transactions/:transactionId',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
        transactionId: z.coerce.number(),
        userId: z.string(),
      }),
      data: {
        orgId: req.params.orgId,
        transactionId: req.params.transactionId,
        userId: req.userId!,
      },
    })
      .andThen((data) => {
        return getTransactionById({
          orgId: data.orgId,
          transactionId: data.transactionId,
          userId: data.userId,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

const FloatFundsTransactionSchema = z.object({
  orgId: z.string(),
  amount: z.number().int(),
  transactionType: z.enum([
    FloatFundsTransactionType.DEBIT,
    FloatFundsTransactionType.CREDIT,
  ]),
  orderId: z.string().optional(),
  orderNumber: z.string().optional(),
  description: z.string(),
  note: z.string().optional(),
  bankAccountName: z.string().optional(),
  bankAccountNumber: z.string().optional(),
})

epayFloatFundsRouter.post(
  '/:orgId/transactions/create',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: FloatFundsTransactionSchema,
      data: {
        ...req.body,
        orgId: req.params.orgId,
      },
    })
      .andThen((data) => {
        return createTransaction({
          orgId: data.orgId,
          amount: data.amount,
          transactionType: data.transactionType,
          createdByUserId: req.userId!,
          orderNumber: data.orderNumber,
          orderId: data.orderId,
          description: data.description,
          note: data.note,
          bankAccountName: data.bankAccountName,
          bankAccountNumber: data.bankAccountNumber,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

epayFloatFundsRouter.get(
  '/:orgId/balance',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
      }),
      data: {
        orgId: req.params.orgId,
      },
    })
      .andThen((data) => {
        return getBalance({
          requstedByUserId: req.userId!,
          orgId: data.orgId,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

epayFloatFundsRouter.post(
  '/:orgId/activate',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
      }),
      data: {
        orgId: req.params.orgId,
      },
    })
      .andThen((data) => {
        return activateFloatFundsForOrganization({
          orgId: data.orgId,
          updatedByUserId: req.userId!,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

epayFloatFundsRouter.post(
  '/:orgId/deactivate',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
      }),
      data: {
        orgId: req.params.orgId,
      },
    })
      .andThen((data) => {
        return deactivateFloatFundsForOrganization({
          orgId: data.orgId,
          updatedByUserId: req.userId!,
        })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)
