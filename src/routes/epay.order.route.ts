import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'

import { OrderStatus, OrderType, ProductOrderNoteStatus } from '@prisma/client'
import { ADMIN_ROLES } from 'constants/admin'
import { epyGetProductOrderListWithFilter } from 'epay-data/epay-product-order.data'
import { createProductOrderNote } from 'services/product-order-note.service'
import {
  cancelOrderWithNote,
  holdOrderWithNote,
  resumeOrder,
} from 'services/product-order.service'
import { epayOrdersListSchema } from 'types/epay.order'
import logger from 'utils/logger'

import { validate } from 'utils/validation'
import { releaseProductOrder } from 'services/product-order/order-release.service'

export const epayOrderRouter = express.Router()

epayOrderRouter.get(
  '/list',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validate({
      schema: epayOrdersListSchema,
      data: req.query,
    })
      .andThen((data) => {
        return epyGetProductOrderListWithFilter(data)
      })
      .match(
        (result) => {
          return res.json(result)
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

const releaseOrderStatusArgsSchema = z.object({
  orderId: z.string(),
  paymentDate: z.string(),
  disableMockKey: z.string().optional(),
})

export type ReleaseOrderStatusArgs = z.infer<
  typeof releaseOrderStatusArgsSchema
>

epayOrderRouter.post(
  '/:id/release',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validate({
      schema: releaseOrderStatusArgsSchema,
      data: {
        ...req.body,
        orderId: req.params.id,
      },
    })
      .andThen((data) => {
        return releaseProductOrder({
          orderId: data.orderId,
          paymentDate: data.paymentDate,
          userId: req.userId!,
          isWindcavePayment: false,
        })
      })
      .match(
        (result) => res.status(200).json(result),
        (error) => res.status(400).json(error)
      )
  }
)

const orderHoldCancelSchema = z.object({
  orderId: z.string(),
  noteTitle: z.string().optional(),
  noteMessage: z.string(),
})

epayOrderRouter.post(
  '/:id/hold',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validate({
      schema: orderHoldCancelSchema,
      data: {
        ...req.body,
        orderId: req.params.id,
      },
    })
      .andThen((data) => holdOrderWithNote({ ...data, userId: req.userId! }))
      .match(
        (order) => {
          logger.info('Order put on hold', order)
          return res.status(200).json(order)
        },
        (err) => res.status(400).json(err)
      )
  }
)

epayOrderRouter.post(
  '/:id/resume',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validate({
      schema: z.string(),
      data: req.params.id,
    })
      .andThen((data) => resumeOrder(data))
      .match(
        (order) => {
          logger.info('Order put on hold', order)
          return res.status(200).json(order)
        },
        (err) => res.status(400).json(err)
      )
  }
)

epayOrderRouter.post(
  '/:id/cancel',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validate({
      schema: orderHoldCancelSchema,
      data: {
        ...req.body,
        orderId: req.params.id,
      },
    })
      .andThen((data) => cancelOrderWithNote({ ...data, userId: req.userId! }))
      .match(
        (order) => {
          logger.info('Order cancelled', order)
          return res.status(200).json(order)
        },
        (err) => res.status(400).json(err)
      )
  }
)

epayOrderRouter.post(
  '/:id/note',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validate({
      schema: z.object({
        productOrderId: z.string(),
        message: z.string(),
        title: z.string().optional(),
        status: z
          .enum([
            ProductOrderNoteStatus.CANCELLED,
            ProductOrderNoteStatus.GENERAL,
            ProductOrderNoteStatus.ON_HOLD,
          ])
          .optional(),
      }),
      data: {
        ...req.body,
        productOrderId: req.params.id,
      },
    })
      .andThen((data) => {
        return createProductOrderNote({ ...data, userId: req.userId! })
      })
      .match(
        (note) => {
          logger.info(`Note created for ${note.productOrderId}`, note)
          return res.status(200).json(note)
        },
        (err) => res.status(400).json(err)
      )
  }
)
