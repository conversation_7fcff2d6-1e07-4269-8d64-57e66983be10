import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'
import {
  getAllCustomProductOrders,
  getCustomProductOrderInvoice,
  getInvoiceForAdmin,
  getShippingDetailsForAdmin,
  releaseCustomOrder,
} from 'services/admin-organization.service'

import { validate, validateWithErrorData } from 'utils/validation'
import { UserRole } from '@prisma/client'
import logger from 'utils/logger'
import { epayOrdersListSchema } from 'types/epay.order'

export const epayAccountsRouter = express.Router()

epayAccountsRouter.get(
  '/:orderNumber/invoice',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validate({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: req.params,
    })
      .andThen((data) => {
        return getInvoiceForAdmin(data.orderNumber)
      })
      .match(
        (invoice) => {
          return res.status(200).json({ invoice })
        },
        (err) => {
          return res.status(400).json({ err })
        }
      )
  }
)

epayAccountsRouter.get(
  '/:orderNumber/shipping/list',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validate({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: req.params,
    })
      .andThen((data) => {
        return getShippingDetailsForAdmin(data.orderNumber)
      })
      .match(
        (shippingDetails) => {
          return res.json({ shippingDetails })
        },
        (err) => {
          return res.status(400).json({ err })
        }
      )
  }
)

epayAccountsRouter.get(
  '/custom/list',
  authorizeRole([UserRole.EPAY_ACCOUNTS, UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validate({
      schema: epayOrdersListSchema,
      data: req.query,
    })
      .andThen((data) => getAllCustomProductOrders(data))
      .match(
        (result) => res.json(result),
        (err) => res.status(400).json(err)
      )
  }
)

epayAccountsRouter.post(
  '/custom/:id/release',
  authorizeRole([UserRole.EPAY_ACCOUNTS, UserRole.EPAY_ADMIN]),
  async (req, res) => {
    logger.debug('req.body =', req.body)
    return validateWithErrorData({
      schema: z.object({
        orderId: z.string(),
        paymentDate: z.string(),
      }),
      data: {
        ...req.body,
        orderId: req.params.id,
      },
    })
      .andThen((data) => {
        return releaseCustomOrder({
          userId: req.userId!,
          orderId: data.orderId,
          paymentDate: new Date(data.paymentDate),
        })
      })
      .match(
        (data) => res.status(200).json(data),
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayAccountsRouter.get(
  '/custom/:orderNumber/invoice',
  authorizeRole([UserRole.EPAY_ADMIN, UserRole.EPAY_ACCOUNTS]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: req.params,
    })

    if (input.isErr()) {
      return res.status(400).send(input.error)
    }

    const invoice = await getCustomProductOrderInvoice({
      orderNumber: input.value.orderNumber,
    })

    if (invoice.isErr()) {
      return res.status(400).send(invoice.error)
    }

    return res.status(200).json({ invoice: invoice.value })
  }
)
