import { UserRole } from '@prisma/client'
import { virtualCardsListWithFilter } from 'data/product-order-item-card.data'
import express from 'express'
import 'express-async-errors' // required in all router files
import { authorizeRole } from 'middlewares/authorize-role'
import {
  transformStringToNumber,
  validateWithErrorData,
} from 'utils/validation'
import { z } from 'zod'
import {
  reissueVirtualCardToNewRecipientEmailAddress,
  resendVirtualCardToRecipientEmail,
} from 'services/product-order/virtual-card.service'
import { ok } from 'neverthrow'
import { reissueVirtualCard } from 'external-apis/ren/card-reissue-virtual-card.api'

export const virtualCardRouter = express.Router()

const listCardsFilterSchema = z.object({
  filter: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

virtualCardRouter.get(
  '/reissue/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      data: req.query,
      schema: listCardsFilterSchema,
    })
      .andThen((data) => {
        return virtualCardsListWithFilter({
          orgId: req.orgId!,
          filter: data.filter,
          page: data.page,
          pageSize: data.pageSize,
        })
      })
      .match(
        (virtualCards) => res.status(200).json(virtualCards),
        (err) => res.status(400).json(err)
      )
  }
)

const resendVirtualCardSchema = z.object({
  cardProxyNumber: z.string(),
  productOrderItemCardId: z.string(),
})

virtualCardRouter.post(
  '/resend',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      data: req.body,
      schema: resendVirtualCardSchema,
    })
      .andThen((data) => {
        return resendVirtualCardToRecipientEmail({
          cardProxyNumber: data.cardProxyNumber,
          productOrderItemCardId: data.productOrderItemCardId,
        })
      })
      .match(
        () => res.status(200).json({ success: true }),
        (err) => res.status(400).json(err)
      )
  }
)

const reissueVirtualCardSchema = z.object({
  cardProxyNumber: z.string(),
  oldRecipientEmail: z.string().email(),
  newRecipientEmail: z.string().email(),
  productOrderItemId: z.string(),
  productOrderItemCardId: z.string(),
})

virtualCardRouter.post(
  '/reissue',
  authorizeRole([UserRole.ORG_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      data: req.body,
      schema: reissueVirtualCardSchema,
    })
      .andThen((data) => {
        return reissueVirtualCardToNewRecipientEmailAddress({
          orgId: req.orgId!,
          createdByUserId: req.userId!,
          cardProxyNumber: data.cardProxyNumber,
          newRecipientEmail: data.newRecipientEmail,
          productOrderItemId: data.productOrderItemId,
          productOrderItemCardId: data.productOrderItemCardId,
        })
      })
      .match(
        () => res.status(200).json({ success: true }),
        (err) => res.status(400).json(err)
      )
  }
)
