import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { PaymentMethod, UserRole } from '@prisma/client'
import { authorizeRole } from 'middlewares/authorize-role'
import { uploadLogo } from 'services/card.service'
import {
  createCustomProductAndOrder,
  getAllApprovedCustomProductsForOrg,
  getCustomProductOrderInvoiceForOrg,
  getCustomProductOrderListForOrg,
  getCustomProductOrderPaymentSummary,
  submitCustomProductOrder,
} from 'services/custom-product.service'

import { error } from 'utils/error'
import { validate, validateWithErrorData } from 'utils/validation'
import logger from 'utils/logger'

export const customProductRouter = express.Router()

export const uploadLogoSchema = z.object({
  fileExtension: z.string(),
  contentType: z.string(),
  productName: z.string(),
})

export type UploadLogoSchema = z.infer<typeof uploadLogoSchema>

customProductRouter.post(
  '/upload-logo',
  authorizeRole([
    UserRole.ORG_ADMIN,
    UserRole.ORG_BASIC,
    UserRole.EPAY_ADMIN,
    UserRole.EPAY_COSTUMER_SERVICE,
  ]),
  (req, res) => {
    return validate({ schema: uploadLogoSchema, data: req.body })
      .andThen((data) => {
        logger.debug(JSON.stringify(data))
        return uploadLogo({
          fileExtension: data.fileExtension,
          contentType: data.contentType,
          productName: data.productName,
        })
      })
      .match(
        (data) => {
          return res.status(200).json(data)
        },
        (e) => {
          return res.status(400).json(e)
        }
      )
  }
)

const customProductSchema = z.object({
  name: z.string(),
  designId: z.string(),
  logoUrl: z.string(),
  displayLogoUrl: z.string(),
  logoFileName: z.string(), // DON'T use logoFilename here. This is coming through url encoded but it's not safe to assume this is the case. It causes SFTP
})

export type CustomProductSchema = z.infer<typeof customProductSchema>

customProductRouter.post(
  '/create',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: customProductSchema,
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const logoFilename = input.value.logoUrl.split('/').pop() // getting the filename from the URL ensures it is encoded so that I can decode it later

    const createCustom = await createCustomProductAndOrder({
      name: input.value.name,
      designId: input.value.designId,
      logoUrl: input.value.logoUrl,
      logoFileName: decodeURIComponent(logoFilename!),
      orgId: req.orgId!,
      userId: req.userId!,
      displayLogoUrl: input.value.displayLogoUrl,
    })

    if (createCustom.isErr()) {
      return res.status(400).json(createCustom.error)
    }

    return res.status(200).json(createCustom.value)
  }
)

const submitCustomProductSchema = z.object({
  paymentMethod: z.nativeEnum(PaymentMethod),
  billingAddress: z.string(),
  city: z.string(),
  country: z.string(),
  postCode: z.string(),
  orderNumber: z.string(),
  orgId: z.string(),
  purchaseOrderNumber: z.string().max(20).nullable().optional(),
  isLiveAgent: z.boolean().optional().default(false),
  authorizedByName: z.string().optional(),
  authorizedByEmail: z.string().optional(),
})

export type SubmitCustomProductProps = z.infer<typeof submitCustomProductSchema>

customProductRouter.post(
  '/:orderNumber/submit',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  (req, res) => {
    logger.debug('submitCustomProductOrder', req.body)

    return validate({
      schema: submitCustomProductSchema,
      data: {
        ...req.params,
        ...req.body,
        orgId: req.orgId!,
      },
    })
      .andThen((data) => submitCustomProductOrder(data))
      .match(
        (data) => {
          return res.status(200).json(data)
        },
        (e) => {
          return res.status(400).json(e)
        }
      )
  }
)

customProductRouter.get(
  '/order/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  (req, res) => {
    return getCustomProductOrderListForOrg({ orgId: req.orgId! }).match(
      (customProductOrderList) => {
        return res.json(customProductOrderList)
      },
      (e) => {
        return res.status(400).json(e)
      }
    )
  }
)

customProductRouter.get(
  '/:orderNumber/invoice',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  (req, res) => {
    const validationSchema = z.object({
      orderNumber: z.string(),
    })

    const validation = validationSchema.safeParse(req.params)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return getCustomProductOrderInvoiceForOrg(
      validation.data.orderNumber,
      req.orgId!
    ).match(
      (invoice) => {
        return res.json({ invoice })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

customProductRouter.get(
  '/products/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  (req, res) => {
    return getAllApprovedCustomProductsForOrg(req.orgId!).match(
      (customProducts) => {
        return res.status(200).json(customProducts)
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

customProductRouter.get(
  '/:orderNumber/payment-summary',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: { orderNumber: req.params.orderNumber },
    })
      .andThen((data) => {
        return getCustomProductOrderPaymentSummary({
          orderNumber: data.orderNumber,
          orgId: req.orgId!,
        })
      })
      .match(
        (paymentSummary) => {
          return res.status(200).json(paymentSummary)
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)
