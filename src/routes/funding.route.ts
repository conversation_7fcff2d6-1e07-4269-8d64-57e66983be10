import express from 'express'
import 'express-async-errors' // required in all router files

import { PaymentMethod, UserRole } from '@prisma/client'
import { authorizeRole } from 'middlewares/authorize-role'
import {
  completeFundingProductOrder,
  createEmptyFundingOrder,
  createFundingProductOrderItemsForProductOrder,
  getFundingOrderPaymentSummary,
  updateCardsUsedInFundingOrder,
} from 'services/funding.service'
import {
  transformStringToNumber,
  validateWithErrorData,
} from 'utils/validation'
import { z } from 'zod'
import { getAvailableCards } from 'services/funding.service'
import { submitProductOrder } from 'services/product-order.service'
import { getFundingOrderItemByCRN } from 'data/funding-product-order-item.data'
import { NO_LOCK_CODE } from 'services/product-order/product-order-util'

export const fundingRouter = express.Router()

const AvailableCardsListSchema = z.object({
  searchTerm: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

export type AvailableCardsListProps = z.infer<typeof AvailableCardsListSchema>

fundingRouter.get(
  '/available-cards/list',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: AvailableCardsListSchema,
      data: req.query,
    })
      .andThen((data: AvailableCardsListProps) => {
        return getAvailableCards({ orgId: req.orgId!, props: data })
      })
      .match(
        (result) => {
          return res.status(200).json(result)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

const FundingOrderSchema = z.object({
  lockCodeType: z.enum([
    NO_LOCK_CODE,
    'unique_for_each_card',
    'single_lock_code_for_order',
  ]),
  orderLockCode: z.string().optional(),
  cards: z.array(
    z.object({
      productCode: z.number(),
      crn: z.string(),
      unitPriceInDollars: z.number().min(1),
      recipientEmail: z.union([
        z.string().email(),
        z.string().max(0),
        z.undefined(),
      ]),
    })
  ),
})

const createFundingOrderItemsSchema = FundingOrderSchema.extend({
  productOrderId: z.string(),
  orgId: z.string(),
})

export type FundingOrderArgs = z.infer<typeof FundingOrderSchema>

// Submit funding order:
// 1. Create empty funding order
// 2. Create cards
// 3. Calculate totals and Update order status
fundingRouter.post(
  '/order/create-empty-order',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
        userId: z.string(),
      }),
      data: {
        orgId: req.orgId!,
        userId: req.userId!,
      },
    })
      .andThen(createEmptyFundingOrder)
      .match(
        (fundingOrder) => res.status(200).json(fundingOrder),
        (error) => res.status(400).json(error)
      )
  }
)

fundingRouter.post(
  '/order/create-funding-order-items',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: createFundingOrderItemsSchema,
      data: {
        ...req.body,
        orgId: req.orgId,
      },
    })
      .andThen(createFundingProductOrderItemsForProductOrder)
      .match(
        (fundingOrderItems) => res.status(200).json(fundingOrderItems),
        (error) => res.status(400).json(error)
      )
  }
)

fundingRouter.post(
  '/order/complete-order',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        productOrderId: z.string(),
      }),
      data: req.body,
    })
      .andThen(completeFundingProductOrder)
      .match(
        (fundingOrder) => res.status(200).json(fundingOrder),
        (error) => res.status(400).json(error)
      )
  }
)

const submitFundingOrderSchema = z.object({
  params: z.object({
    orderNumber: z.string(),
  }),
  body: z.object({
    billingAddress: z.string(),
    city: z.string(),
    country: z.string(),
    paymentMethod: z.nativeEnum(PaymentMethod),
    postcode: z.string(),
    purchaseOrderNumber: z.string().optional(),
    lockCode: z.string().optional(),
    cardReferenceNumbers: z
      .array(z.string())
      .min(1, 'At least one card reference number is required'),
    isLiveAgent: z.boolean().optional().default(false),
    authorizedByName: z.string().optional(),
    authorizedByEmail: z.string().optional(),
  }),
})

fundingRouter.put(
  '/order/:orderNumber/submit',
  authorizeRole([UserRole.ORG_BASIC, UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: submitFundingOrderSchema,
      data: req,
    })

    if (input.isErr()) {
      return res.status(400).send(input.error)
    }

    const { cardReferenceNumbers, ...submitOrderData } = input.value.body

    // Step 1: Update cards used in funding order
    const updateCardsResult = await updateCardsUsedInFundingOrder(
      cardReferenceNumbers
    )

    if (updateCardsResult.isErr()) {
      return res.status(400).send(updateCardsResult.error)
    }

    if (!updateCardsResult.value.success) {
      return res.status(400).json({
        message: 'Some cards are already used in a funding order',
        alreadyUsedCRNs: updateCardsResult.value.alreadyUsedCRNs,
      })
    }

    // Step 2: Submit the funding order
    const submitOrder = await submitProductOrder({
      orgId: req.orgId!,
      orderNumber: input.value.params.orderNumber,
      billingAddress: submitOrderData.billingAddress,
      city: submitOrderData.city,
      country: submitOrderData.country,
      paymentMethod: submitOrderData.paymentMethod,
      postcode: submitOrderData.postcode,
      purchaseOrderNumber: submitOrderData.purchaseOrderNumber,
      lockCode: submitOrderData.lockCode,
      selectedDeliveryRecipients: [],
      secureDelivery: false,
      isLiveAgent: submitOrderData.isLiveAgent,
      authorizedByName: submitOrderData.authorizedByName,
      authorizedByEmail: submitOrderData.authorizedByEmail,
    })

    if (submitOrder.isErr()) {
      // TODO: Implement a rollback mechanism to revert the card updates
      // since the order submission failed
      return res.status(400).send(submitOrder.error)
    }

    return res.status(200).json(submitOrder.value)
  }
)

fundingRouter.get(
  '/:orderNumber/payment-summary',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orderNumber: z.string(),
      }),
      data: { orderNumber: req.params.orderNumber },
    })
      .andThen((data) => {
        return getFundingOrderPaymentSummary(data.orderNumber)
      })
      .match(
        (paymentSummary) => {
          return res.status(200).json(paymentSummary)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

fundingRouter.get('/crn/:crn', async (req, res) => {
  return validateWithErrorData({
    schema: z.object({
      crn: z.string(),
    }),
    data: { crn: req.params.crn },
  })
    .andThen((data) => {
      return getFundingOrderItemByCRN(data.crn)
    })
    .match(
      (fundingOrderItem) => {
        return res.status(200).json(fundingOrderItem)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
})
