import {
  CorporateApplicationStatus,
  IdentityType,
  OrganizationType,
} from '@prisma/client'

import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'

import { error } from 'utils/error'

import {
  deleteBeneficialOwner,
  deleteDirector,
  getBeneficialOwnerPhotoSignedUrl,
  getCorporateApplication,
  listCorporateApplication,
  save,
  submit,
  updateApplicant,
  updateApplicationForOtherOrgTypeThenUpdateUserThenCreateOrg,
  updateApplicationWithCompanyDetails,
  updateBeneficialOwners,
  updateDirectors,
} from 'services/corporate-application.service'
import logger from 'utils/logger'
import { validateWithErrorData } from 'utils/validation'

export const corporateApplicationRouter = express.Router()

corporateApplicationRouter.get(
  '/list',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    return (await listCorporateApplication({ userId: req.userId! })).match(
      (corporateApplications) => {
        return res.json({
          corporateApplications,
        })
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

corporateApplicationRouter.get(
  '/:id',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({
        id: z.string(),
      }),
    })

    const validation = validationSchema.safeParse(req)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await getCorporateApplication({
        userId: req.userId!,
        corporateApplicationId: validation.data.params.id,
      })
    ).match(
      (corporateApplication) => {
        return res.json({
          corporateApplication,
        })
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

const Z_ORGANIZATION_OPTIONAL = z.object({
  organizationType: z.nativeEnum(OrganizationType),
  name: z.string().optional(),
  tradingName: z.string().optional(),
  nzbn: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  suburb: z.string().optional(),
  country: z.string().optional(),
  zip: z.string().optional(),
  natureAndPurpose: z.string().optional(),
  phone: z.string().optional(),
  cPartnerUserId: z.string().optional(),
})

corporateApplicationRouter.post(
  '/:id/organization/update',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ id: z.string() }),
      body: Z_ORGANIZATION_OPTIONAL,
    })

    const validation = validationSchema.safeParse(req)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await updateApplicationWithCompanyDetails({
        id: validation.data.params.id,
        userId: req.userId!,
        data: validation.data.body,
      })
    ).match(
      (corporateApplication) => {
        return res.json({
          corporateApplication,
        })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

const Z_APPLICANT_OPTIONAL = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phoneNbr: z.string().optional(),
  branch: z.string().optional(),
})

corporateApplicationRouter.post(
  '/:id/applicant/update',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = Z_APPLICANT_OPTIONAL

    const validation = validationSchema.safeParse(req.body)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return updateApplicant({ id: req.userId!, data: validation.data }).match(
      ({ firstName, lastName, phoneNbr, branch }) => {
        return res.json({
          applicant: {
            firstName,
            lastName,
            phoneNbr,
            branch,
          },
        })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

const Z_IDENTITIES_OPTIONAL = z.array(
  z.object({
    id: z.string().optional(),
    firstName: z.string().optional(),
    givenName: z.string().optional(),
    lastName: z.string().optional(),
    dateOfBirth: z.coerce.date().optional(),
    address: z.string().optional(),
    city: z.string().optional(),
    country: z.string().optional(),
    zip: z.string().optional(),
    identificationType: z.nativeEnum(IdentityType).nullable().optional(),
    identificationNbr: z.string().optional(),
    identificationPhotoUrl: z.string().optional(),
    passportExpiryDate: z.coerce.date().optional(),
    driverLicenseVersion: z.string().optional(),
  })
)

corporateApplicationRouter.post(
  '/:id/directors/update',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ id: z.string() }),
      body: Z_IDENTITIES_OPTIONAL,
    })

    const validation = validationSchema.safeParse(req)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await updateDirectors({
        corporateApplicationId: validation.data.params.id,
        userId: req.userId!,
        directors: validation.data.body,
      })
    ).match(
      (directors) => {
        return res.json({
          directors,
        })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

corporateApplicationRouter.post(
  '/:corporateApplicationId/director/:directorId/delete',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({
        corporateApplicationId: z.string(),
        directorId: z.string(),
      }),
    })

    const validation = validationSchema.safeParse(req)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await deleteDirector({
        corporateApplicationId: validation.data.params.corporateApplicationId,
        userId: req.userId!,
        directorId: validation.data.params.directorId,
      })
    ).match(
      (result) => {
        return res.json(result)
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

const Z_BENEFICIAL_OWNERS_UPDATE = z
  .object({
    hasNoIndividualBeneficialOwner: z.boolean().optional(),
    beneficialOwners: Z_IDENTITIES_OPTIONAL.optional(),
  })
  .refine(
    (data) =>
      data.hasNoIndividualBeneficialOwner !== undefined ||
      data.beneficialOwners !== undefined,
    {
      message:
        "Either 'hasNoIndividualBeneficialOwner' or 'beneficialOwners' must be provided",
    }
  )

corporateApplicationRouter.post(
  '/:id/beneficial-owners/update',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ id: z.string() }),
      body: Z_BENEFICIAL_OWNERS_UPDATE,
    })

    logger.info('req.body', req.body)

    const validation = await validateWithErrorData({
      schema: validationSchema,
      data: {
        params: req.params,
        body: req.body,
      },
    })

    if (validation.isErr()) {
      logger.error(validation.error)
      return res.status(400).json(validation.error)
    }

    const { params, body } = validation.value

    return (
      await updateBeneficialOwners({
        corporateApplicationId: params.id,
        userId: req.userId!,
        hasNoIndividualBeneficialOwner: body.hasNoIndividualBeneficialOwner,
        beneficialOwners: body.beneficialOwners,
      })
    ).match(
      (beneficialOwners) => {
        return res.json({ beneficialOwners })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

corporateApplicationRouter.post(
  '/:corporateApplicationId/beneficial-owner/signed-url',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ corporateApplicationId: z.string() }),
      body: z.object({
        contentType: z.string(),
        fileExtension: z.string(),
      }),
    })

    const validation = validationSchema.safeParse(req)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await getBeneficialOwnerPhotoSignedUrl({
        userId: req.userId!,
        corporateApplicationId: validation.data.params.corporateApplicationId,
        contentType: validation.data.body.contentType,
        fileExtension: validation.data.body.fileExtension,
      })
    ).match(
      (result) => {
        return res.json(result)
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

corporateApplicationRouter.post(
  '/:corporateApplicationId/beneficial-owner/:beneficialOwnerId/delete',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({
        corporateApplicationId: z.string(),
        beneficialOwnerId: z.string(),
      }),
    })

    const validation = validationSchema.safeParse(req)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await deleteBeneficialOwner({
        corporateApplicationId: validation.data.params.corporateApplicationId,
        userId: req.userId!,
        beneficialOwnerId: validation.data.params.beneficialOwnerId,
      })
    ).match(
      (result) => {
        return res.json(result)
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

const Z_CORPORATE_APPLICATION_OPTIONAL = z.object({
  applicationStatus: z.nativeEnum(CorporateApplicationStatus),
  organization: Z_ORGANIZATION_OPTIONAL,
  applicant: Z_APPLICANT_OPTIONAL,
  directors: Z_IDENTITIES_OPTIONAL,
  beneficialOwners: Z_BENEFICIAL_OWNERS_UPDATE,
  kycCompletionPreference: z.enum(['FINISH_LATER']).optional(),
})

export type ZCorporateApplicationOptional = z.infer<
  typeof Z_CORPORATE_APPLICATION_OPTIONAL
>

corporateApplicationRouter.post(
  '/:id/update/:kycCompletionPreference?',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({
        id: z.string(),
        kycCompletionPreference: z.enum(['FINISH_LATER']).optional(),
      }),
      body: Z_CORPORATE_APPLICATION_OPTIONAL,
    })

    const validation = validationSchema.safeParse(req)
    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await save({
        userId: req.userId!,
        corporateApplicationId: validation.data.params.id,
        corporateApplication: validation.data.body,
        kycCompletionPreference: validation.data.params.kycCompletionPreference,
      })
    ).match(
      (status) => {
        return res.status(200).json(status)
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

const Z_CORPORATE_APPLICATION = z.object({
  organization: z.object({
    organizationType: z.nativeEnum(OrganizationType),
    name: z.string(),
    tradingName: z.string().optional(),
    nzbn: z.string(),
    address: z.string().optional(),
    city: z.string().optional(),
    suburb: z.string().optional(),
    country: z.string().optional(),
    zip: z.string().optional(),
    principalPlace: z.string().optional(),
    natureAndPurpose: z.string().optional(),
    phone: z.string().optional(),
    cPartnerUserId: z.string().optional(),
  }),
  applicant: z.object({
    firstName: z.string(),
    lastName: z.string(),
    phoneNbr: z.string().optional(),
    branch: z.string().optional(),
  }),
  directors: z.array(
    z.object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
    })
  ),
  beneficialOwners: Z_BENEFICIAL_OWNERS_UPDATE,
  declaration: z.literal(true),
})

export type ZCorporateApplication = z.infer<typeof Z_CORPORATE_APPLICATION>

corporateApplicationRouter.post(
  '/:id/submit',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({
        id: z.string(),
      }),
      body: Z_CORPORATE_APPLICATION,
    })

    const validation = await validateWithErrorData({
      schema: validationSchema,
      data: {
        params: req.params,
        body: req.body,
      },
    })

    if (validation.isErr()) {
      logger.error(validation.error)

      return res.status(400).json(validation.error)
    }

    return (
      await submit({
        userId: req.userId!,
        corporateApplicationId: validation.value.params.id,
        corporateApplication: validation.value.body,
      })
    ).match(
      (applicationStatus) => {
        logger.debug('applicationStatus', applicationStatus)
        return res.status(200).json(applicationStatus)
      },
      (err) => {
        if (typeof err == 'string') {
          return error({ res, errorType: err, data: {} })
        }

        return res.status(400).json(err)
      }
    )
  }
)

const Z_ORGANIZATION_TYPE_OTHER = z.object({
  name: z.string(),
  nzbn: z.string().optional(),
  tradingName: z.string().optional(),
  firstName: z.string(),
  lastName: z.string(),
  phoneNbr: z.string().optional(),
  id: z.string(),
})

export type ZOrganizationTypeOther = z.infer<typeof Z_ORGANIZATION_TYPE_OTHER>

corporateApplicationRouter.post(
  '/:id/organization-type/other',
  async (req, res) => {
    return validateWithErrorData({
      schema: Z_ORGANIZATION_TYPE_OTHER,
      data: {
        ...req.body,
        id: req.params.id,
      },
    })
      .andThen((data) => {
        return updateApplicationForOtherOrgTypeThenUpdateUserThenCreateOrg({
          userId: req.userId!,
          applicationId: data.id,
          data: {
            name: data.name,
            nzbn: data.nzbn,
            tradingName: data.tradingName,
            firstName: data.firstName,
            lastName: data.lastName,
            phoneNbr: data.phoneNbr,
          },
        })
      })
      .match(
        (application) => {
          return res.json(application)
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)
