import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { checkApi<PERSON><PERSON> } from 'middlewares/check-api-key'

import {
  //addCardReferenceManually,
  getBatchIdsForOrder,
  sendLockCodeEmailsByOrderNumber,
} from 'services/epay.service'
import { uploadLogoToSftp } from 'services/placard.service'

import { EPAY_PORTAL_API_KEY } from 'utils/config'
import logger from 'utils/logger'
import { validate, validateInput } from 'utils/validation'

import { processQueue } from 'services/product-order/order-processor.service'
import { activateOrder } from 'services/product-order/activate-cards.service'

export const epayCardRouter = express.Router()

// Populates missing CRNs and sends recipient emails from cron job

epayCardRouter.post(
  '/v2.1/crn/update',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    const DEFAULT_AGE_IN_MINUTES = 60

    const input = await validate({
      data: req.body,
      schema: z.object({
        useRen: z.boolean().optional().default(true),
        orderAgeInMinutes: z
          .number()
          .optional()
          .default(DEFAULT_AGE_IN_MINUTES), // CRN, Order ID or Company name
      }),
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    logger.debug('starting V2.1 crn update')

    // Start processing the queue with the provided order age
    processQueue(
      input.value.orderAgeInMinutes,
      input.value.useRen
    ).catch((error) => {
      // Log any errors that occur during queue processing
      logger.error(error, 'Error during queue processing')
    })

    // Return immediately after initiating the process
    res.send('Processing initiated')
  }
)

// Gets Batch Numbers for an Order
epayCardRouter.get(
  '/order/:orderId',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (_req, res) => {
    logger.debug('epayCardRouter.get /order/:orderId')
    const orderId = _req.params.orderId
    const result = await getBatchIdsForOrder(orderId)
    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    return res.status(200).json(result.value)
  }
)

epayCardRouter.post(
  '/activate',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    return validate({
      schema: z.object({
        orderNumber: z.string(),
        orgId: z.string(),
      }),
      data: req.body,
    })
      .andThen((data) => {
        return activateOrder({
          orderNumber: data.orderNumber,
          orgId: data.orgId,
        })
      })
      .match(
        (data) => {
          return res.status(200).json(data)
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

epayCardRouter.post(
  '/logo/upload',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    console.log('console.log = epayCardRouter.post /logo/upload')
    logger.debug('epayCardRouter.post /logo/upload')
    const input = await validateInput({
      schema: z.object({
        logoKey: z.string(),
      }),
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const result = await uploadLogoToSftp({ key: input.value.logoKey })
    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    return res.status(200).json(result.value)
  }
)
