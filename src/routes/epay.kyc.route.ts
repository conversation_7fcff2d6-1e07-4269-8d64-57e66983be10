import { CorporateApplicationStatus, KycStatus, UserRole } from '@prisma/client'

import express from 'express'
import 'express-async-errors'
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'
import { checkPermissions } from 'middlewares/check-permissions'

import { error } from 'utils/error'

import {
  createCorporateApplicationNote,
  deleteCorporateApplicationNote,
  getCorporateApplication,
  getSignedUrlForImage,
  listCorporateApplicationsWithStatusCount,
  processCorporateApplication,
  updateCorporateApplicationNote,
} from 'services/kyc.service'
import { transformStringToNumber, validate } from 'utils/validation'

export const epayKycRouter = express.Router()

const listSchema = z.object({
  status: z.nativeEnum(CorporateApplicationStatus).optional(),
  search: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

epayKycRouter.get(
  '/list',
  authorizeRole(['EPAY_COMPLIANCE', 'EPAY_ADMIN']),
  async (req, res) => {
    return validate({ schema: listSchema, data: req.query })
      .andThen((data) => {
        return listCorporateApplicationsWithStatusCount({
          search: data.search ?? '',
          status: data.status,
          page: data.page,
          pageSize: data.pageSize,
        })
      })
      .match(
        ({ corporateApplications, counts }) => {
          return res.status(200).json({
            corporateApplications,
            counts,
          })
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayKycRouter.get(
  '/corporate-application/:id',
  authorizeRole(['EPAY_COMPLIANCE', 'EPAY_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ id: z.string() }),
    })

    const validation = validationSchema.safeParse(req)

    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (await getCorporateApplication(validation.data.params.id)).match(
      (corporateApplication) => {
        if (!corporateApplication) {
          return error({ res, errorType: 'NOT_FOUND' })
        }
        return res.json({
          corporateApplication,
        })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

epayKycRouter.post(
  '/corporate-application/:id/note/create',
  authorizeRole(['EPAY_COMPLIANCE', 'EPAY_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ id: z.string() }),
      body: z.object({
        title: z.string().optional(),
        message: z.string(),
      }),
    })

    const validation = validationSchema.safeParse(req)

    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return await createCorporateApplicationNote({
      userId: req.userId!,
      corporateApplicationId: validation.data.params.id,
      title: validation.data.body.title,
      message: validation.data.body.message,
    }).match(
      (corporateApplicationNote) => {
        return res.json({ note: corporateApplicationNote })
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

epayKycRouter.post(
  '/corporate-application/note/:id/update',
  authorizeRole(['EPAY_COMPLIANCE', 'EPAY_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ id: z.string() }),
      body: z.object({
        title: z.string().optional(),
        message: z.string(),
      }),
    })

    const validation = validationSchema.safeParse(req)

    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return await updateCorporateApplicationNote({
      id: validation.data.params.id,
      title: validation.data.body.title,
      message: validation.data.body.message,
    }).match(
      (corporateApplicationNote) => {
        return res.json({ note: corporateApplicationNote })
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

const deleteSchema = z.object({ id: z.string() })

epayKycRouter.post(
  '/corporate-application/note/:id/delete',
  authorizeRole(['EPAY_COMPLIANCE', 'EPAY_ADMIN']),
  async (req, res) => {
    return validate({ schema: deleteSchema, data: req.params })
      .andThen((data) => {
        return deleteCorporateApplicationNote({
          id: data.id,
        })
      })
      .match(
        (corporateApplicationNote) => {
          return res.json({ note: corporateApplicationNote })
        },
        (err) => {
          return error({ res, errorType: err })
        }
      )
  }
)

type ValidCorporateApplicationStatus = Extract<
  CorporateApplicationStatus,
  'APPROVED' | 'DECLINED' | 'REQUIRES_INFO' | 'PRE_APPROVED'
>

const PartialCorporateApplicationStatus: Record<
  ValidCorporateApplicationStatus,
  ValidCorporateApplicationStatus
> = {
  APPROVED: 'APPROVED',
  DECLINED: 'DECLINED',
  REQUIRES_INFO: 'REQUIRES_INFO',
  PRE_APPROVED: 'PRE_APPROVED',
}

epayKycRouter.post(
  '/corporate-application/:id/update',
  authorizeRole(['EPAY_COMPLIANCE', 'EPAY_ADMIN']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({ id: z.string() }),
      body: z.object({
        status: z.nativeEnum(PartialCorporateApplicationStatus),
        kycStatus: z.nativeEnum(KycStatus).optional(),
      }),
    })

    const validation = validationSchema.safeParse(req)

    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    return (
      await processCorporateApplication({
        corporateApplicationId: validation.data.params.id,
        status: validation.data.body.status,
        kycStatus: validation.data.body.kycStatus,
      })
    ).match(
      (corporateApplication) => {
        if (!corporateApplication) {
          return error({ res, errorType: 'NOT_FOUND' })
        }

        return res.json({
          status: corporateApplication.status,
          kycStatus: corporateApplication.kycStatus,
        })
      },
      (err) => {
        return res.status(400).json(err)
      }
    )
  }
)

epayKycRouter.get(
  '/signed-identity-image-url',
  authorizeRole([UserRole.EPAY_ADMIN, UserRole.EPAY_COMPLIANCE]),
  checkPermissions(['read:identity-photo']),
  async (req, res) => {
    return validate({
      schema: z.string(),
      data: req.query.key,
    })
      .andThen((key) => getSignedUrlForImage(key))
      .match(
        (url) => res.status(200).json({ url }),
        (error) => res.status(400).json(error)
      )
  }
)
