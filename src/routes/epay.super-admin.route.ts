import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { UserRole } from '@prisma/client'
import { authorizeRole } from 'middlewares/authorize-role'
import { getCompanyListForSuperAdminWithFilter } from 'services/super-admin.service'
import {
  transformStringToNumber,
  validateWithErrorData,
} from 'utils/validation'

export const epaySuperAdminRouter = express.Router()

//get company list

epaySuperAdminRouter.get(
  '/organization/list',
  authorizeRole([UserRole.EPAY_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        searchTerm: z.string().optional(),
        page: transformStringToNumber({ defaultNumber: 1 }),
        pageSize: transformStringToNumber({ defaultNumber: 10 }),
      }),
      data: req.query,
    })
      .andThen((data) => {
        return getCompanyListForSuperAdminWithFilter({
          searchTerm: data.searchTerm,
          page: data.page,
          pageSize: data.pageSize,
        })
      })
      .match(
        (organizations) => {
          res.status(200).json(organizations)
        },
        (error) => {
          res.status(400).json(error)
        }
      )
  }
)
