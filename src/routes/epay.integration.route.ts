import express from 'express'
import 'express-async-errors' // required in all router files

import {
  getCrnOrderReportAsCsv,
  getProductOrderReportAsCsv,
  sendReleasedOrderReport,
} from 'services/epay.service'
import { checkApi<PERSON>ey } from 'middlewares/check-api-key'
import {
  EPAY_API_PASSWORD,
  EPAY_API_USERNAME,
  EPAY_INTEGRATION_URL_1,
  EPAY_INTEGRATION_URL_2,
  EPAY_PORTAL_API_KEY,
  IS_PROD,
} from 'utils/config'
import { validateInput, validateWithErrorData } from 'utils/validation'
import { z } from 'zod'
import { addSkipKycEmail } from 'services/kyc.service'
import { deleteOrgAndUserByUserId } from 'services/user.service'
import RequestWithBackoff from 'helpers/request-with-backoff'
import logger from 'utils/logger'
import { fromPromise } from 'neverthrow'
import { ERRORS } from 'utils/error'
import { getCardDetails } from 'external-apis/ren/get-card-details.api'

export const epayIntegrationRouter = express.Router()

epayIntegrationRouter.post(
  '/released-orders-report/send',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (_req, res) => {
    const result = await sendReleasedOrderReport()
    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    return res.status(400).json(result.value)
  }
)

epayIntegrationRouter.post(
  '/add-skip-email',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (_req, res) => {
    return validateWithErrorData({
      schema: z.object({ email: z.string() }),
      data: _req.body,
    })
      .andThen((input) => {
        return addSkipKycEmail({ email: input.email })
      })
      .match(
        (result) => {
          return res.status(200).json('Email added to skip list')
        },
        (error) => {
          return res.status(400).json(error)
        }
      )
  }
)

const orderDateSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})
epayIntegrationRouter.get(
  '/report/crns-by-order-date-csv',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    const validData = await validateInput({
      schema: orderDateSchema,
      data: req.query,
    })

    if (validData.isErr()) {
      return res.status(400).json(validData.error)
    }
    const input = validData.value

    const startDate = input.startDate ? new Date(input.startDate) : new Date()
    const endDate = input.endDate
      ? new Date(input.endDate)
      : new Date(startDate.setDate(startDate.getDate() - 30))

    const result = await getCrnOrderReportAsCsv({ startDate, endDate })

    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    const fileName = `card_order_report_${startDate.getTime()}_${endDate.getTime()}.csv`
    res.setHeader('Content-disposition', `attachment; filename=${fileName}`)
    res.set('Content-Type', 'text/csv')
    return res.status(200).send(result.value.csvContent)
  }
)

epayIntegrationRouter.get(
  '/report/product-orders-by-order-date-csv',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    const validData = await validateInput({
      schema: orderDateSchema,
      data: req.query,
    })

    if (validData.isErr()) {
      return res.status(400).json(validData.error)
    }
    const input = validData.value

    const startDate = input.startDate ? new Date(input.startDate) : new Date()
    const endDate = input.endDate
      ? new Date(input.endDate)
      : new Date(startDate.setDate(startDate.getDate() - 30))

    const result = await getProductOrderReportAsCsv({ startDate, endDate })

    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    const fileName = `product_order_report_${startDate.getTime()}_${endDate.getTime()}.csv`
    res.setHeader('Content-disposition', `attachment; filename=${fileName}`)
    res.set('Content-Type', 'text/csv')
    return res.status(200).send(result.value.csvContent)
  }
)

epayIntegrationRouter.delete(
  '/delete-org-and-user-by-user-id',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    if (!IS_PROD) {
      const validData = await validateInput({
        schema: z.object({
          userId: z.string(),
        }),
        data: req.query,
      })

      if (validData.isErr()) {
        return res.status(400).json(validData.error)
      }

      return deleteOrgAndUserByUserId({ userId: validData.value.userId }).match(
        (result) => res.status(200).send(result),
        (error) => res.status(400).json(error)
      )
    }

    return res.status(400).json('Not allowed in production')
  }
)
/// Can manipulate the responses of this method for testing purposes
epayIntegrationRouter.get(
  '/test-response',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    logger.debug('test response')
    return res.status(200).json({ success: true })
  }
)

/// Testing the backoff mechanism
epayIntegrationRouter.get(
  '/test-backoff',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    const token = req.headers['x-api-key']
    const epayApi = new RequestWithBackoff({
      apiUrls: [EPAY_INTEGRATION_URL_1, EPAY_INTEGRATION_URL_2],
      initialTimeout: 1000,
    })

    const result = await fromPromise(
      epayApi.request({
        url: `/epay/integration/test-response`,
        method: 'get',
        headers: {
          'x-api-key': `${token}`,
        },
      }),
      (error) => {
        logger.error(error, `failed to send from backoff`)
        return ERRORS.EXTERNAL_API
      }
    )

    if (result.isOk()) {
      return res.status(200).json(result.value)
    } else {
      return res.status(400).json(result.error)
    }
  }
)

epayIntegrationRouter.post(
  '/test-ren-proof-of-life',
  checkApiKey(EPAY_PORTAL_API_KEY),
  async (req, res) => {
    if (!req.body.proxyNumber) {
      return res.status(400).json({ message: 'Invalid parameter' })
    }
    return getCardDetails(req.body.proxyNumber).match(
      (cardDetail) => {
        return res.status(200).json({
          proxyNumber: cardDetail.proxyNumber,
          firstName: cardDetail.encodeFirstName,
          cardSubType: cardDetail.cardSubType,
          productCode: cardDetail.productCode,
        })
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
  }
)
