import express from 'express'
import 'express-async-errors' // required in all router files

import { z } from 'zod'
import { UserRole } from '@prisma/client'

import { authorizeRole } from 'middlewares/authorize-role'

import { error } from 'utils/error'
import {
  getUser,
  findUserWithinOrg,
  updateUser,
  updateUserWithinOrg,
  createUser,
  deleteUserCompletely,
} from 'services/user.service'
import {
  validate,
  validateInput,
  validateWithErrorData,
} from 'utils/validation'
import logger from 'utils/logger'
import { sendResetPasswordEmail } from 'services/auth0.service'

export const userRouter = express.Router()

userRouter.get('/', async (req, res) => {
  getUser({ id: req.userId! }).match(
    (user) => {
      return res.status(200).json(user)
    },
    (err) => {
      return res.status(400).json(err)
    }
  )
})

const updateSelfValidationSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
})
userRouter.post('/update', (req, res) => {
  const validation = updateSelfValidationSchema.safeParse(req.body)

  if (!validation.success) {
    return error({ res, errorType: 'BAD_INPUT' })
  }

  return updateUser({
    id: req.userId!,
    data: {
      firstName: validation.data.firstName,
      lastName: validation.data.lastName,
    },
  }).match(
    () => {
      return res.json({ updated: true })
    },
    () => {
      return error({ res, errorType: 'BAD_INPUT' })
    }
  )
})

export const getUserSchema = z.object({ id: z.string() })

userRouter.get(
  '/:id',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER']),
  async (req, res) => {
    return validate({
      schema: getUserSchema,
      data: req.params,
    })
      .andThen((data) => {
        return findUserWithinOrg({ id: data.id, orgId: req.orgId! })
      })
      .match(
        (user) => {
          return res.json({
            user: {
              id: user.id,
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              role: user.role,
              branch: user.branch,
            },
          })
        },
        (err) => {
          return error({ res, errorType: err })
        }
      )
  }
)

const createUserValidationSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string(),
  role: z.nativeEnum({
    [UserRole.ORG_ADMIN]: UserRole.ORG_ADMIN,
    [UserRole.ORG_MANAGER]: UserRole.ORG_MANAGER,
    [UserRole.ORG_BASIC]: UserRole.ORG_BASIC,
  }),
  branch: z.string().optional(),
})

export type CreateUserValidationProps = z.infer<
  typeof createUserValidationSchema
>

userRouter.post(
  '/create',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_MANAGER]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: z.object({
        firstName: z.string(),
        lastName: z.string(),
        email: z.string(),
        role: z.enum([
          UserRole.ORG_ADMIN,
          UserRole.ORG_MANAGER,
          UserRole.ORG_BASIC,
        ]),
        branch: z.string().optional(),
      }),
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).send(input.error)
    }

    const orgUser = await createUser({
      orgId: req.orgId!,
      role: input.value.role,
      firstName: input.value.firstName,
      lastName: input.value.lastName,
      email: input.value.email,
      branch: input.value.branch,
    })

    if (orgUser.isErr()) {
      return res.status(400).send(orgUser.error)
    }

    return res.status(200).json(orgUser.value)
  }
)

const updateUserValidationSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  role: z
    .nativeEnum({
      [UserRole.ORG_ADMIN]: UserRole.ORG_ADMIN,
      [UserRole.ORG_MANAGER]: UserRole.ORG_MANAGER,
      [UserRole.ORG_BASIC]: UserRole.ORG_BASIC,
    })
    .optional(),
  branch: z.string().optional(),
})

export type UpdateUserValidationProps = z.infer<
  typeof updateUserValidationSchema
>

userRouter.post(
  '/:id/update',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER']),
  async (req, res) => {
    const validationSchema = z.object({
      params: z.object({
        id: z.string(),
      }),
      body: updateUserValidationSchema,
    })

    const validation = validationSchema.safeParse(req)

    if (!validation.success) {
      return error({ res, errorType: 'BAD_INPUT' })
    }

    const updateUser = await updateUserWithinOrg({
      userId: req.userId!,
      requesterRole: req.role!,
      orgId: req.orgId!,

      requesterId: validation.data.params.id,
      firstName: validation.data.body.firstName,
      lastName: validation.data.body.lastName,
      email: validation.data.body.email,
      role: validation.data.body.role,
      branch: validation.data.body.branch,
    })

    if (updateUser.isErr()) {
      return res.status(400).json(updateUser.error)
    }

    return res.status(200).json(updateUser.value)
  }
)

userRouter.post('/change-password', async (req, res) => {
  const validationSchema = z.object({
    email: z.string(),
  })

  const validation = validationSchema.safeParse(req.body)

  if (!validation.success) {
    return error({ res, errorType: 'BAD_INPUT' })
  }

  const changePassword = await sendResetPasswordEmail({
    email: req.body.email,
    isAdmin: req.role?.substring(0, 4) === 'EPAY',
  })

  if (changePassword.isErr()) {
    logger.info(`failed to send reset password email`, changePassword.error)

    return res.status(400).json(changePassword.error)
  }

  return res.status(200).json(changePassword)
})

const deleteSchema = z.object({
  id: z.string(),
})

userRouter.post(
  '/:id/delete',
  authorizeRole([UserRole.ORG_ADMIN]),
  async (req, res) => {
    const input = await validateInput({
      schema: deleteSchema,
      data: req.params,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const deleteUserResult = await deleteUserCompletely({
      userId: input.value.id,
      orgId: req.orgId!,
    })

    if (deleteUserResult.isErr()) {
      return res.status(400).json(deleteUserResult.error)
    }

    return res.json(deleteUserResult.value)
  }
)
