import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeR<PERSON> } from 'middlewares/authorize-role'
import {
  findProductWithDiscountForOrg,
  findProductsForOrg,
  findProductsForOrgCSV,
} from 'services/product.service'
import { error } from 'utils/error'
import { centsToDollars } from 'utils/numeric'
import {
  transformStringToNumber,
  validate,
  validateWithErrorData,
} from 'utils/validation'

export const productRouter = express.Router()

const listSchema = z.object({
  filter: z
    .enum(['All', 'Personalized', 'Virtual', 'Physical'])
    .optional()
    .default('All'),
  search: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

export type ProductListProps = z.infer<typeof listSchema>

productRouter.get(
  '/list',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER', 'ORG_BASIC']),
  (req, res) => {
    return validate({ schema: listSchema, data: req.query })
      .andThen((data) => {
        return findProductsForOrg({
          orgId: req.orgId,
          filter: data.filter,
          search: data.search,
          page: data.page,
          pageSize: data.pageSize,
        })
      })
      .match(
        (data) => {
          return res.json({
            products: data.products,
            filters: [
              {
                name: 'All',
                count: data.all,
              },
              {
                name: 'Personalized',
                count: data.personalized,
              },
              {
                name: 'Virtual',
                count: data.virtual,
              },
              {
                name: 'Physical',
                count: data.physical,
              },
            ],
          })
        },
        (e) => {
          return error({
            res,
            errorType: e,
          })
        }
      )
  }
)

productRouter.get(
  '/csv',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER', 'ORG_BASIC']),
  (req, res) => {
    return findProductsForOrgCSV({
      orgId: req.orgId!,
    }).match(
      (data) => {
        const { orgProducts, otherProducts } = data.reduce<{
          orgProducts: string[]
          otherProducts: string[]
        }>(
          (acc, product) => {
            const courier = product.deliveryMethods.includes('COURIER')
              ? 'yes'
              : 'no'
            const email = product.deliveryMethods.includes('EMAIL')
              ? 'yes'
              : 'no'
            const pickup = product.deliveryMethods.includes('PICKUP')
              ? 'yes'
              : 'no'
            const min = product.minValue ? centsToDollars(product.minValue) : ''
            const max = product.minValue ? centsToDollars(product.maxValue) : ''
            const fixed =
              min === '' && max === ''
                ? product.fixedValues.map((value) => {
                    return centsToDollars(value)
                  })
                : ''

            const productCsvString = [
              product.productCode,
              product.name,
              courier,
              email,
              pickup,
              min,
              max,
              fixed === '' ? fixed : `"${JSON.stringify(fixed)}"`,
            ].join(',')

            if (product.organizationId) {
              acc.orgProducts.push(productCsvString)
            } else {
              acc.otherProducts.push(productCsvString)
            }

            return acc
          },
          {
            orgProducts: [],
            otherProducts: [],
          }
        )

        let csvList

        if (orgProducts.length > 0) {
          csvList = [
            'Product code,Product name,Courier,Email,Pickup,Min,Max,Fixed',
            orgProducts,
            '',
            otherProducts,
          ].flat()
        } else {
          csvList = [
            'Product code,Product name,Courier,Email,Pickup,Min,Max,Fixed',
            otherProducts,
          ].flat()
        }

        return res.json({
          csvString: csvList.join('\r\n'),
        })
      },
      (e) => {
        return error({
          res,
          errorType: e,
        })
      }
    )
  }
)

const productSchema = z.object({
  productCode: z.string().transform((productCode) => {
    const number = Number(productCode)

    if (!number) {
      throw new Error('Invalid number format')
    }

    return number
  }),
})

productRouter.get('/:productCode', async (req, res) => {
  const input = await validateWithErrorData({
    schema: productSchema,
    data: req.params,
  })

  if (input.isErr()) {
    return res.status(400).json(input.error)
  }

  const product = await findProductWithDiscountForOrg({
    productCode: input.value.productCode,
    orgId: req.orgId!,
  })

  if (product.isErr()) {
    return res.status(400).json(product.error)
  }

  return res.status(200).json(product.value)
})
