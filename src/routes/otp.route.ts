import express from 'express'
import 'express-async-errors' // required in all router files

import { z } from 'zod'
import { validateWithErrorData } from 'utils/validation'
import { checkAuth0RedirectJwt } from 'middlewares/check-jwt'
import {
  createRedirectToken,
  getPhoneOtpNumberForUser,
} from 'services/otp.service'
import { ERRORS } from 'utils/error'
import {
  sendSMSChallenge,
  sendWhatsAppChallenge,
  verificationCheck,
} from 'services/twilio.service'
import { saveUserPhoneNumber } from 'services/otp.service'
import logger from 'utils/logger'

export const otpRouter = express.Router()

// This needs a valid auth0 JWT token which we will exchange for a new EPAY JWT token
otpRouter.post('/send', checkAuth0RedirectJwt, async (req, res) => {
  logger.inspect(req.body, 'body')

  const input = await validateWithErrorData({
    schema: z.object({
      phoneNumber: z.string(),
      channel: z.enum(['sms', 'whatsapp']),
    }),
    data: {
      // Phone number will be in auth token when they have a verified phone in auth0
      phoneNumber: req.body?.phoneNumber || req.phoneNumber,
      channel: req.body?.channel || 'whatsapp',
    },
  })

  logger.inspect(input, 'input')

  if (input.isErr()) {
    return res.status(400).json(input.error)
  }

  // We still need this so that the phone number can be stored against the userId
  const savePhoneNumberResult = await saveUserPhoneNumber({
    userId: req.userId!,
    phoneNumber: input.value.phoneNumber,
  })

  if (savePhoneNumberResult.isErr()) {
    return res.status(400).json(savePhoneNumberResult.error)
  }

  if (input.value.channel === 'whatsapp') {
    const result = await sendWhatsAppChallenge(input.value.phoneNumber)

    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    return res.json(result)
  } else {
    const result = await sendSMSChallenge(input.value.phoneNumber)

    if (result.isErr()) {
      return res.status(400).json(result.error)
    }

    return res.json(result)
  }
})

otpRouter.post('/verify', checkAuth0RedirectJwt, async (req, res) => {
  const input = await validateWithErrorData({
    schema: z.object({
      otp: z.string(),
      state: z.string(),
    }),
    data: req.body,
  })

  if (input.isErr()) {
    return res.status(400).json(input.error)
  }

  const phone = await getPhoneOtpNumberForUser(req.userId!)
  if (phone.isErr()) {
    return res.status(400).json(phone.error)
  }

  const result = await verificationCheck({
    phoneNumber: phone.value,
    code: input.value.otp,
  })

  if (result.isErr()) {
    return res.status(400).json(result.error)
  } else {
    if (!result.value.valid) {
      return res.status(400).json({
        code: ERRORS.UNAUTHORIZED.code,
        message: 'Invalid OTP',
      })
    }
  }

  const responseToken = createRedirectToken({
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion, @typescript-eslint/no-non-null-asserted-optional-chain
    authToken: req.headers.authorization?.split(' ')[1]!,
    state: input.value.state,
    phoneNumber: result.value.phoneNumber!,
  })

  return res.status(200).json({ responseToken })
})
