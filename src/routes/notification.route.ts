// *************************************************************************************************
// This file contains the routes for THIRD PARTY integrations ONLY
// *************************************************************************************************
import express from 'express'
import 'express-async-errors' // required in all router files

import z from 'zod'

import { validate } from 'utils/validation'
import { validateProductOrderSession } from 'services/windcave.service'
import { approveCustomProduct } from 'services/product.service'
import { checkApiKey } from 'middlewares/check-api-key'
import { PLACARD_API_KEY } from 'utils/config'
import logger from 'utils/logger'
import { notifyTeam } from 'services/postmark.service'

export const notificationRouter = express.Router()

const productOrderPaymentSchema = z.object({
  sessionId: z.string(),
})

// This is a notification from windcave that the payment has been made
// DO NOT REMOVE OR RENAME THIS ENDPOINT - unless you check with the windcave team
notificationRouter.get('/product/order/payment', async (req, res) => {
  return validate({
    schema: productOrderPaymentSchema,
    data: req.query,
  })
    .andThen((data) => {
      return validateProductOrderSession({ id: data.sessionId })
    })
    .match(
      () => {
        return res.send('ok')
      },
      (e) => {
        logger.warn('Something went wrong in validating windcave session', e)

        // Returning ok, to aviod windcave making request again (ending up in a loop)
        return res.send('ok')
      }
    )
})

const logoReadySchema = z.object({
  logo: z.string(),
})

// This is a notification from placard that the logo is ready
// DO NOT REMOVE OR RENAME THIS ENDPOINT - unless you check with the placard team
notificationRouter.post(
  '/logo-ready',
  checkApiKey(PLACARD_API_KEY),
  async (req, res) => {
    logger.info('logo ready from placard =', req.body)

    return validate({ schema: logoReadySchema, data: req.body })
      .andThen((data) => {
        return approveCustomProduct({ logo: data.logo })
      })
      .match(
        (data) => {
          logger.info('logo ready', data.logo)
          notifyTeam({
            summary: `[logo] - acknowledged by Placard`,
            description: `[${JSON.stringify(data)}]`,
          })
          return res.status(200).json({
            message: 'acknowledged',
            logo: data.logo,
            logoName: data.logoName,
            productName: data.productName,
            logoUrl: data.logoUrl,
          })
        },
        (e) => {
          notifyTeam({
            summary: `[logo] - error when acknowledged by Placard`,
            description: `Logo: ${req.body.logo}\nError: ${e}`,
          })
          logger.error(`error in logo ready, ${e}`)
          return res.status(400).json(e)
        }
      )
  }
)
