import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'
import { updateCustomProductName } from 'services/product.service'
import { validateWithErrorData } from 'utils/validation'

export const epayProductRouter = express.Router()

epayProductRouter.post(
  '/:productId/update-name',
  authorizeRole(['EPAY_ADMIN', 'EPAY_COSTUMER_SERVICE']),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        productId: z.string(),
        productName: z.string(),
      }),
      data: {
        ...req.params,
        ...req.body,
      },
    })
      .andThen((data) => {
        return updateCustomProductName({
          productId: data.productId,
          productName: data.productName,
        })
      })
      .match(
        () => res.status(200).json({ status: 'OK' }),
        (err) => {
          return res.status(400).json({ err })
        }
      )
  }
)
