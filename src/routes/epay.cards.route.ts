import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { ADMIN_ROLES } from 'constants/admin'
import { authorizeRole } from 'middlewares/authorize-role'
import {
  
  findCardByCrn,
  listCardsWithFilters,
} from 'services/card.service'
import { transformStringToNumber, validate } from 'utils/validation'

import { blockManyCardsByCrnV2 } from 'services/product-order/block-cards.service'
import { activateCardsByCrns } from 'services/product-order/activate-cards.service'

export const epayCardsRouter = express.Router()

const activateAndBlockSchema = z.object({
  cardReferenceIds: z.array(z.string()),
})

epayCardsRouter.get('/list', authorizeRole(ADMIN_ROLES), async (req, res) => {
  const input = await validate({
    data: req.query,
    schema: z.object({
      filter: z.string().optional(), // CRN, Order ID or Company name
      page: transformStringToNumber({ defaultNumber: 1 }),
      pageSize: transformStringToNumber({ defaultNumber: 10 }),
    }),
  })

  if (input.isErr()) {
    return res.status(400).json(input.error)
  }

  const cardsListWithFilter = await listCardsWithFilters({
    orgName: input.value.filter,
    orderId: input.value.filter,
    referenceId: input.value.filter,
    page: input.value.page,
    pageSize: input.value.pageSize,
  })

  if (cardsListWithFilter.isErr()) {
    return res.status(400).json(cardsListWithFilter.error)
  }

  return res.status(200).json(cardsListWithFilter.value)
})

epayCardsRouter.get(
  '/crn/:id',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    const input = await validate({
      data: req.params,
      schema: z.object({ id: z.string() }),
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const card = await findCardByCrn(input.value.id)

    if (card.isErr()) {
      return res.status(400).json(card.error)
    }

    return res.status(200).json(card.value)
  }
)

epayCardsRouter.post(
  '/activate',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validate({
      schema: activateAndBlockSchema,
      data: req.body,
    })
      .andThen((data) => {
        return activateCardsByCrns({
          crns: data.cardReferenceIds,
        })
      })
      .match(
        (data) => {
          return res.json({ activated: data.totalActivated })
        },
        (e) => {
          return res.status(400).json(e)
        }
      )
  }
)

epayCardsRouter.post('/block', authorizeRole(ADMIN_ROLES), async (req, res) => {
  return validate({
    schema: activateAndBlockSchema,
    data: req.body,
  })
    .andThen((data) => {
      return blockManyCardsByCrnV2({
        crns: data.cardReferenceIds,
      })
    })
    .match(
      (data) => {
        return res.json({ blocked: data.length })
      },
      (e) => {
        return res.status(400).json(e)
      }
    )
})
