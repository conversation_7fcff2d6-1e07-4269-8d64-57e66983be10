import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'

import { getUserSchema } from './user.route'

import {
  validate,
  validateInput,
  validateWithErrorData,
} from 'utils/validation'

import { error } from 'utils/error'
import logger from 'utils/logger'
import {
  getEpayUsers,
  findUserWithoutOrg,
  deleteUserCompletely,
} from 'services/user.service'
import { log } from 'console'
import { UserRole } from '@prisma/client'
import { createEpayUser, updateEpayUser } from 'services/epay-admin.service'
import {
  addSuperAdminToOrg,
  revokeSuperAdmin,
} from 'services/super-admin.service'

export const epayUserRouter = express.Router()

epayUserRouter.get('/list', authorizeRole(['EPAY_ADMIN']), async (req, res) => {
  return getEpayUsers().match(
    (users) => {
      logger.info('epayUsers =', users)

      return res.json({ users })
    },
    (err) => {
      return error({ res, errorType: err })
    }
  )
})

epayUserRouter.get('/:id', authorizeRole(['EPAY_ADMIN']), async (req, res) => {
  return validate({ schema: getUserSchema, data: req.params })
    .andThen((data) => {
      return findUserWithoutOrg({ id: data.id })
    })
    .match(
      (user) => {
        logger.info('epayUser =', user)

        return res.json({
          user: {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            role: user.role,
          },
        })
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
})

const updateUserValidationSchema = z.object({
  id: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  role: z
    .nativeEnum({
      [UserRole.EPAY_ADMIN]: UserRole.EPAY_ADMIN,
      [UserRole.EPAY_ACCOUNTS]: UserRole.EPAY_ACCOUNTS,
      [UserRole.EPAY_COMPLIANCE]: UserRole.EPAY_COMPLIANCE,
      [UserRole.EPAY_COSTUMER_SERVICE]: UserRole.EPAY_COSTUMER_SERVICE,
      [UserRole.EPAY_REPORTS]: UserRole.EPAY_REPORTS,
    })
    .optional(),
})

export type UpdateEpayUserValidationProps = z.infer<
  typeof updateUserValidationSchema
>

epayUserRouter.post(
  '/:id/update',
  authorizeRole(['EPAY_ADMIN']),
  async (req, res) => {
    const input = await validateInput({
      schema: updateUserValidationSchema,
      data: { ...req.params, ...req.body },
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const updateUserResult = await updateEpayUser({
      userId: input.value.id,
      firstName: input.value.firstName!,
      lastName: input.value.lastName!,
      email: input.value.email!,
      role: input.value.role!,
      requesterRole: req.role!,
    })

    if (updateUserResult.isErr()) {
      return res.status(400).json(updateUserResult.error)
    }

    return res.status(200).json(updateUserResult.value)
  }
)

const createEpayUserValidationSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string(),
  role: z.enum([
    UserRole.EPAY_ADMIN,
    UserRole.EPAY_ACCOUNTS,
    UserRole.EPAY_COMPLIANCE,
    UserRole.EPAY_COSTUMER_SERVICE,
    UserRole.EPAY_REPORTS,
  ]),
})

export type CreateEpayUserValidationProps = z.infer<
  typeof createEpayUserValidationSchema
>

epayUserRouter.post(
  '/create',
  authorizeRole(['EPAY_ADMIN']),
  async (req, res) => {
    logger.info('create epayUser', req.body)

    const input = await validateInput({
      schema: createEpayUserValidationSchema,
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const newUser = await createEpayUser({
      firstName: input.value.firstName,
      lastName: input.value.lastName,
      email: input.value.email,
      role: input.value.role,
      requesterRole: req.role!,
    })

    if (newUser.isErr()) {
      return res.status(400).json(newUser.error)
    }

    return res.json(newUser.value)
  }
)

const deleteSchema = z.object({
  id: z.string(),
})

epayUserRouter.post(
  '/:id/delete',
  authorizeRole(['EPAY_ADMIN']),
  async (req, res) => {
    const input = await validateInput({
      schema: deleteSchema,
      data: req.params,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const deleteUserResult = await deleteUserCompletely({
      userId: input.value.id,
      orgId: req.orgId!,
    })

    if (deleteUserResult.isErr()) {
      return res.status(400).json(deleteUserResult.error)
    }

    return res.json(deleteUserResult.value)
  }
)

const superAdminAddSchema = z.object({
  userEmail: z.string(),
  targetOrgId: z.string(),
})

epayUserRouter.post(
  '/super-admin/add',
  authorizeRole(['EPAY_ADMIN']),
  async (req, res) => {
    const input = await validateInput({
      schema: superAdminAddSchema,
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    return addSuperAdminToOrg({
      userId: req.userId!,
      userEmail: input.value.userEmail,
      targetOrgId: input.value.targetOrgId,
    }).match(
      (result) => {
        return res.status(200).json(result)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
  }
)

const superAdminRevokeSchema = z.object({
  userEmail: z.string(),
})

epayUserRouter.post(
  '/super-admin/revoke',
  authorizeRole(['EPAY_ADMIN']),
  async (req, res) => {
    const input = await validateInput({
      schema: superAdminRevokeSchema,
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    return revokeSuperAdmin({
      userEmail: input.value.userEmail,
      userId: req.userId!,
    }).match(
      (result) => {
        return res.status(200).json(result)
      },
      (error) => {
        return res.status(400).json(error)
      }
    )
  }
)
