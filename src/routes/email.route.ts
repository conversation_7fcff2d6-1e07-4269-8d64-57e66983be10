import 'express-async-errors' // required in all router files
import express from 'express'
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'

import { validateWithErrorData } from 'utils/validation'
import { sendContactSupportEmail } from 'services/email.service'
import { UserRole } from '@prisma/client'

export const emailRouter = express.Router()

const helpEmailSchema = z.object({
  name: z.string(),
  customerEmail: z.string().email(),
  message: z.string(),
  subject: z.string(),
  phoneNumber: z.string(),
})

export type HelpEmail = z.infer<typeof helpEmailSchema>

emailRouter.post(
  '/help',
  authorizeRole([UserRole.ORG_ADMIN, UserRole.ORG_BASIC, UserRole.ORG_MANAGER]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: helpEmailSchema,
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).json(input)
    }

    const { name, customerEmail, message, subject, phoneNumber } = input.value

    const sendEmail = await sendContactSupportEmail({
      name,
      customerEmail,
      message,
      subject,
      phoneNumber,
    })

    if (sendEmail.isErr()) {
      return res.status(500).send(sendEmail.error)
    }

    return res.status(200).json(sendEmail.value)
  }
)
