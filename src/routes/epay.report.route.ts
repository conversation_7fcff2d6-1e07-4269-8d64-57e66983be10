import {
  crnReportDefinition,
  customerDetailsReportDefinition,
  productOrderReportDefinition,
} from 'data/epay-report.data'
import { getReportJobStatus } from 'epay-data/epay-report-job'
import express from 'express'
import 'express-async-errors' // required in all router files
import { authorizeRole } from 'middlewares/authorize-role'
import {
  createAndProcessReportJob,
  getSignedUrlForReport,
} from 'services/report.service'

import { validateWithErrorData } from 'utils/validation'
import { z } from 'zod'

export const epayReportRouter = express.Router()

const DEFAULT_START_DATE = new Date('2023-11-01')
const DEFAULT_END_DATE = new Date()

const baseReportSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

const purchaseReportSchema = baseReportSchema.extend({
  useReleaseDate: z.boolean().optional().default(false),
  orderNumber: z.string().optional(),
})

epayReportRouter.get(
  '/job/:jobId',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.string(),
      data: req.params.jobId,
    })
      .andThen((jobId) =>
        getReportJobStatus({
          jobId,
          userId: req.userId!,
        })
      )
      .match(
        (data) => res.status(200).json(data),
        (error) => res.status(400).json(error)
      )
  }
)

// Update to the CRNs by order date endpoint
epayReportRouter.post(
  '/crns-by-order-date-csv',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validateWithErrorData({
      schema: purchaseReportSchema,
      data: req.body,
    })
      .andThen((input) => {
        // Always provide start and end dates
        const startDate = input.startDate
          ? new Date(input.startDate)
          : DEFAULT_START_DATE

        const endDate = input.endDate
          ? new Date(input.endDate)
          : DEFAULT_END_DATE

        return createAndProcessReportJob({
          reportDef: crnReportDefinition,
          startDate,
          endDate,
          userId: req.userId!,
          filters: {
            useReleaseDate: input.useReleaseDate,
            orderNumber: input.orderNumber,
          },
        })
      })
      .match(
        (jobId) => res.status(200).json({ jobId }),
        (error) => res.status(400).json(error)
      )
  }
)

// Update to the product orders by order date endpoint
epayReportRouter.post(
  '/product-orders-by-order-date-csv',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validateWithErrorData({
      schema: purchaseReportSchema,
      data: req.body,
    })
      .andThen((input) => {
        // Always provide start and end dates
        const startDate = input.startDate
          ? new Date(input.startDate)
          : DEFAULT_START_DATE

        const endDate = input.endDate
          ? new Date(input.endDate)
          : DEFAULT_END_DATE

        return createAndProcessReportJob({
          reportDef: productOrderReportDefinition,
          startDate,
          endDate,
          userId: req.userId!,
          filters: {
            useReleaseDate: input.useReleaseDate,
            orderNumber: input.orderNumber,
          },
        })
      })
      .match(
        (jobId) => res.status(200).json({ jobId }),
        (error) => res.status(400).json(error)
      )
  }
)

epayReportRouter.post(
  '/customer-details',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validateWithErrorData({
      schema: baseReportSchema,
      data: req.body,
    })
      .andThen((input) => {
        const startDate = input.startDate
          ? new Date(input.startDate)
          : new Date()
        const endDate = input.endDate
          ? new Date(input.endDate)
          : new Date(startDate.setDate(startDate.getDate() - 30))

        return createAndProcessReportJob({
          reportDef: customerDetailsReportDefinition,
          startDate,
          endDate,
          userId: req.userId!,
        })
      })
      .match(
        (jobId) => res.status(200).json({ jobId }),
        (error) => res.status(400).json(error)
      )
  }
)

epayReportRouter.get(
  '/signed-url',
  authorizeRole(['EPAY_ACCOUNTS', 'EPAY_ADMIN']),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        key: z.string(),
      }),
      data: req.query,
    })
      .andThen((data) => getSignedUrlForReport(data.key))
      .match(
        (data) => res.status(200).json(data),
        (error) => res.status(400).json(error)
      )
  }
)
