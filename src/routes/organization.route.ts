import { OrganizationType, UserRole } from '@prisma/client'

import express from 'express'
import 'express-async-errors' // required in all router files
import { z } from 'zod'

import { authorizeRole } from 'middlewares/authorize-role'

import {
  createOrganisationForSkipKycUser,
  getCompanyNameFromInfolog,
  getOrganizationUsersFromOrgId,
} from 'services/organization.service'

import { searchInfologAndVerifyNZBN } from 'services/infolog.service'
import { error } from 'utils/error'
import logger from 'utils/logger'
import { validateWithErrorData } from 'utils/validation'
import {
  getEnableStockCardsValueForOrg,
  setBrandedStockCardsValueForOrg,
  setCustomerOrderReference,
  setOrderVisibilityRestrictionForOrg,
} from 'data/organization.data'

export const organizationRouter = express.Router()

const createOrganizationSchema = z.object({
  orgName: z.string(),
  nzbn: z.string(),
  organizationType: z.nativeEnum(OrganizationType),
  firstName: z.string(),
  lastName: z.string(),
})

export type CreateOrganization = z.infer<typeof createOrganizationSchema>

organizationRouter.post(
  '/create/basic',
  authorizeRole([UserRole.ORG_ADMIN]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: createOrganizationSchema,
      data: req.body,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const result = await createOrganisationForSkipKycUser({
      orgName: input.value.orgName,
      nzbn: input.value.nzbn,
      organizationType: input.value.organizationType,
      userId: req.userId!,
      firstName: input.value.firstName,
      lastName: input.value.lastName,
      email: req.userEmail!,
    })

    if (result.isErr()) {
      return res.status(400).send(result.error)
    }

    return res.status(200).json(result.value)
  }
)

organizationRouter.get(
  '/users',
  authorizeRole(['ORG_ADMIN', 'ORG_MANAGER']),
  async (req, res) => {
    return getOrganizationUsersFromOrgId({ orgId: req.orgId! }).match(
      (users) => {
        return res.json({ users })
      },
      (err) => {
        return error({ res, errorType: err })
      }
    )
  }
)

organizationRouter.get(
  '/nzbn/verify',
  authorizeRole(['ORG_ADMIN']),
  async (req, res) => {
    logger.debug('params', req.query)
    const input = await validateWithErrorData({
      schema: z.object({
        nzbn: z.string(),
        companyName: z.string(),
        corporateApplicationId: z.string(),
      }),
      data: req.query,
    })

    if (input.isErr()) {
      return res.status(400).json(input.error)
    }

    const result = await searchInfologAndVerifyNZBN({
      nzbn: input.value.nzbn,
      companyName: input.value.companyName,
      corporateApplicationId: input.value.corporateApplicationId,
      userId: req.userId!,
    })

    return result.match(
      (successValue) => {
        return res.status(200).json(successValue)
      },
      (errorValue) => {
        return res.status(400).send(errorValue)
      }
    )
  }
)

organizationRouter.get(
  '/create-with-nzbn',
  authorizeRole([UserRole.ORG_ADMIN]),
  async (req, res) => {
    const input = await validateWithErrorData({
      schema: z.object({ nzbn: z.string() }),
      data: req.query,
    })

    if (input.isErr()) {
      return res.status(400).send(input.error)
    }

    const result = await getCompanyNameFromInfolog({ nzbn: input.value.nzbn })

    return result.match(
      (successValue) => {
        return res.status(200).json(successValue)
      },
      (errorValue) => {
        return res.status(400).send(errorValue)
      }
    )
  }
)

organizationRouter.put(
  '/order-reference',
  authorizeRole([UserRole.ORG_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        enabled: z.boolean(),
      }),
      data: { enabled: req.body.enabled },
    })
      .andThen((data) => {
        return setCustomerOrderReference({
          orgId: req.orgId!,
          enabled: data.enabled,
        })
      })
      .match(
        () => {
          return res.status(200).json({ message: 'success' })
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

organizationRouter.get('/branded-stock-cards', async (req, res) => {
  return getEnableStockCardsValueForOrg(req.orgId!).match(
    (value) => {
      return res.status(200).json(value)
    },
    (err) => {
      return res.status(400).json(err)
    }
  )
})

organizationRouter.put(
  '/branded-stock-cards',
  authorizeRole([UserRole.ORG_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        enabled: z.boolean(),
      }),
      data: { enabled: req.body.enabled },
    })
      .andThen((data) => {
        return setBrandedStockCardsValueForOrg({
          orgId: req.orgId!,
          enabled: data.enabled,
        })
      })
      .match(
        () => {
          return res.status(200).json({ message: 'success' })
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

organizationRouter.put(
  '/order-visibility-restriction',
  authorizeRole([UserRole.ORG_ADMIN]),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        enabled: z.boolean(),
      }),
      data: { enabled: req.body.enabled },
    })
      .andThen((data) => {
        return setOrderVisibilityRestrictionForOrg({
          orgId: req.orgId!,
          enabled: data.enabled,
        })
      })
      .match(
        () => {
          return res.status(200).json({ message: 'success' })
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)
