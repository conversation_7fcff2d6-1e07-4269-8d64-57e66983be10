import { ADMIN_ROLES } from 'constants/admin'
import {
  getGlobalDiscounts,
  globalDiscountUpdateSchema,
  updateGlobalDiscounts,
} from 'data/discount.data'
import express from 'express'
import 'express-async-errors' // required in all router files
import { authorizeRole } from 'middlewares/authorize-role'
import {
  getOrganizationsWithDiscounts,
  updateOrgDiscounts,
} from 'services/organization.service'
import {
  transformStringToNumber,
  validate,
  validateWithErrorData,
} from 'utils/validation'
import { z } from 'zod'

export const epayDiscountsRouter = express.Router()

export const epayDiscountsListSchema = z.object({
  query: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

epayDiscountsRouter.get(
  '/organization/list',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validate({
      schema: epayDiscountsListSchema,
      data: req.query,
    })
      .andThen((data) => {
        return getOrganizationsWithDiscounts({
          page: data.page,
          pageSize: data.pageSize,
          orgName: data.query,
        })
      })
      .match(
        (organizations) => {
          return res.json(organizations)
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayDiscountsRouter.post(
  '/organization/:orgId/update',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validateWithErrorData({
      schema: z.object({
        orgId: z.string(),
        loadingFee: z.number().optional().default(0),
        digitalFee: z.number().optional().default(0),
        shippingFee: z.number().optional().default(0),
      }),
      data: {
        ...req.body,
        orgId: req.params.orgId,
      },
    })
      .andThen((data) => {
        return updateOrgDiscounts({
          orgId: data.orgId,
          loadingFee: data.loadingFee,
          digitalFee: data.digitalFee,
          shippingFee: data.shippingFee,
        })
      })
      .match(
        (organization) => {
          return res.json(organization)
        },
        (err) => {
          return res.status(400).json(err)
        }
      )
  }
)

epayDiscountsRouter.get(
  '/global',
  authorizeRole(ADMIN_ROLES),
  async (_, res) => {
    return getGlobalDiscounts().match(
      (discount) => res.json(discount),
      (err) => res.status(400).json(err)
    )
  }
)

epayDiscountsRouter.put(
  '/global',
  authorizeRole(ADMIN_ROLES),
  async (req, res) => {
    return validateWithErrorData({
      schema: globalDiscountUpdateSchema,
      data: {
        ...req.body,
        userId: req.userId,
      },
    })
      .andThen(updateGlobalDiscounts)
      .match(
        (discount) => res.json(discount),
        (err) => res.status(400).json(err)
      )
  }
)
