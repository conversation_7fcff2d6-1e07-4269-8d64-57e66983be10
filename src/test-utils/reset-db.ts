import { prisma } from 'utils/prisma'

export default async () => {
  await prisma.$transaction([
    prisma.beneficialOwner.deleteMany({
      where: {
        firstName: 'test',
      },
    }),
    prisma.director.deleteMany({
      where: {
        firstName: 'test',
      },
    }),
    prisma.corporateApplication.deleteMany({
      where: {
        userId: {
          startsWith: 'test',
        },
      },
    }),
    prisma.user.deleteMany({
      where: {
        id: {
          startsWith: 'test',
        },
      },
    }),
    prisma.customProductOrder.deleteMany({
      where: {
        id: {
          startsWith: 'test',
        },
      },
    }),
    prisma.productOrder.deleteMany({
      where: {
        id: {
          startsWith: 'test',
        },
      },
    }),
  ])
}
