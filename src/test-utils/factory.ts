import type {
  Ben<PERSON>icialOwn<PERSON>,
  CustomProduct<PERSON>rde<PERSON>,
  Director,
  ProductOrder,
  UserRole,
} from '@prisma/client'

import { v4 as uuidv4 } from 'uuid'

export function getUser(role: UserRole = 'ORG_ADMIN') {
  const id = uuidv4().replace(/.{4}/, 'test')
  const email = `'test-${id.slice(4)}@email.com'`
  return { id, role, email }
}

export function getDirector(): Partial<Director> {
  return {
    firstName: 'test',
    lastName: 'user',
    identificationType: 'DRIVER_LICENSE',
    identificationNbr: uuidv4(),
    driverLicenseVersion: uuidv4(),
  }
}

export function getBeneficialOwner(): Partial<BeneficialOwner> {
  return {
    firstName: 'test',
    lastName: 'user',
    identificationType: 'PASSPORT',
    identificationNbr: uuidv4(),
  }
}
