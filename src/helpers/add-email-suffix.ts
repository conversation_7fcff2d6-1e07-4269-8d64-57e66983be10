type EmailParts = {
  localPart: string
  domain: string
  fullEmail: string
}

export function appendEmailSuffix(email: string, suffix: string): EmailParts {
  const atIndex = email.indexOf('@')
  if (atIndex === -1) {
    throw new Error('Invalid email: missing "@" symbol.')
  }

  // Strip any leading '+' or '_' characters from the suffix.
  const normalizedSuffix = suffix.replace(/^[+_]+/, '')

  const local = email.slice(0, atIndex)
  const domain = email.slice(atIndex + 1)

  const plusIndex = local.indexOf('+')
  let localPart: string

  if (plusIndex === -1) {
    // No plus sign exists, so add one then the normalised suffix.
    localPart = `${local}+${normalizedSuffix}`
  } else {
    // A plus sign exists, so append an underscore and the normalised suffix.
    const base = local.slice(0, plusIndex + 1) // e.g. "nathan+"
    const currentSuffix = local.slice(plusIndex + 1) // e.g. "sample" if present
    localPart = `${base}${currentSuffix}_${normalizedSuffix}`
  }

  return { localPart, domain, fullEmail: `${localPart}@${domain}` }
}
