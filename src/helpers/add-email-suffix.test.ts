import { describe, it, expect } from 'vitest'
import { appendEmailSuffix } from "./add-email-suffix";

describe('appendEmailSuffix', () => {
  // --- Valid email without a plus sign ---
  it('appends suffix correctly when no plus sign exists', () => {
    const result = appendEmailSuffix('<EMAIL>', 'dev')
    expect(result.localPart).toBe('john+dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })

  // --- Valid email with a plus sign ---
  it('appends suffix correctly when a plus sign exists in the local part', () => {
    const result = appendEmailSuffix('<EMAIL>', 'dev')
    expect(result.localPart).toBe('john+test_dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })

  // --- Suffix normalization: Remove leading '+' ---
  it('removes leading "+" from suffix when no plus exists in email', () => {
    const result = appendEmailSuffix('<EMAIL>', '+dev')
    expect(result.localPart).toBe('john+dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })

  it('removes leading "+" from suffix when plus exists in email', () => {
    const result = appendEmailSuffix('<EMAIL>', '+dev')
    expect(result.localPart).toBe('john+test_dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })

  // --- Suffix normalization: Remove leading '_' ---
  it('removes leading "_" from suffix when no plus exists in email', () => {
    const result = appendEmailSuffix('<EMAIL>', '_dev')
    expect(result.localPart).toBe('john+dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })

  it('removes leading "_" from suffix when plus exists in email', () => {
    const result = appendEmailSuffix('<EMAIL>', '_dev')
    expect(result.localPart).toBe('john+test_dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })

  // --- Email with an empty local part ---
  it('handles email with empty local part (only "@" with domain)', () => {
    const result = appendEmailSuffix('@example.com', 'dev')
    expect(result.localPart).toBe('+dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('+<EMAIL>')
  })

  it('handles email with only "@" symbol (empty local and domain)', () => {
    const result = appendEmailSuffix('@', 'dev')
    expect(result.localPart).toBe('+dev')
    expect(result.domain).toBe('')
    expect(result.fullEmail).toBe('+dev@')
  })

  // --- Error cases ---
  it('throws error when email is missing "@"', () => {
    expect(() => appendEmailSuffix('johnexample.com', 'dev'))
      .toThrow('Invalid email: missing "@" symbol.')
  })

  it('throws error when email is an empty string', () => {
    expect(() => appendEmailSuffix('', 'dev'))
      .toThrow('Invalid email: missing "@" symbol.')
  })

  // --- Email with a trailing plus in the local part ---
  it('handles email with a trailing plus in the local part', () => {
    const result = appendEmailSuffix('<EMAIL>', 'dev')
    // local = "john+", currentSuffix is empty, so result is "john+_dev"
    expect(result.localPart).toBe('john+_dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })

  // --- Email with multiple plus signs ---
  it('handles email with multiple plus signs by appending to the group after the first plus', () => {
    const result = appendEmailSuffix('<EMAIL>', 'dev')
    // First plus is at index 4, so:
    //   base = "john+"
    //   currentSuffix = "test+example"
    // Expected localPart: "john+test+example_dev"
    expect(result.localPart).toBe('john+test+example_dev')
    expect(result.domain).toBe('example.com')
    expect(result.fullEmail).toBe('<EMAIL>')
  })
});
