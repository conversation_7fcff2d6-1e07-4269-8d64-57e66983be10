import { describe, it, expect } from 'vitest'

import {
  splitAddress,
  splitAddressByComma,
  splitAddressBySpace,
} from 'helpers/address-splitter'

describe('splitAddress', () => {
  it('should split first part before comma and remove comma and space before second part', async () => {
    const fullAddress =
      'Life Pharmacy North City, North City Shopping Centre, Shop 125/2 Titahi Bay Road'
    const addresses = splitAddressByComma(fullAddress)

    expect(addresses.address1).toEqual('Life Pharmacy North City')
    expect(addresses.address2).toEqual('North City Shopping Centre')
  })

  it('should split first part before comma and remove comma and space before second part', async () => {
    const fullAddress =
      'Nufarm NZ, Central Park Corporate Centre, Building 3, Level 4, 666 Great South Road, Ellerslie, Auckland 1051'
    const addresses = splitAddressByComma(fullAddress)

    expect(addresses.address1).toEqual(
      'Nufarm NZ, Central Park Corporate Centre'
    )
    expect(addresses.address2).toEqual('Building 3, Level 4')
    expect(addresses.address3).toEqual('666 Great South Road, Ellerslie')
    expect(addresses.address4).toEqual('Auckland 1051')
    expect(addresses.address5).toEqual(undefined)
  })

  it('should NOT split if character length less than 60', async () => {
    const fullAddress = 'Life Pharmacy North City, North'
    const addresses = splitAddressByComma(fullAddress)

    expect(addresses.address1).toEqual('Life Pharmacy North City, North')
    expect(addresses.address2).toEqual(undefined)
  })

  // This will cause our order to fail but we will handle it in the next release
  it('should NOT split when over 60 characters with NO commas', async () => {
    const fullAddress =
      'Life Pharmacy North City North City Shopping Centre Shop 125/2 Titahi Bay Road This is a long address taht is over 60 characters'
    const addresses = splitAddressByComma(fullAddress)

    expect(addresses.address1).toEqual(fullAddress)
    expect(addresses.address2).toEqual(undefined)
  })
})

it('should combine address1 and address2 if their combined length is less than 60 characters', async () => {
  const fullAddress = '123 Main St, Suite 200, Building 3'
  const addresses = splitAddressByComma(fullAddress)

  expect(addresses.address1).toEqual('123 Main St, Suite 200, Building 3')
  expect(addresses.address2).toEqual(undefined)
})

it('should split into address3 and address4 if address3 is greater than 60 characters', async () => {
  const fullAddress =
    '123 Main St, Suite 200, Building 3, City, Some Long Street Name That Exceeds Sixty Characters, Another Address Part'
  const addresses = splitAddressByComma(fullAddress)

  expect(addresses.address1).toEqual('123 Main St, Suite 200, Building 3, City')
  expect(addresses.address2).toEqual(
    'Some Long Street Name That Exceeds Sixty Characters'
  )
  expect(addresses.address3).toEqual('Another Address Part')
  expect(addresses.address4).toEqual(undefined)
})

it('should handle very short addresses correctly', async () => {
  const fullAddress = 'Short Address'
  const addresses = splitAddressByComma(fullAddress)

  expect(addresses.address1).toEqual('Short Address')
  expect(addresses.address2).toEqual(undefined)
})

it('should handle addresses that exactly hit the 40-character mark', async () => {
  const fullAddress = '123 Main St, Suite 200, Building 3, City'
  const addresses = splitAddressByComma(fullAddress)

  expect(addresses.address1).toEqual('123 Main St, Suite 200, Building 3, City')
  expect(addresses.address2).toEqual(undefined)
  expect(addresses.address3).toEqual(undefined)
})

describe('splitAddressBySpace', () => {
  it('should handle an address exactly 40 characters long', () => {
    const address = '123 Main St, Apt 4, Springfield, IL 62701'
    const result = splitAddressBySpace(address)
    expect(result.address1).toBe('123 Main St, Apt 4, Springfield, IL')
    expect(result.address2).toBe('62701')
  })

  it('should split an address with a word exactly at the 40 character mark', () => {
    const address = '123 Main St, Apt 4, Springfield, Illinois 62701'
    const result = splitAddressBySpace(address)
    expect(result.address1).toBe('123 Main St, Apt 4, Springfield,')
    expect(result.address2).toBe('Illinois 62701')
  })

  it('should handle an address with a very long word', () => {
    const address = '123 SupercalifragilisticexpialidociousStreet, NY 12345'
    const result = splitAddressBySpace(address)
    expect(result.address1).toBe('123')
    expect(result.address2).toBe('SupercalifragilisticexpialidociousStreet,')
    expect(result.address3).toBe('NY 12345')
  })

  it('should handle an address with multiple commas', () => {
    const address =
      '123, Main Street, Apt 4, Building A Springfield, IL, 62701, USA'
    const result = splitAddressBySpace(address)
    expect(result.address1).toBe('123, Main Street, Apt 4, Building A')
    expect(result.address2).toBe('Springfield, IL, 62701, USA')
  })

  it('should handle an address with no spaces', () => {
    const address = '123MainStreet,Springfield,IL62701'
    const result = splitAddressBySpace(address)
    expect(result.address1).toBe('123MainStreet,Springfield,IL62701')
    expect(result.address2).toBeUndefined()
  })

  it('should handle an empty address', () => {
    const address = ''
    const result = splitAddressBySpace(address)
    expect(result.address1).toBeUndefined()
    expect(result.address2).toBeUndefined()
  })
})

describe('splitAddress', () => {
  it('should split by comma when there are two or fewer parts', () => {
    const address = '123 Main St, Springfield, IL 62701'
    const result = splitAddress(address)
    expect(result.address1).toBe('123 Main St, Springfield, IL 62701')
    expect(result.address2).toBeUndefined()
  })

  it('should split by space when there are more than two comma-separated parts', () => {
    const address = '123 Main St, Apt 4, Springfield, IL 62701, USA'
    const result = splitAddress(address)
    expect(result.address1).toBe('123 Main St, Apt 4, Springfield')
    expect(result.address2).toBe('IL 62701, USA')
  })

  it('should handle an address with no commas', () => {
    const address = '123 Main Street Springfield IL 62701'
    const result = splitAddress(address)
    expect(result.address1).toBe('123 Main Street Springfield IL 62701')
    expect(result.address2).toBeUndefined()
  })

  it('should handle an address with a very long word', () => {
    const address =
      '123 SupercalifragilisticexpialidociousStreet, Springfield, IL'
    const result = splitAddress(address)
    expect(result.address1).toBe('123')
    expect(result.address2).toBe('SupercalifragilisticexpialidociousStreet,')
  })

  it('should handle an address exactly 40 characters long', () => {
    const address = '5b Luana Way, Apt 4, Bucklands Beach, Auckland 62701'
    const result = splitAddress(address)
    expect(result.address1).toBe('5b Luana Way, Apt 4, Bucklands Beach')
    expect(result.address2).toBe('Auckland 62701')
  })

  it('should handle an empty address', () => {
    const address = ''
    const result = splitAddress(address)
    expect(result.address1).toBeUndefined()
    expect(result.address2).toBeUndefined()
  })

  it('should handle an address with multiple spaces between words', () => {
    const address = '  123   Main   Street,   Springfield,   IL   62701  '
    const result = splitAddress(address)
    expect(result.address1).toBe('123   Main   Street, Springfield')
    expect(result.address2).toBe('IL   62701')
  })

  it('should handle an address with leading/trailing spaces', () => {
    const address = '  123 Main St, Springfield, IL 62701  '
    const result = splitAddress(address)
    expect(result.address1).toBe('123 Main St, Springfield, IL 62701')
    expect(result.address2).toBeUndefined()
  })

  it('should handle an address with only commas', () => {
    const address = ',,,,'
    const result = splitAddress(address)
    expect(result.address1).toBe(',,,,')
    expect(result.address2).toBeUndefined()
  })

  it('should handle an address with a mix of very short and long parts', () => {
    const address = 'A, VeryLongStreetNameThatExceedsFortyCharacters, B, C'
    const result = splitAddress(address)
    expect(result.address1).toBe('A,')
    expect(result.address2).toBe('VeryLongStreetNameThatExceedsFortyCharacters,')
  })

  it('should handle an address with non-ASCII characters', () => {
    const address = '123 Étrange Rue, Città, Région 12345 blah blah blah'
    const result = splitAddress(address)
    expect(result.address1).toBe('123 Étrange Rue, Città')
    expect(result.address2).toBe('Région 12345 blah blah blah')
  })

  it('should handle an address with numbers and special characters', () => {
    const address = '123-A Main St. #456, Building 7/8, Springfield'
    const result = splitAddress(address)
    expect(result.address1).toBe('123-A Main St. #456, Building 7/8')
    expect(result.address2).toBe('Springfield')
  })
})
