export function splitAddressByComma(fullAddress: string) {
  const maxLengthForCourierLabel = 40 // limited by courier labels
  const parts = fullAddress.split(',').map((part) => part.trim()) // Split and trim all parts
  const combinedParts = []

  let currentPart = ''

  for (const part of parts) {
    if (
      (currentPart + (currentPart ? ', ' : '') + part).length <=
      maxLengthForCourierLabel
    ) {
      // If adding this part doesn't exceed the max length, append it
      currentPart += (currentPart ? ', ' : '') + part
    } else {
      // If adding this part would exceed the max length, start a new part
      if (currentPart) {
        combinedParts.push(currentPart)
      }
      currentPart = part
    }
  }

  // Add the last part if it's not empty
  if (currentPart) {
    combinedParts.push(currentPart)
  }

  // Map combinedParts to address1, address2, etc.
  const [address1, address2, address3, address4, address5] = combinedParts

  return { address1, address2, address3, address4, address5 }
}

export function splitAddressBySpace(fullAddress: string) {
  const maxLengthForCourierLabel = 40 // limited by courier labels
  const words = fullAddress.trim().split(/\s+/)
  const parts = []
  let currentPart = ''

  for (const word of words) {
    if (word.length > maxLengthForCourierLabel) {
      // If the current word is longer than the max length, add it as a separate part
      if (currentPart) {
        parts.push(currentPart.trim())
        currentPart = ''
      }
      parts.push(word)
    } else if ((currentPart + ' ' + word).length <= maxLengthForCourierLabel) {
      currentPart += (currentPart ? ' ' : '') + word
    } else {
      parts.push(currentPart.trim())
      currentPart = word
    }
  }

  if (currentPart) {
    parts.push(currentPart.trim())
  }

  // Map parts to address1, address2, etc.
  const [address1, address2, address3, address4, address5] = parts

  return { address1, address2, address3, address4, address5 }
}

export function splitAddress(fullAddress: string) {
  if (!fullAddress) {
    return { address1: undefined, address2: undefined }
  }

  if (fullAddress.length <= 40) {
    return { address1: fullAddress.trim(), address2: undefined }
  }

  const splitByComma = splitAddressByComma(fullAddress)

  const splitBySpace = splitAddressBySpace(fullAddress)

  // Choose splitBySpace if address1 or address2 of splitByComma is greater than 40 characters
  const commaHasOverflow =
    (splitByComma.address1 && splitByComma.address1.length > 40) ||
    (splitByComma.address2 && splitByComma.address2.length > 40)

  const parts =
    commaHasOverflow || splitByComma.address3 ? splitBySpace : splitByComma

  // Ensure we only return at most two parts
  const { address1, address2 } = parts

  return {
    address1: address1 || undefined,
    address2: address2 || undefined,
  }
}
