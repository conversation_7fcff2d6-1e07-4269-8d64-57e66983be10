export function cardExpiryToYYMM(dateString: string): string {
  // Check if the input string matches the expected format
  if (!/^\d{8}$/.test(dateString)) {
    throw new Error('Invalid date format. Expected yyyyMMDD.')
  }

  // Extract the year and month
  const year = dateString.slice(2, 4)
  const month = dateString.slice(4, 6)

  // Combine and return
  return year + month
}

// Example usage:
try {
  const formattedDate = cardExpiryToYYMM('20261031')
  console.log(formattedDate) // Output: "2610"

  // Test with an invalid input
  // const invalidDate = formatDateToYYMM("2026103");  // This would throw an error
} catch (error) {
  console.error(error)
}

/**
 * Formats a date to MM/DD/YYYY format
 * @param {Date|string|number} date - A Date object, date string, or timestamp
 * @returns {string} Date formatted as MM/DD/YYYY
 */
export function formatDateDDMMYYYY(date: Date, separator = '-') {
  // Check if the date is valid
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date input')
  }

  // Get month, day, and year
  const month = String(date.getMonth() + 1).padStart(2, '0') // Months are 0-based
  const day = String(date.getDate()).padStart(2, '0')
  const year = date.getFullYear()

  // Return formatted date
  return `${day}${separator}${month}${separator}${year}`
}
