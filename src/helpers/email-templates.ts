import {
  EPAY_KYC_ALTERNATIVE_EMAIL,
  EPAY_KYC_EMAIL,
  IS_PROD,
  IS_STAGING,
} from 'utils/config'

export const epayEmails = `${EPAY_KYC_EMAIL}, ${EPAY_KYC_ALTERNATIVE_EMAIL}`

export const EPAY_REVIEW_LOGO = IS_PROD || IS_STAGING ? 34344076 : 34420227
export const CUSTOMER_LOGO_APPROVED =
  IS_PROD || IS_STAGING ? 34286673 : 34420225
export const CUSTOMER_LOGO_SUBMITTED =
  IS_PROD || IS_STAGING ? 34225976 : 34420229
export const PAYMENT_RECEIVED = IS_PROD || IS_STAGING ? 34215486 : 34420234
export const CUSTOMER_DIRECT_CREDIT_ORDER_SUBMITTED =
  IS_PROD || IS_STAGING ? 33812576 : 34420233
export const EPAY_ORDER_DIRECT_CREDIT =
  IS_PROD || IS_STAGING ? 34344208 : 34420226
export const CUSTOMER_ORDER_CREDIT_CARD =
  IS_PROD || IS_STAGING ? 34215487 : 34420232
export const NEW_USER_CREATED = IS_PROD || IS_STAGING ? 34480845 : 34417438
export const CONTACT_SUPPORT = IS_PROD || IS_STAGING ? 34554634 : 34554497
export const INDIVIDUAL_LOCKCODE_EMAIL =
  IS_PROD || IS_STAGING ? 33595625 : 34420230
export const APPROVED_APPLICATION = IS_PROD || IS_STAGING ? 33812575 : 34420228
export const LOCKCODE_LIST_BY_ADDRESS_EMAIL =
  IS_PROD || IS_STAGING ? 33587797 : 34420231
export const SINGLE_LOCK_CODE = IS_PROD ? 34274050 : 34420235
export const FUNDED_ORDER_RELEASE_EMAIL =
  IS_PROD || IS_STAGING ? 38122133 : 38097003
export const STOCK_CARD_ORDER = IS_PROD || IS_STAGING ? 38250745 : 37651367
// Email send when scheduled order is sent
export const SCHEDULED_ORDER_RELEASED =
  IS_PROD || IS_STAGING ? 39658939 : 39658896
