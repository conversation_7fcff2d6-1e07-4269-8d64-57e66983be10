// lib/request-with-backoff.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import logger from 'utils/logger'

// Wrapper around axios to retry request on failure
// Accepts multiple API endpoints to try
// Exponential backoff
class RequestWithBackoff {
  private clients: AxiosInstance[]
  private retries: number
  private delayFactor: number
  private apiUrls: string[]
  private path = ''

  constructor({
    apiUrls,
    initialTimeout = 10000,
    retries = 3,
    delayFactor = 2,
  }: {
    apiUrls: string[]
    initialTimeout?: number
    retries?: number
    delayFactor?: number
  }) {
    this.apiUrls = apiUrls
    this.clients = apiUrls.map((url) => {
      const client = axios.create({ baseURL: url, timeout: initialTimeout })

      client.interceptors.request.use((config) => {
        logger.info(`Making request to: ${config.baseURL}${config.url}`)
        return config
      })

      return client
    })
    this.retries = retries
    this.delayFactor = delayFactor
  }

  public async request(config: AxiosRequestConfig): Promise<AxiosResponse> {
    logger.debug(`request started...`)
    let lastError
    for (let attempt = 0; attempt < this.retries; attempt++) {
      for (const client of this.clients) {
        try {
          logger.debug(
            `request-with-backoff::request url(${client.getUri()}${
              config.url
            }) attempt: ${attempt + 1}`
          )
          const response = await client.request(config)

          logger.info('Request completed successfully.')
          return response.data
        } catch (error) {
          lastError = error
          //console.error(error)

          if (axios.isAxiosError(error)) {
            if (
              error.response?.status === 503 ||
              error.code === 'ECONNREFUSED' ||
              error.code === 'ECONNABORTED'
            ) {
              logger.warn(
                `request-with-backoff::request Server (${client.getUri()}${
                  config.url
                }) unavailable. Retrying. Error: ${error.message}`
              )
            } else {
              logger.error(
                `request-with-backoff::request: HTTP Status: [${
                  error.response?.status
                }]. No retrying. Error: ${JSON.stringify(error.response?.data)}`
              )
              throw lastError
            }
          }
        }
      }

      const delay = Math.pow(this.delayFactor, attempt) * 1000
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
    logger.error(
      `Unable to reach servers [${this.apiUrls.join('.')}] with path: [${
        config.baseURL
      }] without success. Last Error: ${lastError}`
    )
    throw lastError
  }
}

export default RequestWithBackoff
