import { describe, it, expect } from 'vitest'
import { cardExpiryToYYMM } from './data-helper'

describe('formatDateToYYMM', () => {
  it('should correctly format a valid date string', () => {
    expect(cardExpiryToYYMM('20261031')).toBe('2610')
    expect(cardExpiryToYYMM('20300101')).toBe('3001')
    expect(cardExpiryToYYMM('20991231')).toBe('9912')
  })

  it('should handle different months correctly', () => {
    expect(cardExpiryToYYMM('20260101')).toBe('2601')
    expect(cardExpiryToYYMM('20260201')).toBe('2602')
    expect(cardExpiryToYYMM('20261201')).toBe('2612')
  })

  it('should throw an error for invalid date formats', () => {
    expect(() => cardExpiryToYYMM('2026103')).toThrow(
      'Invalid date format. Expected yyyyMMDD.'
    )
    expect(() => cardExpiryToYYMM('202610311')).toThrow(
      'Invalid date format. Expected yyyyMMDD.'
    )
    expect(() => cardExpiryToYYMM('2026abcd')).toThrow(
      'Invalid date format. Expected yyyyMMDD.'
    )
  })

  it('should handle edge cases', () => {
    expect(cardExpiryToYYMM('20000101')).toBe('0001')
    expect(cardExpiryToYYMM('20991231')).toBe('9912')
  })

  it('should not validate the actual date, only the format', () => {
    // Note: This test passes because the function doesn't validate if it's a real date
    expect(cardExpiryToYYMM('20261361')).toBe('2613')
  })
})
