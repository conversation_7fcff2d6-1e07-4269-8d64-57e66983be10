import { expect, describe, it } from 'vitest'
import { splitName } from './name-helper'

describe('splitName function', () => {
  it('should handle a single name', () => {
    const result = splitName('<PERSON>')
    expect(result).toEqual({
      firstName: '<PERSON>',
      middleName: '',
      lastName: '',
    })
  })

  it('should handle two names', () => {
    const result = splitName('<PERSON>')
    expect(result).toEqual({
      firstName: '<PERSON>',
      middleName: '',
      lastName: '<PERSON>',
    })
  })

  it('should handle three names', () => {
    const result = splitName('<PERSON>')
    expect(result).toEqual({
      firstName: '<PERSON>',
      middleName: '<PERSON>',
      lastName: '<PERSON>',
    })
  })

  it('should handle four names and assign the last name with overflow', () => {
    const result = splitName('<PERSON>')
    expect(result).toEqual({
      firstName: '<PERSON>',
      middleName: '<PERSON>',
      lastName: '<PERSON>',
    })
  })

  it('should handle names with extra spaces', () => {
    const result = splitName('  <PERSON>  ')
    expect(result).toEqual({
      firstName: '<PERSON>',
      middleName: '<PERSON>',
      lastName: '<PERSON>',
    })
  })

  it('should handle empty input', () => {
    const result = splitName('')
    expect(result).toEqual({
      firstName: '',
      middleName: '',
      lastName: '',
    })
  })
})
