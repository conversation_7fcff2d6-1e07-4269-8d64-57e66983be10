export type NameParts = {
  firstName: string
  middleName: string
  lastName: string
}

export function splitName(fullName: string): NameParts {
  const nameParts = fullName.trim().split(/\s+/)

  let firstName = ''
  let middleName = ''
  let lastName = ''

  if (nameParts.length === 1) {
    firstName = nameParts[0]
  } else if (nameParts.length === 2) {
    firstName = nameParts[0]
    lastName = nameParts[1]
  } else if (nameParts.length === 3) {
    firstName = nameParts[0]
    middleName = nameParts[1]
    lastName = nameParts[2]
  } else if (nameParts.length > 3) {
    firstName = nameParts[0]
    middleName = nameParts[1]
    lastName = nameParts.slice(2).join(' ')
  }

  return {
    firstName,
    middleName,
    lastName,
  }
}
