// utils/promise-helpers.ts

import { ResultAsync } from 'neverthrow'
import { SimpleError } from 'types/simple-error'

export async function unwrapFromPromise<T>(
  resultAsync: ResultAsync<T, SimpleError>
): Promise<T> {
  try {
    const result = await resultAsync.match<{ success: boolean; data?: T; error?: SimpleError }>(
      (value) => ({ success: true, data: value }),
      (error: SimpleError) => ({ success: false, error })
    )

    if (!result.success) {
      throw result.error
    }

    return result.data as T
  } catch (error) {
    throw error
  }
}