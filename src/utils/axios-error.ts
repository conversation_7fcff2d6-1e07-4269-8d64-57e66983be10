import { AxiosError } from 'axios'
// import { Err } from "../models/types";

import logger from './logger'
import { ERRORS } from './error'

// Log the correct error and return the error
export const handleAxiosError = (error: unknown, url: string) => {
  let status, statusText, responseText

  if (error instanceof AxiosError) {
    if (error.response) {
      status = error.response.status
      statusText = error.response.statusText

      try {
        responseText = JSON.stringify(error.response.data)
      } catch (err: unknown) {
        responseText = `${error.response.data}`
      }
    }

    const message = `${url} is returning ${status} ${statusText}: ${responseText}`

    if (status === 400) {
      logger.warn(message, error)
      return {
        code: ERRORS.EXTERNAL_API_BAD_REQUEST.code,
        message:
          'Bad Request. The server could not understand the request due to invalid syntax.',
      }
    }

    if (status === 429) {
      logger.error(error, message)
      return {
        code: ERRORS.EXTERNAL_API_THROTTLING.code,
        message:
          'We are experiencing too many requests for this service. Check the service logs for further details.',
      }
    }

    if (status === 401) {
      logger.error(error, message)
      return {
        code: ERRORS.EXTERNAL_API_UNAUTHORIZED.code,
        message:
          'There was a problem with the requests for this service. Check the service logs for further details.',
      }
    }

    if (status === 404) {
      logger.error(message)
      return {
        code: ERRORS.EXTERNAL_API_NOT_FOUND.code,
        message: 'There was no entity found for the args passed.',
      }
    }

    // defualt Axios error response
    logger.error(message)
    return {
      code: ERRORS.EXTERNAL_API.code,
      message:
        'There was a problem completing this request. Check your arguments are correct or check the service logs for further details.',
    }
  }

  // Catch all for unexpected errors
  logger.error(
    error,
    `An unexpected error type was passed to the axiosErrorHelper`
  )

  return {
    code: ERRORS.EXTERNAL_API.code,
    message: 'Unknown error type returned by axios.',
  }
}
