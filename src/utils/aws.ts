import {
  S3<PERSON>lient,
  PutO<PERSON>Command,
  GetO<PERSON>Command,
  CreateMultipartUploadCommand,
  Upload<PERSON>artCommand,
  CompleteMultipartUploadCommand,
  CompletedPart,
  CopyObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns'
import { Upload } from '@aws-sdk/lib-storage'

import {
  fromPromise,
  errAsync,
  okAsync,
  err,
  ok,
  ResultAsync,
} from 'neverthrow'

import {
  AWS_REGION,
  AWS_ADMIN_ID,
  AWS_ADMIN_SECRET,
  AWS_S3_BUCKET,
} from './config'
import { ERRORS, ErrorResponse, PickErrorCodeKey } from './error'
import logger from './logger'
import { Readable } from 'stream'
import { errorCodes, SimpleError } from 'types/simple-error'

const DEFAULT_EXPIRES_IN = 600

const s3Client = new S3Client({
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ADMIN_ID,
    secretAccessKey: AWS_ADMIN_SECRET,
  },
})

const snsClient = new SNSClient({
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ADMIN_ID,
    secretAccessKey: AWS_ADMIN_SECRET,
  },
})

type getS3SignedUrlProps = {
  id: string
  contentType: string
  fileExtension: string
  path?: string
  bucket?: string
}

export function getS3SignedUrlPut({
  id,
  contentType,
  fileExtension,
  path,
  bucket = AWS_S3_BUCKET,
}: getS3SignedUrlProps) {
  if (!fileExtension || !contentType) {
    return errAsync('S3_MISSING_REQUIRED_FIELD' as const)
  }

  const fileName = `${id}.${fileExtension}`

  const key = path ? `${path}/${fileName}` : fileName

  const bucketParams = {
    Bucket: bucket,
    Key: key,
    ContentType: `${contentType}/${fileExtension}`,
  }

  const command = new PutObjectCommand(bucketParams)

  return fromPromise(
    getSignedUrl(s3Client, command, {
      expiresIn: DEFAULT_EXPIRES_IN,
    }),
    (error): PickErrorCodeKey<'S3_SIGNED'> => {
      logger.error(error)

      return 'S3_SIGNED'
    }
  ).map((signedUrl) => {
    const [url] = signedUrl.split('?')

    return { signedUrl, url }
  })
}

export async function getS3SignedUrlGet({
  key,
  bucket = AWS_S3_BUCKET,
}: {
  key: string
  bucket?: string
}) {
  const bucketParams = {
    Bucket: bucket,
    Key: key,
  }
  try {
    const command = new GetObjectCommand(bucketParams)

    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: DEFAULT_EXPIRES_IN,
    })

    return signedUrl
  } catch (e) {
    logger.error(e)
  }
}

export async function getFileStreamFromS3({
  key,
  bucket = AWS_S3_BUCKET,
}: {
  key: string
  bucket?: string
}) {
  logger.debug(`getFileStreamFromS3... key=${key}`)
  const bucketParams = {
    Bucket: bucket,
    Key: key,
  }

  try {
    const command = new GetObjectCommand(bucketParams)
    const response = await s3Client.send(command)
    return okAsync(response.Body as Readable)
  } catch (error) {
    logger.error(
      error,
      `failed to get file stream from s3 key: ${key} bucket: ${bucket}`
    )
    return errAsync(ERRORS.S3_ERROR_GET_FILESTREAM)
  }
}

export type AwsS3ContentType = 'application/json' | 'text/csv' | 'text/plain'

// export function createMultipartUpload({
//   bucket,
//   key,
//   contentType,
// }: {
//   bucket: string
//   key: string
//   contentType: AwsS3ContentType
// }) {
//   const command = new CreateMultipartUploadCommand({
//     Bucket: bucket,
//     Key: key,
//     ContentType: contentType,
//   })

//   return fromPromise(s3Client.send(command), (e) => {
//     logger.error(e, `Failed to initiate multipart upload`)
//     return ERRORS.S3_ERROR
//   }).map((response) => {
//     return response.UploadId
//   })
// }

// Function to upload a part of the multipart upload
// export function uploadPart({
//   bucket,
//   key,
//   partNumber,
//   body,
//   uploadId,
// }: {
//   bucket: string
//   key: string
//   partNumber: number
//   body: Readable | Buffer | Uint8Array
//   uploadId: string
// }): ResultAsync<CompletedPart, SimpleError> {
//   const command = new UploadPartCommand({
//     Bucket: bucket,
//     Key: key,
//     PartNumber: partNumber,
//     UploadId: uploadId,
//     Body: body,
//   })

//   return fromPromise(s3Client.send(command), (e) => {
//     logger.error(e, `Failed to upload part: ${partNumber}`)
//     return {
//       code: errorCodes.s3.UPLOAD_ERROR,
//       message: `Failed to upload part: ${partNumber}`,
//     } as SimpleError
//   }).andThen((response) => {
//     if (!response.ETag) {
//       return errAsync({
//         code: errorCodes.s3.UPLOAD_ERROR,
//         message: 'Missing ETag in upload response',
//       })
//     }

//     return okAsync({
//       PartNumber: partNumber,
//       ETag: response.ETag,
//     })
//   })
// }

// Function to complete the multipart upload
// export function completeMultipartUpload({
//   bucket,
//   key,
//   parts,
//   uploadId,
// }: {
//   bucket: string
//   key: string
//   parts: CompletedPart[]
//   uploadId: string
// }) {
//   const command = new CompleteMultipartUploadCommand({
//     Bucket: bucket,
//     Key: key,
//     UploadId: uploadId,
//     MultipartUpload: { Parts: parts },
//   })
//   return fromPromise(s3Client.send(command), (e) => {
//     logger.error(e, `Failed to complete multipart upload`)
//     return ERRORS.S3_ERROR
//   }).map(() => {
//     return ok(undefined)
//   })
// }

export function s3Put({
  bucket,
  key,
  contentType,
  body,
}: {
  bucket: string
  key: string
  contentType: string
  body: Buffer | Readable
  append?: boolean
}) {
  const putParams = {
    Bucket: bucket,
    Key: key,
    ContentType: contentType,
    Body: body,
  }

  const command = new PutObjectCommand(putParams)
  return fromPromise(
    s3Client.send(command).then((response) => {
      const result = {
        status: 'Success',
        message: 'File uploaded successfully',
        ETag: response.ETag,
        key: key,
        bucket: bucket,
        contentType: contentType,
      }
      return ok(result)
    }),
    (e) => {
      logger.error(e, `s3Put error: ${key} ${bucket} ${contentType}`)
      return {
        code: errorCodes.s3.UPLOAD_ERROR,
        message: 'Failed to upload part',
      } as SimpleError
    }
  )
}

export function s3Copy({
  sourceBucket,
  sourceKey,
  destinationBucket,
  destinationKey,
}: {
  sourceBucket: string
  sourceKey: string
  destinationBucket: string
  destinationKey: string
}) {
  const command = new CopyObjectCommand({
    CopySource: `${sourceBucket}/${sourceKey}`,
    Bucket: destinationBucket,
    Key: destinationKey,
  })

  return fromPromise(
    s3Client.send(command).then(() => {
      return ok({ status: 'Success', message: 'File copied successfully' })
    }),
    (e) => {
      logger.error(
        e,
        `s3Copy error: sourceBucket = [${sourceBucket}], sourceKey = [${sourceKey}], destinationBucket = [${destinationBucket}], destinationKey = [${destinationKey}]`
      )
      return err(ERRORS.S3_ERROR)
    }
  )
}

export function s3Delete({ key, bucket }: { key: string; bucket: string }) {
  const command = new DeleteObjectCommand({
    Bucket: bucket,
    Key: key,
  })

  return fromPromise(
    s3Client.send(command).then(() => {
      return ok({ status: 'Success', message: 'File copied successfully' })
    }),
    (e) => {
      logger.error(e, `s3Delete error: key = [${key}], bucket = [${bucket}]`)
      return err(ERRORS.S3_ERROR)
    }
  )
}

export function s3Move({
  sourceBucket,
  sourceKey,
  destinationBucket,
  destinationKey,
}: {
  sourceBucket: string
  sourceKey: string
  destinationBucket: string
  destinationKey: string
}) {
  return s3Copy({ sourceBucket, sourceKey, destinationBucket, destinationKey })
    .andThen(() => s3Delete({ key: sourceKey, bucket: sourceBucket }))
    .map(() => ok({ key: destinationKey, bucket: destinationBucket }))
    .mapErr((e) => {
      return e
    })
}

export function sendTextMessage({
  phoneNumber,
  message,
}: {
  phoneNumber: string
  message: string
}) {
  const params = {
    Message: message,
    PhoneNumber: phoneNumber, // Ensure the number is in E.164 format
  }

  const command = new PublishCommand(params)
  return fromPromise(
    snsClient.send(command).then(() => {
      return { sent: true }
    }),
    (error) => {
      logger.error(
        error,
        `sendTextMessage error: phoneNumber = [${phoneNumber}]`
      )
      return {
        code: ERRORS.SNS_ERROR.code,
        message: 'Error sending text message',
      } as ErrorResponse
    }
  )
}

//const PART_SIZE = 5 * 1024 * 1024 // 5MB minimum part size for S3

type StreamContext = {
  bucket: string
  key: string
  uploadId: string
  buffer: Buffer
  partNumber: number
  parts: CompletedPart[]
}

type StreamToS3Result = ResultAsync<void, SimpleError>

export function streamToS3({
  bucket,
  key,
  contentType,
  stream,
}: {
  bucket: string
  key: string
  contentType: AwsS3ContentType
  stream: Readable
}): ResultAsync<void, SimpleError> {
  return fromPromise(
    new Upload({
      client: s3Client,
      params: {
        Bucket: bucket,
        Key: key,
        Body: stream,
        ContentType: contentType,
        ChecksumAlgorithm: 'CRC32',
      },

      // Optional: you can adjust partSize and queueSize here if necessary
    })
      .done()
      .then(() => {
        // Upload complete
      }),
    (error): SimpleError => {
      logger.error(
        error,
        `Failed to upload file using Upload class for key [${key}]`
      )
      return {
        code: errorCodes.s3.UPLOAD_ERROR,
        message: 'Failed to upload file via Upload class',
      }
    }
  )
}

// async function handleChunk(context: StreamContext, chunk: Buffer | string) {
//   const data = Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk)
//   const newBuffer = Buffer.concat([context.buffer, data])

//   if (newBuffer.length < PART_SIZE) {
//     return okAsync({
//       ...context,
//       buffer: newBuffer,
//     })
//   }

//   return uploadPart({
//     bucket: context.bucket,
//     key: context.key,
//     partNumber: context.partNumber,
//     uploadId: context.uploadId,
//     body: newBuffer,
//   })
//     .map((part) => ({
//       ...context,
//       buffer: Buffer.from(''),
//       partNumber: context.partNumber + 1,
//       parts: [...context.parts, part],
//     }))
//     .mapErr((error) => {
//       logger.error(error, 'Failed to upload part')
//       return {
//         code: errorCodes.s3.UPLOAD_ERROR,
//         message: 'Failed to upload part',
//       } as SimpleError
//     })
// }

// function finalizeUpload(
//   context: StreamContext
// ): ResultAsync<void, SimpleError> {
//   logger.debug(
//     'finalizeUpload... handling remaining buffer and completing upload'
//   )

//   // First get all parts including any remaining buffer
//   const getAllParts =
//     context.buffer.length > 0
//       ? uploadPart({
//           bucket: context.bucket,
//           key: context.key,
//           partNumber: context.partNumber,
//           uploadId: context.uploadId,
//           body: context.buffer,
//         }).andThen((part) => {
//           if ('code' in part) {
//             return errAsync({
//               code: errorCodes.s3.UPLOAD_ERROR,
//               message: 'Failed to upload final part',
//             })
//           }
//           return okAsync([...context.parts, part])
//         })
//       : okAsync(context.parts)

//   // Complete the upload with validated parts
//   return getAllParts.andThen((validParts) =>
//     completeMultipartUpload({
//       bucket: context.bucket,
//       key: context.key,
//       uploadId: context.uploadId,
//       parts: validParts,
//     }).map(() => undefined)
//   )
// }
