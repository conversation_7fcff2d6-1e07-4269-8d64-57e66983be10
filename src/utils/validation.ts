import z from 'zod'

import { ResultAsync, errAsync, okAsync } from 'neverthrow'
import logger from './logger'

export function transformStringToNumber({
  defaultNumber,
}: {
  defaultNumber?: number
} = {}) {
  return z
    .string()
    .optional()
    .transform((value) => {
      if (!value) {
        if (!defaultNumber) {
          throw new Error('Invalid number format')
        }
        return defaultNumber
      }
      const parsedNumber = Number(value)
      if (isNaN(parsedNumber)) {
        throw new Error('Invalid number format')
      }
      return parsedNumber
    })
}

// DEPRECATED - doesn't return a good error message
export function validate<S extends z.Schema>({
  schema,
  data,
}: {
  schema: S
  data: unknown
}): ResultAsync<z.infer<S>, 'BAD_INPUT'> {
  const validation = schema.safeParse(data)

  if (!validation.success) {
    return errAsync('BAD_INPUT' as const)
  }

  return okAsync(validation.data)
}

export function validateInput<S extends z.Schema>({
  schema,
  data,
}: {
  schema: S
  data: unknown
}): ResultAsync<z.infer<S>, z.ZodError<any>> {
  const validation = schema.safeParse(data)

  if (!validation.success) {
    return errAsync(validation.error)
  }

  return okAsync(validation.data)
}

export function validateWithErrorData<S extends z.Schema>({
  schema,
  data,
}: {
  schema: S
  data: unknown
}): ResultAsync<z.infer<S>, { errorType: 'BAD_INPUT'; data: any }> {
  const validation = schema.safeParse(data)

  if (!validation.success) {
    logger.error(validation.error, 'Validation error:')
    return errAsync({ errorType: 'BAD_INPUT' as const, data: validation.error })
  }

  return okAsync(validation.data)
}
