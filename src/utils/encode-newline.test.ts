import { describe, it, expect } from 'vitest'
import { encodeNewlines } from './encode-newline'

describe('newline encoding/decoding', () => {
  it('should encode single newline to HTML entities', () => {
    const input = 'Hello\nWorld'
    const expected = 'Hello&lt;br&gt;World'
    expect(encodeNewlines(input)).toBe(expected)
  })

  it('should encode multiple newlines', () => {
    const input = 'Hello\nDear\nWorld'
    const expected = 'Hello&lt;br&gt;Dear&lt;br&gt;World'
    expect(encodeNewlines(input)).toBe(expected)
  })

  it('should handle empty string', () => {
    expect(encodeNewlines('')).toBe('')
  })

  it('should handle string without newlines', () => {
    expect(encodeNewlines('Hello World')).toBe('Hello World')
  })
})
