import { TgglClient } from 'tggl-client'
import {
  IS_DEV,
  IS_LOCAL,
  IS_PROD,
  IS_STAGING,
  TGGL_API_KEY,
} from './config'
import axios from 'axios'

export type FlagKey = 'testTggl' | 'superAdmin' // Update this to match your feature flags
/***
 * This Flags class only works for environments and not for contexts
 */
class Flags {
  private static instance: Flags
  private client: TgglClient
  private initialized = false
  private config: any

  private constructor() {
    const apiKey = TGGL_API_KEY
    if (!apiKey) {
      throw new Error('TGGL_API_KEY environment variable is not set')
    }
    // Retreieve feature flags every 30 seconds
    this.client = new TgglClient(apiKey, { pollingInterval: 30000 })
    this.client.onFetchFail((error) => {
      console.log(error, 'Tggl feature flags refresh failed:')
    })
  }

  private async getConfig() {
    const response = await axios.get(`https://api.tggl.io/config`, {
      headers: { 'x-tggl-api-key': TGGL_API_KEY },
    })

    if (response.status === 200) {
      return response.data
    } else {
      console.log(`Failed to get config from Tggl API: ${response.status}`)
    }
  }

  private async setInitialContext() {
    // this.config = await this.getConfig()

    // console.dir(this.config, { depth: null })
    const environment = Flags.getEnvironment()

    if (!environment) {
      throw new Error('Environment could not be determined for feature flags')
    }

    await this.client.setContext({ environment })
    this.initialized = true
  }

  public static getEnvironment(): string {
    if (IS_PROD) return 'production'
    if (IS_STAGING) return 'staging'
    if (IS_DEV || IS_LOCAL) return 'development'
    return ''
  }

  public static getInstance(): Flags {
    if (!Flags.instance) {
      Flags.instance = new Flags()
    }
    return Flags.instance
  }

  public async initialize(): Promise<void> {
    if (!this.initialized) {
      await this.setInitialContext()
    }
  }

  public async isActive(
    featureName: FlagKey,
    userId?: string
  ): Promise<boolean> {
    if (!this.initialized) {
      throw new Error(
        'Feature flags have not been initialized. Call initialize() first.'
      )
    }

    const newContext = { environment: Flags.getEnvironment(), userId }

    const myFlags = await this.client.evalContext(newContext)
    return myFlags.isActive(featureName)
  }
}

export function getFlagInstance(): Flags {
  return Flags.getInstance()
}

export async function isFeatureActive({
  key,
  userId,
}: {
  key: FlagKey
  userId?: string
}): Promise<boolean> {
  const instance = getFlagInstance()
  return await instance.isActive(key, userId)
}

export async function initializeFeatureFlags(): Promise<void> {
  try {
    const instance = getFlagInstance()
    await instance.initialize()
    console.log('Tggl feature flag service initialized successfully')
  } catch (error) {
    console.error('Failed to initialize Tggl:', error)
    throw error
  }
}
