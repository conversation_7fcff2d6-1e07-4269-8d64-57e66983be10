import { describe, it, expect } from 'vitest'
import { containsSpecialCharOrAccent } from '../contains-special-chars'

describe('containsSpecialCharOrAccent', () => {
  it('should return false for strings with only alphanumeric characters', () => {
    expect(containsSpecialCharOrAccent('abc123')).toBe(false)
    expect(containsSpecialCharOrAccent('ABC123')).toBe(false)
    expect(containsSpecialCharOrAccent('abcABC123')).toBe(false)
  })

  it('should return false for strings with spaces and common punctuation', () => {
    expect(containsSpecialCharOrAccent('abc 123')).toBe(false)
    expect(containsSpecialCharOrAccent('ABC, 123')).toBe(false)
    expect(containsSpecialCharOrAccent('abc. ABC! 123?')).toBe(false)
    expect(containsSpecialCharOrAccent('Hello, world!')).toBe(false)
    expect(containsSpecialCharOrAccent('"Quote" (parens)')).toBe(false)
  })

  it('should return true for strings with special characters', () => {
    expect(containsSpecialCharOrAccent('abc@123')).toBe(true)
    expect(containsSpecialCharOrAccent('ABC#123')).toBe(true)
    expect(containsSpecialCharOrAccent('abc$ABC123')).toBe(true)
  })

  it('should return true for strings with accented characters', () => {
    expect(containsSpecialCharOrAccent('abcé123')).toBe(true)
    expect(containsSpecialCharOrAccent('ABCñ123')).toBe(true)
    expect(containsSpecialCharOrAccent('abcÖABC123')).toBe(true)
  })

  it('should return true for strings with Unicode characters outside Basic Latin and Latin-1 Supplement', () => {
    expect(containsSpecialCharOrAccent('abc漢字123')).toBe(true)
    expect(containsSpecialCharOrAccent('ABC🎉123')).toBe(true)
  })

  it('should return false for an empty string', () => {
    expect(containsSpecialCharOrAccent('')).toBe(false)
  })
})
