export function toASCII(str: string): string {
  let asciiStr = ''
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i)
    // ASCII characters have a code between 0 and 127
    if (charCode >= 0 && charCode <= 127) {
      asciiStr += str[i]
    } else {
      // Replace non-ASCII characters with a placeholder or remove them
      asciiStr += '?' // You can choose any placeholder character
    }
  }
  return asciiStr
}

export function ensureTrailingSlash(str: string): string {
  return str.endsWith('/') ? str : str + '/'
}

// creates a windows OS compatible filename
// timestamp can be passed by Date.now() or new Date().getTime()
export function timestampToFilename(timestamp: number): string {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`
}
