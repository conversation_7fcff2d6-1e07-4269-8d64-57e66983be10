/* eslint-disable no-console */
import dotenv from 'dotenv'
import path from 'path'
import { z } from 'zod'

export const environment = (process.env.ENV ??= 'local')
dotenv.config({
  path: path.resolve(`.env.${environment}`),
})
console.log(`load env for [${environment}]`)

export const ENVIRONMENT = environment ?? ''

const secretsSchema = z.object({
  epayApiUsername: z.string().nonempty(),
  epayApiPassword: z.string().nonempty(),
  postmarkApiKey: z.string().nonempty(),
  goSweetApiKey: z.string().nonempty(),
  jwtSecret: z.string().nonempty(),
  i2cPassword: z.string().nonempty(),
  infoLogClientSecret: z.string().nonempty(),
  awsUserSecret: z.string().nonempty(),
  soapAuthPassword: z.string().nonempty(),
  windcavePassword: z.string().nonempty(),
  placardSecret: z.string().nonempty(),
  epayPortalApiKey: z.string().nonempty(),
  i2cPhysicalStartingNumbers: z.string().nonempty(),
  i2cPhysicalCardProgramId: z.string().nonempty(),
  i2cVirtualStartingNumbers: z.string().nonempty(),
  i2cVirtualCardProgramId: z.string().nonempty(),
  auth0ClientSecret: z.string().nonempty(),
  auth0SigningSecret: z.string().nonempty(),
  corporatePortalAppId: z.string().nonempty(),
  adminPortalAppId: z.string().nonempty(),
  placardSftpPassword: z.string().optional(),
  epaySftpPassword: z.string().optional(),
  epaySftpPassphrase: z.string().optional(),
  disableReportedToEpayUpdate: z.string().optional(),
  auth0RedirectSecret: z.string().nonempty(),
  twilioAuthToken: z.string().nonempty(),
  tgglApiKey: z.string().nonempty(),
  renBinNumber: z.string(),
})

export let secrets: z.infer<typeof secretsSchema>

if (environment === 'local') {
  secrets = {
    jwtSecret: process.env.JWT_SECRET ?? '',
    goSweetApiKey: process.env.GO_SWEET_SPOT_API_KEY ?? '',
    epayApiUsername: '',
    epayApiPassword: '',
    postmarkApiKey: process.env.POSTMARK_API_KEY ?? '',
    awsUserSecret: process.env.AWS_ADMIN_SECRET ?? '',
    i2cPassword: '',
    soapAuthPassword: '',
    infoLogClientSecret: process.env.INFO_LOG_CLIENT_SECRET ?? '',
    windcavePassword: process.env.WINDCAVE_PASSWORD ?? '',
    placardSecret: process.env.PLACARD_API_KEY ?? '',
    epayPortalApiKey: process.env.EPAY_PORTAL_API_KEY ?? '',
    i2cPhysicalStartingNumbers: '',
    i2cPhysicalCardProgramId: '',
    i2cVirtualStartingNumbers: '',
    i2cVirtualCardProgramId: '',
    auth0ClientSecret: process.env.AUTH0_CLIENT_SECRET ?? '',
    auth0SigningSecret: process.env.AUTH0_SIGNING_SECRET ?? '',
    corporatePortalAppId: process.env.CORPORATE_PORTAL_APP_ID ?? '',
    adminPortalAppId: process.env.ADMIN_PORTAL_APP_ID ?? '',
    placardSftpPassword: process.env.PLACARD_SFTP_PASSWORD ?? '',
    epaySftpPassword: process.env.EPAY_SFTP_PASSWORD ?? '',
    epaySftpPassphrase: process.env.EPAY_SFTP_PASSPHRASE ?? '',
    disableReportedToEpayUpdate:
      process.env.DISABLE_REPORTED_TO_EPAY_UPDATE === 'true' ? 'true' : 'false',
    auth0RedirectSecret: process.env.AUTH0_REDIRECT_SECRET ?? '',
    twilioAuthToken: process.env.TWILIO_AUTH_TOKEN ?? '',
    tgglApiKey: process.env.TGGL_API_KEY ?? '',
    renBinNumber: process.env.REN_BIN_NUMBER ?? '',
  }
} else {
  let secretsVariableName = `${environment.toUpperCase()}_JSON_SECRETS`
  if (secretsVariableName === 'STAGING_JSON_SECRETS') {
    secretsVariableName = 'PROD_JSON_SECRETS' // staging uses prod secrets
  }

  console.log(`load secrets from [${secretsVariableName}]`)

  const secretsJson = process.env[secretsVariableName]

  if (!secretsJson) {
    throw new Error(`Secrets [${secretsVariableName}] not found`)
  }

  const validatedSecrets = secretsSchema.safeParse(
    JSON.parse(secretsJson ?? '')
  )
  if (!validatedSecrets.success) {
    throw new Error(`Secrets Validation Error \r ${validatedSecrets.error}`)
  }

  secrets = validatedSecrets.data
}

export const JWT_SECRET = secrets.jwtSecret
export const GO_SWEET_SPOT_API_KEY = secrets.goSweetApiKey
export const EPAY_API_USERNAME = secrets.epayApiUsername
export const EPAY_API_PASSWORD = secrets.epayApiPassword

export const AWS_ADMIN_SECRET = secrets.awsUserSecret
export const I2C_PASSWORD = secrets.i2cPassword
export const SOAP_AUTH_PASSWORD = secrets.soapAuthPassword
export const INFO_LOG_CLIENT_SECRET = secrets.infoLogClientSecret
export const WINDCAVE_PASSWORD = secrets.windcavePassword
export const PLACARD_API_KEY = secrets.placardSecret
export const EPAY_PORTAL_API_KEY = secrets.epayPortalApiKey

export const I2C_PHYSICAL_STARTING_NUMBERS = secrets.i2cPhysicalStartingNumbers
export const I2C_PHYSICAL_CARD_PROGRAM_ID = secrets.i2cPhysicalCardProgramId
export const I2C_VIRTUAL_STARTING_NUMBERS = secrets.i2cVirtualStartingNumbers
export const I2C_VIRTUAL_CARD_PROGRAM_ID = secrets.i2cVirtualCardProgramId
export const REN_BIN_NUMBER = secrets.renBinNumber

export const TWILIO_AUTH_TOKEN = secrets.twilioAuthToken

export const POSTMARK_API_KEY = secrets.postmarkApiKey
export const TGGL_API_KEY = secrets.tgglApiKey

export const CLIENT_URL_BASE = process.env.CLIENT_URL_BASE ?? ''
export const API_URL_BASE = process.env.API_URL_BASE ?? ''
export const CLIENT_DOMAIN = process.env.CLIENT_DOMAIN ?? ''

export const CORPORATE_PORTAL_URL_BASE =
  process.env.CORPORATE_PORTAL_URL_BASE ?? ''
export const ADMIN_PORTAL_URL_BASE = process.env.ADMIN_PORTAL_URL_BASE ?? ''

export function setDatabaseUrl(databaseUrl: string) {
  DATABASE_URL = databaseUrl
  process.env.DATABASE_URL = databaseUrl
}
export let DATABASE_URL = process.env.DATABASE_URL ?? ''
export const AWS_REGION = process.env.AWS_REGION ?? ''
export const AWS_ADMIN_ID = process.env.AWS_ADMIN_ID ?? ''

export const AWS_COGNITO_USER_POOL_ID =
  process.env.AWS_COGNITO_USER_POOL_ID ?? ''
export const AWS_COGNITO_CLIENT_ID = process.env.AWS_COGNITO_CLIENT_ID ?? ''
export const AWS_S3_BUCKET = process.env.AWS_S3_BUCKET ?? ''
export const AWS_S3_BUCKET_CARD = process.env.AWS_S3_BUCKET_CARD ?? ''
export const AWS_S3_BUCKET_REPORT = process.env.AWS_S3_BUCKET_REPORT ?? ''

export const INFO_LOG_CLIENT_ID = process.env.INFO_LOG_CLIENT_ID ?? ''
export const INFO_LOG_USERNAME = process.env.INFO_LOG_USERNAME ?? ''
export const INFO_LOG_BASE_URL = process.env.INFO_LOG_BASE_URL ?? ''

export const I2C_URL = process.env.I2C_URL ?? ''
export const I2C_ID = process.env.I2C_ID ?? ''
export const I2C_USER_ID = process.env.I2C_USER_ID ?? ''

export const TWILIO_VERIFY_SERVICE_SID =
  process.env.TWILIO_VERIFY_SERVICE_SID ?? ''
export const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID ?? ''

export const REN_VCARD_BRANCH = process.env.REN_VCARD_BRANCH ?? ''

export const FORCE_LOG_TO_CONSOLE = Boolean(process.env.FORCE_LOG_TO_CONSOLE)

export const SENDER_EMAIL = process.env.SENDER_EMAIL ?? ''

export const SOAP_GIFT_CARD_URL = process.env.SOAP_GIFT_CARD_URL ?? ''
export const SOAP_GIFT_CARD_ACTION = process.env.SOAP_GIFT_CARD_ACTION ?? ''

export const SOAP_AUTH_USERNAME = process.env.SOAP_AUTH_USERNAME ?? ''
export const SOAP_MESSAGE_EXCHANGE_MESSAGE_TYPE =
  process.env.SOAP_MESSAGE_EXCHANGE_MESSAGE_TYPE ?? ''
export const SOAP_MESSAGE_EXCHANGE_USER_CODE =
  process.env.SOAP_MESSAGE_EXCHANGE_USER_CODE ?? ''

export const USE_MOCKING =
  (!process.env.USE_MOCKING ||
    process.env.USE_MOCKING.toLowerCase() !== 'false') ??
  true

export const WINDCAVE_URL = process.env.WINDCAVE_URL ?? ''
export const WINDCAVE_USER = process.env.WINDCAVE_USER ?? ''
// staging and prod share the same secrets
export const sftpPrivateKeySecretPrefix =
  environment === 'staging' ? 'PROD' : `${environment.toUpperCase()}`
export const SFTP_PRIVATE_KEY =
  process.env[`${sftpPrivateKeySecretPrefix}_SFTP_PRIVATE_KEY`] ?? '' // this is a secret manager value
export const PLACARD_SFTP_PASSWORD = secrets.placardSftpPassword ?? undefined
export const SFTP_HOST = process.env.SFTP_HOST ?? ''
export const SFTP_PORT = process.env.SFTP_PORT ?? ''
export const SFTP_USERNAME = process.env.SFTP_USERNAME ?? ''
export const SFTP_REMOTE_PATH = process.env.SFTP_REMOTE_PATH ?? ''

export const EPAY_SFTP_PRIVATE_KEY =
  process.env[`${environment.toUpperCase()}_EPAY_SFTP_PRIVATE_KEY`] ?? '' // this is a secret manager value
export const EPAY_SFTP_PASSWORD = secrets.epaySftpPassword ?? undefined
export const EPAY_SFTP_PASSPHRASE = secrets.epaySftpPassphrase ?? undefined
export const EPAY_SFTP_HOST = process.env.EPAY_SFTP_HOST ?? ''
export const EPAY_SFTP_PORT = process.env.EPAY_SFTP_PORT ?? ''
export const EPAY_SFTP_USERNAME = process.env.EPAY_SFTP_USERNAME ?? ''
export const EPAY_SFTP_REMOTE_PATH = process.env.EPAY_SFTP_REMOTE_PATH ?? ''
export const EPAY_INTEGRATION_URL_1 = process.env.EPAY_INTEGRATION_URL_1 ?? ''
export const EPAY_INTEGRATION_URL_2 = process.env.EPAY_INTEGRATION_URL_2 ?? ''

export const AUTH0_ISSUER_BASE_URL = process.env.AUTH0_ISSUER_BASE_URL ?? ''
export const AUTH0_AUDIENCE = process.env.AUTH0_AUDIENCE ?? ''
export const AUTH0_CLIENT_ID = process.env.AUTH0_CLIENT_ID ?? ''
export const AUTH0_CLIENT_SECRET = secrets.auth0ClientSecret
export const AUTH0_SIGNING_SECRET = secrets.auth0SigningSecret
export const AUTH0_CONNECTION = process.env.AUTH0_CONNECTION ?? ''
export const AUTH0_CONNECTION_ID = process.env.AUTH0_CONNECTION_ID ?? ''
export const AUTH0_ADMIN_CONNECTION = process.env.AUTH0_ADMIN_CONNECTION ?? ''
export const AUTH0_ADMIN_CONNECTION_ID =
  process.env.AUTH0_ADMIN_CONNECTION_ID ?? ''

export const JWT_EXPIRY = process.env.JWT_EXPIRY ?? '3h'

export const IS_DEV = ENVIRONMENT === 'dev'
export const IS_PROD = ENVIRONMENT === 'prod'
export const IS_LOCAL = ENVIRONMENT === 'local'
export const IS_STAGING = ENVIRONMENT === 'staging'

export const EPAY_ADMIN_EMAIL = process.env.EPAY_ADMIN_EMAIL ?? ''
export const EPAY_SUPPORT_EMAIL = process.env.EPAY_SUPPORT_EMAIL ?? ''
export const EPAY_KYC_EMAIL = process.env.EPAY_KYC_EMAIL ?? ''
export const EPAY_KYC_ALTERNATIVE_EMAIL =
  process.env.EPAY_KYC_ALTERNATIVE_EMAIL ?? ''

export const CORPORATE_PORTAL_APP_ID = secrets.corporatePortalAppId ?? ''
export const ADMIN_PORTAL_APP_ID = secrets.adminPortalAppId ?? ''
export const AUTH0_REDIRECT_SECRET = secrets.auth0RedirectSecret ?? ''

export const EPAY_REPORT_PROD_TEST = process.env.EPAY_REPORT_PROD_TEST ?? false
export const TEAM_NOTIFICATION_EMAIL = process.env.TEAM_NOTIFICATION_EMAIL ?? ''

export const CREDIT_CARD_SURCHARGE = Number(
  process.env.CREDIT_CARD_SURCHARGE ?? 0.025
)

export const IS_GITHUB_ACTIONS_ENV =
  process.env.IS_GITHUB_ACTIONS_ENV == 'true' ? true : false

export const REN_URL = process.env.REN_URL ?? ''
export const REN_URL2 = process.env.REN_URL2 ?? ''
export const REN_PART_ID = process.env.REN_PART_ID ?? ''
export const REN_DOMAIN_CERT =
  process.env[`${sftpPrivateKeySecretPrefix}_REN_DOMAIN_CERT`] ?? '' // this is a secret manager value
export const REN_INTERMIDIATE_CERT =
  process.env[`${sftpPrivateKeySecretPrefix}_REN_REN_INTERMIDIATE_CERT`] ?? '' // this is a secret manager value
export const REN_CA_CERT =
  process.env[`${sftpPrivateKeySecretPrefix}_REN_CA_CERT`] ?? '' // this is a secret manager value

// FEATURE FLAGS
export const DISABLE_REPORTED_TO_EPAY_UPDATE =
  secrets.disableReportedToEpayUpdate === 'true' ? true : false

export const SEND_ORDER_TO_EPAY =
  process.env.SEND_ORDER_TO_EPAY === 'true' ? true : false

export const ORDER_LOCK_TIMEOUT_IN_MINUTES = process.env
  .ORDER_LOCK_TIMEOUT_IN_MINUTES
  ? Number(process.env.ORDER_LOCK_TIMEOUT_IN_MINUTES)
  : 30 // default to 30 minutes
export const DEFERRED_DELAY_BEFORE_RETRY_IN_MINUTES = process.env
  .DEFERRED_DELAY_BEFORE_RETRY_IN_MINUTES
  ? Number(process.env.DEFERRED_DELAY_BEFORE_RETRY_IN_MINUTES)
  : 5 // default to 5 minutes
