import { parseString } from 'xml2js'
import { from<PERSON><PERSON><PERSON>, err, ok, okAsync, errAsync } from 'neverthrow'
import axios from 'axios'

import {
  SOAP_GIFT_CARD_URL,
  SOAP_GIFT_CARD_ACTION,
  SOAP_AUTH_USERNAME,
  SOAP_AUTH_PASSWORD,
  SOAP_MESSAGE_EXCHANGE_MESSAGE_TYPE,
  SOAP_MESSAGE_EXCHANGE_USER_CODE,
} from 'utils/config'
import logger from './logger'
import { ERRORS, ErrorResponse, PickErrorCodeKey } from './error'
import { lookup } from 'dns'

function getCardReferenceNumbersFromMessageExchangeSOAP({
  xml,
  batchNumber,
}: {
  xml: string
  batchNumber: string
}) {
  return fromPromise(
    new Promise<string[]>((resolve, reject) => {
      parseString(xml, (err, res) => {
        if (err) {
          reject(err)
        }

        const description =
          res['soap:Envelope']['soap:Body'][0].MessageExchangeResponse[0]
            .MessageExchangeResult[0].EpmServiceResponse[0].Description[0]

        if (description.toLowerCase() === 'ok') {
          try {
            const cards =
              res['soap:Envelope']['soap:Body'][0].MessageExchangeResponse[0]
                .MessageExchangeResult[0].EpmServiceResponse[0].OtherData[0]
                .Order[0].Batch[0].Card

            const crn = cards.map(({ CRN }: { CRN: string[] }) => {
              return CRN[0].trim()
            })

            resolve(crn)
          } catch (error) {
            logger.error(
              error,
              `getCardReferenceNumbersFromMessageExchangeSOAP -> Error getting cards from ${batchNumber}. Structure of response might have changed. ${JSON.stringify(
                res
              )}`
            )
            reject(
              `Error retrieving CRNs for batch [${batchNumber}. Structure of response might have changed.`
            )
          }
        } else {
          logger.warn(
            `No CRN from Epay API for [${batchNumber}: ${description}`
          )
          reject(
            `Failed to get CRN from Epay API [${batchNumber}: ${description}`
          )
        }
      })
    }),
    (error) => {
      logger.error(error, 'Falied to parse XML from EPAY CRN call')

      return err({
        code: ERRORS.EXTERNAL_API.code,
        message: JSON.stringify(error),
      })
    }
  )
}

function buildSoapMessageExchangeString(
  batchNumber: string,
  password = SOAP_AUTH_PASSWORD
) {
  return `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:gif="http://compass.net.nz/WebServices/GiftCard"> <soapenv:Header><gif:AuthHeader><gif:Username>${SOAP_AUTH_USERNAME}</gif:Username><gif:Password>${password}</gif:Password></gif:AuthHeader></soapenv:Header><soapenv:Body><gif:MessageExchange><gif:MessageType>${SOAP_MESSAGE_EXCHANGE_MESSAGE_TYPE}</gif:MessageType><gif:UserCode>${SOAP_MESSAGE_EXCHANGE_USER_CODE}</gif:UserCode><gif:BatchNumber>${batchNumber}</gif:BatchNumber></gif:MessageExchange></soapenv:Body></soapenv:Envelope>`
}

function makeSoapMessageExchangeCall(batchNumber: string) {
  return fromPromise(
    axios.post(
      SOAP_GIFT_CARD_URL,
      buildSoapMessageExchangeString(batchNumber),
      {
        headers: {
          'Content-Type': 'text/xml',
          SOAPAction: SOAP_GIFT_CARD_ACTION,
        },
      }
    ),
    (error) => {
      const message = `Failed to call MessageExchange SOAP for batch [${batchNumber}]`
      logger.error(error, message)

      return errAsync({
        code: ERRORS.EXTERNAL_API.code,
        message: `${message}: ${JSON.stringify(error)}`,
      } as ErrorResponse)
    }
  ).andThen((res) => {
    if (res.status !== 200) {
      const message = `epay soap request error for batch [${batchNumber}]: ${JSON.stringify(
        res.data
      )}`
      logger.error(message)
      return errAsync({
        code: ERRORS.EXTERNAL_API.code,
        message,
      } as ErrorResponse)
    }

    return okAsync(res.data as string)
  })
}

export function retrieveCardReferenceNumber({
  batchNumber,
  quantity,
}: {
  batchNumber: string
  quantity: number
}) {
  return makeSoapMessageExchangeCall(batchNumber)
    .andThen((xml) =>
      getCardReferenceNumbersFromMessageExchangeSOAP({ xml, batchNumber })
    )
    .andThen((crns) => {
      if (crns.length != quantity) {
        logger.error(
          new Error('Order mismatch'),
          `Order mismatch, expected [${quantity}] card references got [${crns.length}]`
        )
        return errAsync({
          code: ERRORS.BAD_INPUT.code,
          message: `Order mismatch, expected [${quantity}] card references got [${
            crns.length
          }]: ${JSON.stringify(crns)}`,
        } as ErrorResponse)
      }

      return okAsync(crns)
    })
}
