import { Response } from 'express'
import { TypeOf } from 'zod'

type Error = {
  code: string
  message?: string
  data?: any
}
export type ErrorResponse =
  | {
      code: string
      message: string
    }
  | { errorType: 'BAD_INPUT'; data: any }

export const ERRORS = {
  EXTERNAL_API: { code: 'EX-100', message: 'External API error' },
  EXTERNAL_API_THROTTLING: {
    code: 'EX-101',
    message: 'External API throttling',
  },
  EXTERNAL_API_UNAUTHORIZED: {
    code: 'EX-102',
    message: 'External API unauthorized',
  },

  EXTERNAL_API_NOT_FOUND: { code: 'EX-103', message: 'External API not found' },
  EXTERNAL_API_BAD_REQUEST: {
    code: 'EX-104',
    message: 'External API bad request',
  },

  BAD_INPUT: { code: 'API-100', message: 'Bad input' },

  UNAUTHORIZED: { code: 'API-101' },

  COGNITO_INVALID_CREDENTIAL: {
    code: 'COG-100',
    message: 'Invalid login credentials.',
  },
  COGNITO_EMAIL_EXIST: {
    code: 'COG-101',
    message: 'Email already exist.',
  },
  COGNITO_VERIFICATION_CODE_INVALID: {
    code: 'COG-102',
  },
  COGNITO_VERIFICATION_CODE_ATTEMPTS_EXCEEDED: {
    code: 'COG-103',
  },
  COGNITO_VERIFICATION_CODE_EXPIRED: {
    code: 'COG-104',
  },
  COGNITO_NOT_VERIFIED: {
    code: 'COG-105',
    message: 'Email is not verified.',
  },
  COGNITO_NEW_PASSWORD_REQUIRED: {
    code: 'COG-106',
    message: 'New password required',
  },
  COGNITO_PASSWORD_CHANGE_FAILED: {
    code: 'COG-107',
    message: 'Failed to change password',
  },
  COGNITO_MULTIPLE_ROLES: {
    code: 'COG-108',
    message: 'Illegal multiple roles',
  },
  COGNITO_USER_DELETE_FAILED: {
    code: 'COG-109',
    message: 'Failed to delete user',
  },
  COGNITO_USER_UPDATE_FAILED: {
    code: 'COG-110',
    message: 'User not found',
  },
  S3_SIGNED: {
    code: 'S3-100',
    message: 'Something went wrong generating the signed url',
  },
  S3_MISSING_REQUIRED_FIELD: {
    code: 'S3-101',
    message: 'Missing required field',
  },
  S3_ERROR: {
    code: 'S3-102',
    message: 'S3 error',
  },
  S3_ERROR_GET_FILESTREAM: {
    code: 'S3-103',
    message: 'Get filestream from S3 failed',
  },
  SNS_ERROR: {
    code: 'SNS-100',
    message: 'SNS error',
  },

  KYC_ORG_NAME: {
    code: 'KYC-100',
    message: "Provided organization name doesn't match NZBN.",
  },
  KYC_IDENTITY: {
    code: 'KYC-101',
    message: 'Incorrect or missing identities.',
  },
  KYC_ORG_ALREADY_EXISTS: {
    code: 'KYC-102',
    message: 'Organization already exists.',
  },
  SFTP_ERROR: {
    code: 'SFTP-100',
    message: 'SFTP error',
  },

  DATABASE_ERROR: {
    code: 'DB-100',
    message: 'Database operation failed',
  },
  NOT_FOUND: { code: 'DB-101', message: 'Not found' },
  ALREADY_EXIST: { code: 'DB-102', message: 'Already exist' },
  UNIQUE_CONSTRAINT: {
    code: 'DB-103',
    message: 'Unique constraint error',
  },
  FOREIGN_KEY_CONSTRAINT: {
    code: 'DB-104',
    message: 'Foreign key constraint',
  },
  MISSING_REQUIRED_FIELD: {
    code: 'DB-105',
    message: 'Missing required field',
  },
  ILLEGAL_ACTION: {
    code: 'DB-106',
    message: 'Illegal action',
  },
  ORGANIZATION_ALREADY_EXISTS: {
    code: 'DB-107',
    message: 'Organization already exists',
  },

  I2C_SOAP: {
    code: 'I2C-100',
    message: 'Multiple errors',
  },

  SEND_GRID_FAILED: {
    code: 'SG-100',
    message: 'Failed to send email',
  },

  INVALID_PRODUCT_ORDER: {
    code: 'PO-100',
    message: 'Invalid order',
  },
  POSTMARK_FAILED: {
    code: 'PM-100',
    message: 'Failed to send email',
  },
  CARD_WITHOUT_LOCK_CODE: {
    code: 'CA-100',
    message: 'Card without lock code',
  },
  UNKNOWN_ERROR: {
    code: 'UNKNOWN-100',
    message: 'Unknown error',
  },
  WINDCAVE_DUPLICATE_NOTIFICATION: {
    code: 'WC-100',
    message: 'Duplicate notification',
  },
  WINDCAVE_DECLINED_TRANSACTION: {
    code: 'WC-101',
    message: 'Declined transaction',
  },
  WINDCAVE_CANCELLED_TRANSACTION: {
    code: 'WC-102',
    message: 'Cancelled transaction',
  },
  WINDCAVE_SESSION_INCOMPLETE: {
    code: 'WC-103',
    message: 'Session incomplete',
  },
  CONFIG: {
    code: 'CFG-100',
    message: 'Error with the configuration',
  },
}

export type ErrorCode = (typeof ERRORS)[keyof typeof ERRORS]

type Errors = typeof ERRORS
type ErrorKey = keyof Errors

export type PickErrorCodeKey<T extends ErrorKey> = {
  [K in T]: K
}[T]

export type ErrorObject<T extends ErrorKey> = Errors[T]

export function error({
  res,
  errorType,
  message,
  data,
}: {
  res: Response
  errorType: ErrorKey
  message?: string
  data?: any
}) {
  const response: Error = ERRORS[errorType]

  if (message) {
    response.message = message
  }

  if (data) {
    response.data = data
  }

  return res.status(400).json(response)
}

export function unauthorized(res: Response) {
  return res.status(401).json({
    code: ERRORS.UNAUTHORIZED.code,
    message: 'Invalid Authentication',
  })
}
