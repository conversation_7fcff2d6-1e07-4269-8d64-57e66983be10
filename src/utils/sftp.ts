import SFTPClient from 'ssh2-sftp-client'
import * as fs from 'fs'
import { getFileStreamFromS3 } from 'utils/aws'
import logger from 'utils/logger'
import { errAsync, okAsync, ResultAsync } from 'neverthrow'
import path from 'path'
import { ERRORS } from 'utils/error'
import { IS_GITHUB_ACTIONS_ENV } from './config'

const sftpClient = new SFTPClient()
const MAX_RETRIES = 5
const INITIAL_DELAY = 2000

async function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
export async function getFilestreamFromS3({
  key,
  bucket,
}: {
  key: string
  bucket: string
}) {
  logger.debug(`getFilestreamFromS3... key=${key}, bucket=${bucket}`)
  const fsResult = await getFileStreamFromS3({
    key,
    bucket,
  })

  if (fsResult.isErr()) {
    logger.info(`error getting file stream from s3 ${key}`)
    return errAsync(ERRORS.S3_ERROR_GET_FILESTREAM)
  }
  logger.debug(`successfully got file stream from s3 ${key}`)
  const filename = path.basename(key)

  return okAsync({ filestream: fsResult.value, filename: filename })
}

export async function uploadToSftp({
  fileStream,
  remotePath,
  options,
}: {
  fileStream: NodeJS.ReadableStream
  remotePath: string
  options: SFTPClient.ConnectOptions
}) {
  if (IS_GITHUB_ACTIONS_ENV) {
    return okAsync({ message: 'Not needed in GITHUB actions env' })
  }

  if (options.password) {
    logger.debug(`sftp password = ${options.password.substring(0, 3)}`)
  }
  if (options.passphrase) {
    logger.debug(
      `sftp passphrase = ${options.passphrase.toString().substring(0, 3)}`
    )
  }
  if (typeof options.privateKey === 'string') {
    logger.debug(`SFTP_PRIVATE_KEY=${options.privateKey.substring(0, 100)}`)
  }

  logger.debug(`sftp username = ${options.username}`)
  logger.debug(`sftp host = ${options.host}`)
  logger.debug(`sftp port = ${options.port}`)

  let retries = 0
  let delay = INITIAL_DELAY

  while (retries < MAX_RETRIES) {
    try {
      logger.debug(
        `Attempt ${retries + 1} to connect to sftp :: ${remotePath}...`
      )

      await sftpClient.connect(options)
      logger.debug(`Connected to sftp ${options.host}`)
      await sftpClient.put(fileStream, remotePath)
      logger.debug(`Successfully uploaded to sftp ${remotePath}`)

      await sftpClient.end()
      return okAsync({ message: 'Successfully uploaded to SFTP' })
    } catch (error) {
      logger.error(
        error,
        `Error uploading to sftp ${remotePath}: ${JSON.stringify(error)}`
      )

      retries++
      if (retries >= MAX_RETRIES) {
        await sftpClient.end()
        logger.error(
          `Error uploading to sftp ${remotePath} after ${retries} retries.`
        )
        return errAsync(ERRORS.SFTP_ERROR)
      }

      logger.info(`Retrying in ${delay}ms...`)
      await sleep(delay)
      delay *= 2 // Exponential backoff
    }
  }

  // This line should never be reached, but TypeScript needs it
  return errAsync(ERRORS.SFTP_ERROR)
}

// export async function uploadToSftpNoRetry({
//   fileStream,
//   remotePath, // including filename
//   options,
// }: {
//   fileStream: NodeJS.ReadableStream
//   remotePath: string
//   options: SFTPClient.ConnectOptions
// }) {
//   if (IS_GITHUB_ACTIONS_ENV) {
//     return okAsync({ message: 'Not needed in GITHUB actions env' })
//   }
//   try {
//     logger.debug(`begin function uploadToSftp(...) :: ${remotePath}...`)
//     logger.debug(`sftp username = ${options.username}`)
//     logger.debug(`sftp host = ${options.host}`)
//     logger.debug(`sftp port = ${options.port}`)
//     if (options.password) {
//       logger.debug(`sftp password = ${options.password!.substring(0, 3)}`)
//     }
//     if (options.passphrase) {
//       logger.debug(
//         `sftp passphrase = ${options.passphrase!.toString().substring(0, 3)}`
//       )
//     }

//     if (typeof options.privateKey === 'string') {
//       logger.debug(`SFTP_PRIVATE_KEY=${options.privateKey?.substring(0, 100)}`)
//     }

//     await sftpClient.connect(options)
//     logger.debug(`connected to sftp ${options.host}`)
//     await sftpClient.put(fileStream, remotePath)
//     logger.debug(`successfully uploaded to sftp ${remotePath}`)
//   } catch (error) {
//     logger.error(
//       error,
//       `error uploading to sftp ${remotePath}: ${JSON.stringify(error)}`
//     )
//     return errAsync(ERRORS.SFTP_ERROR)
//   } finally {
//     await sftpClient.end()
//   }

//   return okAsync({ message: 'Successfully uploaded to SFTP' })
// }
