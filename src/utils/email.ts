/**
 * Validate and sanitize an email address.
 * Trims leading/trailing spaces, removes any non-ASCII characters, and checks for a valid email format.
 * @param email The email address to validate.
 * @returns True if the email is valid, false otherwise.
 */
export function validateEmail(email?: string | null): boolean {
  // Return false if the email is null or undefined
  if (!email) {
    return false
  }

  // Regular expression to validate email format
  // Prevents consecutive dots in domain part
  const emailRegex =
    /^[a-zA-Z0-9._+-]+@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*\.[a-zA-Z]{2,}$/

  // Return true if the email matches the regex, otherwise false
  return emailRegex.test(email)
}
