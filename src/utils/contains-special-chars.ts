export function containsSpecialCharOrAccent(str: string): boolean {
  // This regular expression matches any character that is not a-z, A-Z, 0-9, space, or common punctuation
  const specialCharRegex = /[^a-zA-Z0-9\s.,;:!?'"()-]/

  // This checks if the string contains any character outside the Basic Latin and Latin-1 Supplement Unicode blocks
  const containsNonBasicLatin = str
    .split('')
    .some((char) => char.charCodeAt(0) > 255)

  return specialCharRegex.test(str) || containsNonBasicLatin
}
