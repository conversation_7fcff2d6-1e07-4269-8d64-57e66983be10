// Adheres to teh internatinal E.164 phone number format
// Proceeded by a '+' sign
// Maximum 15 Numbers
// Minimum 7 Numbers
// No Brackets
export function validatePhoneNumber(value: string) {
  const validPhoneNumberRegExp = /^\+(?:[0-9] ?){6,14}[0-9]$/

  return validPhoneNumberRegExp.test(value)
}

export const phoneErrorMessage =
  "Phone numbers must follow the international E.164 format. (leading '+' followed by at least 7 numbers, to a maximum of 15)."
