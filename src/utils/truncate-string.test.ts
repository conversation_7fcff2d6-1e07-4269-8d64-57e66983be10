import { describe, it, expect } from 'vitest'
import { truncateString } from './truncate-string'


describe('truncateString', () => {
  it('should return an empty string for null input', () => {
    expect(truncateString(null, 5)).toBe('')
  })

  it('should return an empty string for undefined input', () => {
    expect(truncateString(undefined, 5)).toBe('')
  })

  it('should return the original string if its length is less than maxLength', () => {
    expect(truncateString('Hello', 10)).toBe('Hello')
  })

  it('should truncate the string if its length is greater than maxLength', () => {
    expect(truncateString('Hello, World!', 5)).toBe('Hello')
  })

  it('should return the original string if maxLength equals the string length', () => {
    expect(truncateString('Hello', 5)).toBe('Hello')
  })

  it('should handle empty string input', () => {
    expect(truncateString('', 5)).toBe('')
  })

  it('should handle negative maxLength by treating it as 0', () => {
    expect(truncateString('Hello', -5)).toBe('')
  })

  it('should handle decimal maxLength by flooring it', () => {
    expect(truncateString('Hello, World!', 7.8)).toBe('Hello, ')
  })

  it('should handle very large maxLength', () => {
    expect(truncateString('Hello', 1000)).toBe('Hello')
  })
})