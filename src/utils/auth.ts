// import { CognitoJwtVerifier } from 'aws-jwt-verify'
// import { fromPromise } from 'neverthrow'
// import jwt from 'jsonwebtoken'

// import { User, UserRole } from '@prisma/client'

// import {
//   AWS_COGNITO_CLIENT_ID,
//   AWS_COGNITO_USER_POOL_ID,
//   JWT_SECRET,
// } from './config'
// import logger from './logger'

// const verifier = CognitoJwtVerifier.create({
//   userPoolId: AWS_COGNITO_USER_POOL_ID,
//   tokenUse: 'access',
//   clientId: AWS_COGNITO_CLIENT_ID,
// })

// export function verifyCognitoToken({ token }: { token: string }) {
//   return fromPromise(verifier.verify(token), (error) => {
//     logger.error(error)

//     return error
//   })
// }

// export function getCognitoToken({ token }: { token: string }) {
//   const payload = jwt.decode(token, {
//     json: true,
//   })

//   return payload?.sub ?? ''
// }

// export function verifyAuthToken({ token }: { token: string }) {
//   const payload = jwt.decode(token, {
//     json: true,
//   })

//   return verifyCognitoToken({ token: payload?.sub ?? '' }).map(() => {
//     return payload as {
//       userId: string
//       userEmail: string
//       userRole: UserRole
//       orgId: string | undefined
//       orgName: string | undefined
//       sub: string
//     }
//   })
// }

// export function createAuthToken({
//   user,
//   cognitoAccessToken,
// }: {
//   user: User & {
//     organization: {
//       id: string
//       name: string
//     } | null
//   }
//   cognitoAccessToken: string
// }) {
//   return jwt.sign(
//     {
//       userId: user.id,
//       userEmail: user.email,
//       userRole: user.role,
//       orgId: user.organization?.id ?? undefined,
//       orgName: user.organization?.name ?? undefined,
//       sub: cognitoAccessToken,
//     },
//     JWT_SECRET
//   )
// }

// export type JwtPayload = {
//   userId: string
//   userEmail: string
//   userRole: string
//   orgId?: string
//   orgName?: string
// }

// export function createJwtToken({ payload }: { payload: JwtPayload }) {
//   return jwt.sign(payload, JWT_SECRET)
// }
