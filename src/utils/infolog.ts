import type {
  NzbnEntity,
  QuickIdSearchResponse,
  SearchInfologResponse,
} from '../types/infolog'

import axios from 'axios'
import CryptoJS from 'crypto-js'
import { v4 as uuidv4 } from 'uuid'

import {
  INFO_LOG_BASE_URL,
  INFO_LOG_CLIENT_ID,
  INFO_LOG_CLIENT_SECRET,
  INFO_LOG_USERNAME,
} from 'utils/config'

function buildHeaders({
  method,
  url,
}: {
  method: 'GET' | 'POST'
  url: string
}) {
  const nonce = uuidv4()
  const requestTimeStamp = Math.floor(new Date().getTime() / 1000)

  const signatureRawData =
    INFO_LOG_CLIENT_ID + nonce + requestTimeStamp + method + url

  const signature = CryptoJS.enc.Utf8.parse(signatureRawData)
  const secretByteArray = CryptoJS.enc.Base64.parse(INFO_LOG_CLIENT_SECRET)
  const signatureBytes = CryptoJS.HmacSHA256(signature, secretByteArray)
  const requestSignatureBase64String =
    CryptoJS.enc.Base64.stringify(signatureBytes)

  const hmacKey =
    'amx ' +
    INFO_LOG_CLIENT_ID +
    ':' +
    nonce +
    ':' +
    requestTimeStamp +
    ':' +
    requestSignatureBase64String

  return {
    Authorization: hmacKey,
    'X-User': INFO_LOG_USERNAME,
  }
}

export const infoLogSessionReference = (name: string, nzbn: string) => {
  return `${name} ${nzbn}`
}

export function getEntity(props: { SearchId?: string; NZBN: string }) {
  const url = `${INFO_LOG_BASE_URL}/nzbn/v1/get/entity`

  return axios.get<{ Entity: NzbnEntity }>(url, {
    headers: buildHeaders({ method: 'GET', url }),
    data: props,
  })
}

export function searchInfolog(props: {
  SessionReference: string
  Name: string
}) {
  const url = `${INFO_LOG_BASE_URL}/nzbn/v1/search/entity`

  return axios.post<SearchInfologResponse>(
    url,
    {
      ...props,
    },
    {
      headers: buildHeaders({ method: 'POST', url }),
      timeout: 0, // disable axios timeout
    }
  )
}

type PEPCheckBase = {
  SessionReference: string
  LastName: string
  FirstName: string
  DateOfBirth: Date
}

type DriverLicensePEPCheck = PEPCheckBase & {
  IncludeDriverLicenseCheck: true
  DriversLicenceNumber: string
  DriversLicenceVersion: string
}

type PassportPEPCheck = PEPCheckBase & {
  IncludePassportCheck: true
  TravelDocNum: string
  ExpiryDate: Date
  PassportConsent: true
}

export async function pepCheck(
  props: DriverLicensePEPCheck | PassportPEPCheck
) {
  const url = `${INFO_LOG_BASE_URL}/quikid/v1/search`

  const res = await axios.post<QuickIdSearchResponse>(
    url,
    {
      ...props,
      IncludeNZBNCheck: false,
      IncludePropertyCheck: false,
      IncludeDirectoryCheck: false,
      IncludeMVRCheck: false,
      IncludeTenencyCheck: false,
      RunRapidIDPep: true,
      SearchConsent: true,
    },
    {
      headers: buildHeaders({ method: 'POST', url }),
    }
  )

  if (res.status === 200) {
    const unmatchedFields = []

    // Driver license check
    if (res.data.NZTAResult.IsIncluded) {
      if (!res.data.NZTAResult.FirstNameMatch) {
        unmatchedFields.push('Official First Name')
      }

      if (!res.data.NZTAResult.LastNameMatch) {
        unmatchedFields.push('Official Surname')
      }

      if (!res.data.NZTAResult.DOBMatch) {
        unmatchedFields.push('Date of Birth')
      }

      if (unmatchedFields.length > 0 && res.data.NZTAResult.NZTAReportId) {
        unmatchedFields.push(
          `NZTAReportId: ${res.data.NZTAResult.NZTAReportId}`
        )
      }
    }

    // Passport check
    if (res.data.DIAResult.IsIncluded) {
      if (!res.data.DIAResult.FirstNameMatch) {
        unmatchedFields.push('Official First Name')
      }

      if (!res.data.DIAResult.LastNameMatch) {
        unmatchedFields.push('Official Surname')
      }

      if (!res.data.DIAResult.DOBMatch) {
        unmatchedFields.push('Date of Birth')
      }

      if (unmatchedFields.length > 0 && res.data.DIAResult.DIAReportId) {
        unmatchedFields.push(`DIAReportId: ${res.data.DIAResult.DIAReportId}`)
      }
    }

    const errors = res.data.Errors ?? []

    if (res.data.PepResult?.content?.data?.match_status != 'no_match') {
      errors.push('Failed PEP search')
      errors.push(`PepReportId: ${res.data.PepReportId}`)
    }

    return {
      name: `${props.FirstName} ${props.LastName}`,
      valid: unmatchedFields.length === 0 && errors.length === 0,
      unmatchedFields: unmatchedFields,
      infoLogErrors: errors,
    }
  }

  return {
    valid: false,
    unmatchedFields: [],
    infoLogErrors: ['Request to InfoLog failed'],
  }
}
