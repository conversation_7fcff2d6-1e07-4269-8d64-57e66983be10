/***
 * Truncates a string to a maximum length.
 * If the string is longer than the maxLength, it will be truncated to the maxLength.
 * If the string is shorter than the maxLength, it will be returned as is.
 * If the maxLength is less than or equal to 0, it will return an empty string.
 * @param str - The string to truncate.
 * @param maxLength - The maximum length of the string.
 * @returns The truncated string.
 */
export function truncateString(
  str: string | null | undefined,
  maxLength: number
) {
  if (!str) {
    return ''
  }

  // Ensure maxLength is a positive integer and turns negative to zero
  const length = Math.max(0, Math.floor(maxLength))

  if (str.length > length) {
    return str.slice(0, length)
  }

  return str
}
