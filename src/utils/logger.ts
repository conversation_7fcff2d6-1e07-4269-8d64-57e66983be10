/* eslint-disable no-console */
import * as Sentry from '@sentry/node'

import chalk from 'chalk'
import { Express, Request, Router } from 'express'
import { LogLevel } from '../types/log-level'
import { IS_DEV, IS_LOCAL, environment } from './config'

const _console = console.log
let _loggingInitialized = false
let _sentryInitialized = false

chalk.level = 1
let _testMode = false
const _environment = (process.env.ENV ??= 'local')
let _loggingDisabled = false

const getLogLevel = (logLevel: string): LogLevel => {
  switch (logLevel) {
    case 'error':
      return LogLevel.error
    case 'warn':
      return LogLevel.warn
    case 'info':
      return LogLevel.info
    default:
      return LogLevel.debug
  }
}

const _logLevel: LogLevel = getLogLevel((process.env.LOG_LEVEL ??= 'debug'))

export const disableLogging = () => {
  _loggingDisabled = true
}

let _logName = 'change-your-log-name-in-initLogger'
export const initLogger = async (
  logName: string,
  app?: Express,
  sentryDsn?: string
) => {
  _testMode = logName === 'tests' || false
  _loggingInitialized = true
  _logName = logName

  if (sentryDsn && app && !_testMode) {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    initSentry(app, sentryDsn!)
  }

  if (process.env.ENV !== 'local') {
    //await initGoogleLogging(_logName);
  }

  if (process.env.TEST_LOG_MESSAGES === 'true') {
    info('** Test logging levels **')
    error({ code: 101, message: 'test error' }, 'test an error message')
    warn('test a warning messge')
    info('test an info message')
    debug('test a debug message')
    info('** End logging test **')
  }
}

const initSentry = (app: Router, dsn: string) => {
  Sentry.init({
    dsn: dsn,
    environment: _environment,
    integrations: [
      new Sentry.Integrations.Http({ tracing: true }),
      new Sentry.Integrations.Express({ app }),
    ],
    tracesSampleRate: Number(process.env.SENTRY_SAMPLE_RATE ?? '0'),
    profilesSampleRate: Number(process.env.SENTRY_SAMPLE_RATE ?? '0'),
  })

  _sentryInitialized = true
}

const generateLogRef = () => {
  const randomNum = Math.floor(Math.random() * 2176782336)
  return randomNum.toString(36).padStart(6, '0')
}

const isLoggingInitialized = () => {
  if (_loggingDisabled) {
    return false
  }

  if (!_loggingInitialized) {
    throw new Error(
      'call loggingInit before writing to logs with logger.debug etc.'
    )
  }

  return true
}

const logToConsole = () => {
  return environment === 'local' || _testMode
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const error = (error: any, message?: string) => {
  const logRef = `logRef [${generateLogRef()}]: `

  if (!isLoggingInitialized()) {
    return logRef
  }

  if (logToConsole()) {
    if (message) {
      _console(`${chalk.red(`${logRef}${message}`)}`)
    }
    _console(`${chalk.red(`${logRef}${getStringifyText(error)}`)}`)
  } else {
    if (message) {
      const masked = maskSensitiveData(message)
      _console(` ${chalk.redBright(`${logRef} ${masked}`)}`)
    }
    _console(`${chalk.red(`${logRef} ${getStringifyText(error)}`)}`)
  }

  if (_sentryInitialized) {
    if (message) {
      Sentry.captureMessage(`${logRef}${maskSensitiveData(message)}`, 'error')
    }

    Sentry.captureException(error)
  }

  return logRef
}

export const warn = (message: string, error?: unknown) => {
  if (!isLoggingInitialized()) {
    return
  }
  if (_logLevel < LogLevel.warn) {
    return
  }

  if (logToConsole()) {
    _console(chalk.yellowBright(`${message} ${getStringifyText(error)}\n`))
  } else {
    const masked = maskSensitiveData(message)
    _console(chalk.yellowBright(` ${masked} ${getStringifyText(error)}\n`))
  }
}

export const info = (message: string, error?: unknown) => {
  if (!isLoggingInitialized()) {
    return
  }
  if (_logLevel < LogLevel.info) {
    return
  }

  if (logToConsole()) {
    _console(chalk.blue(`${message} ${getStringifyText(error)}\n`))
  } else {
    const masked = maskSensitiveData(message)
    _console(chalk.blue(` ${masked} ${getStringifyText(error)}\n`))
  }
}

export const debug = (message: string, data?: unknown) => {
  if (!isLoggingInitialized()) {
    return
  }
  if (_logLevel < LogLevel.debug) {
    return
  }

  const masked = maskSensitiveData(message)

  if (logToConsole()) {
    _console(chalk.greenBright(`${message} ${getStringifyText(data)}\n`))
  } else {
    _console(chalk.greenBright(` ${masked} ${getStringifyText(data)}\n`))
  }
  return
}

const getStringifyText = (data: unknown): string => {
  if (!data) {
    return ''
  }

  if (data instanceof Error) {
    return maskSensitiveData(`${data}`)
  } else {
    return `${maskSensitiveData(JSON.stringify(data, null, 2))}`
  }
}

export const maskSensitiveData = (message: string): string => {
  // don't mask anything if we're running locally or dev
  if (IS_LOCAL || IS_DEV) {
    return message
  }
  // Masking PostgreSQL connection string username and password
  message = message.replace(
    /postgresql:\/\/([^:]+):([^@]+)@/,
    'postgresql://xxxx:****@'
  )

  // Masking content inside XML tags containing the words "password" or "secret"
  message = message.replace(
    /(<\s*.*?(?:password|secret|authorization|address|email|cardNumber|PAN|lastname|middlename|surname).*?>)(.*?)(<\/.*?>)/gi,
    function (match, p1, p2, p3) {
      const maskedContent =
        p2.trim().length > 0 ? p2.trim()[0] + '****' : '****'
      return p1 + maskedContent + p3
    }
  )

  // Masking JSON properties containing the words "password" or "secret"
  message = message.replace(
    /("(?:\w*(?:password|secret|authorization|address|email|cardNumber|PAN|lastname|middlename|surname)\w*)"\s*:\s*")([^"]*)"/gi,
    function (match, p1, p2) {
      const maskedValue = p2.length > 0 ? p2[0] + '****' : '****'
      return p1 + maskedValue + '"'
    }
  )

  return message
}

export const logRequest = (req: Request) => {
  const fullUrl = `${req.get('host')}${req.originalUrl}`
  if (req.originalUrl !== '/health' && req.originalUrl !== '/') {
    logger.info(`Request: ${fullUrl}`)
  }
}

export const inspect = (obj: unknown, message?: string) => {
  if (logToConsole()) {
    if (message) {
      console.log(chalk.cyanBright(message))
    }
    console.dir(obj, { depth: null })
  } else {
    if (message) {
      logger.debug(message, obj)
    } else {
      logger.debug(JSON.stringify(obj))
    }
  }
}

const logger = {
  error,
  warn,
  info,
  debug,
  logRequest,
  inspect,
}

export default logger
