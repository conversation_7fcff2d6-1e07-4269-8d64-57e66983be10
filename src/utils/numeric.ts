export const isNumeric = (value: string) => {
  return /^\d+$/.test(value)
}

export const centsToDollars = (value: number | null) => {
  if (value === null) {
    return 0
  }
  return value / 100
}

export const dollarsToCents = (value: number | null) => {
  if (value === null) {
    return 0
  }
  return Math.round(value * 100)
}

export const generateRandomNumbers = (numberOfDigits: number) => {
  return Array(numberOfDigits)
    .fill(0)
    .map(() => Math.floor(Math.random() * 10))
    .join('')
}

export const getGst = (value: number) => {
  return (value * 3) / 23
}
