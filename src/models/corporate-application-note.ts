import { Prisma } from '@prisma/client'
import { fromPromise } from 'neverthrow'

import { PickErrorCodeKey } from 'utils/error'
import { prisma } from 'utils/prisma'
import * as logger from 'utils/logger'

export function createCorporateApplicationNote({
  userId,
  corporateApplicationId,
  title,
  message,
}: {
  userId: string
  corporateApplicationId: string
  title?: string
  message: string
}) {
  return fromPromise(
    prisma.corporateApplicationNote.create({
      data: {
        userId,
        corporateApplicationId,
        title,
        message,
      },
      include: {
        user: {
          select: {
            firstName: true,
          },
        },
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'FOREIGN_KEY_CONSTRAINT'> => {
      logger.error(error)

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2003'
      ) {
        return 'FOREIGN_KEY_CONSTRAINT'
      }

      return 'DATABASE_ERROR'
    }
  )
}

export function updateCorporateApplicationNote({
  id,
  data,
}: {
  id: string
  data: Prisma.CorporateApplicationNoteUpdateInput
}) {
  return fromPromise(
    prisma.corporateApplicationNote.update({
      where: {
        id,
      },
      data,
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'NOT_FOUND'> => {
      logger.error(error)

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        return 'NOT_FOUND'
      }

      return 'DATABASE_ERROR'
    }
  )
}
