import { CorporateApplicationStatus, Prisma } from '@prisma/client'
import { fromPromise } from 'neverthrow'

import { ERRORS, PickErrorCodeKey } from 'utils/error'
import { prisma } from 'utils/prisma'
import * as logger from 'utils/logger'

export async function findManyCorporateApplication({
  userId,
}: {
  userId: string
}) {
  return fromPromise(
    prisma.corporateApplication.findMany({
      where: {
        userId,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'FOREIGN_KEY_CONSTRAINT'> => {
      logger.error(error)

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2003'
      ) {
        return 'FOREIGN_KEY_CONSTRAINT'
      }

      return 'DATABASE_ERROR'
    }
  )
}

export function createCorporateApplication(userId: string) {
  return fromPromise(
    prisma.corporateApplication.create({
      data: {
        userId,
      },
    }),
    (error): Pick<PERSON><PERSON>r<PERSON><PERSON><PERSON>ey<'DATABASE_ERROR' | 'FOREIGN_KEY_CONSTRAINT'> => {
      logger.error(error)

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2003'
      ) {
        return 'FOREIGN_KEY_CONSTRAINT'
      }

      return 'DATABASE_ERROR'
    }
  )
}

export function updateCorporateApplication({
  id,
  data,
}: {
  id: string
  data: Prisma.CorporateApplicationUpdateInput
}) {
  return fromPromise(
    prisma.corporateApplication.update({
      where: {
        id,
      },
      data,
      include: {
        user: true,
        organization: true,
        beneficialOwners: true,
      },
    }),
    (error) => {
      logger.error(error)

      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        // logger.error('Corporate application not found')
        return {
          code: ERRORS.NOT_FOUND.code,
          message: 'Corporate application not found',
        }
      }

      return {
        code: ERRORS.DATABASE_ERROR.code,
        message: 'Database error',
      }
    }
  )
}

export function findCorporateApplication({
  corporateApplicationId,
  userId,
}: {
  corporateApplicationId: string
  userId: string
}) {
  return fromPromise(
    prisma.corporateApplication.findFirst({
      where: {
        id: corporateApplicationId,
        userId,
      },
      include: {
        directors: true,
        beneficialOwners: true,
        user: true,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      logger.error(error)
      return 'DATABASE_ERROR'
    }
  )
}

export async function findCorporateApplicationWithNotes(id: string) {
  return fromPromise(
    prisma.corporateApplication
      .findUnique({
        where: {
          id,
        },
        include: {
          directors: true,
          beneficialOwners: true,
          notes: {
            include: {
              user: {
                select: {
                  firstName: true,
                },
              },
            },
          },
          user: true,
        },
      })
      .then((application) => {
        if (!application) {
          throw 'NOT_FOUND'
        }

        return application
      }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'NOT_FOUND'> => {
      if (error !== 'NOT_FOUND') {
        return 'DATABASE_ERROR'
      }

      return error
    }
  )
}
