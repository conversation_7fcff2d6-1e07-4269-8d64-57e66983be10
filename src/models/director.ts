import { <PERSON><PERSON><PERSON>, Director } from '@prisma/client'
import { fromPromise } from 'neverthrow'

import { PickErrorCodeKey } from 'utils/error'
import { prisma } from 'utils/prisma'
import * as logger from 'utils/logger'

export function upsertDirectors({
  corporateApplicationId,
  directors,
}: {
  corporateApplicationId: string
  directors: Partial<Director>[]
}) {
  logger.info('upsertDirectors')

  return fromPromise(
    prisma.$transaction(
      directors.map((director) => {
        if (director.id) {
          return prisma.director.update({
            where: {
              id: director.id,
            },
            data: director,
          })
        }

        return prisma.director.create({
          data: {
            corporateApplicationId,
            ...director,
          },
        })
      })
    ),
    (
      error
    ): PickErrorCodeKey<
      'DATABASE_ERROR' | 'FOREIGN_KEY_CONSTRAINT' | 'NOT_FOUND'
    > => {
      logger.error(error)

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003': {
            logger.warn('FOREIGN_KEY_CONSTRAINT')
            return 'FOREIGN_KEY_CONSTRAINT'
          }
          case 'P2025': {
            logger.warn('NOT_FOUND')
            return 'NOT_FOUND'
          }
        }
      }

      return 'DATABASE_ERROR'
    }
  )
}

export function createDirector({
  corporateApplicationId,
}: {
  corporateApplicationId: string
}) {
  return fromPromise(
    prisma.director.create({
      data: {
        corporateApplicationId,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'FOREIGN_KEY_CONSTRAINT'> => {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2003'
      ) {
        return 'FOREIGN_KEY_CONSTRAINT'
      }

      return 'DATABASE_ERROR'
    }
  )
}

export function deleteDirector(id: string) {
  return fromPromise(
    prisma.director.delete({
      where: {
        id,
      },
    }),
    (error): PickErrorCodeKey<'DATABASE_ERROR'> => {
      return 'DATABASE_ERROR'
    }
  )
}
