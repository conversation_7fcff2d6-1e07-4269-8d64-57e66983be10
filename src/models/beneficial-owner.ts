import { BeneficialOwner, Prisma } from '@prisma/client'
import { fromPromise } from 'neverthrow'

import { ERRORS } from 'utils/error'
import * as logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function upsertBeneficialOwners({
  corporateApplicationId,
  beneficialOwners,
}: {
  corporateApplicationId: string
  beneficialOwners: Partial<BeneficialOwner>[]
}) {
  return fromPromise(
    prisma.$transaction(
      beneficialOwners.map((beneficialOwner) => {
        if (beneficialOwner.id) {
          return prisma.beneficialOwner.update({
            where: {
              id: beneficialOwner.id,
            },
            data: beneficialOwner,
          })
        }

        return prisma.beneficialOwner.create({
          data: {
            corporateApplicationId,
            ...beneficialOwner,
          },
        })
      })
    ),
    (error) => {
      logger.error(error)

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2003': {
            return ERRORS.FOREIGN_KEY_CONSTRAINT
          }
          case 'P2025': {
            return ERRORS.NOT_FOUND
          }
        }
      }

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function createBeneficialOwner({
  corporateApplicationId,
}: {
  corporateApplicationId: string
}) {
  return fromPromise(
    prisma.beneficialOwner.create({
      data: {
        corporateApplicationId,
      },
    }),
    (error) => {
      logger.error(error)
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2003'
      ) {
        return ERRORS.FOREIGN_KEY_CONSTRAINT
      }

      return ERRORS.DATABASE_ERROR
    }
  )
}

export function deleteBeneficialOwner(id: string) {
  return fromPromise(
    prisma.beneficialOwner.delete({
      where: {
        id,
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  )
}
