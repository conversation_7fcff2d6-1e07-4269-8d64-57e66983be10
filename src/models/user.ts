import type { UserR<PERSON> } from '@prisma/client'

import { Prisma } from '@prisma/client'
import { fromPromise } from 'neverthrow'

import { prisma } from 'utils/prisma'
import { ERRORS, PickErrorCodeKey } from 'utils/error'
import logger from 'utils/logger'

export function findUserById<T extends Prisma.UserInclude>(
  id: string,
  // @ts-expect-error - defined in the generic that it can be undefined
  include: T = undefined
) {
  return fromPromise(
    prisma.user
      .findUnique({
        where: {
          id,
        },
        include,
      })
      .then((user) => {
        if (!user) {
          throw 'NOT_FOUND'
        }

        return user
      }),
    (error): PickErrorCodeKey<'DATABASE_ERROR' | 'NOT_FOUND'> => {
      if (error !== 'NOT_FOUND') {
        return 'DATABASE_ERROR'
      }

      return error
    }
  )
}
// DEPRECATED = use createOrgUser instead
export function createUser({
  id,
  firstName,
  lastName,
  email,
  role,
  branch,
  orgId,
}: {
  id: string
  firstName: string
  lastName: string
  email: string
  role: UserRole
  branch?: string
  orgId?: string
}) {
  return fromPromise(
    prisma.user.create({
      data: {
        id,
        firstName,
        lastName,
        email,
        role,
        branch,
        organization: {
          connect: {
            id: orgId,
          },
        },
      },
    }),
    (
      error
    ): PickErrorCodeKey<
      'DATABASE_ERROR' | 'FOREIGN_KEY_CONSTRAINT' | 'ALREADY_EXIST'
    > => {
      logger.error(error)

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002': {
            return 'ALREADY_EXIST'
          }
          case 'P2003': {
            return 'FOREIGN_KEY_CONSTRAINT'
          }
        }
      }

      return 'DATABASE_ERROR'
    }
  )
}

export function findUserWithCorporateApplication({
  userId,
  corporateApplicationId,
}: {
  userId: string
  corporateApplicationId: string
}) {
  return fromPromise(
    prisma.user.findFirst({
      where: {
        id: userId,
        corporateApplications: {
          id: corporateApplicationId,
        },
      },
      select: {
        corporateApplications: {
          select: {
            name: true,
            nzbn: true,
            infologSessionId: true,
          },
        },
      },
    }),
    (error) => {
      logger.error(error)

      return ERRORS.DATABASE_ERROR
    }
  )
}

/**
 * Find a user that has connections to application and directors/beneficial owners
 * @param  {string} userId - user id
 * @param  {string} corporateApplicationId - application id
 * @param  {string} ids - ids of director or beneficial owner
 * @param  {string} idsOf - the type of the ids, either director or beneficial owner
 * @returns {User | null} - returns the user if connection exist, otherwise null
 */
export function findUserWithCorporateApplicationAndDirectorsOrBeneficialOwners({
  userId,
  corporateApplicationId,
  ids,
  idsOf,
}: {
  userId: string
  corporateApplicationId: string
  ids: string[]
  idsOf: 'directors' | 'beneficialOwners'
}) {
  logger.info('ids in find director or owner=', ids)

  return fromPromise(
    prisma.user.findFirst({
      where: {
        id: userId,
        corporateApplications: {
          id: corporateApplicationId,
          [idsOf]: {
            every: {
              id: {
                in: ids,
              },
            },
          },
        },
      },
      include: {
        corporateApplications: {
          select: {
            name: true,
            nzbn: true,
          },
        },
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  )
}

/**
 * Find a user that has connections to application and director/beneficial owner
 * @param  {string} userId - user id
 * @param  {string} corporateApplicationId - application id
 * @param  {string} id - id of director or beneficial owner
 * @param  {string} idOf - the type of the id, either director or beneficial owner
 * @returns {User | null} - returns the user if connection exist, otherwise null
 */
export async function findUserWithCorporateApplicationAndDirectorOrBeneficialOwner({
  userId,
  corporateApplicationId,
  id,
  idOf,
}: {
  userId: string
  corporateApplicationId: string
  id: string
  idOf: 'directors' | 'beneficialOwners'
}) {
  return fromPromise(
    prisma.user.findFirst({
      where: {
        id: userId,
        corporateApplications: {
          id: corporateApplicationId,
          [idOf]: {
            some: {
              id,
            },
          },
        },
      },
    }),
    (error) => {
      logger.error(error)
      return ERRORS.DATABASE_ERROR
    }
  )
}
