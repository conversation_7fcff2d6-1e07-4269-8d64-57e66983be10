import { OrderStatus, Prisma } from '@prisma/client'
import { err, fromPromise, ok, ResultAsync } from 'neverthrow'

import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export type UpdatedProductOrderData = Prisma.ProductOrderGetPayload<{
  include: {
    user: true
    organization: true
    FundingProductOrderItem: true
    productOrderItems: {
      include: {
        product: {
          include: {
            design: true
            logo: true
          }
        }
      }
    }
  }
}>

export function updateManyProductOrderStatus({
  orderIds,
  orderStatus,
}: {
  orderIds: string[]
  orderStatus: OrderStatus
}) {
  return fromPromise(
    prisma.productOrder.updateMany({
      where: {
        id: { in: orderIds },
      },
      data: { orderStatus: orderStatus },
    }),
    (error) => {
      const message = `updateProductOrderStatus..Failed to update product orders to status [${orderStatus}]: [${orderIds.join(
        ','
      )}]`
      const logRef = logger.error(error, message)

      return err({
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      })
    }
  ).match(
    ({ count }) => {
      logger.info(
        `updateProductOrderStatus..Updated ${count} ProductOrder to status [${orderStatus}]: [${orderIds.join(
          ','
        )}]`
      )
      return count
    },
    () => 0
  )
}

export function updateProductOrderStatusV2({
  orderNumber,
  orderStatus,
  orgId,
}: {
  orderNumber: string
  orderStatus: OrderStatus
  orgId?: string
}) {
  return fromPromise(
    prisma.productOrder.updateMany({
      where: {
        orderNumber,
        organizationId: orgId,
      },
      data: {
        orderStatus,
      },
    }),
    (error) => {
      const message = `updateProductOrderStatusV2.. error updating product order to processing, order number [${orderNumber}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  ).andThen(({ count }) => {
    if (count === 0) {
      const message = `updateProductOrderStatus..Failed to update product order to status [${orderStatus}] for order number [${orderNumber}]`
      logger.warn(message)
      return err({ code: errorCodes.db.ITEM_NOT_FOUND, message } as SimpleError)
    }

    return ok({ updatedOrder: orderNumber, toStatus: orderStatus })
  })
}

interface ActivationResult {
  updated: number
  toStatus: OrderStatus
  failedToUpdate: number
}

export function activateManyOrdersIfAllCardsActivated(orderNumbers: string[]) {
  return fromPromise(
    prisma.productOrder.updateMany({
      where: {
        orderNumber: { in: orderNumbers },
        productOrderItems: {
          every: {
            cardItems: {
              every: {
                activated: true,
              },
            },
          },
        },
      },
      data: {
        orderStatus: OrderStatus.ACTIVATED,
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `activateManyOrdersIfAllCardsActivated.. Failed to update ProductOrder status to ACTIVATED for orders [${orderNumbers}]`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `Failed to activate orders`,
        logRef,
      } as SimpleError
    }
  ).andThen(({ count }) => {
    return ok({
      updated: count,
      toStatus: OrderStatus.ACTIVATED,
      failedToUpdate: orderNumbers.length - count,
    } as ActivationResult)
  })
}

export function getUniqueOrderNumbersForCrnList({
  crns,
  orgId,
}: {
  crns: string[]
  orgId: string
}) {
  return ResultAsync.fromPromise(
    prisma.productOrder.findMany({
      distinct: ['id', 'orderNumber'],
      where: {
        organizationId: orgId,
        productOrderItems: {
          some: {
            cardItems: {
              some: {
                externalCardReferenceNumber: {
                  in: crns,
                },
              },
            },
          },
        },
      },
      select: {
        id: true,
        orderNumber: true,
      },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to get order numbers for crns')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to get order numbers for crns',
        logRef,
      } as SimpleError
    }
  )
}

export type ProductOrderLockCodeEmailData = Prisma.ProductOrderGetPayload<{
  include: {
    user: true
    productOrderItems: {
      include: {
        cardItems: true
        product: true
      }
    }
  }
}>

export function getProductOrderLockCodeEmailData(orderNumber: string) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        orderNumber,
        lockCodeEmailSent: false,
        productOrderItems: {
          some: {
            deliveryMethod: 'COURIER',
            cardItems: {
              every: {},
            },
          },
        },
      },
      include: {
        user: true,
        productOrderItems: {
          where: {
            lockCodeEmailSent: false,
          },
          include: {
            cardItems: true,
            product: true,
          },
        },
      },
    }),
    (error) => {
      logger.warn('failed to query product orders', error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      logger.info(`No product orders found for order number ${orderNumber}`)
      return err({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `No product orders found for order number ${orderNumber}`,
      } as SimpleError)
    }
    return ok(productOrder)
  })
}

export function getProductOrder({
  orderNumber,
  orgId,
}: {
  orderNumber: string
  orgId: string
}) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        orderNumber,
        organizationId: orgId,
      },
      include: {
        user: true,
        organization: true,
        productOrderItems: {
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
        FundingProductOrderItem: {
          include: {
            product: {
              include: {
                design: true,
                logo: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to get product order')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  ).andThen((productOrder) => {
    if (!productOrder) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Product order with id [${orderNumber}] not found`,
      } as SimpleError)
    }

    return ok(productOrder)
  })
}

export function createEmptyProductOrder({
  orgId,
  userId,
}: {
  orgId: string
  userId: string
}) {
  return fromPromise(
    prisma.$transaction(async (tx) => {
      const productOrder = await tx.productOrder.create({
        data: {
          user: {
            connect: {
              id: userId,
            },
          },
          organization: {
            connect: {
              id: orgId,
            },
          },
        },
      })

      return tx.productOrder.update({
        where: { id: productOrder.id },
        data: { orderNumber: productOrder.orderIncrementNumber.toString() },
        select: { orderNumber: true, orderStatus: true, id: true },
      })
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to create empty product order')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  )
}

export function updateProductOrderFees({
  productOrderId,
  digitalFeeTotal,
  discountTotal,
  digitalFeeDiscountTotal,
  loadingFeeDiscountTotal,
  shippingFeeDiscountTotal,
  loadingFeeTotal,
  subTotal,
  totalQuantity,
  orderTotal,
  gstAmount,
  shippingTotal,
  creditCardFee,
}: {
  productOrderId: string
  digitalFeeTotal: number
  discountTotal: number
  digitalFeeDiscountTotal: number
  loadingFeeDiscountTotal: number
  shippingFeeDiscountTotal: number
  loadingFeeTotal: number
  subTotal: number
  totalQuantity: number
  orderTotal: number
  gstAmount: number
  shippingTotal: number
  creditCardFee: number
}) {
  return fromPromise(
    prisma.productOrder.update({
      where: { id: productOrderId },
      data: {
        digitalFeeTotal,
        discountTotal,
        digitalFeeDiscountTotal,
        loadingFeeDiscountTotal,
        shippingFeeDiscountTotal,
        loadingFeeTotal,
        subTotal,
        totalQuantity,
        orderTotal,
        gstAmount,
        shippingTotal,
        creditCardFee,
      },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to update product order fees')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  )
}

export function deletePendingProductOrderAndItemsById({
  productOrderId,
  orgId,
}: {
  productOrderId: string
  orgId: string
}) {
  return fromPromise(
    prisma.$transaction(async (tx) => {
      const productOrder = await tx.productOrder.findUniqueOrThrow({
        where: {
          id: productOrderId,
        },
      })

      if (
        productOrder?.organizationId === orgId &&
        productOrder.orderStatus === OrderStatus.PENDING
      ) {
        const productOrderItemRes = await prisma.productOrderItem.deleteMany({
          where: { productOrderId },
        })

        const productOrderRes = await prisma.productOrder.delete({
          where: { id: productOrderId },
        })

        return [productOrderRes, productOrderItemRes]
      }

      throw new Error(`Unable to delete product order [${productOrderId}]`)
    }),
    (error) => ({
      code: errorCodes.db.UNKNOWN_ERROR,
      message: 'Failed to delete product order and items',
      logRef: logger.error(error, `Failed to delete product order and items`),
    })
  )
}

export function searchProductOrders({
  where,
  page,
  pageSize,
}: {
  where: Prisma.ProductOrderWhereInput
  page: number
  pageSize: number
}) {
  const query = prisma.productOrder.findMany({
    where,
    orderBy: { createdAt: 'desc' },
    take: pageSize,
    skip: (page - 1) * pageSize,
    select: {
      id: true,
      orderType: true,
      orderNumber: true,
      orderStatus: true,
      createdAt: true,
      orderTotal: true,
      totalQuantity: true,
      user: {
        select: {
          firstName: true,
          lastName: true,
        },
      },
    },
  })

  const count = prisma.productOrder.count({ where })

  return fromPromise(prisma.$transaction([query, count]), (error) => {
    logger.warn(
      `there was an error searching orders with where clause: [${JSON.stringify(
        where
      )}]`
    )
    return {
      code: errorCodes.db.UNKNOWN_ERROR,
      message: error,
    } as SimpleError
  })
}

export function getProductOrderWithFundingOrderItems(id: string) {
  return fromPromise(
    prisma.productOrder.findUniqueOrThrow({
      where: { id },
      include: { FundingProductOrderItem: true },
    }),
    (e) => {
      logger.warn(`failed to getFundingProductOrder [${id}]`, e)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: e,
      } as SimpleError
    }
  )
}
