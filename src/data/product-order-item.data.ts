import { err, fromPromise, ok, ResultAsync } from 'neverthrow'
import { prisma } from 'utils/prisma'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger, { error } from 'utils/logger'
import { Prisma } from '@prisma/client'
import { IS_DEV } from 'utils/config'

export type UpdatedProductOrderItemData = Prisma.ProductOrderItemGetPayload<{
  include: {
    product: {
      include: {
        design: true
        logo: true
      }
    }
  }
}>

export type ProductOrderItemWithProduct = Prisma.ProductOrderItemGetPayload<{
  include: {
    product: true
  }
}>

export function updateProductOrderItemWithBatchId({
  id,
  externalBatchId,
}: {
  id: string
  externalBatchId: string
}) {
  return fromPromise(
    prisma.productOrderItem.update({
      where: { id },
      data: { externalBatchId, error: null },
    }),
    (error) => {
      const logRef = logger.error(error, 'Failed to update product order item')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  ).andThen((updatedOrderItem) => {
    if (!updatedOrderItem) {
      const logRef = logger.warn('Product order item not found with id: ', id)
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Product order item id: [${id}] not found`,
        logRef,
      } as SimpleError)
    }

    return ok(updatedOrderItem as UpdatedProductOrderItemData)
  })
}

export type UnProcessedOrderItem = Prisma.ProductOrderItemGetPayload<{
  select: {
    recipientName: true
    externalBatchId: true
    id: true
    quantity: true
    recipientEmail: true
    unitPrice: true
    productOrder: {
      select: {
        id: true
        createdAt: true
        orderNumber: true
        lockCode: true
      }
    }
  }
}>

// Enhancements to this query with the REN migration
// now only process items that are in PROCESSING status
// and have no error
export function GetOrderItemsWithEmptyCardItemsV2(): ResultAsync<
  UnProcessedOrderItem[],
  SimpleError
> {
  // too much data in dev
  const createdAtFilter = IS_DEV
    ? {
        createdAt: {
          gte: new Date(2024, 9, 14).toISOString(),
        },
      }
    : {}

  return fromPromise(
    prisma.productOrderItem.findMany({
      where: {
        product: {
          OR: [
            {
              type: 'CUSTOM',
            },
            {
              type: 'PREZZY',
              cardTypes: {
                has: 'PHYSICAL',
              },
            },
          ],
        },
        externalBatchId: {
          not: null,
        },
        error: {
          equals: null,
        },
        cardItems: {
          none: {},
        },
        productOrder: {
          orderStatus: 'PROCESSING',
          ...createdAtFilter,
        },
      },
      select: {
        recipientName: true,
        externalBatchId: true,
        id: true,
        quantity: true,
        recipientEmail: true,
        unitPrice: true,
        error: true,
        productOrder: {
          select: {
            id: true,
            createdAt: true,
            orderNumber: true,
            lockCode: true,
            orderStatus: true,
          },
        },
      },
      orderBy: [
        {
          productOrder: {
            createdAt: 'asc',
          },
        },
        {
          productOrder: {
            orderNumber: 'asc',
          },
        },
      ],
    }),
    (error) => {
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to get all the empty externalCardReferenceNumbers',
        logRef: logger.error(
          error,
          'failed to get all the empty externalCardReferenceNumber'
        ),
      } as SimpleError
    }
  ).map((orderItems) => {
    return orderItems as UnProcessedOrderItem[]
  })
}

export function updateProductOrderItemError({
  id,
  error,
}: {
  id: string
  error: string
}) {
  return fromPromise(
    prisma.productOrderItem.update({
      where: { id },
      data: { error },
    }),
    (error) =>
      ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to update product order item with error. How ironic.',
        logRef: logger.error(
          error,
          `Failed to update ProductOrderItem [${id}], with error message`
        ),
      } as SimpleError)
  ).map(() => {
    return // no return value
  })
}

export function createProductOrderItems(
  productOrderItems: Prisma.ProductOrderItemCreateManyInput[]
) {
  return fromPromise(
    prisma.productOrderItem.createMany({
      data: productOrderItems,
    }),
    (error) => ({
      code: errorCodes.db.UNKNOWN_ERROR,
      message: 'Failed to create product order items',
      logRef: logger.error(error, `Failed to create product order items`),
    })
  )
}

export function getProductOrderItemsByProductOrderId({
  productOrderId,
  orgId,
}: {
  productOrderId: string
  orgId: string
}) {
  return fromPromise(
    prisma.productOrderItem.findMany({
      where: {
        productOrderId,
        productOrder: {
          organizationId: orgId,
        },
      },
      include: { product: true },
    }),
    (error) => ({
      code: errorCodes.db.UNKNOWN_ERROR,
      message: 'Failed to get product order items',
      logRef: logger.error(error, `Failed to get product order items`),
    })
  )
}

export function updateProductOrderItemRecipientEmail({
  orgId,
  productOrderItemId,
  recipientEmail,
}: {
  orgId: string
  productOrderItemId: string
  recipientEmail: string
}) {
  return fromPromise(
    prisma.productOrderItem.updateMany({
      where: {
        id: productOrderItemId,
        productOrder: { organizationId: orgId },
      },
      data: { recipientEmail },
    }),
    (error) => {
      const message = `Failed to update product order item recipient email: ${recipientEmail}`
      const logRef = logger.error(
        error,
        `updateProductOrderItemRecipientEmail... ${message}`
      )

      return err({
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError)
    }
  ).andThen((result) => {
    if (result.count === 0) {
      const message = `Failed to update product order item recipient email: ${recipientEmail}, productOrderItemId: ${productOrderItemId}`
      const logRef = logger.error(
        `updateProductOrderItemRecipientEmail... ${message}`
      )

      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message,
        logRef,
      } as SimpleError)
    }

    return ok({
      success: true,
    })
  })
}
