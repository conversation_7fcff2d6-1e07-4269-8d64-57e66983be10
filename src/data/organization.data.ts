import { prisma } from 'utils/prisma'
import { errorCodes, SimpleError } from 'types/simple-error'
import { fromPromise } from 'neverthrow'
import logger from 'utils/logger'

export function setCustomerOrderReference({
  orgId,
  enabled,
}: {
  orgId: string
  enabled: boolean
}) {
  return fromPromise(
    prisma.organization.update({
      where: {
        id: orgId,
      },
      data: {
        enableCustomerOrderReference: enabled,
      },
      select: {
        enableCustomerOrderReference: true,
      },
    }),
    (error) => {
      logger.warn(
        `there was an error updating the customer reference for org: [${orgId}]`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}

export function getCustomerOrderReferenceForOrg(orgId: string) {
  return fromPromise(
    prisma.organization.findUnique({
      where: {
        id: orgId,
      },
      select: {
        enableCustomerOrderReference: true,
      },
    }),
    (error) => {
      logger.warn(
        `there was an error getting the customer reference for org: [${orgId}]`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      }
    }
  ).map((org) => org?.enableCustomerOrderReference)
}

export function getEnableStockCardsValueForOrg(orgId: string) {
  return fromPromise(
    prisma.organization.findUnique({
      where: {
        id: orgId,
      },
      select: {
        enableBrandedStockCards: true,
      },
    }),
    (error) => {
      logger.warn(
        `there was an error getting the stock cards value for org: [${orgId}]`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      }
    }
  ).map((org) => {
    return {
      enabled: org?.enableBrandedStockCards,
    }
  })
}

export function setBrandedStockCardsValueForOrg({
  orgId,
  enabled,
}: {
  orgId: string
  enabled: boolean
}) {
  return fromPromise(
    prisma.organization.update({
      where: {
        id: orgId,
      },
      data: {
        enableBrandedStockCards: enabled,
      },
      select: {
        enableBrandedStockCards: true,
      },
    }),
    (error) => {
      logger.warn(
        `there was an error updating the customer reference for org: [${orgId}]`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}

export function getOrganizationById(orgId: string) {
  return fromPromise(
    prisma.organization.findUnique({
      where: { id: orgId },
      select: {
        id: true,
        name: true,
      },
    }),
    (error) => {
      logger.warn(`there was an error getting org: [${orgId}]`)
      return {
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: error,
      } as SimpleError
    }
  )
}

export function getOrderVisibilityRestriction(orgId: string) {
  return fromPromise(
    prisma.organization.findUnique({
      where: { id: orgId },
      select: { enableOrderVisibilityRestriction: true },
    }),
    (error) => {
      logger.warn(`there was an error getting org: [${orgId}]`)
      return {
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: error,
      } as SimpleError
    }
  )
}

export function setOrderVisibilityRestrictionForOrg({
  orgId,
  enabled,
}: {
  orgId: string
  enabled: boolean
}) {
  return fromPromise(
    prisma.organization.update({
      where: { id: orgId },
      data: { enableOrderVisibilityRestriction: enabled },
      select: { enableOrderVisibilityRestriction: true },
    }),
    (error) => {
      logger.warn(`there was an error updating org: [${orgId}]`)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}
