import { prisma } from 'utils/prisma'
import { errorCodes, SimpleError } from 'types/simple-error'
import { err, fromPromise } from 'neverthrow'
import logger from 'utils/logger'

export function createReissueTransactionHistory({
  orgId,
  currentProxyNumber,
  newProxyNumber,
  sequenceNumber,
  currentRecipientEmail,
  newRecipientEmail,
  createdByUserId,
  productOrderItemCardId,
}: {
  orgId: string
  currentProxyNumber: string
  newProxyNumber: string
  sequenceNumber: number
  currentRecipientEmail: string
  newRecipientEmail: string
  createdByUserId: string
  productOrderItemCardId: string
}) {
  logger.info(
    `saving: resissueTransactionHistory...currentProxyNumber: ${currentProxyNumber}, newProxyNumber: ${newProxyNumber}, sequenceNumber: ${sequenceNumber}, currentRecipientEmail: ${currentRecipientEmail}, newRecipientEmail: ${newRecipientEmail}`
  )

  return fromPromise(
    prisma.productOrderItemCard.findUnique({
      where: { id: productOrderItemCardId },
      include: {
        productOrderItem: {
          include: {
            productOrder: true,
          },
        },
      },
    }),
    (error) => {
      const message = `createReissueTransactionHistory... failed to find card [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  ).andThen((card) => {
    if (!card) {
      const message = `Product order item card not found with id [${productOrderItemCardId}] and orgId [${orgId}]`
      const logRef = logger.error(
        errorCodes.db.ITEM_NOT_FOUND,
        `createReissueTransactionHistory...[${message}]`
      )
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message,
        logRef,
      } as SimpleError)
    }

    if (card.productOrderItem.productOrder.organizationId !== orgId) {
      const message = `Organization ID mismatch for card [${productOrderItemCardId}]`
      const logRef = logger.error(
        errorCodes.db.BAD_INPUT,
        `createReissueTransactionHistory... ${message}`
      )
      return err({
        code: errorCodes.external.api.BAD_REQUEST,
        message,
        logRef,
      } as SimpleError)
    }

    return fromPromise(
      prisma.reissueTransactionHistory.create({
        data: {
          currentProxyNumber,
          newProxyNumber,
          sequenceNumber,
          currentRecipientEmail,
          newRecipientEmail,
          createdByUserId,
          productOrderItemCardId,
        },
      }),
      (error) => {
        const message = `Failed to create history record for card [${productOrderItemCardId}]`
        const logRef = logger.error(
          error,
          `createReissueTransactionHistory...${message}`
        )
        return {
          code: errorCodes.db.UNKNOWN_ERROR,
          message,
          logRef,
        } as SimpleError
      }
    )
  })
}
