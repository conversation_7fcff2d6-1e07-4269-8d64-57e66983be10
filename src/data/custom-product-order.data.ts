import { CustomProductOrder } from '@prisma/client'
import { fromPromise } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export function updateCustomProductOrderError({
  id,
  error,
}: {
  id: string
  error: string | null
}) {
  return fromPromise(
    prisma.customProductOrder.update({
      where: { id },
      data: { error },
    }),
    (err) => {
      const logRef = logger.error(
        err,
        `Failed to update error for CustomProductOrder with id ${id}`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `Failed to update error for CustomProductOrder with id ${id}`,
        logRef,
      } as SimpleError
    }
  )
}

export function updateCustomProductOrderWithExternalBatchId({
  id,
  externalBatchId1,
  externalBatchId2,
}: {
  id: string
  externalBatchId1?: string
  externalBatchId2?: string
}) {
  
  const data: { externalBatchId1?: string; externalBatchId2?: string } = {}

  if (externalBatchId1 !== undefined) {
    data.externalBatchId1 = externalBatchId1
  }

  if (externalBatchId2 !== undefined) {
    data.externalBatchId2 = externalBatchId2
  }

  return fromPromise(
    prisma.customProductOrder.update({
      where: { id },
      data,
    }),
    (err) => {
      const logRef = logger.error(
        err,
        `Failed to update externalBatchId for CustomProductOrder with id ${id}`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `Failed to update externalBatchId for CustomProductOrder with id ${id}`,
        logRef,
      } as SimpleError
    }
  )
}
