import {
  FloatFundsTransactionType,
  OrderStatus,
  OrderType,
  PaymentMethod,
  Prisma,
} from '@prisma/client'
import { err, fromPromise, ok } from 'neverthrow'
import { checkErrorCode, errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

/**
 * Creates a new float funds transaction and updates the organization's balance
 */
export function createFloatFundsTransaction({
  orgId,
  amount,
  transactionType,
  createdByUserId,
  orderNumber,
  orderId,
  customProductOrderId,
  description,
  note,
  bankAccountName,
  bankAccountNumber,
}: {
  orgId: string
  amount: number
  transactionType: FloatFundsTransactionType
  createdByUserId: string
  orderNumber?: string
  orderId?: string
  customProductOrderId?: string
  description?: string
  note?: string
  bankAccountName?: string
  bankAccountNumber?: string
}) {
  // Determine if this is a credit or debit to the balance
  const isCredit = transactionType === FloatFundsTransactionType.CREDIT

  // Amount to adjust the balance by (positive for credit, negative for debit)
  const balanceAdjustment = isCredit ? amount : -amount

  return fromPromise(
    prisma.$transaction(
      (tx) => {
        return tx.orgFloatFund
          .findFirst({
            where: { orgId },
          })
          .then((orgFloatFund) => {
            if (!orgFloatFund) {
              return Promise.reject(
                new Error(
                  `Organization float fund not found for orgId: ${orgId}`
                )
              )
            }

            // Calculate new balance
            const balanceAfterTransaction =
              orgFloatFund.currentBalance + balanceAdjustment

            // Prevent negative balance
            if (balanceAfterTransaction < 0) {
              return Promise.reject({
                code: errorCodes.db.BAD_INPUT,
                message: `Insufficient funds: Transaction would result in negative balance (${balanceAfterTransaction})`,
              } as SimpleError)
            }

            const customerCode = orgFloatFund.customerCode

            // Create transaction record
            return tx.floatFundsTransaction
              .create({
                data: {
                  orgId,
                  customerCode,
                  amount,
                  balanceAfterTransaction,
                  floatFundsTransactionType: transactionType,
                  transactionType,
                  orderId,
                  customProductOrderId,
                  orderNumber,
                  description,
                  note,
                  createdBy: createdByUserId,
                  transactionDate: new Date().toISOString(),
                  bankAccountName,
                  bankAccountNumber,
                },
              })
              .then((transaction) => {
                // Update organization balance
                return tx.orgFloatFund
                  .update({
                    where: { orgId },
                    data: {
                      currentBalance: balanceAfterTransaction,
                      lastUpdatedDate: new Date().toISOString(),
                    },
                  })
                  .then(() => transaction) // Return the transaction as the result
              })
          })
      },
      {
        isolationLevel: 'Serializable', // Using serializable to prevent race conditions
      }
    ),
    (error) => {
      if (checkErrorCode(error, errorCodes.db.BAD_INPUT)) {
        return error as SimpleError
      }

      const message = `createFloatFundsTransaction.. Failed to create transaction for organization [${orgId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Organization float fund not found for orgId: ${orgId}`,
        logRef,
      } as SimpleError
    }
  ).andThen((transaction) => {
    logger.info(
      `createFloatFundsTransaction.. Created ${transactionType} transaction [${transaction.id}] for organization [${orgId}] with amount [${amount}]`
    )
    return ok(transaction)
  })
}

export function getFloatFundsTransactions({
  orgId,
  startDate,
  endDate,
  transactionType,
  page,
  pageSize,
  orderNumber,
}: {
  orgId: string
  startDate?: Date
  endDate?: Date
  transactionType?: FloatFundsTransactionType
  page: number
  pageSize: number
  orderNumber?: string
}) {
  const where: Prisma.FloatFundsTransactionWhereInput = {
    orgId,
  }

  // Add date range filter if provided
  if (startDate || endDate) {
    where.transactionDate = {
      ...(startDate && { gte: startDate }),
      ...(endDate && { lte: endDate }),
    }
  }

  // Add transaction type filter if provided
  if (transactionType) {
    where.transactionType = transactionType
  }

  // Add order number filter if provided
  if (orderNumber) {
    where.orderNumber = {
      contains: orderNumber,
      mode: 'insensitive',
    }
  }

  return fromPromise(
    Promise.all([
      prisma.floatFundsTransaction.count({ where }),
      prisma.floatFundsTransaction.findMany({
        where,
        orderBy: {
          transactionDate: 'desc',
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        select: {
          id: true,
          amount: true,
          balanceAfterTransaction: true,
          transactionType: true,
          orderNumber: true,
          description: true,
          note: true,
          transactionDate: true,
          createdByUser: true,
        },
      }),
    ]),
    (error) => {
      const message = `getFloatFundsTransactions.. Failed to fetch transactions for organization [${orgId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to fetch transactions',
        logRef,
      } as SimpleError
    }
  ).map(([totalCount, transactions]) => ({
    transactions: transactions.map((transaction) => ({
      id: transaction.id,
      amount: transaction.amount,
      balanceAfterTransaction: transaction.balanceAfterTransaction,
      transactionType: transaction.transactionType,
      orderNumber: transaction.orderNumber,
      description: transaction.description,
      note: transaction.note,
      transactionDate: transaction.transactionDate,
      createdBy: `${transaction.createdByUser.firstName || ''} ${
        transaction.createdByUser.lastName || ''
      }`,
    })),
    page,
    pageSize,
    totalCount,
  }))
}

export function getFloatFundsTransactionById(transactionId: number) {
  return fromPromise(
    prisma.floatFundsTransaction.findUnique({
      where: {
        id: transactionId,
      },
      select: {
        id: true,
        amount: true,
        balanceAfterTransaction: true,
        transactionType: true,
        orderNumber: true,
        description: true,
        note: true,
        transactionDate: true,
        createdByUser: true,
        bankAccountName: true,
        bankAccountNumber: true,
      },
    }),
    (error) => {
      const message = `getFloatFundsTransactionById.. Failed to fetch transaction [${transactionId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to fetch transaction',
        logRef,
      } as SimpleError
    }
  ).andThen((transaction) => {
    if (!transaction) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Transaction [${transactionId}] not found`,
      } as SimpleError)
    }

    return ok({
      id: transaction.id,
      amount: transaction.amount,
      balanceAfterTransaction: transaction.balanceAfterTransaction,
      transactionType: transaction.transactionType,
      orderNumber: transaction.orderNumber,
      description: transaction.description,
      note: transaction.note,
      transactionDate: transaction.transactionDate,
      bankAccountName: transaction.bankAccountName,
      bankAccountNumber: transaction.bankAccountNumber,
      createdBy: `${transaction.createdByUser.firstName || ''} ${
        transaction.createdByUser.lastName || ''
      }`,
    })
  })
}

export function getOrgFloatFundsBalance(orgId: string) {
  return fromPromise(
    prisma.orgFloatFund.findUnique({
      where: { orgId: orgId },
      select: {
        currentBalance: true,
        lastUpdatedDate: true,
        isActive: true,
      },
    }),
    (error) => {
      const message = `getOrgFloatFundsBalance.. Failed to fetch balance for organization [${orgId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to fetch organization balance',
        logRef,
      } as SimpleError
    }
  ).andThen((orgFloatFund) => {
    if (!orgFloatFund) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Organization ${orgId} not found`,
      } as SimpleError)
    }
    return ok({
      currentBalance: orgFloatFund.currentBalance,
      lastUpdatedDate: orgFloatFund.lastUpdatedDate,
      isActive: orgFloatFund.isActive,
    })
  })
}

export function upsertFloatFundOrg({
  orgId,
  updatedByUserId,
}: {
  orgId: string
  updatedByUserId: string
}) {
  return fromPromise(
    prisma.organization
      .findUnique({
        where: { id: orgId },
        select: { customerCode: true },
      })
      .then((org) => {
        if (!org?.customerCode) {
          throw new Error(
            `Organization [${orgId}] does not have a customer code`
          )
        }

        return prisma.orgFloatFund.upsert({
          where: {
            orgId: orgId,
          },
          create: {
            orgId: orgId,
            customerCode: org.customerCode,
            currentBalance: 0,
            lastUpdatedDate: new Date().toISOString(),
            isActive: true,
            updatedBy: updatedByUserId,
          },
          update: {},
          select: {
            orgId: true,
            customerCode: true,
            currentBalance: true,
            lastUpdatedDate: true,
            isActive: true,
          },
        })
      }),
    (error) => {
      const message = `upsertFloatFundOrg.. Failed to upsert float fund org for organization [${orgId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to upsert float fund organization',
        logRef,
      } as SimpleError
    }
  ).map((orgFloatFund) => {
    logger.info(
      `upsertFloatFundOrg.. Successfully upserted float fund org for organization [${orgId}]`
    )
    return orgFloatFund
  })
}

export function deactivateFloatFundOrg(orgId: string) {
  return fromPromise(
    prisma.orgFloatFund.update({
      where: {
        orgId: orgId,
      },
      data: {
        isActive: false,
      },
    }),
    (error) => {
      const message = `deactivateFloatFundOrg.. Failed to deactivate float fund org for organization [${orgId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to deactivate float fund organization',
        logRef,
      } as SimpleError
    }
  ).map((orgFloatFund) => {
    logger.info(
      `deactivateFloatFundOrg.. Successfully deactivated float fund org for organization [${orgId}]`
    )
    return orgFloatFund
  })
}

export function activateFloatFundOrg(orgId: string) {
  return fromPromise(
    prisma.orgFloatFund.update({
      where: {
        orgId: orgId,
      },
      data: {
        isActive: true,
      },
    }),
    (error) => {
      const message = `activateFloatFundOrg.. Failed to activate float fund org for organization [${orgId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to activate float fund organization',
        logRef,
      } as SimpleError
    }
  ).map((orgFloatFund) => {
    logger.info(
      `activateFloatFundOrg.. Successfully activated float fund org for organization [${orgId}]`
    )
    return orgFloatFund
  })
}

export function createFloatFundsOrder({
  orgId,
  userId,
  amount,
  billingAddress,
  city,
  country,
  postCode,
  paymentMethod,
  purchaseOrderNumber,
}: {
  orgId: string
  userId: string
  amount: number
  billingAddress: string
  city: string
  country: string
  postCode: string
  paymentMethod: PaymentMethod
  purchaseOrderNumber?: string
}) {
  return fromPromise(
    prisma.$transaction(async (tx) => {
      const productOrder = await tx.productOrder.create({
        data: {
          orderStatus: OrderStatus.SUBMITTED,
          subTotal: amount,
          orderTotal: amount,
          paymentMethod,
          billingAddress,
          city,
          country,
          postCode,
          purchaseOrderNumber,
          orderType: OrderType.FLOAT_FUNDS,
          organization: { connect: { id: orgId } },
          user: { connect: { id: userId } },
        },
      })

      return tx.productOrder.update({
        where: { id: productOrder.id },
        data: { orderNumber: productOrder.orderIncrementNumber.toString() },
        select: { orderNumber: true, orderStatus: true, id: true },
      })
    }),
    (error) => {
      const message = `createFloatFundsOrder.. Failed to create float funds order`
      const logRef = logger.error(error, message)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to create float fund order',
        logRef,
      } as SimpleError
    }
  )
}

export function getUnprocessedFloatFundsOrder({
  orderId,
}: {
  orderId: string
}) {
  return fromPromise(
    prisma.productOrder.findFirst({
      where: {
        id: orderId,
        orderStatus: 'RELEASING',
        orderType: OrderType.FLOAT_FUNDS,
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `Failed to get unprocessed funding order 
        [${orderId}]`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Failed to get unprocessed funding order',
        logRef,
      } as SimpleError
    }
  )
}
