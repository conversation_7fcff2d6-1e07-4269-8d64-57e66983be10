import { fromPromise } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'
import { z } from 'zod'

export function getGlobalDiscounts() {
  return fromPromise(prisma.globalDiscount.findFirstOrThrow(), (error) => {
    logger.warn('There was an error getting all global discounts')
    return {
      code: errorCodes.db.UNKNOWN_ERROR,
      message: error,
    } as SimpleError
  })
}

export const globalDiscountUpdateSchema = z.object({
  loadingFee: z.number().optional().default(0),
  digitalFee: z.number().optional().default(0),
  urbanShipping: z.number().optional().default(0),
  ruralShipping: z.number().optional().default(0),
  createCard: z.number().optional().default(0),
  stockCard: z.number().optional().default(0),
  highResStockCard: z.number().optional().default(0),
  lowResStockCard: z.number().optional().default(0),
  userId: z.string(),
})

export function updateGlobalDiscounts({
  loadingFee,
  digitalFee,
  urbanShipping,
  ruralShipping,
  createCard,
  stockCard,
  highResStockCard,
  lowResStockCard,
  userId,
}: z.infer<typeof globalDiscountUpdateSchema>) {
  return fromPromise(
    prisma.globalDiscount.update({
      where: { id: 1 },
      data: {
        loadingFee,
        digitalFee,
        urbanShipping,
        ruralShipping,
        createCard,
        stockCard,
        highResStockCard,
        lowResStockCard,
        updatedByUserId: userId,
      },
    }),
    (error) => {
      logger.warn('There was an error updating global discount', error)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}
