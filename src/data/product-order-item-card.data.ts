import { <PERSON><PERSON><PERSON>, Prisma, ProductOrderItemCard } from '@prisma/client'
import { err, fromPromise, ok, ResultAsync } from 'neverthrow'
import { FundingOrderArgs } from 'routes/funding.route'
import { errorCodes, SimpleError } from 'types/simple-error'
import { ERRORS } from 'utils/error'
import logger from 'utils/logger'
import { centsToDollars } from 'utils/numeric'
import { prisma } from 'utils/prisma'

export function createProductOrderItemCardV2({
  productOrderItemId,
  externalCardReferenceNumber,
  unitPriceInCents,
  lockCode,
  activated = false,
  isVirtualCard = false,
}: {
  productOrderItemId: string
  externalCardReferenceNumber: string
  unitPriceInCents: number
  lockCode?: string
  activated: boolean
  isVirtualCard?: boolean
}) {
  return fromPromise(
    prisma.productOrderItemCard.create({
      data: {
        externalCardReferenceNumber,
        productOrderItemId,
        unitPriceInCents,
        lockCode,
        activated,
        createdAt: new Date().toISOString(),
        virtualCardSendCount: isVirtualCard ? 1 : 0,
      },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        'Failed to create product order item card'
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
        logRef,
      } as SimpleError
    }
  )
}

export function updateProductOrderItemCardError({
  id,
  errorString,
}: {
  id: string
  errorString: string | null
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: { id },
      data: { error: errorString },
    }),
    (error) => {
      const logRef = logger.error(
        error,
        `Failed to update error for ProductOrderItemCard with id ${id}`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `Failed to update error for ProductOrderItemCard with id ${id}`,
        logRef,
      } as SimpleError
    }
  )
}

export function getProductOrderItemCardsByOrder({
  orderNumber,
  orgId,
}: {
  orderNumber: string
  orgId: string
}): ResultAsync<ProductOrderItemCard[], SimpleError> {
  return fromPromise(
    prisma.productOrderItemCard.findMany({
      where: {
        productOrderItem: {
          deliveryMethod: {
            in: ['COURIER', 'PICKUP'],
          },
          product: {
            type: {
              in: ['CUSTOM', 'PREZZY', 'STOCK'],
            },
          },
          productOrder: {
            orderNumber,
            organizationId: orgId,
          },
        },
      },
    }),
    (error) => {
      logger.warn(
        `Failed to query product order item cards with order number [${orderNumber}]`,
        error
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `Failed to query product order item cards with order number [${orderNumber}]`,
      } as SimpleError
    }
  ).andThen((productOrderItemCards) => {
    if (!productOrderItemCards || productOrderItemCards.length === 0) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Product order item cards not found with order number [${orderNumber}]`,
      } as SimpleError)
    }

    return ok(productOrderItemCards)
  })
}

export function updateProductOrderItemCardActivatedV2({
  productOrderItemCardId,
}: {
  productOrderItemCardId: string
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: {
        id: productOrderItemCardId,
      },
      data: {
        activatedAt: new Date().toISOString(),
        activated: true,
      },
    }),
    (error) => {
      const message = `updateProductOrderItemCardActivated..Failed to update card ACTIVATED for [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}

export function updateProductOrderItemCardFundsLoaded({
  productOrderItemCardId,
}: {
  productOrderItemCardId: string
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: {
        id: productOrderItemCardId,
      },
      data: {
        fundsLoaded: true,
      },
    }),
    (error) => {
      const message = `updateProductOrderItemCardFundsLoaded..Failed to update card FUNDS_LOADED for [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}

export function updateProductOrderItemCardLockCodeSet({
  productOrderItemCardId,
}: {
  productOrderItemCardId: string
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: {
        id: productOrderItemCardId,
      },
      data: {
        lockCodeSet: true,
        lockCodeSetAt: new Date().toISOString(),
      },
    }),
    (error) => {
      const message = `updateProductOrderItemCardLockCodeSet..Failed to update card LOCKCODESET for [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}

export function updateProductOrderItemCardBlockedStatus({
  productOrderItemCardId,
  status,
}: {
  productOrderItemCardId: string
  status: 'block' | 'unblock'
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: {
        id: productOrderItemCardId,
      },
      data: {
        blockedAt: new Date().toISOString(),
        blocked: status === 'block',
      },
    }),
    (error) => {
      const message = `updateProductOrderItemCardBlocked..Failed to update card BLOCKED for [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}

export function getManyPrezzyCardsByCrn({
  crns,
  orgId,
}: {
  crns: string[]
  orgId?: string
}): ResultAsync<ProductOrderItemCard[], SimpleError> {
  return fromPromise(
    prisma.productOrderItemCard.findMany({
      where: {
        externalCardReferenceNumber: {
          in: crns,
        },
        productOrderItem: {
          product: {
            type: {
              in: ['CUSTOM', 'PREZZY', 'STOCK'],
            },
          },
          productOrder: orgId
            ? {
                organizationId: orgId,
              }
            : undefined,
        },
      },
    }),
    (error) => {
      const message = `getManyPrezzyCardsByCrn..Failed to query DB`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      }
    }
  ).andThen((productOrderItemCards) => {
    if (!productOrderItemCards || productOrderItemCards.length === 0) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Product order item cards not found with crns [${crns}]`,
      } as SimpleError)
    }

    return ok(productOrderItemCards)
  })
}

export function findProductOrderItemCardsByCrns(crns: string[]) {
  return fromPromise(
    prisma.productOrderItemCard.findMany({
      where: {
        externalCardReferenceNumber: {
          in: crns,
        },
      },
      select: {
        externalCardReferenceNumber: true,
        lockCode: true,
        activated: true,
        blocked: true,
        unitPriceInCents: true,
        productOrderItem: {
          select: {
            recipientEmail: true,
            customerReference: true,
            productOrder: {
              select: {
                orderNumber: true,
                organization: {
                  select: {
                    name: true,
                  },
                },
              },
            },
            product: {
              select: {
                cardTypes: true,
              },
            },
          },
        },
      },
    }),
    (error) => {
      logger.warn(
        `findProductOrderItemCardsByCrns...Failed to query product order item cards with CRNs [${crns.join(
          ', '
        )}]`,
        error
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `findProductOrderItemCardsByCrns...Failed to query product order item cards with CRNs [${crns.join(
          ', '
        )}]`,
      } as SimpleError
    }
  )
}

export function manageCardsListCardsWithFiltersForOrg({
  filter,
  orgId,
  page,
  pageSize,
}: {
  filter?: string
  orgId?: string
  page: number
  pageSize: number
}) {
  const where: Prisma.ProductOrderItemCardWhereInput = {
    usedInFundingOrder: false,
    NOT: { unitPriceInCents: 0 },
  }

  // Add filter conditions if filter term exists
  if (filter?.trim()) {
    where.OR = [
      {
        externalCardReferenceNumber: {
          contains: filter,
          mode: 'insensitive',
        },
      },
      {
        productOrderItem: {
          productOrder: {
            OR: [
              {
                orderNumber: {
                  contains: filter,
                  mode: 'insensitive',
                },
              },
            ],
          },
        },
      },
    ]
  }

  if (orgId) {
    where.productOrderItem = {
      productOrder: {
        organizationId: orgId,
      },
    }
  }

  const count = prisma.productOrderItemCard.count({
    where,
  })

  const query = prisma.productOrderItemCard.findMany({
    where,
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
      createdAt: 'desc',
    },
    select: {
      externalCardReferenceNumber: true,
      lockCode: true,
      unitPriceInCents: true,
      productOrderItem: {
        select: {
          recipientEmail: true,
          customerReference: true,
          productOrder: {
            select: {
              orderNumber: true,
              organization: {
                select: {
                  name: true,
                },
              },
            },
          },
          product: {
            select: {
              cardTypes: true,
            },
          },
        },
      },
    },
  })

  return fromPromise(prisma.$transaction([count, query]), (error) => {
    logger.warn(
      `failed to query cards, orgId [${orgId}] search [${filter}] page [${page}]`,
      error
    )
    return ERRORS.DATABASE_ERROR
  }).map(([count, cards]) => {
    logger.debug(`found ${cards.length} cards, count = ${count}`)

    return {
      count,
      cards: cards.map((card) => {
        return {
          referenceId: card.externalCardReferenceNumber,
          lockCode: card.lockCode,
          orderNumber: card.productOrderItem.productOrder.orderNumber,
          cardType: card.productOrderItem.product.cardTypes,
          orgName: card.productOrderItem.productOrder.organization.name,
          email: card.productOrderItem.recipientEmail,
          customerReference: card.productOrderItem.customerReference,
          initialBalance: centsToDollars(card.unitPriceInCents),
        }
      }),
    }
  })
}

export function getProductOrderItemCardIds(fundingOrderArgs: FundingOrderArgs) {
  return fromPromise(
    prisma.productOrderItemCard.findMany({
      where: {
        externalCardReferenceNumber: {
          in: fundingOrderArgs.cards.map((item) => item.crn),
        },
      },
      select: {
        id: true,
        externalCardReferenceNumber: true,
      },
    }),
    (error) => {
      logger.error(error, 'Failed to fetch product order item card ids')
      return {
        code: 'DB-100',
        message: 'Database operation failed',
      }
    }
  ).map((cards) => ({
    productOrderItemCards: cards.map((card) => ({
      id: card.id,
      crn: card.externalCardReferenceNumber,
    })),
  }))
}

// data/product-order-item-card.data.ts

export function virtualCardsListWithFilter({
  orgId,
  filter,
  page = 1,
  pageSize = 20,
}: {
  orgId: string
  filter?: string
  page?: number
  pageSize?: number
}) {
  logger.debug(`virtualCardsListWithFilter... ${filter}`)

  const where: Prisma.ProductOrderItemCardWhereInput = {
    productOrderItem: {
      product: {
        cardTypes: {
          has: CardType.VIRTUAL,
        },
      },
      productOrder: {
        organizationId: orgId,
      },
    },
  }

  // Add filter condition if filter term exists
  if (filter?.trim()) {
    where.OR = [
      {
        productOrderItem: {
          productOrder: {
            orderNumber: {
              contains: filter,
              mode: 'insensitive',
            },
          },
        },
      },
      {
        productOrderItem: {
          recipientEmail: {
            contains: filter,
            mode: 'insensitive',
          },
        },
      },
    ]
  }

  const count = prisma.productOrderItemCard.count({
    where,
  })

  const query = prisma.productOrderItemCard.findMany({
    where,
    skip: (page - 1) * pageSize,
    take: pageSize,
    orderBy: {
      createdAt: 'desc',
    },
    select: {
      id: true,
      externalCardReferenceNumber: true,
      unitPriceInCents: true,
      productOrderItem: {
        select: {
          id: true,
          recipientName: true,
          recipientEmail: true,
          customerReference: true,
          productOrder: {
            select: {
              orderNumber: true,
              createdAt: true,
            },
          },
          product: {
            select: {
              name: true,
              design: {
                select: {
                  cardDesignUrl: true,
                },
              },
              logo: {
                select: {
                  logoUrl: true,
                },
              },
            },
          },
        },
      },
    },
  })

  return fromPromise(prisma.$transaction([count, query]), (error) => {
    logger.warn(
      `Failed to query virtual cards, orgId [${orgId}] filter [${filter}] page [${page}]`,
      error
    )
    return ERRORS.DATABASE_ERROR
  }).map(([count, cards]) => {
    logger.debug(`found ${cards.length} cards, count = ${count}`)

    return {
      count,
      virtualCards: cards.map((card) => ({
        id: card.id,
        productOrderItemId: card.productOrderItem.id,
        crn: card.externalCardReferenceNumber,
        orderNumber: card.productOrderItem.productOrder.orderNumber,
        orderDate: card.productOrderItem.productOrder.createdAt,
        productName: card.productOrderItem.product.name,
        designUrl: card.productOrderItem.product.design?.cardDesignUrl,
        logoUrl: card.productOrderItem.product.logo?.logoUrl,
        recipientName: card.productOrderItem.recipientName,
        recipientEmail: card.productOrderItem.recipientEmail,
        initialValue: centsToDollars(card.unitPriceInCents || 0),
      })),
    }
  })
}

export function incrementResendVirtualCardCount({
  productOrderItemCardId,
}: {
  productOrderItemCardId: string
}) {
  return fromPromise(
    prisma.productOrderItemCard.update({
      where: {
        id: productOrderItemCardId,
      },
      data: {
        virtualCardSendCount: {
          increment: 1,
        },
        virtualCardUpdatedAt: new Date(),
      },
    }),
    (error) => {
      const message = `incrementResendVirtualCardCount.... failed fro productOrderItemCardId: [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  )
}

export function updateProductOrderItemCardAfterReissue({
  orgId,
  productOrderItemCardId,
  newProxyNumber,
}: {
  orgId: string
  productOrderItemCardId: string
  newProxyNumber: string
}) {
  return fromPromise(
    prisma.productOrderItemCard.updateMany({
      where: {
        id: productOrderItemCardId,
        productOrderItem: {
          productOrder: {
            organizationId: orgId,
          },
        },
      },
      data: {
        externalCardReferenceNumber: newProxyNumber,
        virtualCardUpdatedAt: new Date(),
      },
    }),
    (error) => {
      const message = `updateProductOrderItemCardAfterReissue.... failed fro productOrderItemCardId: [${productOrderItemCardId}]`
      const logRef = logger.error(error, message)

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message,
        logRef,
      } as SimpleError
    }
  ).andThen(({ count }) => {
    if (count === 0) {
      return err({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `updateProductOrderItemCardAfterReissue.... failed fro productOrderItemCardId: [${productOrderItemCardId}]`,
      } as SimpleError)
    }

    return ok({
      success: true,
    })
  })
}
