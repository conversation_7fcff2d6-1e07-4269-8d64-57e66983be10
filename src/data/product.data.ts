import { Prisma } from '@prisma/client'
import { err, from<PERSON>rom<PERSON>, ok, ResultAsync } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'

export type UpdatedProductStatusResult = Prisma.ProductGetPayload<{
  include: {
    customProductOrder: {
      include: {
        user: true
      }
    }
    logo: {
      select: {
        id: true
        name: true
        logoUrl: true
      }
    }
    design: {
      select: {
        externalCardDesignId: true
        externalCardType: true
        designType: true
        resolution: true
      }
    }
  }
}>

export function updateProductToApproved({
  id,
}: {
  id: string
}): ResultAsync<UpdatedProductStatusResult, SimpleError> {
  logger.info(`updating status to APPROVED for product id: ${id}`)

  return fromPromise(
    prisma.product.update({
      where: {
        id,
      },
      data: {
        status: 'APPROVED',
      },
      include: {
        customProductOrder: {
          where: {
            productId: id,
          },
          include: {
            user: true,
          },
        },
        logo: {
          select: {
            id: true,
            name: true,
            logoUrl: true,
          },
        },
        design: {
          select: {
            externalCardDesignId: true,
            externalCardType: true,
            designType: true,
            resolution: true,
          },
        },
      },
    }),
    (err) => {
      const message = `Failed to update product status to APPROVED for product id: ${id}`
      const logRef = logger.error(err, message)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Database operation failed',
        logRef,
      } as SimpleError
    }
  ).andThen((product) => {
    if (!product) {
      const logRef = logger.error(`Product not found with id: ${id}`)
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `Product not found with id: ${id}`,
        logRef,
      } as SimpleError)
    }

    return ok(product as UpdatedProductStatusResult)
  })
}
