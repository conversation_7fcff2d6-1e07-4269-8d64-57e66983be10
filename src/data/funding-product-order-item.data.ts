import { Prisma } from '@prisma/client'
import { err, fromPromise } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { centsToDollars } from 'utils/numeric'
import { prisma } from 'utils/prisma'

export function fundingOrderItemListWithFilters({
  page,
  filter,
  pageSize,
  orgId,
}: {
  filter?: string
  page: number
  pageSize: number
  orgId: string
}) {
  // Start with the base `where` clause
  const where: Prisma.FundingProductOrderItemWhereInput = {
    fundsLoaded: true,
    productOrder: {
      organizationId: orgId,
    },
  }

  if (filter) {
    where.OR = [
      {
        externalCardReferenceNumber: {
          contains: filter,
          mode: 'insensitive',
        },
      },
      {
        productOrder: {
          orderNumber: {
            contains: filter,
            mode: 'insensitive',
          },
        },
      },
    ]
  }

  logger.debug('fundingOrderItemListWithFilters...', { where, page, pageSize })

  return fromPromise(
    prisma.$transaction([
      prisma.fundingProductOrderItem.count({ where }),
      prisma.fundingProductOrderItem.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          externalCardReferenceNumber: true,
          productOrder: {
            select: {
              orderNumber: true,
            },
          },
          productOrderItemCard: {
            select: {
              lockCode: true,
              activated: true,
              blocked: true,
              unitPriceInCents: true,
              productOrderItem: {
                select: {
                  recipientEmail: true,
                  product: {
                    select: {
                      cardTypes: true,
                    },
                  },
                },
              },
            },
          },
        },
      }),
    ]),
    (error) => {
      logger.warn(
        `fundingOrderItemListWithFilters...Failed to query funding product order item cards with search term [${filter}]`,
        error
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `fundingOrderItemListWithFilters... Failed to query funding product order item cards with search term [${filter}]`,
      } as SimpleError
    }
  ).map(
    ([count, cards]) => (
      logger.debug(
        `fundingOrderItemListWithFilters...Found ${cards.length} cards, count = ${count}`
      ),
      {
        count,
        cards: cards.map((card) => {
          return {
            referenceId: card.externalCardReferenceNumber,
            lockCode: card.productOrderItemCard?.lockCode,
            orderNumber: card.productOrder.orderNumber,
            cardType:
              card.productOrderItemCard?.productOrderItem.product.cardTypes,
            email: card.productOrderItemCard?.productOrderItem.recipientEmail,
            isActivated: card.productOrderItemCard?.activated,
            isBlocked: card.productOrderItemCard?.blocked,
            initialBalance: centsToDollars(
              card.productOrderItemCard?.unitPriceInCents ?? 0
            ),
          }
        }),
      }
    )
  )
}

export function getFundingOrderItemByCRN(crn: string) {
  logger.debug('getFundingOrderItemByCRN...', { crn })

  return fromPromise(
    prisma.fundingProductOrderItem.findFirst({
      where: {
        externalCardReferenceNumber: crn,
      },
      select: {
        externalCardReferenceNumber: true,
        recipientEmail: true,
        productOrder: {
          select: {
            orderNumber: true,
            organization: {
              select: {
                name: true,
              },
            },
          },
        },
        productOrderItemCard: {
          select: {
            lockCode: true,
            activated: true,
            blocked: true,
          },
        },
        product: {
          select: {
            cardTypes: true,
          },
        },
      },
    }),
    (error) => {
      logger.warn(
        `getFundingOrderItemByCRN...failed to query product order item card with crn [${crn}]`,
        error
      )

      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `getFundingOrderItemByCRN...Failed to query product order item card with crn [${crn}]`,
      } as SimpleError
    }
  ).map((card) => {
    if (!card) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: `getFundingOrderItemByCRN....Failed to find product order item card with crn [${crn}]`,
      })
    }

    return {
      referenceId: card.externalCardReferenceNumber,
      lockCode: card.productOrderItemCard?.lockCode,
      orderNumber: card.productOrder.orderNumber,
      cardType: card.product.cardTypes,
      orgName: card.productOrder.organization.name,
      email: card.recipientEmail,
      isActivated: card.productOrderItemCard?.activated,
      isBlocked: card.productOrderItemCard?.blocked,
    }
  })
}

export function createFundingProductOrderItems(
  items: Prisma.FundingProductOrderItemCreateManyInput[]
) {
  return fromPromise(
    prisma.fundingProductOrderItem.createMany({
      data: items,
    }),
    (error) => {
      logger.error(error, 'Failed to create funding product order items')
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: `Failed to create funding product order items`,
      } as SimpleError
    }
  )
}
