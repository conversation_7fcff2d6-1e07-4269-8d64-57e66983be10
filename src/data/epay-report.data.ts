import { Prisma, ReportType, User } from '@prisma/client'
import { fromPromise, ok } from 'neverthrow'
import { FetchBatchArgs, ReportDefinition } from 'types/reports'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { centsToDollars } from 'utils/numeric'
import { prisma } from 'utils/prisma'

type CrnReportRecord = {
  purchaseDate: string | null
  orderNumber: string | null
  customerId: string | null
  customerName: string
  crn: string | null
  faceValue: number
  loadFee: number
  loadingFeeDiscount: number
  otherDiscount: number
  digitalFee: number
  gst: number
  cardProgramName: string
  cardBatchNumber: string | null
  orgId: string
  comments: string | null
}

type CustomerDetailsTransformPayload = Prisma.OrganizationGetPayload<{
  include: {
    corporateApplications: {
      include: {
        user: true
      }
    }
  }
}> & {
  user?: User
}

type ProductOrderTransformPayload = Prisma.ProductOrderGetPayload<{
  include: {
    organization: {
      include: {
        corporateApplications: true
      }
    }
    user: true
    productOrderNotes: {
      orderBy: {
        createdAt: 'desc'
      }
      take: 1
    }
  }
}>

type ProductOrderRecord = {
  purchaseDate: string
  orderNumber: string | null
  customerId: string | null
  customerName: string
  totalOrderValue: number
  totalPaymentValue: number
  totalFaceValue: number
  totalLoadFee: number
  quantity: number | null
  otherDiscount: number
  loadingFeeDiscount: number
  totalShipmentFee: number
  totalConvenienceFee: number
  totalDigitalDeliveryFee: number
  totalGst: number
  paymentMethod: string | null
  releaseDate?: string | null
  releasedBy: string | null
  comments: string | null
  orderStatus: string
  orgId: string
  orgCreatedAt: string
  orgUpdatedAt: string
  orderedByEmail: string
  orderedByPhone: string | null
  kycStatus: string | null
}

type ProductOrderItemCardWithIncludes = Prisma.ProductOrderItemCardGetPayload<{
  include: {
    productOrderItem: {
      include: {
        product: true
        productOrder: {
          include: {
            organization: true
            productOrderNotes: {
              orderBy: {
                createdAt: 'desc'
              }
              take: 1
            }
          }
        }
      }
    }
  }
}>

export const crnReportDefinition: ReportDefinition = {
  type: ReportType.CRN_ORDER,
  fetchBatch: ({
    startDate,
    endDate,
    page = 1,
    batchSize,
    filters,
  }: FetchBatchArgs) => {
    logger.info(`Fetching CRN report batch for page [${page}]`)

    const dateField = (filters as { useReleaseDate?: boolean })?.useReleaseDate
      ? 'submittedAt'
      : 'createdAt'

    const productOrderConditions: any = {}

    // Add orderNumber filter if provided
    if ((filters as { orderNumber?: string })?.orderNumber) {
      productOrderConditions.orderNumber = {
        equals: (filters as { orderNumber?: string }).orderNumber,
      }
    } else {
      productOrderConditions[dateField] = {
        gte: startDate,
        lte: endDate,
      }
    }

    // Build the complete where clause
    const where = {
      productOrderItem: {
        productOrder: productOrderConditions,
      },
    }

    return fromPromise(
      prisma.productOrderItemCard.findMany({
        take: batchSize,
        skip: (page - 1) * batchSize,
        where,
        include: {
          productOrderItem: {
            include: {
              product: true,
              productOrder: {
                include: {
                  organization: true,
                  productOrderNotes: {
                    orderBy: {
                      createdAt: 'desc',
                    },
                    take: 1,
                  },
                },
              },
            },
          },
        },
        orderBy: {
          productOrderItem: {
            productOrder: {
              [dateField]: 'asc',
            },
          },
        },
      }),
      (error) => {
        logger.error(error, 'Failed to fetch CRN report batch')
        return {
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Failed to fetch CRN report batch',
        } as SimpleError
      }
    ).andThen((cards) => {
      if (page === 1) {
        return fromPromise(
          prisma.productOrderItemCard.count({
            where,
          }),
          (error) => {
            logger.error(error, 'Failed to fetch CRN report count')
            return {
              code: errorCodes.db.UNKNOWN_ERROR,
              message: 'Failed to fetch CRN report count',
            } as SimpleError
          }
        ).map((totalCount) => {
          const hasNextPage = cards.length === batchSize
          const nextPage = hasNextPage ? page + 1 : undefined

          return {
            records: cards,
            nextPage,
            totalCount,
          }
        })
      } else {
        const hasNextPage = cards.length === batchSize
        const nextPage = hasNextPage ? page + 1 : undefined

        return ok({
          records: cards,
          nextPage,
        })
      }
    })
  },
  getHeaders: () => [
    { id: 'purchaseDate', title: 'Purchase Date' },
    { id: 'orderNumber', title: 'Order Number' },
    { id: 'customerId', title: 'Partner ID (Customer ID)' },
    { id: 'customerName', title: 'Customer/Stakeholder Name' },
    { id: 'crn', title: 'Card Reference Number' },
    { id: 'faceValue', title: 'Face Value' },
    { id: 'loadFee', title: 'Load Fee' },
    { id: 'otherDiscount', title: 'Other Discount(s) (GST incl)' },
    { id: 'loadingFeeDiscount', title: 'Load Fee Discount (GST incl)' },
    { id: 'digitalFee', title: 'Digital fee' },
    { id: 'gst', title: 'GST' },
    { id: 'cardProgramName', title: 'Card Program Name' },
    { id: 'cardBatchNumber', title: 'Card batch number' },
    { id: 'orgId', title: 'Org Id' },
    { id: 'comments', title: 'Comments' },
  ],
  transformRecord: (
    item: ProductOrderItemCardWithIncludes
  ): CrnReportRecord => {
    const {
      loadingFee,
      discount,
      digitalFee,
      quantity,
      productOrder,
      loadingFeeDiscount,
    } = item.productOrderItem
    const { submittedAt, organization, productOrderNotes } = productOrder

    const otherDiscount = discount
      ? centsToDollars(
          discount - (loadingFeeDiscount ? loadingFeeDiscount : 0)
        ) / quantity
      : 0

    const lastComment =
      productOrderNotes.length > 0 ? productOrderNotes[0].message : null

    return {
      purchaseDate: submittedAt ? formatDateTime(submittedAt) : null,
      orderNumber: productOrder.orderNumber,
      customerId: organization.customerCode,
      customerName: organization.name,
      crn: item.externalCardReferenceNumber,
      faceValue: centsToDollars(item.unitPriceInCents ?? 0),
      loadFee: loadingFee ? centsToDollars(loadingFee) / quantity : 0,
      otherDiscount,
      loadingFeeDiscount: loadingFeeDiscount
        ? centsToDollars(loadingFeeDiscount) / quantity
        : 0,
      digitalFee: digitalFee ? centsToDollars(digitalFee) / quantity : 0,
      gst: centsToDollars(productOrder.gstAmount),
      cardProgramName:
        item.productOrderItem.deliveryMethod === 'COURIER'
          ? 'PHYSICAL'
          : 'VIRTUAL',
      cardBatchNumber: item.productOrderItem.externalBatchId,
      orgId: organization.id,
      comments: lastComment,
    }
  },
}

export const productOrderReportDefinition: ReportDefinition = {
  type: ReportType.PRODUCT_ORDER,
  fetchBatch: ({
    startDate,
    endDate,
    page = 1,
    batchSize,
    filters,
  }: FetchBatchArgs) => {
    logger.info(`Fetching Product Order report batch for page [${page}]`)

    const dateField = (filters as { useReleaseDate?: boolean })?.useReleaseDate
      ? 'submittedAt'
      : 'createdAt'

    const where: any = {}

    if ((filters as { orderNumber?: string })?.orderNumber) {
      where.orderNumber = {
        equals: (filters as { orderNumber?: string }).orderNumber,
      }
    } else {
      where[dateField] = {
        gte: startDate,
        lte: endDate,
      }
    }

    return fromPromise(
      prisma.$transaction(async (tx) => {
        const productOrders = await tx.productOrder.findMany({
          where,
          take: batchSize,
          skip: (page - 1) * batchSize,
          include: {
            organization: {
              include: {
                corporateApplications: true,
              },
            },
            user: true,
            productOrderNotes: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 1,
            },
          },
          orderBy: { [dateField]: 'asc' },
        })

        const auth0Ids = Array.from(
          productOrders.reduce<Set<string>>((acc, po) => {
            acc.add(`auth0|${po.user.id}`)
            return acc
          }, new Set())
        )

        // Then fetch phone numbers for these users
        const otps = await tx.oneTimePassword.findMany({
          where: {
            userId: {
              in: auth0Ids,
            },
          },
        })

        return productOrders.map((po) => {
          const otp = otps.find((o) => o.userId === `auth0|${po.user.id}`)
          return {
            ...po,
            user: {
              ...po.user,
              phoneNbr: otp?.phoneNumber || null,
            },
          }
        })
      }),
      (error) => {
        logger.error(error, 'Failed to fetch Product Order report batch')
        return {
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Failed to fetch Product Order report batch',
        } as SimpleError
      }
    ).andThen((productOrders) => {
      if (page === 1) {
        return fromPromise(prisma.productOrder.count({ where }), (error) => {
          logger.error(error, 'Failed to fetch Product Order report count')
          return {
            code: errorCodes.db.UNKNOWN_ERROR,
            message: 'Failed to fetch Product Order report count',
          } as SimpleError
        }).map((totalCount) => {
          const hasNextPage = productOrders.length === batchSize
          const nextPage = hasNextPage ? page + 1 : undefined

          return {
            records: productOrders,
            nextPage,
            totalCount,
          }
        })
      } else {
        const hasNextPage = productOrders.length === batchSize
        const nextPage = hasNextPage ? page + 1 : undefined

        return ok({
          records: productOrders,
          nextPage,
        })
      }
    })
  },
  getHeaders: () => [
    { id: 'purchaseDate', title: 'Purchase Date' },
    { id: 'orderNumber', title: 'Order Number' },
    { id: 'customerId', title: 'Partner ID (Customer ID)' },
    { id: 'customerName', title: 'Customer/Stakeholder Name' },
    { id: 'totalOrderValue', title: 'Total order value' },
    { id: 'totalPaymentValue', title: 'Total payment value' },
    { id: 'totalFaceValue', title: 'Total Face Value' },
    { id: 'totalLoadFee', title: 'Total Load Fee' },
    { id: 'otherDiscount', title: 'Other Discount(s) (GST Inc)' },
    { id: 'loadingFeeDiscount', title: 'Load Fee Discount (GST Inc)' },
    { id: 'quantity', title: 'Cards quantity' },
    { id: 'totalShipmentFee', title: 'Total shipment Fee' },
    {
      id: 'totalConvenienceFee',
      title: 'Total Convenience Fee (credit card fee)',
    },
    { id: 'totalDigitalDeliveryFee', title: 'Total Digital Delivery Fee' },
    { id: 'totalGst', title: 'Total Fees GST' },
    { id: 'paymentMethod', title: 'Payment Method' },
    { id: 'releaseDate', title: 'Order Released Date' },
    { id: 'releasedBy', title: 'Released by' },
    { id: 'orderStatus', title: 'Order Status' },
    { id: 'orderedByEmail', title: 'Ordered By Email' },
    { id: 'orderedByPhone', title: 'Ordered By Phone' },
    { id: 'orgId', title: 'Org Id' },
    { id: 'kycStatus', title: 'KYC Status' },
    { id: 'orgCreatedAt', title: 'Application Approved Date' },
    { id: 'orgUpdatedAt', title: 'Org Last Modified Date' },
    { id: 'comments', title: 'Comments' },
  ],
  transformRecord: (
    productOrder: ProductOrderTransformPayload
  ): ProductOrderRecord => {
    const lastComment =
      productOrder.productOrderNotes.length > 0
        ? productOrder.productOrderNotes[0].message
        : null

    const discount = centsToDollars(productOrder.discountTotal)

    const otherDiscount = centsToDollars(
      productOrder.discountTotal! - productOrder.loadingFeeDiscountTotal!
    )
    const orderTotal = centsToDollars(productOrder.orderTotal)

    return {
      purchaseDate: formatDateTime(productOrder.createdAt),
      orderNumber: productOrder.orderNumber,
      customerId: productOrder.organization.customerCode,
      customerName: productOrder.organization.name,
      totalOrderValue: orderTotal + discount,
      totalPaymentValue: orderTotal,
      totalFaceValue: centsToDollars(productOrder.subTotal),
      totalLoadFee: centsToDollars(productOrder.loadingFeeTotal),
      otherDiscount,
      loadingFeeDiscount: centsToDollars(productOrder.loadingFeeDiscountTotal),
      quantity: productOrder.totalQuantity,
      totalShipmentFee: centsToDollars(productOrder.shippingTotal),
      totalConvenienceFee: centsToDollars(productOrder.creditCardFee),
      totalDigitalDeliveryFee: centsToDollars(productOrder.digitalFeeTotal),
      totalGst: centsToDollars(productOrder.gstAmount),
      paymentMethod: productOrder.paymentMethod,
      releaseDate: productOrder.submittedAt
        ? formatDateTime(productOrder.submittedAt)
        : productOrder.submittedAt,
      releasedBy: productOrder.releasedBy,
      comments: lastComment,
      orderStatus: productOrder.orderStatus,
      orgId: productOrder.organization.id,
      orgCreatedAt: formatDateTime(productOrder.organization.createdAt),
      orgUpdatedAt: formatDateTime(productOrder.organization.updatedAt),
      orderedByEmail: productOrder.user.email,
      orderedByPhone: productOrder.user.phoneNbr,
      kycStatus:
        productOrder.organization.corporateApplications?.kycStatus ?? null,
    }
  },
}

export const customerDetailsReportDefinition: ReportDefinition = {
  type: ReportType.CUSTOMER_DETAIL,
  fetchBatch: ({ startDate, endDate, page = 1, batchSize }: FetchBatchArgs) => {
    logger.info(`Fetching Customer Details report batch for page [${page}]`)

    const where = {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    }

    return fromPromise(
      prisma.$transaction(async (tx) => {
        const orgs = await tx.organization.findMany({
          where,
          take: batchSize,
          skip: (page - 1) * batchSize,
          include: {
            corporateApplications: {
              include: {
                user: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        })

        const auth0Ids = Array.from(
          orgs.reduce<Set<string>>((acc, org) => {
            const userId = org.corporateApplications?.userId
            if (userId) {
              acc.add(`auth0|${userId}`)
            }
            return acc
          }, new Set())
        )

        // Then fetch phone numbers for these users
        const otps = await tx.oneTimePassword.findMany({
          where: {
            userId: {
              in: auth0Ids,
            },
          },
        })

        return orgs.map((org) => {
          const otp = otps.find(
            (o) => o.userId === `auth0|${org.corporateApplications?.userId}`
          )
          return {
            ...org,
            user: {
              ...org.corporateApplications?.user,
              phoneNbr: otp?.phoneNumber || null,
            },
          }
        })
      }),
      (error) => {
        logger.error(error, 'Failed to fetch Customer Details report batch')
        return {
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Failed to fetch Customer Details report batch',
        } as SimpleError
      }
    ).andThen((items) => {
      if (page === 1) {
        return fromPromise(prisma.organization.count({ where }), (error) => {
          logger.error(error, 'Failed to fetch Customer Details report count')
          return {
            code: errorCodes.db.UNKNOWN_ERROR,
            message: 'Failed to fetch Customer Details report count',
          } as SimpleError
        }).map((totalCount) => {
          const hasNextPage = items.length === batchSize
          const nextPage = hasNextPage ? page + 1 : undefined

          return {
            records: items,
            nextPage,
            totalCount,
          }
        })
      } else {
        const hasNextPage = items.length === batchSize
        const nextPage = hasNextPage ? page + 1 : undefined

        return ok({
          records: items,
          nextPage,
        })
      }
    })
  },
  getHeaders: () => [
    { id: 'customerCode', title: 'Partner ID' },
    { id: 'name', title: 'Organisation' },
    { id: 'updatedAt', title: 'Last modified Date' },
    { id: 'status', title: 'Status' },
    { id: 'nzbn', title: 'NZBN' },
    { id: 'adminEmail', title: 'Admin Email Address' },
    { id: 'adminPhone', title: 'Admin Mobile Number' },
    { id: 'createdAt', title: 'Account Creation Date' },
    { id: 'kycStatus', title: 'KYC Status' },
  ],
  transformRecord: (
    customerDetail: CustomerDetailsTransformPayload
  ): CustomerDetailRecord => {
    return {
      customerCode: customerDetail.customerCode,
      name:
        customerDetail.corporateApplications?.tradingName ||
        customerDetail.tradingName,
      updatedAt: formatDateTime(customerDetail.updatedAt),
      status: customerDetail.corporateApplications?.status,
      nzbn: customerDetail.nzbn,
      adminEmail: customerDetail.user?.email ?? null,
      adminPhone: customerDetail.user?.phoneNbr ?? null,
      createdAt: formatDateTime(customerDetail.createdAt),
      kycStatus: customerDetail.corporateApplications?.kycStatus ?? null,
    }
  },
}

type CustomerDetailRecord = {
  customerCode: string | null
  name: string | null
  updatedAt: string
  status?: string
  nzbn: string
  adminEmail: string | null
  adminPhone: string | null
  createdAt: string
  kycStatus: string | null
}

function formatDateTime(date: Date) {
  // Get date components
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  // Get time components
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  // Format to YYYY-MM-DD HH:MM:SS
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
