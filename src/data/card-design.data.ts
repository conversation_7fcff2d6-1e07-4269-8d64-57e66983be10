import { err, from<PERSON>romise, ok } from 'neverthrow'
import { errorCodes, SimpleError } from 'types/simple-error'
import logger from 'utils/logger'
import { prisma } from 'utils/prisma'
import { DesignType } from '@prisma/client'

export function getCardDesignList(designType?: DesignType) {
  const where: any = {
    isPrezzyDesign: true,
  }

  if (designType) {
    where.designType = designType
  }

  return fromPromise(
    prisma.cardDesign.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    (error) => {
      logger.warn(
        `There was an error getting card designs${
          designType ? ` for designType [${designType}]` : ''
        }`
      )
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}

export function updateCardDesignAvailability(id: string, available: boolean) {
  return fromPromise(
    prisma.cardDesign.update({
      where: { id },
      data: { available },
    }),
    (error) => {
      logger.warn(`There was an updating card design availability for [${id}]`)
      return {
        code: errorCodes.db.UNKNOWN_ERROR,
        message: error,
      } as SimpleError
    }
  )
}

export function getHighAndLowResDesigns() {
  return getCardDesignList(DesignType.STOCK).andThen((cardDesigns) => {
    const highResDesign = cardDesigns.find(
      (design) => design.resolution === 'HIGH'
    )
    const lowResDesign = cardDesigns.find(
      (design) => design.resolution === 'LOW'
    )

    if (!highResDesign || !lowResDesign) {
      return err({
        code: errorCodes.db.ITEM_NOT_FOUND,
        message: 'High or low res designs not found',
      } as SimpleError)
    }

    return ok({
      highResDesign,
      lowResDesign,
    })
  })
}
