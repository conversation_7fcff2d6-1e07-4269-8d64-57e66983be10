import e from 'express'

export const errorCodes = {
  // generic error code
  UNEXPECTED: 'UNEXPECTED_ERROR', // used for things like configuration errors
  UNAUTHORIZED: 'UNAUTHORIZED',

  // sepcific error codes
  external: {
    api: {
      UNKNOWN_ERROR: 'EXTERNAL_API_UNKNOWN_ERROR',
      BAD_REQUEST: 'EXTERNAL_API_BAD_REQUEST',
      BAD_INPUT_VALUE: 'EXTERNAL_API_BAD_INPUT_VALUE',
    },
    ren: {
      REQUEST_FAILED: 'REN_REQUEST_FAILED',
      INVALID_RESPONSE_STRUCTURE: 'REN_INVALID_RESPOSNE_STRUCTURE',
      TSX_FAILED: 'REN_TSX_FAILED',
    },
    auth0: {
      FAILED_TO_GET_MANAGEMENT_TOKEN: 'AUTH0_FAILED_TO_GET_MANAGEMENT_TOKEN',
    },
    postmark: {
      POSTMARK_REQUEST_FAILED: 'POSTMARK_REQUEST_FAILED',
    },
    epay: {
      EPAY_REQUEST_FAILED: 'EPAY_REQUEST_FAILED',
    },
  },
  db: {
    UNKNOWN_ERROR: 'DB_UNKNOWN_ERROR',
    ITEM_NOT_FOUND: 'DB_ITEM_NOT_FOUND',
    BAD_INPUT: 'DB_BAD_INPUT',
  },
  order: {
    UNEXPECTED_STATUS: 'ORDER_UNEXPECTED_STATUS',
    PROCESSING_ERROR: 'ORDER_PROCESSING_ERROR',
  },
  card: {
    BATCH_PROCESSING_ERROR: 'CARD_BATCH_PROCESSING_ERROR',
    LOCK_CODE_NOT_FOUND: 'CARD_LOCK_CODE_NOT_FOUND',
  },

  report: {
    CREATE_ERROR: 'REPORT_CREATE_ERROR',
    NOT_FOUND: 'REPORT_NOT_FOUND',
  },
  s3: {
    MOVE_ERROR: 'S3_MOVE_ERROR',
    APPEND_ERROR: 'S3_APPEND_ERROR',
    UPLOAD_ERROR: 'S3_UPLOAD_ERROR',
    STREAM_ERROR: 'S3_STREAM_ERROR',
  },
}

export interface SimpleError {
  code: string
  message: string
  logRef?: string
}

export function checkErrorCode(error: unknown, code: string) {
  if (
    typeof error === 'object' &&
    error !== null &&
    'code' in error 
  ){
    return error.code === code
  }

  return false
}
