export type Session = {
  id: string
  state: 'cancelled' | 'complete'
  type: 'purchase'
  amount: string
  currency: string
  currencyNumeric: number
  merchantReference: string
  methods: ['card']
  expires: string // date
  callbackUrls: {
    approved: string
    declined: string
    cancelled: string
  }
  notificationUrl: string
  storeCard: boolean
  clientType: string
  links: {
    href: string
    rel: string
    method: string
  }[]
  transactions: any
  customerId?: string
  metaData?: string[]
}
