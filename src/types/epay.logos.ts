import { z } from 'zod'
import { ProductStatus } from '@prisma/client'
import { transformStringToNumber } from 'utils/validation'

export const epayLogosListSchema = z.object({
  filterText: z.string().optional(),
  status: z.nativeEnum(ProductStatus).optional(),
  endDate: z.string().optional(),
  startDate: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
})

export type EpayLogosListProps = z.infer<typeof epayLogosListSchema>
