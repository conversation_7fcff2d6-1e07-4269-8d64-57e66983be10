import { z } from 'zod'
import { OrderStatus } from '@prisma/client'
import { transformStringToNumber } from 'utils/validation'

export const epayOrdersListSchema = z.object({
  filterText: z.string().optional(),
  status: z.nativeEnum(OrderStatus).optional(),
  endDate: z.string().optional(),
  startDate: z.string().optional(),
  page: transformStringToNumber({ defaultNumber: 1 }),
  pageSize: transformStringToNumber({ defaultNumber: 10 }),
  useReleaseDate: z
    .union([z.boolean(), z.string().transform((val) => val === 'true')])
    .optional(),
})

export type EpayOrdersListProps = z.infer<typeof epayOrdersListSchema>
