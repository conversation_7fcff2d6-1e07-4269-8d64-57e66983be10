// NZBN API Docs

export type NzbnEntity = {
  EntityName: string
  NZBN: string
  EntityStatusCode: string
  EntityStatusDescription: string
  EntityTypeCode: string
  EntityTypeDescription: string
  RegistrationDate: Date | null
  StartDate: Date | null
  SourceRegister: string
  SourceRegisterUniqueIdentifier: string
  GstStatus: string
  GstEffectiveDate: Date | null
  LastUpdatedDate: Date | null
  LocationIdentifier: string
  AustralianBusinessNumber: string
  AustralianCompanyNumber: string
  AustralianServiceAddress: string
  EntityNames: NzbnEntityName[]
  TradingNames: NzbnTradingName[]
  EntityStatus: NzbnEntityStatus[]
  EmailAddress: EMail[]
  OtherAddress: NzbnAddress[]
  RegisteredAddress: NzbnAddress[]
  PostalAddress: NzbnAddress[]
  PrincipalPlaceOfActivity: NzbnAddress[]
  PhysicalAddress: NzbnAddress[]
  PhoneNumber: NzbnPhoneNumber[]
  Website: NzbnWebsite[]
  IndustryClassification: IndustryClassification[]
  PrivacySettings: NzbnPrivacySettings
  Company: NzbnCompany
  Roles: NzbnRole[]
}

type NzbnEntityName = {
  EntityName: string
  StartDate: Date | null
  EndDate: Date | null
}

type NzbnTradingName = {
  UniqueIdentifier: string
  Name: string
  StartDate: Date | null
  EndDate: Date | null
}

type NzbnEntityStatus = {
  EntityStatus: string
  StatusDescription: string
  StartDate: Date | null
  EndDate: Date | null
  StatusReasonCode: string
  Comment: string
}

type EMail = {
  UniqueIdentifier: string
  EmailAddress: string
  EmailPurpose: string
}

type NzbnAddress = {
  UniqueIdentifier: string
  StartDate: Date | null
  EndDate: Date | null
  CareOf: string
  Address1: string
  Address2: string
  Address3: string
  Address4: string
  PostCode: string
  CountryCode: string
  PafId: string
  Description: string
  PostalIsPhysicalAddress: boolean
  PostalIsRegisteredAddress: boolean
  DataSet: string
  Success: boolean
  Confidence: number | null
  MatchResult: string
  ParcelId: number | null
  DeliveryPointId: number | null
  ParsedPostCode: string
  Canonical: string
  CanonicalHash: string
  WhenParsed: string
  ParserVersionDate: string
  ParserVersionFile: string
}

type NzbnPhoneNumber = {
  UniqueIdentifier: string
  PhonePurpose: string
  PhonePurposeDescription: string
  PhoneCountryCode: string
  PhoneAreaCode: string
  PhoneNumber: string
  StartDate: Date | null
}

type NzbnWebsite = {
  UniqueIdentifier: string
  Url: string
  StartDate: Date | null
}

type IndustryClassification = {
  UniqueIdentifier: string
  ClassificationCode: string
  ClassificationDescription: string
}

type NzbnPrivacySettings = {
  NzbnPrivateInformation: boolean
  EntityTypePrivateInformation: boolean
  StartDatePrivateInformation: boolean
  StatusPrivateInformation: boolean
  LocationIdPrivateInformation: boolean
  NamePrivateInformation: boolean
  TradingNamePrivateInformation: boolean
  WebSitePrivateInformation: boolean
  BusinessClassificationPrivateInformation: boolean
  GstStatusPrivateInformation: boolean
  PhonePrivateInformation: boolean
  EmailPrivateInformation: boolean
  RegisteredAddressPrivateInformation: boolean
  PostalAddressPrivateInformation: boolean
  PrincipalAddressPrivateInformation: boolean
  OtherAddressPrivateInformation: boolean
  PartnersPrivateInformation: boolean
  TrusteesPrivateInformation: boolean
  PublicSuggestChangesYn: boolean
  GstEffectiveDatePrivateInformation: boolean
  PhysicalAddressPrivateInformation: boolean
  AustralianBusinessNumberPrivateInformation: boolean
  AustralianCompanyNumberPrivateInformation: boolean
  AustralianServiceAddressPrivateInformation: boolean
}

type NzbnCompany = {
  AnnualReturnFilingMonth: number | null
  FinancialReportFilingMonth: number | null
  NzsxCode: string
  AnnualReturnLastFiled: Date | null
  HasConstitutionFiled: boolean | null
  CountryOfOrigin: string
  OverseasCompany: string
  ExtensiveShareholding: boolean | null
  StockExchangeListed: boolean | null
  Shareholding: NzbnShareholding
  UltimateHoldingCompany: NzbnUltimateHoldingCompany
  InsolvencyDetails: NzbnInsolvencyDetails
}

type NzbnShareholding = {
  NumberOfShares: number | null
  ShareAllocation: NzbnShareParcel[]
  HistoricShareholder: NzbnHistoricShareholder[]
}

type NzbnShareParcel = {
  Allocation: number | null
  Shareholder: NzbnShareholder[]
  Hash: string
}

export type NzbnShareholder = {
  Type: string
  AppointmentDate: Date | null
  VacationDate: Date | null
  IndividualShareholder: NzbnPerson
  OtherShareholder: NzbnOtherShareholder
  ShareholderAddress: NzbnAddress
  Hash: string
}

type NzbnOtherShareholder = {
  CurrentEntityName: string
  NZBN: string
  CompanyNumber: string
  EntityType: string
}

type NzbnHistoricShareholder = {
  Type: string
  AppointmentDate: Date | null
  VacationDate: Date | null
  HistoricIndividualShareholder: NzbnPerson
  HistoricOtherShareholder: NzbnOtherShareholder
  HistoricShareholderAddress: NzbnAddress
  Hash: string
}

export type NzbnRole = {
  RoleType: string
  RoleStatus: string
  StartDate: Date | null
  EndDate: Date | null
  AsicDirectorshipYn: boolean | null
  AsicName: string
  ACN: string
  RoleEntity: NzbnRoleEntity
  RolePerson: NzbnPerson
  RoleAddress: NzbnAddress[]
  RoleAsicAddress: NzbnAddress[]
  UniqueIdentifier: string
  Hash: string
}

type NzbnRoleEntity = {
  NZBN: string
  EntityName: string
}

type NzbnPerson = {
  FirstName: string
  MiddleNames: string
  LastName: string
  FullName: string
}

type NzbnUltimateHoldingCompany = {
  YN: boolean | null
  Name: string
  Type: string
  Number: number | null
  NZBN: string
  Country: string
  EffectiveDate: Date | null
  UltimateHoldingCompanyAddress: NzbnAddress[]
}

type NzbnInsolvencyDetails = {
  Commenced: Date | null
  InsolvencyReport: NzbnInsolvencyReport[]
  InsolvencyAppointee: NzbnInsolvencyAppointee[]
}

type NzbnInsolvencyReport = {
  Name: string
  Filed: boolean | null
  Date: Date | null
}

type NzbnInsolvencyAppointee = {
  FirstName: string
  MiddleNames: string
  LastName: string
  FullName: string
  OrganisationName: string
  AppointmentDate: Date | null
  VacationDate: Date | null
  Email: string
  InsolvencyAppointeePhoneNumber: NzbnPhoneNumber[]
  InsolvencyAppointeeAddress: NzbnAddress
}

// NZTA API Docs

type DriverLicenseCheckComponent = {
  DriverLicenseCheckComponentId: string
  StartDate: Date
  EndDate: Date | null
  Type: string
  Description: string
  Status: string
}

export type DriverLicenseCheckResponse = {
  FirstName: string
  MiddleName: string
  LastName: string
  LicenseNumber: string
  LicenseStatus: string
  LicenseActive: boolean
  LicenseComponents: DriverLicenseCheckComponent
}

export type DriverLicenseVerifyResponse = {
  FirstName: string
  MiddleName: string[]
  LastName: string
  BirthDate: Date
  LicenseNumber: string
  LicenseVersion: string
  LicenseMatch: boolean
  FirstNameMatch: boolean | null
  MiddleNameMatch: boolean | null
  LastNameMatch: boolean | null
  BirthDateMatch: boolean | null
}

// DIA API Docs

type Gender = 'Female' | 'Male'
type PhoneNumberType = 'Telephone' | 'Cellphone'

export type PassportValidationResponse = {
  TravelDocumentNumber: string
  TravelDocumentType: string
  TravelDocumentIssueDate: Date | null
  TravelDocumentExpiryDate: Date | null
  TravelDocumentPlaceOfIssue: string
  FirstName: string
  LastName: string
  FirstNameAtBirth: string
  LastNameAtBirth: string
  Gender: Gender | null
  DateOfBirth: Date | null
  PlaceOfBirth: string
  CountryOfBirth: string
  EyeColour: string
  EmailAddress: string
  PostalAddress: string
  ResidentialAddress: string
  HomePhoneNumber: string
  HomePhoneNumberType: PhoneNumberType
  WorkPhoneNumber: string
  WorkPhoneNumberType: PhoneNumberType
  ResponseType: string
  ResponseCode: string
  ResponseText: string
  Exceptions: string[]
  MatchedFields: string[]
  UnmatchedFields: string[]
}

// QuickID API Docs

type SmartIDNZTAResult = {
  IsIncluded: boolean
  NZTAReportId: string | null
  FirstNameMatch: boolean | null
  MiddleNameMatch: boolean | null
  LastNameMatch: boolean | null
  DOBMatch: boolean | null
  AddressMatch: boolean | null
}

type SmartIDDIAResult = {
  IsIncluded: boolean
  DIAReportId: string | null
  FirstNameMatch: boolean | null
  MiddleNameMatch: boolean | null
  LastNameMatch: boolean | null
  DOBMatch: boolean | null
  AddressMatch: boolean | null
}

type Fulldata = {
  reference: string
  filters: any
  match_status: string
  risk_level: string
  search_term: string
  submitted_term: string
  total_hits: number
  limit: number
  offset: number
  hits: any
}

type Contents = {
  data: Fulldata
}

type AMLRiskScreeningDataResponse = {
  code: string
  status: string
  content: Contents
}

export type QuickIdSearchResponse = {
  CRAResult: any
  OfficerResult: any
  LINZResult: any
  NZTAResult: SmartIDNZTAResult
  MVRResult: any
  TINZResult: any
  DIAResult: SmartIDDIAResult
  BirthCertResult: any
  AdvertResult: any
  PepResult: AMLRiskScreeningDataResponse
  PepReportId: string
  Errors: string[]
}

// nzbn/search/entity response

type MatchOnNameOnlyItem = {
  NZBN: string
  CurrentName: string
  StatusCode: string
  StatusDescription: string
  BodyType: string
  BodyTypeDescription: string
  IncorporationDate: string
  CurrentAddress: string
  MatchName: string
  JaroWinklerMatchScore: number
  NameStartDate: string | null
  NameEndDate: string | null
  MatchAddress: string
  AddressStartDate: string
  AddressEndDate: string | null
  IsActiveEntity: boolean
}

export type SearchInfologResponse = {
  SearchId: string
  MatchOnNameOnlyItems: MatchOnNameOnlyItem[]
  MatchOnAddressOnlyItems: any[]
  MatchOnNumberOnlyItems: any[]
  MatchOnNameAndAddressItems: any[]
}
