import { Prisma, ReportType } from '@prisma/client'
import { ResultAsync } from 'neverthrow'
import { SimpleError } from './simple-error'

export interface ReportDefinition {
  type: ReportType
  fetchBatch: (args: FetchBatchArgs) => ResultAsync<BatchResult, SimpleError>
  transformRecord: (record: any) => Record<string, any>
  getHeaders: () => Array<{ id: string; title: string }>
}

export interface FetchBatchArgs {
  startDate: Date
  endDate: Date
  page?: number
  batchSize: number
  filters?: Prisma.JsonValue | null
}

export interface BatchResult {
  records: any[]
  nextPage?: number
  totalCount?: number
}

export interface ProcessBatchArgs {
  jobId: string
  tempKey: string
  batchResult: BatchResult
  reportDef: ReportDefinition
  isFirstBatch: boolean
  totalProcessed: number
}
