## Setting up your local dev environment

## 0.1 Install PNPM if you haven't already

# Install dependencies

## Pre-requisites

```
npm install pnpm -g
```

## Install Node Modules

```
pnpm install
```

## Install AWS CLI

[Installation instructions](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)

## 4. Create Secret File

copy the contents of .env.secret-example

```
cp .env.secret-example .env.local
```

# Install Postgress

**Download the PostgreSQL Docker Image**
Prerequisite: Docker installed and running on your local dev machine.

```

docker pull postgres

```

**Run the Docker Container by itself**

APPLICATION DATABASE

```console
docker run --name eapyDb -p 5460:5432 -e POSTGRES_USER=epay_user -e POSTGRES_PASSWORD=postgresPW -e POSTGRES_DB=epayDB -d postgres
```


_`docker run` is the command used to create and run a new container based on an already downloaded image._

_`--name myPostgresDb` is the name we assign to the container that we are creating._

_`-p 5432:5432` is the port mapping. Postgres natively exposes the port 5432, and we have to map that port (that lives within Docker) to a local port. In this case, the local 5455 port maps to Docker's 5432 port._

_`-e POSTGRES_USER=[APP_NAME]_user`, is the database user defined in the `.local.env` file._

_`-e POSTGRES_PASSWORD=postgresPW` is the password defined in the `.env.secret` file. You will have to create a secret file if one doesn't exist because they only exist on local dev machine._

_`-e POSTGRES_DB=postgresDB` is the name of the DB defined in the `.local.env` file._

_`-d` indicates that the container run in a detached mode. This means that the container runs in a background process._

_`postgres` is the name of the image we are using to create the container._

_As a result, you will see the newly created container on the CLI (running `docker ps`)_

_If you forgot which environment variables you've defined for that container, you can retrieve them using Docker Desktop or by running:_
`docker exec myPostgresDb env`

## Add Database URL to Prisma configuration

```
echo DATABASE_URL=**********************************************/epayDB > ./prisma/.env
```

# Create the Database Schema

```
pnpm prisma db push
```

## Connecting DBeaver (or other SQL Client) to your docker postgresDB

TODO: Add instructions about using a proxy to connect to the DB (not implemented yet in AWS)

Create a new connection with the following inputs:

- **Host** = localhost
- **Port** = 5432
- **Database** = epayDB
- **Username** = epay_user
- **Password** = postgresPW

Test the connection, then finish.

# Integrating Auth0 Post-Login Actions with Your Local Development Environment

This guide outlines steps on setting up and testing Auth0 post-login actions within your local environment to streamline the development process.

**Problem:** Our Auth0 post-login action needs to retrieve user data from our API. However, Auth0 Actions run in a secure, isolated cloud environment, preventing direct access to our local API.

**Solution:** Mocking the API response using Postman Mock Server allows us to simulate the interaction locally. This allows us to login locally without having the Action access our local API.

1. Locate the **Auth0 Actions Mock** collection in Postman
2. Under the `POST /auth/signin` endpoint, add or duplicate an **example**
3. In the example created above, update the **request body** with the expected payload from the auth0 action and **response body** with the expected response from your local API
4. The mock server is configured to match the request body, ensuring it returns the saved example's response when the request's body matches

## Start the API locally

Start the server with nodemon so you have hot reload.

```

pnpm dev

```

_Be default the server starts on port 5000. You can override this in your local `.env.local` file._

# Pull Requests that contain a Database Migration

Try to only have 1 extra migration folder per PR. If you have to refector your schema multiple times you can end up with a migration for each refactor.


# Testing
## Unit Tests

```
pnpm test
```

## Integration Tests

```
pnpm test:int
```

## Integration test filtered by path
example to only test routes
```
pnpm test:int routes
```

## Run all tests once
```
pnpm test:all
```