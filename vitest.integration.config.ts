import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
  resolve: {
    alias: {
      routes: path.resolve(__dirname, 'src/routes'),
      constants: path.resolve(__dirname, 'src/constants'),
      middlewares: path.resolve(__dirname, 'src/middlewares'),
      models: path.resolve(__dirname, 'src/models'),
      utils: path.resolve(__dirname, 'src/utils'),
      'test-utils': path.resolve(__dirname, 'src/test-utils'),
      types: path.resolve(__dirname, 'src/types'),
      services: path.resolve(__dirname, 'src/services'),
      helpers: path.resolve(__dirname, 'src/helpers'),
      app: path.resolve(__dirname, 'src/app.ts'),
      data: path.resolve(__dirname, 'src/data'),
      'external-apis': path.resolve(__dirname, 'src/external-apis'),
      'epay-data': path.resolve(__dirname, 'src/epay-data'),
    },
  },
  test: {
    globalSetup: ['./tests/setup-integration-tests.ts'],
    setupFiles: ['./tests/before-all-integration.ts'],
    include: ['tests/integration/**/*.test.ts'],
    environment: 'node',
    env: {},
    poolOptions: {
      forks: { singleFork: true },
    },
  },
})
