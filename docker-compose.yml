version: "3.9"
services:
  myPostgresDb:
    image: postgres:latest
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgresDB
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped

  epay-api:
    stdin_open: true
    build:
      context: .
      dockerfile: Dockerfile
    container_name: epay-api
    depends_on:
      - myPostgresDb
    ports:
      - "5000:5000"
    environment:
      ENV: local
      DATABASE_URL: ************************************************/postgresDB
    restart: always

volumes:
  postgres-data:
