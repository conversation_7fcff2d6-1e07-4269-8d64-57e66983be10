WITH org_admins AS (
    SELECT DISTINCT ON (u."organizationId")
        u.id,
        u.email,
        u."firstName",
        u."lastName",
        u."organizationId"
    FROM "User" u
    WHERE u.role = 'ORG_ADMIN'
    ORDER BY u."organizationId", u.id
)
SELECT DISTINCT
    cl.name AS card_logo_name,
    p.status AS product_status,
    cpo."orderNumber" AS order_number,
    o.name AS organization_name,
    CONCAT(oa."firstName", ' ', oa."lastName") AS admin_name,
    oa.email AS admin_email
FROM 
    "CardLogo" cl
JOIN "Product" p ON cl.id = p."logoId"
JOIN "CustomProductOrder" cpo ON p.id = cpo."productId"
JOIN "Organization" o ON cpo."organizationId" = o.id
LEFT JOIN org_admins oa ON o.id = oa."organizationId"
WHERE 
    p.status NOT IN ('APPROVED', 'DECLINED')
    AND o.name != 'Da Company Limited'
ORDER BY 
    cl.name, cpo."orderNumber";