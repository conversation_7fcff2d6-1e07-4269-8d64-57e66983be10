import { prisma } from '../src/utils/prisma'

prisma
  .$transaction([
    prisma.beneficialOwner.deleteMany(),
    prisma.director.deleteMany(),
    prisma.corporateApplication.deleteMany(),
    // prisma.corporateOrganization.deleteMany(),
    // prisma.customCard.deleteMany(),
    // prisma.baseCard.deleteMany(),
    prisma.user.deleteMany(),
  ])
  .then(() => {
    console.log('reset complete')
    prisma.$disconnect()
  })
