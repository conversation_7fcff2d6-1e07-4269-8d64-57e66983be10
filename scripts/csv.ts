import parser from 'csv-parser'
import fs from 'fs'

const logos: { stakeholderId: string; filename: string }[] = []

const stakeholderIds: {
  [id: string]: {
    email: string
    userIds: string[]
  }
} = {}
const activeAccounts: {
  stakeholderId: string
  email: string
  userId: string
}[] = []

const first = () => {
  return new Promise((res, rej) => {
    fs.createReadStream(
      '/Users/<USER>/Downloads/placard and i2c logo folder/stake-id-logo.csv'
    )
      .pipe(
        parser({
          headers: [
            'Stakeholder ID',
            'Stakeholder Name',
            'Card Design ID',
            'Logo Filename',
            '',
          ],
          skipLines: 1,
        })
      )
      .on('data', (data) => {
        const stakeholderId = data['Stakeholder ID']
        const logo = data['Logo Filename'].split('_')[1]

        logos.push({ stakeholderId, filename: logo })
      })
      .on('end', () => {
        console.log('first done')
        res('done')
      })
  })
}

const second = () => {
  return new Promise((res, rej) => {
    fs.createReadStream(
      '/Users/<USER>/Downloads/placard and i2c logo folder/stakeholder-ids.csv'
    )
      .pipe(
        parser({
          headers: [
            'stakeholderId',
            'Company Name',
            'Address Details',
            'Suburb',
            'City',
            'Postcode',
            '_1',
            'Customername',
            'Contact Details',
            'email',
            'userId',
          ],
          skipLines: 1,
        })
      )
      .on('data', (data) => {
        const stakeholderId = data.stakeholderId
        const email = data.email
        const userId = data.userId

        if (stakeholderIds[stakeholderId]) {
          stakeholderIds[stakeholderId].userIds.push(userId)
        } else {
          stakeholderIds[stakeholderId] = {
            email,
            userIds: [userId],
          }
        }
      })
      .on('end', () => {
        console.log('second done')
        res('done')
      })
  })
}

const third = () => {
  let activeStakeholders = 0
  return new Promise((res, rej) => {
    fs.createReadStream(
      '/Users/<USER>/Downloads/placard and i2c logo folder/active.csv'
    )
      .pipe(
        parser({
          headers: ['accountName', 'email', 'stakeholderId'],
          skipLines: 1,
        })
      )
      .on('data', (data) => {
        const email = data.email
        const stakeholderId = data.stakeholderId

        if (email) {
          const stakeholder = stakeholderIds[stakeholderId]
          activeStakeholders++

          stakeholder.userIds.forEach((userId) => {
            activeAccounts.push({
              stakeholderId,
              email,
              userId,
            })
          })
        }
      })
      .on('end', () => {
        console.log('third done')
        res('done')
      })
  })
}

const main = async () => {
  console.log('first')
  await first()
  console.log('second')
  await second()
  console.log('third')
  await third()

  process.stdout.write(JSON.stringify(logos) + '\n')
  // process.stdout.write(JSON.stringify(activeAccounts) + '\n')
}

main()
