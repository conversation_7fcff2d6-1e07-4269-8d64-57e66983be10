import { execSync } from 'child_process'

const containerName = 'test-postgres'
const imageName = 'postgres'
const dbName = 'epay_test_db'
const dbUser = 'postgres'
const dbPass = 'postgresPW'
const dbPort = 5470

export const TEST_DATABASE_URL = `postgresql://${dbUser}:${dbPass}@localhost:${dbPort}/${dbName}?schema=public`

function imageExists(imageName: string): boolean {
  try {
    const output = execSync(`docker image inspect ${imageName}`).toString()
    return output.includes(imageName)
  } catch (error) {
    return false
  }
}

function waitForPostgres(maxRetries = 30, retryInterval = 1000) {
  // ... (keep the existing waitForPostgres function)
}

export async function setupDatabase() {
  try {
    console.log('Setting up test database...')

    // Check if the container already exists and remove it if it does
    if (
      execSync(`docker ps -a -q -f name=${containerName}`).toString().trim()
    ) {
      console.log('Removing existing container...')
      execSync(`docker rm -f ${containerName}`)
    }

    // Check if the PostgreSQL image exists, pull only if it doesn't
    if (!imageExists(imageName)) {
      console.log(
        'PostgreSQL image not found locally. Pulling from Docker Hub...'
      )
      execSync(`docker pull ${imageName}`)
    } else {
      console.log('PostgreSQL image found locally.')
    }

    // Start the PostgreSQL container
    console.log('Starting PostgreSQL container...')
    execSync(
      `docker run --name ${containerName} -p ${dbPort}:5432 -e POSTGRES_USER=${dbUser} -e POSTGRES_PASSWORD=${dbPass} -e POSTGRES_DB=${dbName} -d ${imageName}`
    )

    // Wait for PostgreSQL to be ready
    waitForPostgres()

    // Set the DATABASE_URL environment variable
    process.env.DATABASE_URL = TEST_DATABASE_URL

    // Apply Prisma migrations
    console.log('Applying Prisma migrations...')
    execSync('npx prisma migrate deploy')

    // Run the test seed
    console.log('Running test seed...')
    execSync('ts-node prisma/seed-test.ts')

    console.log('Database setup complete!')
  } catch (error) {
    console.error('Error setting up database:', error)

    throw error
  }
}
