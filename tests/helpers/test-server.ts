import { UserRole } from '@prisma/client'
import { createApp, UserContext } from 'app'
import { Application } from 'express'

export function getTestServer(testContext?: UserContext | null | undefined) {
  let context = testContext

  if (testContext === undefined) {
    context = {
      userId: '658e5ccb31ed5183faa803b2',
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      role: 'ORG_ADMIN',
    }
  }

  if (context !== null) {
    return createApp(context)
  } else {
    return createApp()
  }
}
