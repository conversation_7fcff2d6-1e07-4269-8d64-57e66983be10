import {
  DeliveryMethod,
  OrderProcessingStatus,
  OrderStatus,
} from '@prisma/client'
import { processScheduledOrder } from 'services/product-order/process-scheduled-order.service'
import { prisma } from 'utils/prisma'
import { describe, expect, it } from 'vitest'

describe('processScheduledOrder', () => {
  it('should set orderStatus to SCHEDULED', async () => {
    const date = new Date('01/01/2025').toISOString()
    const productOrder = await createScheduledOrderAndItems(date)
    const scheduledOrder = await processScheduledOrder({
      orderId: productOrder.id,
      paymentDate: new Date('01/01/2025'),
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      isWindcavePayment: false,
    })

    if (scheduledOrder.isErr()) {
      throw new Error('shouldnt error')
    }

    expect(scheduledOrder.value).toHaveProperty(
      'orderStatus',
      OrderStatus.SCHEDULED
    )
  })
  it('should put the order into the orderProcessingQueue', async () => {
    const date = new Date('01/01/2025').toISOString()
    const productOrder = await createScheduledOrderAndItems(date)
    const scheduledOrder = await processScheduledOrder({
      orderId: productOrder.id,
      paymentDate: new Date('01/01/2025'),
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      isWindcavePayment: false,
    })

    if (scheduledOrder.isErr()) {
      throw new Error('shouldnt error')
    }

    const data = await prisma.orderProcessingQueue.findFirstOrThrow({
      where: {
        orderNumber: productOrder.orderNumber!,
      },
    })

    expect(data).toHaveProperty('status', OrderProcessingStatus.PENDING)
    expect(data).toHaveProperty('orderNumber', productOrder.orderNumber)
    expect(data).toHaveProperty('productOrderId', productOrder.id)
    expect(data).toHaveProperty('dateCreated')
    expect(data).toHaveProperty('lastUpdated')
  })
})

async function createScheduledOrderAndItems(scheduledDate: string) {
  return prisma.$transaction(async (tx) => {
    const virtualCard = await tx.product.findFirstOrThrow({
      where: {
        name: 'Prezzy Virtual',
      },
    })

    const productOrder = await tx.productOrder.create({
      data: {
        paymentMethod: 'BANK_TRANSFER',
        orderStatus: OrderStatus.RELEASING,
        scheduledDate,
        user: {
          connect: {
            id: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
          },
        },
        organization: {
          connect: {
            id: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
          },
        },
        productOrderItems: {
          create: [
            {
              productId: virtualCard.id,
              productCode: virtualCard.productCode,
              recipientName: 'Hello World',
              quantity: 1,
              unitPrice: 500,
              deliveryMethod: DeliveryMethod.EMAIL,
              recipientEmail: '<EMAIL>',
            },
          ],
        },
      },
    })

    return tx.productOrder.update({
      where: { id: productOrder.id },
      data: { orderNumber: productOrder.orderIncrementNumber.toString() },
    })
  })
}
