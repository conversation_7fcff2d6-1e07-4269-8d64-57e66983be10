import { revokeSuperAdmin } from 'services/super-admin.service'
import { describe, expect, it, vi } from 'vitest'

describe('super-admin.service.test/revokeSuperAdmin', () => {
  it('should post super_admin_config as null to auth0', async () => {
    const res = await revokeSuperAdmin({
      userEmail: '<EMAIL>',
      userId: '6681e8455755f4a4ec11d442',
    })

    if (res.isErr()) {
      throw new Error('Shouldnt error')
    }

    expect(res.value.success).toBeTruthy()
  })
})
