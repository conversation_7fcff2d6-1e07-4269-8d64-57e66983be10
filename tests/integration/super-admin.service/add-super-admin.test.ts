import { addSuperAdminToOrg } from 'services/super-admin.service'
import { describe, expect, it } from 'vitest'

describe('super-admin.service.test/getSuperAdminUserInfo', () => {
  it('should post super_admin_config to auth0', async () => {
    const res = await addSuperAdminToOrg({
      userEmail: '<EMAIL>',
      userId: '6681e8455755f4a4ec11d442',
      targetOrgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
    })

    if (res.isErr()) {
      console.log(res.error)
      throw new Error('Shouldnt error')
    }

    expect(res.value.targetOrgId).toBe(
      '6c16a991-e43b-41bd-9e6a-a8120cb311e5'
    )
  })
})
