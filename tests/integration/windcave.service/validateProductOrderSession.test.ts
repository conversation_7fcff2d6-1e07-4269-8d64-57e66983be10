import { describe, test, expect, vi } from 'vitest'
import axios from 'axios'
import { validateProductOrderSession } from 'services/windcave.service'
import { customProductOrderRes, productOrderRes } from '../../data/windcave'

describe.skip('validateProductOrderSession', () => {
  test('should validate product order successfully', async () => {
    const spyOnAxiosGet = vi
      .spyOn(axios, 'get')
      .mockResolvedValue({ status: 200, data: productOrderRes })

    const spyOnAxiosPost = vi
      .spyOn(axios, 'post')
      .mockResolvedValue({ status: 200, data: {} })

    const res = await validateProductOrderSession({ id: 'wdincave123' })

    expect(spyOnAxiosGet).toBeTruthy()
    expect(spyOnAxiosPost).toBeTruthy()
    expect(res.isOk()).toBeTruthy()
  })

  test('should validate CUSTOM product order successfully', async () => {
    const spyOnAxiosGet = vi
      .spyOn(axios, 'get')
      .mockResolvedValue({ status: 200, data: customProductOrderRes })

    const spyOnAxiosPost = vi
      .spyOn(axios, 'post')
      .mockResolvedValue({ status: 200, data: {} })

    const res = await validateProductOrderSession({ id: 'wdincave123' })

    expect(spyOnAxiosGet).toBeTruthy()
    expect(spyOnAxiosPost).toBeTruthy()
    expect(res.isOk()).toBeTruthy()
  })
})
