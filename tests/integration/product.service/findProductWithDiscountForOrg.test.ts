import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { findProductWithDiscountForOrg } from 'services/product.service'
import { prisma } from 'utils/prisma'

describe.skip('findProductWithDiscountForOrg', () => {
  beforeAll(async () => {
    await prisma.discount.create({
      data: {
        id: 'findProductWithDiscountForOrg-test',
        loadingFee: 1000,
        digitalFee: 500,
        organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      },
    })
  })

  afterAll(async () => {
    await prisma.discount.delete({
      where: {
        id: 'findProductWithDiscountForOrg-test',
      },
    })
  })

  it('should return the correct product', async () => {
    const result = await findProductWithDiscountForOrg({
      productCode: 1,
      orgId: 'orgId',
    })

    if (result.isErr()) {
      throw new Error(JSON.stringify(result.error))
    } else {
      expect(result.value).toHaveProperty('id')
      expect(result.value).toHaveProperty('name')
      expect(result.value).toHaveProperty('productCode')
      expect(result.value).toHaveProperty('designUrl')
      expect(result.value).toHaveProperty('logoUrl')
      expect(result.value).toHaveProperty('cardTypes')
      expect(result.value).toHaveProperty('fixedValues')
      expect(result.value).toHaveProperty('min')
      expect(result.value).toHaveProperty('max')
      expect(result.value).toHaveProperty('details')
      expect(result.value).toHaveProperty('conditions')
      expect(result.value).toHaveProperty('redeem')
      expect(result.value).toHaveProperty('deliveryMethods')
      expect(result.value).toHaveProperty('organizationId')
      expect(result.value).toHaveProperty('loadingFee')
      expect(result.value).toHaveProperty('digitalFee')
      expect(result.value).toHaveProperty('discount')
    }
  })

  it('should calculate the discount correctly with loading fee', async () => {
    const result = await findProductWithDiscountForOrg({
      productCode: 1,
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
    })

    if (result.isErr()) {
      throw new Error(JSON.stringify(result.error))
    } else {
      expect(result.value).toHaveProperty('discount', 10)
    }
  })

  it('should calculate the discount to exclude digital fee discount', async () => {
    const result = await findProductWithDiscountForOrg({
      productCode: 4,
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
    })

    if (result.isErr()) {
      throw new Error(JSON.stringify(result.error))
    } else {
      expect(result.value).toHaveProperty('discount', 10)
    }
  })
})
