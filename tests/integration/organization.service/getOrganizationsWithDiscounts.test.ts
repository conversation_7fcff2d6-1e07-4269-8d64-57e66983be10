import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { getOrganizationsWithDiscounts } from 'services/organization.service'
import { prisma } from 'utils/prisma'

describe.skip('getOrganizationsWithDiscounts', () => {
  beforeAll(async () => {
    // Seed organizations with discounts
    await prisma.organization.create({
      data: {
        id: 'org1',
        type: 'LIMITED_LIABILITY_COMPANY',
        name: 'Organization One',
        tradingName: '',
        nzbn: '9429048680272',
        discounts: {
          create: {
            loadingFee: 1000,
            digitalFee: 500,
          },
        },
      },
    })
    await prisma.organization.create({
      data: {
        id: 'org2',
        type: 'LIMITED_LIABILITY_COMPANY',
        name: 'Organization Two',
        tradingName: '',
        nzbn: '9429048680278',
        discounts: {
          create: {
            loadingFee: 1500,
            digitalFee: 700,
          },
        },
      },
    })
  })

  afterAll(async () => {
    // Clean up
    await prisma.discount.deleteMany({})
    await prisma.organization.deleteMany({})
  })

  it('should return all organizations with discounts', async () => {
    const result = await getOrganizationsWithDiscounts({
      page: 1,
      pageSize: 10,
    })

    if (result.isErr()) {
      throw new Error(JSON.stringify(result.error))
    } else {
      expect(result.value.organizations.length).toBeGreaterThan(0)
      expect(result.value.organizations[1]).toHaveProperty('id')
      expect(result.value.organizations[1]).toHaveProperty('name')
      expect(result.value.organizations[1]).toHaveProperty('loadingFee')
      expect(result.value.organizations[1]).toHaveProperty('digitalFee')
    }
  })

  it.skip('should return organizations filtered by orgName', async () => {
    const result = await getOrganizationsWithDiscounts({
      page: 1,
      pageSize: 10,
      orgName: 'One',
    })

    if (result.isErr()) {
      throw new Error(JSON.stringify(result.error))
    } else {
      expect(result.value.organizations.length).toBe(1)
      expect(result.value.organizations[1].name).toBe('Organization One')
    }
  })
})
