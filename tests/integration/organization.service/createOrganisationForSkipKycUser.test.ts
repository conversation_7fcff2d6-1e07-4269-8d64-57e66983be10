import { okAsync } from 'neverthrow'
import { beforeAll, describe, expect, it, vi } from 'vitest'
import { createOrganisationForSkipKycUser } from 'services/organization.service'
import * as onboardingService from 'services/onboarding.service'
import { prisma } from 'utils/prisma'
import { createUsersForSkipKycUserOrgTest } from '../db/skip-kyc-reset'

describe.skip('createOrganisationForSkipKycUser', () => {
  beforeAll(async () => {
    await createUsersForSkipKycUserOrgTest()
  })

  it('should return the user', async () => {
    vi.spyOn(onboardingService, 'checkEmailIsInSkipKyc').mockReturnValue(
      okAsync({ email: '<EMAIL>' })
    )

    const result = await createOrganisationForSkipKycUser({
      nzbn: '12345678',
      orgName: 'Hello World',
      userId: 'testuser123',
      organizationType: 'LIMITED_LIABILITY_COMPANY',
      gstNumber: '***********',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    })

    if (result.isErr()) {
      throw new Error(JSON.stringify(result.error))
    } else {
      const org = await prisma.organization.findUnique({
        where: {
          id: result.value.organizationId!,
        },
      })

      expect(result.value).toHaveProperty(
        'email',
        '<EMAIL>'
      )
      expect(org).toHaveProperty('nzbn', '12345678')
    }
  })

  it('should create discount correctly based on nzbn and email', async () => {
    const result = await createOrganisationForSkipKycUser({
      nzbn: '9429039626950',
      orgName: 'Above & Beyond',
      userId: 'testuserwithdiscount',
      organizationType: 'LIMITED_LIABILITY_COMPANY',
      gstNumber: '***********',
      firstName: 'Jane',
      lastName: 'Doe',
      email: '<EMAIL>',
    })

    if (result.isErr()) {
      throw new Error(JSON.stringify(result.error))
    } else {
      const discount = await prisma.discount.findFirst({
        where: {
          organizationId: result.value.organizationId!,
        },
      })

      expect(discount?.loadingFee).toEqual(300)
      expect(discount?.digitalFee).toEqual(90)
      expect(discount?.expiryDate).toEqual(new Date(2024, 5, 1))
    }
  })
})
