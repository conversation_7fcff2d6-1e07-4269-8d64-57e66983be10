import { beforeAll, describe, expect, it, vi } from 'vitest'
import {
  CorporateApplicationStatus,
  IdentityType,
  OrganizationType,
} from '@prisma/client'
import { submit } from 'services/corporate-application.service'
import { createCorporateApplicationData } from '../db/corporate-application-reset'
import axios from 'axios'
import { prisma } from 'utils/prisma'

const corpApplication = {
  organization: {
    organizationType: OrganizationType.LIMITED_LIABILITY_COMPANY,
    name: 'NEXT CHAPTER PETS LTD',
    tradingName: '',
    nzbn: '9429031437004',
    address: '18 Northcroft Street',
    city: 'Auckland',
    suburb: 'Takapuna',
    country: 'New Zealand',
    zip: '0622',
    principalPlace: '',
    natureAndPurpose: '',
    phone: '',
    cPartnerUserId: '',
  },
  applicant: { firstName: 'John', lastName: 'Doe', phoneNbr: '', branch: '' },
  directors: [
    {
      id: 'corporate-application-director-1',
      firstName: 'Mr',
      givenName: '',
      lastName: 'John',
      dateOfBirth: new Date('1990-01-01T00:00:00.000Z'),
      address: '12 Shortland St',
      city: 'Auckland',
      country: 'New Zealand',
      zip: '1234',
      identificationType: IdentityType.DRIVER_LICENSE,
      identificationNbr: 'DD12345',
      driverLicenseVersion: '123',
    },
  ],
  beneficialOwners: {
    beneficialOwners: [
      {
        id: 'corporate-application-owner-1',
        firstName: 'Mr',
        givenName: '',
        lastName: 'John',
        dateOfBirth: new Date('1990-01-01T00:00:00.000Z'),
        address: '12 Shortland St',
        city: 'Auckland',
        country: 'New Zealand',
        zip: '1234',
        identificationType: IdentityType.DRIVER_LICENSE,
        identificationNbr: 'DD12345',
        driverLicenseVersion: '123',
      },
    ],
  },
  declaration: true as const,
}

const mockGetEntity = () => {
  vi.spyOn(axios, 'get').mockResolvedValue({
    status: 200,
    data: {
      Entity: {
        EntityName: 'NEXT CHAPTER PETS LTD',
        Roles: [
          {
            RoleType: 'Director',
            RoleStatus: 'ACTIVE',
            RolePerson: {
              FirstName: 'Mr',
              LastName: 'John',
            },
          },
        ],
        Company: {
          Shareholding: {
            NumberOfShares: 100,
            ShareAllocation: [
              {
                Allocation: 100,
                Shareholder: [
                  {
                    IndividualShareholder: {
                      FirstName: 'Mr',
                      LastName: 'John',
                    },
                  },
                ],
              },
            ],
          },
        },
      },
    },
  })
}

describe.skip('submitApplication', () => {
  beforeAll(async () => {
    await createCorporateApplicationData()
    mockGetEntity()
  })

  it('should submit application successfully', async () => {
    const res = await submit({
      userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      corporateApplicationId: 'corporate-application-1',
      corporateApplication: corpApplication,
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    }

    expect(res.value).toHaveProperty('applicationStatus', 'APPROVED')
  })

  it('should update status to APPROVED/PENDING for applications with beneficial owners', async () => {
    const res = await submit({
      userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      corporateApplicationId: 'corporate-application-1',
      corporateApplication: corpApplication,
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    }

    const savedApplication = await prisma.corporateApplication.findUnique({
      where: { id: 'corporate-application-1' },
    })

    expect(savedApplication).toHaveProperty(
      'status',
      CorporateApplicationStatus.APPROVED
    )
  })

  it('should update status to APPROVED/PENDING for applications WITHOUT beneficial owners', async () => {
    const application = {
      ...corpApplication,
      beneficialOwners: { hasNoIndividualBeneficialOwner: true },
    }

    const res = await submit({
      userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      corporateApplicationId: 'corporate-application-1',
      corporateApplication: application,
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    }

    const savedApplication = await prisma.corporateApplication.findUnique({
      where: { id: 'corporate-application-1' },
    })

    expect(savedApplication).toHaveProperty(
      'status',
      CorporateApplicationStatus.PENDING
    )
  })
})
