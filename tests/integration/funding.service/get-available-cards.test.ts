import {
  orderCardsFromSupplier,
  submitProductOrder,
  tryCreateStockCardOrder,
} from 'services/product-order.service'
import { beforeAll, describe, it } from 'vitest'
import {
  NEXT_CHAPTER_ORG_ID,
  USER_ID_SEEDED_123,
} from '../../../prisma/seed-test'
import { StockCardOrder } from 'routes/stock.route'

describe('corporate-application/:id/organization-type/other', () => {
  type NewOrderResponse = {
    orderNumber: string
    orderStatus: string
    id: string
  }

  let stockOrderNumber = ''
  let stockOrderId = ''
  beforeAll(async () => {
    const stockCardOrderItems: StockCardOrder = [
      {
        productCode: 5, // Assuming this is the product code for 'Woohooo new card'
        quantity: 10,
        resolution: 'LOW',
        deliveryMethod: {
          type: 'COURIER',
          address: '123 Test Street',
          suburb: 'Test Suburb',
          city: 'Test City',
          postcode: '1234',
        },
      },
    ]

    const orderResult = await tryCreateStockCardOrder({
      userId: USER_ID_SEEDED_123,
      stockCardOrderItems,
      orgId: NEXT_CHAPTER_ORG_ID,
    })

    if (orderResult.isOk()) {
      stockOrderNumber = orderResult.value.orderNumber
      stockOrderId = orderResult.value.id
    }

    await submitProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: stockOrderNumber,
      billingAddress: '5B Funding Test',
      city: 'Auckland',
      country: 'New Zealand',
      postcode: '2014',
      purchaseOrderNumber: null,
      lockCode: '',
      paymentMethod: 'BANK_TRANSFER',
      secureDelivery: false,
    })

    // submit order and create cards
    await orderCardsFromSupplier({
      orderId: stockOrderId,
      disableMocking: false,
      userId: USER_ID_SEEDED_123,
      paymentDate: new Date(),
    })

    if (orderResult.isOk()) {
      console.log(orderResult, 'sumbitted order')
    } else {
      console.log(orderResult.error, 'error submitting order')
    }
  })

  it.skip('should create a funding order into pending status', async () => {
    // todo: add test
  })
})
