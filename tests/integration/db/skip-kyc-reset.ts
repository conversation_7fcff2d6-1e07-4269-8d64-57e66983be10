import { UserRole } from '@prisma/client'
import { prisma } from 'utils/prisma'

export async function createUsersForSkipKycUserOrgTest() {
  await prisma.user.create({
    data: {
      id: 'testuser123',
      email: '<EMAIL>',
      role: UserRole.ORG_ADMIN,
      firstName: 'Mr',
      lastName: 'Test',
      organizationId: null,
    },
  })

  await prisma.user.create({
    data: {
      id: 'testuserwithdiscount',
      email: '<EMAIL>',
      role: UserRole.ORG_ADMIN,
      firstName: 'Mrs',
      lastName: 'Discount',
      organizationId: null,
    },
  })
}
