import { prisma } from 'utils/prisma'

export async function resetCrnUpdateForOrder(productOrderId: string) {
  const orderSingle = await prisma.productOrder.update({
    where: {
      id: productOrderId,
    },
    data: {
      orderStatus: 'PENDING',
      lockCodeEmailSent: false,
    },
    select: {
      productOrderItems: true,
    },
  })

  for (const orderItem of orderSingle.productOrderItems) {
    await prisma.productOrderItem.update({
      where: {
        id: orderItem.id,
      },

      data: {
        lockCodeEmailSent: false,
      },
    })

    await prisma.productOrderItemCard.deleteMany({
      where: {
        productOrderItemId: orderItem.id,
      },
    })
  }
}

export async function resetProductOrdersForRelease(overrides?: any) {
  const productOrder = await prisma.productOrder.upsert({
    where: {
      id: 'abb621ee-37d5-4a1a-97a4-0a1c57f43755',
    },
    create: {
      id: 'abb621ee-37d5-4a1a-97a4-0a1c57f43755',
      orderStatus: 'SUBMITTED',
      orderNumber: '15234',
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      totalQuantity: 2,
      subTotal: 10000,
      secureDelivery: false,
      loadingFeeTotal: 1190,
      digitalFeeTotal: 0,
      shippingTotal: 652,
      discountTotal: 90,
      gstAmount: 98,
      orderTotal: 12046,
      creditCardFee: 294,
      lockCode: null,
      lockCodeEmailSent: false,
      createdAt: '2024-04-10T19:57:17.901Z',
      reportedToEpay: false,
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
    },
    update: {
      orderStatus: 'SUBMITTED',
      submittedAt: null,
      releasedBy: null,
    },
  })

  const prezzyRed = await prisma.product.findFirst({
    where: {
      name: 'Prezzy Red',
    },
  })

  await prisma.productOrderItem.upsert({
    where: {
      id: '70d7dcf9-212d-47c2-889d-5a55713686d2',
    },
    create: {
      id: '70d7dcf9-212d-47c2-889d-5a55713686d2',
      productOrderId: productOrder.id,
      productId: prezzyRed?.id!,
      productCode: prezzyRed?.productCode!,
      quantity: 2,
      deliveryMethod: 'COURIER',
      unitPrice: 5000,
      recipientName: 'John Doe',
      recipientEmail: '<EMAIL>',
      recipientAddress: '5B Luna Way',
      recipientCity: 'Auckland',
      recipientCountry: 'New Zealand',
      recipientSuburb: 'Bucklands Beach',
      recipientPostCode: '2014',
      externalBatchId: null,
      loadingFee: 1190,
      deliveryBatchId: '1',
      digitalFee: 0,
      discount: 90,
      lockCodeEmailSent: false,
    },
    update: {
      externalBatchId: null,
    },
  })

  await prisma.productOrderItem.upsert({
    where: {
      id: '70d7dcf9-212d-47c2-889d-5a55713686d3',
    },
    create: {
      id: '70d7dcf9-212d-47c2-889d-5a55713686d3',
      productOrderId: productOrder.id,
      productId: prezzyRed?.id!,
      productCode: prezzyRed?.productCode!,
      quantity: 2,
      deliveryMethod: 'COURIER',
      unitPrice: 5000,
      recipientName: 'John Doe',
      recipientEmail: '<EMAIL>',
      recipientAddress: '5B Luna Way',
      recipientCity: 'Auckland',
      recipientCountry: 'New Zealand',
      recipientSuburb: 'Bucklands Beach',
      recipientPostCode: '2014',
      externalBatchId: null,
      deliveryRecipient: true,
      loadingFee: 1190,
      deliveryBatchId: '1',
      digitalFee: 0,
      discount: 90,
      lockCodeEmailSent: false,
      ...overrides?.['70d7dcf9-212d-47c2-889d-5a55713686d3'],
    },
    update: {
      externalBatchId: null,
      ...overrides?.['70d7dcf9-212d-47c2-889d-5a55713686d3'],
    },
  })
}

export async function resetPartialOrderForRelease() {
  const productOrder = await prisma.productOrder.upsert({
    where: {
      id: '4da77f39-1abe-46fe-9d7b-267f39f79042',
    },
    create: {
      id: '4da77f39-1abe-46fe-9d7b-267f39f79042',
      orderStatus: 'SUBMITTED',
      orderNumber: '00123',
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      totalQuantity: 2,
      subTotal: 10000,
      secureDelivery: false,
      loadingFeeTotal: 1190,
      digitalFeeTotal: 0,
      shippingTotal: 652,
      discountTotal: 90,
      gstAmount: 98,
      orderTotal: 12046,
      creditCardFee: 294,
      lockCode: null,
      lockCodeEmailSent: false,
      createdAt: '2024-04-10T19:57:17.901Z',
      reportedToEpay: false,
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
    },
    update: {
      orderStatus: 'SUBMITTED',
      submittedAt: null,
      releasedBy: null,
    },
  })

  const prezzyRed = await prisma.product.findFirst({
    where: {
      name: 'Prezzy Red',
    },
  })

  await prisma.productOrderItem.upsert({
    where: {
      id: '99c48dad-a4cf-4f84-87b0-9b446006781d',
    },
    create: {
      id: '99c48dad-a4cf-4f84-87b0-9b446006781d',
      productOrderId: productOrder.id,
      productId: prezzyRed?.id!,
      productCode: prezzyRed?.productCode!,
      quantity: 2,
      deliveryMethod: 'COURIER',
      unitPrice: 5000,
      recipientName: 'John Doe',
      recipientEmail: '<EMAIL>',
      recipientAddress: '5B Luna Way',
      recipientCity: 'Auckland',
      recipientCountry: 'New Zealand',
      recipientSuburb: 'Bucklands Beach',
      recipientPostCode: '2014',
      externalBatchId: 'externalBatchId-123',
      loadingFee: 1190,
      deliveryBatchId: '1',
      digitalFee: 0,
      discount: 90,
      lockCodeEmailSent: false,
    },
    update: {},
  })

  await prisma.productOrderItem.upsert({
    where: {
      id: '1f85d259-84a9-428d-9727-9a84d104fcba',
    },
    create: {
      id: '1f85d259-84a9-428d-9727-9a84d104fcba',
      productOrderId: productOrder.id,
      productId: prezzyRed?.id!,
      productCode: prezzyRed?.productCode!,
      quantity: 2,
      deliveryMethod: 'COURIER',
      unitPrice: 5000,
      recipientName: 'John Doe',
      recipientEmail: '<EMAIL>',
      recipientAddress: '5B Luna Way',
      recipientCity: 'Auckland',
      recipientCountry: 'New Zealand',
      recipientSuburb: 'Bucklands Beach',
      recipientPostCode: '2014',
      deliveryRecipient: true,
      loadingFee: 1190,
      deliveryBatchId: '1',
      digitalFee: 0,
      discount: 90,
      lockCodeEmailSent: false,
    },
    update: {},
  })
}
