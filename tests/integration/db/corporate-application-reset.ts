import { IdentityType } from '@prisma/client'
import { prisma } from 'utils/prisma'

export const ORG_ID_NEXT_CHAPTER_PETS = '6c16a991-e43b-41bd-9e6a-a8120cb311e5'
export async function createCorporateApplicationData() {
  await prisma.corporateApplication.create({
    data: {
      id: 'corporate-application-1',
      organizationId: ORG_ID_NEXT_CHAPTER_PETS,
      organizationType: 'LIMITED_LIABILITY_COMPANY',
      name: 'NEXT CHAPTER PETS LTD',
      tradingName: '',
      nzbn: '9429031437004',
      gstNumber: '',
      address: '13 Northcroft Street',
      city: 'Auckland',
      suburb: 'Takapuna',
      country: 'New Zealand',
      zip: '0622',
      principalPlace: '',
      natureAndPurpose: '',
      phone: '',
      status: 'DRAFT',
      createdAt: '2023-06-19T23:06:37.141Z',
      updatedAt: '2023-06-20T01:03:54.668Z',
      submittedAt: '2023-06-19T23:17:13.147Z',
      userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
    },
  })

  await prisma.director.create({
    data: {
      id: 'corporate-application-director-1',
      firstName: 'Mr',
      givenName: '',
      lastName: 'John',
      dateOfBirth: new Date('1990-01-01T00:00:00.000Z'),
      address: '12 Shortland St',
      city: 'Auckland',
      country: 'New Zealand',
      zip: '1234',
      identificationType: IdentityType.DRIVER_LICENSE,
      identificationNbr: 'DD12345',
      driverLicenseVersion: '123',
      corporateApplicationId: 'corporate-application-1',
    },
  })

  await prisma.beneficialOwner.create({
    data: {
      id: 'corporate-application-owner-1',
      firstName: 'Mr',
      givenName: '',
      lastName: 'John',
      dateOfBirth: new Date('1990-01-01T00:00:00.000Z'),
      address: '12 Shortland St',
      city: 'Auckland',
      country: 'New Zealand',
      zip: '1234',
      identificationType: IdentityType.DRIVER_LICENSE,
      identificationNbr: 'DD12345',
      driverLicenseVersion: '123',
      corporateApplicationId: 'corporate-application-1',
    },
  })
}
