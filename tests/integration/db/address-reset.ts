import { prisma } from 'utils/prisma'

export async function resetAddresses() {
  await prisma.address.createMany({
    data: [
      {
        id: 'address1',
        address: '11 Hello World',
        city: 'Auckland',
        suburb: 'Milford',
        postCode: '0606',
        isDefault: true,
        country: 'NZ',
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      },
      {
        id: 'address2',
        address: '1 Shakes Rd',
        city: 'Auckland',
        suburb: 'Milford',
        postCode: '0606',
        isDefault: false,
        country: 'NZ',
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      },
    ],
  })
}
