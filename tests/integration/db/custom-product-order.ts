/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { prisma } from 'utils/prisma'

export const CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS = {
  id: '3659ad1c-7968-48d5-9743-333317f34332',
  orderNumber: 'LOGO789',
}

export async function seedCustomProductOrderForSubmit() {
  const prezzyRed = await prisma.product.findFirst({
    where: {
      name: 'Prezzy Red',
    },
  })

  await prisma.customProductOrder.upsert({
    where: {
      id: '8a03f8a0-3b38-4d4c-a21f-e29a55f00f77',
    },
    create: {
      id: '8a03f8a0-3b38-4d4c-a21f-e29a55f00f77',
      orderStatus: 'PENDING',
      orderNumber: '15000',
      paymentMethod: 'CREDIT_CARD',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      orderTotal: 5000,
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      productId: prezzyRed?.id!,
    },
    update: {},
  })

  await prisma.customProductOrder.upsert({
    where: {
      id: '099c2288-85b8-44eb-be3f-8b1ac48db346',
    },
    create: {
      id: '099c2288-85b8-44eb-be3f-8b1ac48db346',
      orderStatus: 'PENDING',
      orderNumber: 'LOGO111',
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      orderTotal: 5000,
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      productId: prezzyRed?.id!,
    },
    update: {},
  })

  await prisma.customProductOrder.upsert({
    where: {
      id: '099c2288-85b8-44eb-be3f-8b1ac48dl123',
    },
    create: {
      id: '099c2288-85b8-44eb-be3f-8b1ac48dl123',
      orderStatus: 'PENDING',
      orderNumber: 'LOGO123',
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      orderTotal: 5125,
      creditCardFee: 125,
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      productId: prezzyRed?.id!,
    },
    update: {},
  })

  await prisma.customProductOrder.upsert({
    where: {
      id: '099c2288-85b8-44eb-be3f-8b1ac48dp123',
    },
    create: {
      id: '099c2288-85b8-44eb-be3f-8b1ac48dp123',
      orderStatus: 'PENDING',
      orderNumber: 'LOGO456',
      paymentMethod: 'CREDIT_CARD',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      orderTotal: 5125,
      creditCardFee: 125,
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      productId: prezzyRed?.id!,
    },
    update: {},
  })

  await prisma.customProductOrder.upsert({
    where: {
      id: CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS.id,
    },
    create: {
      id: CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS.id,
      orderStatus: 'PENDING',
      orderNumber: CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS.orderNumber,
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      orderTotal: 10000,
      creditCardFee: 0,
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      productId: prezzyRed?.id!,
    },
    update: {},
  })
}

export async function seedCustomProductOrder() {
  const user = '********-1a28-4a4f-b9ed-ff7bee40d5d2'

  const prezzyRedCardDesign = await prisma.cardDesign.findFirst({
    where: {
      externalCardDesignId: '6',
    },
  })

  if (!prezzyRedCardDesign) {
    throw new Error('Prezzy Red card design not found')
  }

  const orgId = '6c16a991-e43b-41bd-9e6a-a8120cb311e5'

  const henryIsAwesomeLogo = await prisma.cardLogo.upsert({
    where: {
      id: 'b2fa5d2b-ef54-4da9-8dea-e1ecae779349',
    },
    create: {
      id: 'b2fa5d2b-ef54-4da9-8dea-e1ecae779349',
      name: 'henry_is_awesome_d17718dc-e107-4bf8-bada-51d86ba714e9.png',
      logoUrl:
        'https://prezzy-card.s3.ap-southeast-2.amazonaws.com/logo/henry_is_awesome_d17718dc-e107-4bf8-bada-51d86ba714e9.png',
      externalCardLogoId: null,
      organizationId: orgId,
    },
    update: {},
  })

  const henryIsAwesomeProduct = await prisma.product.upsert({
    where: {
      id: 'ebcdce3e-01cc-456c-80a6-f1dfa0996181',
    },
    create: {
      id: 'ebcdce3e-01cc-456c-80a6-f1dfa0996181',
      name: 'henry is awesome',
      status: 'SENT',
      productCode: 6,
      type: 'CUSTOM',
      loadingFee: 595,
      organizationId: orgId,
      logoId: henryIsAwesomeLogo.id,
      designId: prezzyRedCardDesign.id!,
    },
    update: {},
  })

  await prisma.customProductOrder.create({
    data: {
      id: 'id-0',
      orderStatus: 'PROCESSING',
      paymentMethod: 'BANK_TRANSFER',
      windcaveSessionId: null,
      windcaveSessionConfirmed: false,
      purchaseOrderNumber: '*********',
      billingAddress: '36 punawai pl',
      city: 'Taupo',
      country: 'NZ ',
      postCode: '3330',
      gstNumber: null,
      discountTotal: 0,
      logoUploadFee: 5000,
      gstAmount: 750,
      orderTotal: 5750,
      createdAt: '2024-03-04T19:59:24.281Z',
      submittedAt: null,
      organizationId: orgId,
      userId: user,
      productId: henryIsAwesomeProduct.id,
    },
  })

  const testingAgainLogo = await prisma.cardLogo.upsert({
    where: {
      id: '5d9ef6ef-3bb1-4d99-9e75-6c7571d40cfa',
    },
    create: {
      id: '5d9ef6ef-3bb1-4d99-9e75-6c7571d40cfa',
      name: 'testing_again_again_9746013d-f9b3-4573-b8cb-8b9b7ee2eea3.png',
      logoUrl:
        'https://prezzy-card.s3.ap-southeast-2.amazonaws.com/logo/testing_again_again_9746013d-f9b3-4573-b8cb-8b9b7ee2eea3.png',
      externalCardLogoId: null,
      organizationId: orgId,
    },
    update: {},
  })

  const testingAgainAgainProduct = await prisma.product.upsert({
    where: {
      id: 'e56772c7-4220-4daf-aaea-17b86e0eb119',
    },
    create: {
      id: 'e56772c7-4220-4daf-aaea-17b86e0eb119',
      name: 'testing again again',
      status: 'PENDING',
      productCode: 14,
      type: 'CUSTOM',
      loadingFee: 595,
      organizationId: orgId,
      designId: prezzyRedCardDesign.id,
      logoId: testingAgainLogo.id,
    },
    update: {},
  })

  await prisma.customProductOrder.create({
    data: {
      id: 'id-1',
      orderStatus: 'PROCESSING',
      paymentMethod: 'BANK_TRANSFER',
      windcaveSessionId: null,
      windcaveSessionConfirmed: false,
      purchaseOrderNumber: '*********',
      billingAddress: '36 punawai pl',
      city: 'Taupo',
      country: 'NZ ',
      postCode: '3330',
      gstNumber: null,
      discountTotal: 0,
      logoUploadFee: 5000,
      gstAmount: 750,
      orderTotal: 5750,
      createdAt: '2024-03-04T00:35:17.424Z',
      submittedAt: null,
      organizationId: orgId,
      userId: user,
      productId: testingAgainAgainProduct.id,
    },
  })

  const logoAnotherTestLogo = await prisma.cardLogo.upsert({
    where: {
      id: 'd2867888-37bd-456b-9259-f8424207d577',
    },
    create: {
      id: 'd2867888-37bd-456b-9259-f8424207d577',
      name: 'another_test_card_313dc2b6-da6b-4341-bf7d-9944c5fc0dd0.png',
      logoUrl:
        'https://prezzy-card.s3.ap-southeast-2.amazonaws.com/logo/another_test_card_313dc2b6-da6b-4341-bf7d-9944c5fc0dd0.png',
      externalCardLogoId: null,
      organizationId: orgId,
    },
    update: {},
  })

  const anotherTestCardProduct = await prisma.product.upsert({
    where: {
      id: '2b5505ca-ab85-4b4f-a131-d470273d0fd2',
    },
    create: {
      id: '2b5505ca-ab85-4b4f-a131-d470273d0fd2',
      name: 'Another test card',
      status: 'PENDING',
      productCode: 12,
      type: 'CUSTOM',
      loadingFee: 595,
      organizationId: orgId,
      designId: prezzyRedCardDesign.id,
      logoId: logoAnotherTestLogo.id,
    },
    update: {},
  })

  await prisma.customProductOrder.create({
    data: {
      id: 'id-2',
      orderStatus: 'PROCESSING',
      paymentMethod: 'BANK_TRANSFER',
      windcaveSessionId: null,
      windcaveSessionConfirmed: false,
      purchaseOrderNumber: '*********',
      billingAddress: '36 punawai pl',
      city: 'Taupo',
      country: 'NZ ',
      postCode: '3330',
      gstNumber: null,
      discountTotal: 0,
      logoUploadFee: 5000,
      gstAmount: 750,
      orderTotal: 5750,
      createdAt: '2024-02-16T01:41:23.089Z',
      submittedAt: null,
      organizationId: orgId,
      userId: user,
      productId: anotherTestCardProduct.id,
    },
  })

  const testLogoSizeLogo = await prisma.cardLogo.upsert({
    where: {
      id: '5f14861a-77b8-4c1d-acea-b46dcfeea56a',
    },
    create: {
      id: '5f14861a-77b8-4c1d-acea-b46dcfeea56a',
      name: 'test_logo_size_7ba696e4-b8f4-41d4-9962-521363dec5ca.png',
      logoUrl:
        'https://prezzy-card.s3.ap-southeast-2.amazonaws.com/logo/test_logo_size_7ba696e4-b8f4-41d4-9962-521363dec5ca.png',
      externalCardLogoId: null,
      organizationId: orgId,
    },
    update: {},
  })

  const testLogoSizeProduct = await prisma.product.upsert({
    where: {
      id: '40ec4f59-0a48-4219-967d-f9ef7770232c',
    },
    create: {
      id: '40ec4f59-0a48-4219-967d-f9ef7770232c',
      name: 'Test logo size',
      status: 'PENDING',
      productCode: 11,
      type: 'CUSTOM',
      loadingFee: 595,
      organizationId: orgId,
      designId: prezzyRedCardDesign.id,
      logoId: testLogoSizeLogo.id,
    },
    update: {},
  })

  await prisma.customProductOrder.create({
    data: {
      id: 'id-3',
      orderStatus: 'PROCESSING',
      paymentMethod: 'BANK_TRANSFER',
      windcaveSessionId: null,
      windcaveSessionConfirmed: false,
      purchaseOrderNumber: '*********',
      billingAddress: '36 punawai pl',
      city: 'Taupo',
      country: 'NZ ',
      postCode: '3330',
      gstNumber: null,
      discountTotal: 0,
      logoUploadFee: 5000,
      gstAmount: 750,
      orderTotal: 5750,
      createdAt: '2024-02-09T02:31:34.639Z',
      submittedAt: null,
      organizationId: orgId,
      userId: user,
      productId: testLogoSizeProduct.id,
    },
  })
}
