import { prisma } from 'utils/prisma'

export async function resetCardsForTest() {
  const productOrder = await prisma.productOrder.create({
    data: {
      id: 'CARD_SERVICE_ORDER_1',
      orderStatus: 'CONFIRMED',
      orderNumber: 'CARD_SERVICE_ORDER_NUM1',
      paymentMethod: 'BANK_TRANSFER',
      createdAt: '2024-02-06T19:57:17.901Z',
      organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
    },
  })

  const prezzyRed = await prisma.product.findFirst({
    where: {
      name: 'Prezzy Red',
    },
  })

  await prisma.productOrderItem.createMany({
    data: [
      {
        id: 'CARD_SERVICE_ORDER_ITEM_1',
        productOrderId: productOrder.id,
        productId: prezzyRed?.id!,
        productCode: prezzyRed?.productCode!,
        quantity: 1,
        deliveryMethod: 'COURIER',
        unitPrice: 5000,
        recipientName: '<PERSON>',
        recipientEmail: '<EMAIL>',
        recipientAddress: '5B Luna Way',
        recipientCity: 'Auckland',
        recipientCountry: 'New Zealand',
        recipientSuburb: 'Bucklands Beach',
        recipientPostCode: '2014',
        externalBatchId: 'mock-123',
        loadingFee: 1190,
        deliveryBatchId: '1',
        digitalFee: 0,
        discount: 90,
        lockCodeEmailSent: false,
      },
      {
        id: 'CARD_SERVICE_ORDER_ITEM_2',
        productOrderId: productOrder.id,
        productId: prezzyRed?.id!,
        productCode: prezzyRed?.productCode!,
        quantity: 2,
        deliveryMethod: 'COURIER',
        unitPrice: 15000,
        recipientName: 'John Doe',
        recipientEmail: '<EMAIL>',
        recipientAddress: '5B Luna Way',
        recipientCity: 'Auckland',
        recipientCountry: 'New Zealand',
        recipientSuburb: 'Bucklands Beach',
        recipientPostCode: '2014',
        externalBatchId: 'mock-456',
        loadingFee: 1190,
        deliveryBatchId: '2',
        digitalFee: 0,
        discount: 90,
        lockCodeEmailSent: false,
      },
    ],
  })

  await prisma.productOrderItemCard.createMany({
    data: [
      {
        externalCardReferenceNumber: 'crn-111',
        productOrderItemId: 'CARD_SERVICE_ORDER_ITEM_1',
        blocked: true,
        activated: true,
        lockCode: '1234',
      },
      {
        externalCardReferenceNumber: 'crn-222',
        productOrderItemId: 'CARD_SERVICE_ORDER_ITEM_2',
        blocked: true,
        activated: true,
        lockCode: '1234',
      },
      {
        externalCardReferenceNumber: 'crn-333',
        productOrderItemId: '70d7dcf9-212d-47c2-889d-5a55713686d1',
        blocked: false,
        activated: true,
      },
    ],
  })
}
