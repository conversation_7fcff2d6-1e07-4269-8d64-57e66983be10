import { prisma } from 'utils/prisma'

export const USER_HARRY_DOES_NOT_HAVE_OTP = '658e5ccb31ed5183faa803b2'
export const USER_BOB_HAS_EXPIRED_OTP = '6599e4e5193ea79c142c8b7e'
export const USER_MARY_HAS_OTP_THAT_IS_HAS_BEEN_USED =
  '6599e51f50c57b82204a4cc9'

export const OTP_FOR_MARY = '999999'
export const OTP_FOR_BOB = '115588'
export async function resetOtpForTests() {
  await prisma.oneTimePassword.deleteMany({
    where: {
      OR: [{ userId: USER_HARRY_DOES_NOT_HAVE_OTP }],
    },
  })

  const elevenMinutesAgo = new Date()
  elevenMinutesAgo.setMinutes(elevenMinutesAgo.getMinutes() - 11)

  await prisma.oneTimePassword.upsert({
    where: { userId: USER_BOB_HAS_EXPIRED_OTP },
    create: {
      userId: USER_BOB_HAS_EXPIRED_OTP,
      otp: OTP_FOR_BOB,
      createdAt: elevenMinutesAgo,
      verified: false,
      phoneNumber: '555666777',
    },
    update: {
      otp: '999999',
      createdAt: elevenMinutesAgo,
      verified: false,
      phoneNumber: '555666777',
    },
  })

  await prisma.oneTimePassword.upsert({
    where: { userId: USER_MARY_HAS_OTP_THAT_IS_HAS_BEEN_USED },
    create: {
      userId: USER_MARY_HAS_OTP_THAT_IS_HAS_BEEN_USED,
      otp: OTP_FOR_MARY,
      createdAt: new Date(),
      verified: true,
      phoneNumber: '123456789',
    },
    update: {
      otp: '115588',
      createdAt: new Date(),
      verified: true,
      phoneNumber: '123456789',
    },
  })
}
