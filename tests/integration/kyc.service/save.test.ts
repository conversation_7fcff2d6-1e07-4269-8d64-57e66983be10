import { CorporateApplicationStatus, OrganizationType } from '@prisma/client'
import { save } from 'services/corporate-application.service'
import { prisma } from 'utils/prisma'
import { describe, expect, it } from 'vitest'

describe('save function', () => {
  it('should save application as DRAFT when kycCompletionPreference is not FINISH_LATER', async () => {
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        id: 'test-user-id',
      },
    })
    if (!user) {
      throw new Error('Error creating user')
    }

    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })
    if (!application) {
      throw new Error('Error creating application')
    }

    const result = await save({
      userId: user.id,
      corporateApplicationId: application.id,
      corporateApplication: {
        applicationStatus: CorporateApplicationStatus.DRAFT,
        organization: {
          name: 'Updated Test Org',
          nzbn: '9429048680272',
          organizationType: OrganizationType.LIMITED_LIABILITY_COMPANY,
        },
        directors: [],
        beneficialOwners: {
          beneficialOwners: [],
        },
        applicant: {},
      },
    })

    if (result.isOk()) {
      expect(result.isOk()).toBeTruthy()
      expect(result.value).toEqual({
        applicationStatus: CorporateApplicationStatus.DRAFT,
      })
    } else {
      throw new Error('Save operation failed')
    }

    // Verify the application was updated in the database
    const updatedApplication = await prisma.corporateApplication.findUnique({
      where: { id: application.id },
    })
    expect(updatedApplication?.status).toBe(CorporateApplicationStatus.DRAFT)
    expect(updatedApplication?.name).toBe('Updated Test Org')
  })

  it('should save application as PRE_APPROVED and create organization when kycCompletionPreference is FINISH_LATER', async () => {
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        id: 'test-user-id-2',
      },
    })
    if (!user) {
      throw new Error('Error creating user')
    }

    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })
    if (!application) {
      throw new Error('Error creating application')
    }

    const result = await save({
      userId: user.id,
      corporateApplicationId: application.id,

      corporateApplication: {
        applicationStatus: CorporateApplicationStatus.DRAFT,
        organization: {
          name: 'Pre-Approved Org',
          nzbn: '9429048680273',
          organizationType: OrganizationType.LIMITED_LIABILITY_COMPANY,
        },
        directors: [],
        beneficialOwners: {
          beneficialOwners: [],
        },
        applicant: {},
      },
      kycCompletionPreference: 'FINISH_LATER',
    })

    if (result.isOk()) {
      expect(result.isOk()).toBeTruthy()
      expect(result.value).toEqual({
        applicationStatus: CorporateApplicationStatus.PRE_APPROVED,
      })
    } else {
      throw new Error('Save operation failed')
    }

    // Verify the application was updated and organization was created
    const updatedApplication = await prisma.corporateApplication.findUnique({
      where: { id: application.id },
      include: { organization: true },
    })
    expect(updatedApplication?.status).toBe(
      CorporateApplicationStatus.PRE_APPROVED
    )
    expect(updatedApplication?.name).toBe('Pre-Approved Org')
    expect(updatedApplication?.organization).not.toBeNull()
    expect(updatedApplication?.organization?.name).toBe('Pre-Approved Org')
  })

  it('should not create a new organization when kycCompletionPreference is FINISH_LATER and org already exists', async () => {
    // Create a user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        id: 'test-user-id-5',
      },
    })
    if (!user) {
      throw new Error('Error creating user')
    }

    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    if (!application) {
      throw new Error('Error creating application')
    }

    const result = await save({
      userId: user.id,
      kycCompletionPreference: 'FINISH_LATER',
      corporateApplicationId: application.id,
      corporateApplication: {
        applicationStatus: CorporateApplicationStatus.PRE_APPROVED,
        organization: {
          name: 'Test Org',
          nzbn: '9429048680274',
          organizationType: OrganizationType.LIMITED_LIABILITY_COMPANY,
        },
        directors: [],
        beneficialOwners: {
          beneficialOwners: [],
        },
        applicant: {},
      },
    })

    if (result.isOk()) {
      expect(result.isOk()).toBeTruthy()
      expect(result.value).toEqual({
        applicationStatus: CorporateApplicationStatus.PRE_APPROVED,
      })
    } else {
      throw new Error('Save operation failed')
    }

    // Verify the application was updated and organization was created
    const createdApplication = await prisma.corporateApplication.findUnique({
      where: { id: application.id },
      include: { organization: true },
    })

    expect(createdApplication?.status).toBe(
      CorporateApplicationStatus.PRE_APPROVED
    )
    expect(createdApplication?.organization).not.toBeNull()
    expect(createdApplication?.organization?.name).toBe('Test Org')

    const updatedApplication = await save({
      userId: user.id,
      corporateApplicationId: application.id,
      kycCompletionPreference: 'FINISH_LATER',
      corporateApplication: {
        applicationStatus: CorporateApplicationStatus.REQUIRES_INFO,
        organization: {
          name: 'Test Org',
          nzbn: '9429048680272',
          organizationType: OrganizationType.LIMITED_LIABILITY_COMPANY,
        },
        directors: [],
        beneficialOwners: {
          beneficialOwners: [],
        },
        applicant: {
          firstName: 'John',
          lastName: 'Doe',
        },
      },
    })
    if (updatedApplication.isOk()) {
      expect(updatedApplication.isOk()).toBeTruthy()
      expect(updatedApplication.value).toEqual({
        applicationStatus: CorporateApplicationStatus.PRE_APPROVED,
      })
    } else {
      throw new Error('Save operation failed')
    }

    // Verify the application was updated and organization was not created
    const updatedApplicationData = await prisma.corporateApplication.findUnique(
      {
        where: { id: application.id },
        include: { organization: true },
      }
    )

    expect(updatedApplicationData?.status).toBe(
      CorporateApplicationStatus.PRE_APPROVED
    )
    expect(updatedApplicationData?.organization?.id).equal(
      createdApplication?.organization?.id
    )
  })

  //update application with new data.

  // it('should handle non-existent application ID', async () => {
  //   const user = await prisma.user.create({
  //     data: {
  //       email: '<EMAIL>',
  //       id: 'test-user-id-4',
  //     },
  //   })
  //   if (!user) {
  //     throw new Error('Error creating user')
  //   }

  //   const result = await save({
  //     userId: user.id,
  //     corporateApplicationId: 'non-existent-id',
  //     corporateApplication: {
  //       organization: {
  //         name: 'Test Org',
  //         nzbn: '9429048680275',
  //         organizationType: OrganizationType.LIMITED_LIABILITY_COMPANY,
  //       },
  //       directors: [],
  //       beneficialOwners: {},
  //       applicant: {},
  //     },
  //   })

  //   expect(result.isErr()).toBeTruthy()
  //   // Add more specific error checks based on your error handling
  // })
})
