import { CorporateApplicationStatus, KycStatus } from '@prisma/client'
import { listCorporateApplicationsWithStatusCount } from 'services/kyc.service'
import { ERRORS } from 'utils/error'
import { prisma } from 'utils/prisma'
import { describe, expect, it, vi } from 'vitest'

describe('listCorporateApplicationsWithStatusCount', () => {
  async function createUserWithApplication(data: {
    name: string
    status: CorporateApplicationStatus
    kycStatus?: KycStatus
    submittedAt?: Date
  }) {
    return prisma.user.create({
      data: {
        id: Math.floor(Math.random() * 1000).toString(),
        email: `${data.name.toLowerCase().replace(' ', '')}@example.com`,
        corporateApplications: {
          create: {
            name: data.name,
            status: data.status,
            kycStatus: data.kycStatus,
            submittedAt: data.submittedAt,
          },
        },
      },
      include: {
        corporateApplications: true,
      },
    })
  }

  it('should return all applications when no search term passed', async () => {
    const countApplications = await prisma.corporateApplication.count({
      where: { status: { not: 'DRAFT' } },
    })

    const result = await listCorporateApplicationsWithStatusCount({
      search: '',
      page: 1,
      pageSize: 10,
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value.corporateApplications).toHaveLength(countApplications)
    }
  })

  it('should filter applications by status', async () => {
    await createUserWithApplication({ name: 'Corp 1', status: 'PENDING' })
    await createUserWithApplication({
      name: 'Corp 2',
      status: 'APPROVED',
      kycStatus: 'DD',
    })
    await createUserWithApplication({
      name: 'Corp 3',
      status: 'APPROVED',
      kycStatus: 'NKYC',
    })
    await createUserWithApplication({ name: 'Corp 4', status: 'DECLINED' })

    const result = await listCorporateApplicationsWithStatusCount({
      search: '',
      status: 'PENDING',
      page: 1,
      pageSize: 10,
    })

    const countPendingApplications = await prisma.corporateApplication.count({
      where: {
        OR: [
          { status: 'PENDING' },
          {
            AND: [{ status: 'APPROVED' }, { kycStatus: 'NKYC' }],
          },
        ],
      },
    })

    const countApprovedApplications = await prisma.corporateApplication.count({
      where: {
        status: 'APPROVED',
        OR: [{ kycStatus: { not: 'NKYC' } }, { kycStatus: null }],
      },
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value.corporateApplications).toHaveLength(
        countPendingApplications
      )
      expect(result.value.counts.PENDING).toBe(countPendingApplications)
      expect(result.value.counts.APPROVED).toBe(countApprovedApplications)
    }
  })

  it('should filter applications by search term', async () => {
    await createUserWithApplication({
      name: 'Pets unlimited',
      status: 'PENDING',
    })
    await createUserWithApplication({
      name: 'Another Corp',
      status: 'APPROVED',
    })

    const result = await listCorporateApplicationsWithStatusCount({
      search: 'pets',
      page: 1,
      pageSize: 10,
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value.corporateApplications).toHaveLength(1)
      expect(result.value.corporateApplications[0].name).toBe('Pets unlimited')
    }
  })

  it('should handle database errors gracefully', async () => {
    // Mock prisma to throw an error
    vi.spyOn(prisma, '$transaction').mockRejectedValueOnce(
      new Error('DB Error')
    )

    const result = await listCorporateApplicationsWithStatusCount({
      search: '',
      page: 1,
      pageSize: 10,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error).toBe(ERRORS.DATABASE_ERROR)
    }
  })
})
