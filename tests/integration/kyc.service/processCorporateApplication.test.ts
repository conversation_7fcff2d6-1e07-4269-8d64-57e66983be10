import { describe, expect, it, vi } from 'vitest'
import { processCorporateApplication } from 'services/kyc.service'
import {
  CorporateApplicationStatus,
  KycStatus,
  OrganizationType,
} from '@prisma/client'
import { prisma } from 'utils/prisma'
import { APPROVED_APPLICATION } from 'helpers/email-templates'
import * as postmarkService from 'services/postmark.service'

import { v4 as uuidv4 } from 'uuid'

describe.skip('processCorporateApplication', () => {
  it('should update corporate application status and kycStatus', async () => {
    const testApplication = await prisma.corporateApplication.findFirst({
      where: { id: '09282fcd-95e7-4cb1-ab40-b8f83998a338' },
      include: { user: true },
    })

    if (!testApplication) {
      throw new Error('Test application not found. Check your seed data.')
    }

    const result = await processCorporateApplication({
      corporateApplicationId: testApplication.id,
      status: CorporateApplicationStatus.REQUIRES_INFO,
      kycStatus: KycStatus.DD,
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value.status).toBe(CorporateApplicationStatus.REQUIRES_INFO)
      expect(result.value.kycStatus).toBe(KycStatus.DD)
    }
  })

  it('should create an organization when application is approved', async () => {
    const testApplication = await prisma.corporateApplication.findFirst({
      where: { id: '09282fcd-95e7-4cb1-ab40-b8f83998a338' },
      include: { user: true, organization: true },
    })

    if (!testApplication) {
      throw new Error('Test application not found. Check your seed data.')
    }

    // If the application already has an organization, delete it for this test
    if (testApplication.organization) {
      await prisma.organization.delete({
        where: { id: testApplication.organization.id },
      })
    }

    // Update the application with all required fields
    await prisma.corporateApplication.update({
      where: { id: testApplication.id },
      data: {
        status: CorporateApplicationStatus.REVIEWING,
        kycStatus: KycStatus.NKYC,
        organizationId: null,
        name: 'Test Organization Name',
        nzbn: '9429046123403', // Example NZBN, replace with a valid one
        organizationType: 'LIMITED_LIABILITY_COMPANY',
        tradingName: 'Test Trading Name',
        address: '123 Test Street',
        city: 'Test City',
        suburb: 'Test Suburb',
        zip: '12345',
        country: 'New Zealand',
        principalPlace: 'Test Principal Place',
        natureAndPurpose: 'Test nature and purpose',
        phone: '0211234567',
      },
    })

    const sendEmailSpy = vi.spyOn(postmarkService, 'sendEmailWithTemplate')
    sendEmailSpy.mockResolvedValue({
      isOk: () => true,
      value: undefined,
    } as any)

    const result = await processCorporateApplication({
      corporateApplicationId: testApplication.id,
      status: CorporateApplicationStatus.APPROVED,
      kycStatus: KycStatus.DD,
    })

    if (result.isErr()) {
      console.error('Error:', JSON.stringify(result.error, null, 2))
      throw new Error(`Failed to process application: ${result.error.message}`)
    }

    expect(result.isOk()).toBe(true)

    if (result.isOk()) {
      expect(result.value.status).toBe(CorporateApplicationStatus.APPROVED)
      expect(result.value.kycStatus).toBe(KycStatus.DD)

      // Check if organization was created
      const organization = await prisma.organization.findFirst({
        where: { users: { some: { id: testApplication.userId } } },
      })
      expect(organization).not.toBeNull()
      if (organization) {
        expect(organization.name).toBe('Test Organization Name')
        expect(organization.nzbn).toBe('9429046123403')
      }

      // Check if email was sent
      expect(sendEmailSpy).toHaveBeenCalledWith({
        email: testApplication.user.email,
        templateId: APPROVED_APPLICATION,
        templateModel: {
          orgName: 'Test Organization Name',
          userName: testApplication.user.firstName,
        },
      })
    }

    sendEmailSpy.mockRestore()
  })

  it('should update KYC status for an approved application without creating a new organization', async () => {
    // First, let's set up an approved application with an existing organization
    // Generate a unique ID for this test run
    const uniqueId = uuidv4()

    // First, let's set up an approved application with an existing organization
    const testUser = await prisma.user.create({
      data: {
        id: uniqueId,
        email: `test-${uniqueId}@example.com`,
        firstName: 'Test',
        lastName: 'User',
      },
    })

    const testOrganization = await prisma.organization.create({
      data: {
        name: `Test Organization ${uniqueId}`,
        nzbn: `9429046123403${uniqueId.slice(0, 4)}`,
        type: OrganizationType.LIMITED_LIABILITY_COMPANY,
        users: {
          connect: { id: testUser.id },
        },
      },
    })

    const testApplication = await prisma.corporateApplication.create({
      data: {
        status: CorporateApplicationStatus.APPROVED,
        kycStatus: KycStatus.DD,
        name: `Test Organization ${uniqueId}`,
        nzbn: `9429046123403${uniqueId.slice(0, 4)}`,
        userId: testUser.id,
        organizationId: testOrganization.id,
      },
    })

    // Now, let's process the application with a new KYC status
    const result = await processCorporateApplication({
      corporateApplicationId: testApplication.id,
      status: CorporateApplicationStatus.APPROVED, // Same status
      kycStatus: KycStatus.EDD, // New KYC status
    })

    expect(result.isOk()).toBe(true)

    if (result.isOk()) {
      // Check that the application status remains APPROVED
      expect(result.value.status).toBe(CorporateApplicationStatus.APPROVED)

      // Check that the KYC status has been updated
      expect(result.value.kycStatus).toBe(KycStatus.EDD)

      // Fetch the updated application from the database
      const updatedApplication = await prisma.corporateApplication.findUnique({
        where: { id: testApplication.id },
        include: { organization: true },
      })

      // Ensure the application is still associated with the same organization
      expect(updatedApplication?.organizationId).toBe(testOrganization.id)

      // Check that no new organization was created
      const organizationCount = await prisma.organization.count({
        where: { users: { some: { id: testUser.id } } },
      })
      expect(organizationCount).toBe(1)
    }
  })

  it('should not create an organization when application is not approved', async () => {
    const testApplication = await prisma.corporateApplication.findFirst({
      where: { id: '09282fcd-95e7-4cb1-ab40-b8f83998a338' },
      include: { user: true, organization: true },
    })

    if (!testApplication) {
      throw new Error('Test application not found. Check your seed data.')
    }

    // If the application already has an organization, delete it for this test
    if (testApplication.organization) {
      await prisma.organization.delete({
        where: { id: testApplication.organization.id },
      })
    }

    // Reset the application status to REVIEWING
    await prisma.corporateApplication.update({
      where: { id: testApplication.id },
      data: {
        status: CorporateApplicationStatus.REVIEWING,
        kycStatus: KycStatus.NKYC,
        organizationId: null, // Ensure there's no organization associated
      },
    })

    const result = await processCorporateApplication({
      corporateApplicationId: testApplication.id,
      status: CorporateApplicationStatus.DECLINED,
      kycStatus: KycStatus.FKYC,
    })

    if (result.isErr()) {
      console.error('Error:', JSON.stringify(result.error, null, 2))
      throw new Error(`Failed to process application: ${result.error.message}`)
    }

    expect(result.isOk()).toBe(true)

    if (result.isOk()) {
      expect(result.value.status).toBe(CorporateApplicationStatus.DECLINED)
      expect(result.value.kycStatus).toBe(KycStatus.FKYC)

      // Check that no organization was created
      const organization = await prisma.organization.findFirst({
        where: { users: { some: { id: testApplication.userId } } },
      })

      if (organization) {
        console.error(
          'Unexpected organization created:',
          JSON.stringify(organization, null, 2)
        )
      }

      expect(organization).toBeNull()

      // Double-check the application status
      const updatedApplication = await prisma.corporateApplication.findUnique({
        where: { id: testApplication.id },
        include: { organization: true },
      })
      console.log(
        'Updated application:',
        JSON.stringify(updatedApplication, null, 2)
      )
    }
  })
  it('should return an error when the application is not found', async () => {
    const result = await processCorporateApplication({
      corporateApplicationId: 'non-existent-id',
      status: CorporateApplicationStatus.APPROVED,
      kycStatus: KycStatus.DD,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error).toEqual(
        expect.objectContaining({
          code: 'DB-101',
          message: 'Corporate application not found',
        })
      )
    }
  })
})
