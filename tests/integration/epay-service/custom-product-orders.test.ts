import { beforeAll, describe, expect, it } from 'vitest'

import { ProductStatus } from '@prisma/client'
import { getAllCustomProductOrdersForReview } from 'services/epay.service'
import { seedCustomProductOrder } from '../db/custom-product-order'

describe('custom product orders for reviews', () => {
  beforeAll(async () => {
    await seedCustomProductOrder()
  })

  it('should sort PENDING orders first', async () => {
    const result = await getAllCustomProductOrdersForReview({
      page: 1,
      pageSize: 10,
    })

    result.match(
      ({ items }) => {
        expect(items[0]?.status).toEqual(ProductStatus.PENDING)
        expect(items[1]?.status).toEqual(ProductStatus.PENDING)
        expect(items[items.length - 1]?.status).toEqual(ProductStatus.SENT)
      },
      () => {
        throw new Error('should not be an error')
      }
    )
  })

  it('should sort by descending date', async () => {
    const result = await getAllCustomProductOrdersForReview({
      page: 1,
      pageSize: 10,
    })

    result.match(
      ({ items, count }) => {
        expect(items.length).toBe(count)

        expect(items[0].createdAt.getTime()).toBeGreaterThanOrEqual(
          items[1].createdAt.getTime()
        )
        expect(items[1]?.createdAt.getTime()).toBeGreaterThanOrEqual(
          items[2].createdAt.getTime()
        )
      },
      () => {
        throw new Error('should not be an error')
      }
    )
  })
})
