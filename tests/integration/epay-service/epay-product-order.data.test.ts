import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { epayGetProcessedCardsByOrderNumber } from 'epay-data/epay-product-order.data'
import { prisma } from 'utils/prisma'
import { v4 as uuidv4 } from 'uuid'
import { OrderStatus, DeliveryMethod } from '@prisma/client'
import { NEXT_CHAPTER_ORG_ID } from '../../../prisma/seed-test'

describe('epayGetProcessedCardsByOrderNumber', () => {
  const mockOrderNumber = 'TEST-ORDER-123'
  const mockOrderId = uuidv4()

  // Using the seeded Prezzy Virtual product
  const PREZZY_VIRTUAL_PRODUCT_ID = 'b1750180-2dfb-43f8-af00-a0d45ccd8fd0'
  const TEST_USER_ID = '64511261-1a28-4a4f-b9ed-ff7bee40d5d2' // <EMAIL>

  beforeAll(async () => {
    // Create test data in the database using seeded product
    await prisma.productOrder.create({
      data: {
        id: mockOrderId,
        orderNumber: mockOrderNumber,
        orderTotal: 2000,
        orderStatus: OrderStatus.COMPLETED,
        organization: {
          connect: {
            id: NEXT_CHAPTER_ORG_ID,
          },
        },
        user: {
          connect: {
            id: TEST_USER_ID,
          },
        },
        productOrderItems: {
          create: {
            id: uuidv4(),
            productId: PREZZY_VIRTUAL_PRODUCT_ID,
            productCode: 1,
            deliveryMethod: DeliveryMethod.EMAIL,
            unitPrice: 1000,
            quantity: 2,
            recipientName: 'Test Recipient',
            recipientEmail: '<EMAIL>',
            loadingFee: 100,
            loadingFeeDiscount: 0,
            digitalFee: 50,
            digitalFeeDiscount: 0,
            cardItems: {
              create: [
                {
                  id: uuidv4(),
                  externalCardReferenceNumber: 'CARD-REF-1',
                  fundsLoaded: true,
                },
                {
                  id: uuidv4(),
                  externalCardReferenceNumber: 'CARD-REF-2',
                  fundsLoaded: true,
                },
              ],
            },
          },
        },
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    await prisma.productOrderItemCard.deleteMany({
      where: {
        productOrderItem: {
          productOrder: {
            orderNumber: mockOrderNumber,
          },
        },
      },
    })
    await prisma.productOrderItem.deleteMany({
      where: {
        productOrder: {
          orderNumber: mockOrderNumber,
        },
      },
    })
    await prisma.productOrder.deleteMany({
      where: {
        orderNumber: mockOrderNumber,
      },
    })
  })

  it('should successfully retrieve processed cards for an order', async () => {
    const result = await epayGetProcessedCardsByOrderNumber({
      orderNumber: mockOrderNumber,
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      const data = result.value
      if (!data) throw new Error('Data should not be null')

      expect(data.orderNumber).toBe(mockOrderNumber)
      expect(data.id).toBe(mockOrderId)
      expect(data.productOrderItems).toHaveLength(1)
      expect(data.productOrderItems[0].cardItems).toHaveLength(2)
      expect(
        data.productOrderItems[0].cardItems[0].externalCardReferenceNumber
      ).toBe('CARD-REF-1')
      expect(
        data.productOrderItems[0].cardItems[1].externalCardReferenceNumber
      ).toBe('CARD-REF-2')
    }
  })

  it('should return error when order is not found', async () => {
    const result = await epayGetProcessedCardsByOrderNumber({
      orderNumber: 'NON-EXISTENT-ORDER',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe('DB_ITEM_NOT_FOUND')
      expect(result.error.message).toBe(
        'Error getting processed cards for order [NON-EXISTENT-ORDER]'
      )
    }
  })
})
