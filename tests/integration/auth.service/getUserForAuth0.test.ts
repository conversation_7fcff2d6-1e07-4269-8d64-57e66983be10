import { getUserForAuth0 } from 'services/auth.service'
import { ERRORS } from 'utils/error'
import { describe, expect, it } from 'vitest'

describe('getUserForAuth0', () => {
  it('should return the correct data when user exists in DB', async () => {
    const userId = '64511261-1a28-4a4f-b9ed-ff7bee40d5d2'
    const userEmail = '<EMAIL>'
    const epayAppId = process.env.CORPORATE_PORTAL_APP_ID!

    const res = await getUserForAuth0({
      userId,
      userEmail,
      epayAppId,
      isLiveAgent: false,
    })
    expect(res.isOk()).toBeTruthy()

    const user = res._unsafeUnwrap()
    expect(user).toHaveProperty('userId', userId)
    expect(user).toHaveProperty('userEmail', userEmail)
    expect(user).toHaveProperty('userRole', 'ORG_ADMIN')
    expect(user).toHaveProperty('orgId', '6c16a991-e43b-41bd-9e6a-a8120cb311e5')
    expect(user).toHaveProperty('orgName', 'Next Chapter Studio Limited')
    expect(user).toHaveProperty('isLiveAgent', false)
    expect(user).toHaveProperty('expiresAt', null)
  })

  it('should create a new user when user does NOT exist in DB AND came from corporate portal', async () => {
    const userId = 'new-user-123456'
    const userEmail = '<EMAIL>'
    const epayAppId = process.env.CORPORATE_PORTAL_APP_ID!

    const res = await getUserForAuth0({
      userId,
      userEmail,
      epayAppId,
      isLiveAgent: false,
    })
    expect(res.isOk()).toBeTruthy()

    const user = res._unsafeUnwrap()
    expect(user).toHaveProperty('userId', userId)
    expect(user).toHaveProperty('userEmail', userEmail)
    expect(user).toHaveProperty('userRole', 'ORG_ADMIN')
    expect(user).toHaveProperty('orgId', null)
    expect(user).toHaveProperty('orgName', null)
    expect(user).toHaveProperty('isLiveAgent', false)
    expect(user).toHaveProperty('expiresAt', null)
  })

  it('should throw UNAUTHORIZED error when user does not exist in DB AND came from non-corporate portal', async () => {
    const userId = 'new-user-7889'
    const userEmail = '<EMAIL>'
    const epayAppId = 'not-corp-portal-id'

    const res = await getUserForAuth0({
      userId,
      userEmail,
      epayAppId,
      isLiveAgent: false,
    })
    expect(res.isErr()).toBeTruthy()

    const error = res._unsafeUnwrapErr()
    expect(error).toHaveProperty('code', ERRORS.UNAUTHORIZED.code)
  })

  it('should return target org data for super admin users', async () => {
    const userId = '64511261-1a28-4a4f-b9ed-ff7bee40d5d2'
    const userEmail = '<EMAIL>'
    const epayAppId = process.env.CORPORATE_PORTAL_APP_ID!

    const expiresAtIso = new Date(
      new Date().getTime() + 5 * 60000
    ).toISOString()

    const res = await getUserForAuth0({
      userId,
      userEmail,
      epayAppId,
      isLiveAgent: true,
      targetOrgId: 'a18060d9-5918-4514-bfb3-ff377f6d42e5',
      expiresAt: expiresAtIso,
    })
    expect(res.isOk()).toBeTruthy()

    const user = res._unsafeUnwrap()
    expect(user).toHaveProperty('userId', userId)
    expect(user).toHaveProperty('userEmail', userEmail)
    expect(user).toHaveProperty('userRole', 'ORG_ADMIN')
    expect(user).toHaveProperty('orgId', 'a18060d9-5918-4514-bfb3-ff377f6d42e5')
    expect(user).toHaveProperty('orgName', 'Test org #1')
    expect(user).toHaveProperty('isLiveAgent', true)
    expect(user).toHaveProperty('expiresAt', expiresAtIso)
  })

  it('should return regular user data for super admin users that have an expired session', async () => {
    const userId = '64511261-1a28-4a4f-b9ed-ff7bee40d5d2'
    const userEmail = '<EMAIL>'
    const epayAppId = process.env.CORPORATE_PORTAL_APP_ID!

    const expiresAtIso = new Date(
      new Date().getTime() - 5 * 60000
    ).toISOString()

    const res = await getUserForAuth0({
      userId,
      userEmail,
      epayAppId,
      isLiveAgent: true,
      targetOrgId: 'a18060d9-5918-4514-bfb3-ff377f6d42e5',
      expiresAt: expiresAtIso,
    })
    expect(res.isOk()).toBeTruthy()

    const user = res._unsafeUnwrap()
    expect(user).toHaveProperty('userId', userId)
    expect(user).toHaveProperty('userEmail', userEmail)
    expect(user).toHaveProperty('userRole', 'ORG_ADMIN')
    expect(user).toHaveProperty('orgId', '6c16a991-e43b-41bd-9e6a-a8120cb311e5')
    expect(user).toHaveProperty('orgName', 'Next Chapter Studio Limited')
    expect(user).toHaveProperty('isLiveAgent', false)
    expect(user).toHaveProperty('expiresAt', null)
  })
})
