import { describe, test, expect } from 'vitest'

import { getUser } from '../../src/test-utils/factory'

import { prisma } from 'utils/prisma'

import { onboard } from '../../src/services/onboarding.service'

describe('onboarding', () => {
  // test('should return application id when new user', async () => {
  //   const user = getUser()
  //   await prisma.user.create({
  //     data: user,
  //   })

  //   const result = await onboard({
  //     userId: user.id,
  //   })

  //   expect(result.isOk()).toBeTruthy()
  //   expect(result._unsafeUnwrap()).toEqual({
  //     id: expect.stringMatching(/.*/), // test that id exist as a string
  //   })
  // })

  test('should fail when user has an application', async () => {
    const user = getUser()
    await prisma.user.create({
      data: user,
    })
    await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })
    const result = await onboard({ userId: user.id })

    expect(result.isErr()).toBeTruthy()
  })

  test('should fail when user has organization', async () => {
    const user = getUser()
    const org = await prisma.organization.create({
      data: {
        name: 'test',
        nzbn: '1234',
        type: 'LIMITED_LIABILITY_COMPANY',
      },
    })
    await prisma.user.create({
      data: {
        ...user,
        organizationId: org.id,
      },
    })

    const res = await onboard({ userId: user.id })

    expect(res.isErr()).toBeTruthy()
  })
})
