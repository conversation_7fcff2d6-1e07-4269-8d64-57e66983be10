import axios from 'axios'
import { describe, it, expect, vi, beforeAll } from 'vitest'
import { OrderStatus } from '@prisma/client'
import { activateCards } from 'services/card.service'
import { ERRORS, ErrorResponse } from 'utils/error'
import { prisma } from 'utils/prisma'
import { resetCardsForTest } from '../db/card-service-reset'

describe.skip('activateCards', () => {
  beforeAll(async () => {
    await resetCardsForTest()
  })

  it('should update blocked to false for all cards', async () => {
    const spyOnAxiosPost = vi
      .spyOn(axios, 'post')
      .mockResolvedValue({ status: 200, data: 'crn-111' })

    const res = await activateCards({
      cardReferenceIds: ['crn-111'],
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    }

    expect(res.value.length).toEqual(1)
    expect(spyOnAxiosPost).toHaveBeenCalledOnce()
  })

  it('should update cards only for the org if its been provided', async () => {
    const res = await activateCards({
      cardReferenceIds: ['crn-111'],
      orgId: '1234',
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    }

    expect(res.value.length).toEqual(0)
  })

  it('should update orderStatus to ACTIVATED when all cards are activated', async () => {
    const spyOnAxiosPost = vi
      .spyOn(axios, 'post')
      .mockResolvedValueOnce({ status: 200, data: 'crn-111' })
      .mockResolvedValueOnce({ status: 200, data: 'crn-222' })

    const res = await activateCards({
      cardReferenceIds: ['crn-111', 'crn-222'],
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    }

    const productOrder = await prisma.productOrder.findUnique({
      where: { id: 'CARD_SERVICE_ORDER_1' },
    })

    expect(spyOnAxiosPost).toHaveBeenCalledTimes(2)
    expect(res.value.length).toEqual(2)
    expect(productOrder?.orderStatus).toEqual(OrderStatus.ACTIVATED)
  })

  it('should return CARD_WITHOUT_LOCK_CODE error if the card does not have a lock code', async () => {
    const res = await activateCards({
      cardReferenceIds: ['crn-333'],
    })

    expect(res.isErr()).toBeTruthy()

    const error = res._unsafeUnwrapErr()

    if (Array.isArray(error)) {
      expect(error[0] as ErrorResponse).toHaveProperty(
        'code',
        ERRORS.CARD_WITHOUT_LOCK_CODE.code
      )
    } else {
      throw new Error('should be an array')
    }
  })
})
