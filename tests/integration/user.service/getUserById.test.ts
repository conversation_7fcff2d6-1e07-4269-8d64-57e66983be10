import { beforeAll, describe, expect, it } from 'vitest'

import { TEST_ORG_1_ID, TEST_ORG_1_USER } from '../../../prisma/seed-test'
import { getUserById } from 'services/user.service'

describe('getUserById', () => {
  beforeAll(async () => {
    return
  })

  it('should get a user', async () => {
    const result = await getUserById({
      id: TEST_ORG_1_USER,
    })

    if (result.isErr()) {
      console.log(result.error, 'should create GetUserById...ERROR')
      throw new Error('should not be an error')
    } else {
      const actual = result?.value

      expect(actual).toHaveProperty('organization.id', TEST_ORG_1_ID)
    }
  })
})
