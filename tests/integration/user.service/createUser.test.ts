import axios from 'axios'
import { SpyInstance, beforeEach, describe, expect, it, vi } from 'vitest'
import { createUser } from 'services/user.service'

const spyOnAxiosPost = (userId: string) => {
  return (
    vi
      .spyOn(axios, 'post')
      // createAuth0User
      .mockResolvedValueOnce({
        status: 200,
        data: { user_id: userId },
      })
      // createChangePasswordTicket
      .mockResolvedValueOnce({
        status: 200,
        data: { ticket: 'ticket-id' },
      })
      // sendEmailWithTemplate
      .mockResolvedValue({})
  )
}

describe('createUser', () => {
  beforeEach(() => {
    vi.spyOn(axios, 'get').mockResolvedValueOnce({})
  })

  it('should create a new user', async () => {
    const axiosPostSpy = spyOnAxiosPost('user-id')

    const result = await createUser({
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      role: 'ORG_ADMIN',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      branch: 'main',
    })

    const res = result._unsafeUnwrap()
    expect(res).toHaveProperty('id', 'user-id')
    expect(res).toHaveProperty(
      'organizationId',
      '6c16a991-e43b-41bd-9e6a-a8120cb311e5'
    )
    expect(res).toHaveProperty('role', 'ORG_ADMIN')
    expect(res).toHaveProperty('email', '<EMAIL>')
    expect(res).toHaveProperty('branch', 'main')
    expect(axiosPostSpy).toHaveBeenCalledTimes(3)
  })

  it('should create a new user with lowercase email', async () => {
    const axiosPostSpy = spyOnAxiosPost('user-id2')

    const result = await createUser({
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      role: 'ORG_ADMIN',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      branch: 'main',
    })

    const res = result._unsafeUnwrap()
    expect(res).toHaveProperty('id', 'user-id2')
    expect(res).toHaveProperty('email', '<EMAIL>')
    expect(axiosPostSpy).toHaveBeenNthCalledWith(
      1,
      expect.stringContaining('/users'),
      expect.stringContaining('<EMAIL>'),
      expect.any(Object)
    )
    expect(axiosPostSpy).toHaveBeenNthCalledWith(
      2,
      expect.stringContaining('/tickets/password-change'),
      expect.stringContaining('<EMAIL>'),
      expect.any(Object)
    )
  })
})
