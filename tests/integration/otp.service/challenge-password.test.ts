import { assert, beforeEach, describe, expect, it, vi } from 'vitest'

import {
  OTP_FOR_MARY,
  USER_HARRY_DOES_NOT_HAVE_OTP,
  USER_BOB_HAS_EXPIRED_OTP,
  resetOtpForTests,
  USER_MARY_HAS_OTP_THAT_IS_HAS_BEEN_USED,
} from '../db/otp-reset'
import { sendChallenge, verifyChallenge } from 'services/otp.service'
import { ERRORS } from 'utils/error'
import * as aws from 'utils/aws'
import { ok } from 'neverthrow'
import { prisma } from 'utils/prisma'
import {
  sendSMSChallenge,
  sendWhatsAppChallenge,
} from 'services/twilio.service'

describe('verifyChallenge', () => {
  beforeEach(async () => {
    await resetOtpForTests()
  })

  it('should return error when one does not exist', async () => {
    verifyChallenge({
      userId: USER_HARRY_DOES_NOT_HAVE_OTP,
      otpSubmission: 'does not exist',
    }).match(
      () => {
        assert.fail('should not be a result')
      },
      (error) => {
        if ('code' in error) {
          expect(error.code).toEqual(ERRORS.NOT_FOUND.code)
        } else {
          assert.fail('should have code')
        }
      }
    )
  })

  it('should return error when challenge password older than 10 minutes', async () => {
    verifyChallenge({
      userId: USER_BOB_HAS_EXPIRED_OTP,
      otpSubmission: OTP_FOR_MARY,
    }).match(
      () => {
        assert.fail('should not be a result')
      },
      (error) => {
        if ('code' in error) {
          expect(error.code).toEqual(ERRORS.NOT_FOUND.code)
        } else {
          assert.fail('should have code')
        }
      }
    )
  })

  it('should return error when challenge password already used', async () => {
    verifyChallenge({
      userId: USER_MARY_HAS_OTP_THAT_IS_HAS_BEEN_USED,
      otpSubmission: OTP_FOR_MARY,
    }).match(
      () => {
        assert(false, 'should not be a result')
      },
      (error) => {
        if ('code' in error) {
          expect(error.code).toEqual(ERRORS.NOT_FOUND.code)
        } else {
          assert(false, 'should have code')
        }
      }
    )
  })

  it('should log failed attempts', async () => {
    vi.spyOn(aws, 'sendTextMessage').mockResolvedValue(ok({ sent: true }))
    await sendChallenge({
      userId: USER_HARRY_DOES_NOT_HAVE_OTP,
      phoneNumber: '44445555',
    })

    await verifyChallenge({
      userId: USER_HARRY_DOES_NOT_HAVE_OTP,
      otpSubmission: '111',
    })

    await verifyChallenge({
      userId: USER_HARRY_DOES_NOT_HAVE_OTP,
      otpSubmission: '111',
    })

    await verifyChallenge({
      userId: USER_HARRY_DOES_NOT_HAVE_OTP,
      otpSubmission: '111',
    })

    const result = await prisma.oneTimePassword.findFirst({
      where: { userId: USER_HARRY_DOES_NOT_HAVE_OTP },
    })

    assert.strictEqual(result?.failedAttempts, 3)
  })
})

describe('sendChallenge', () => {
  beforeEach(async () => {
    await resetOtpForTests()
  })

  it('should create a new otp when one does not exist', async () => {
    sendChallenge({
      userId: USER_HARRY_DOES_NOT_HAVE_OTP,
      phoneNumber: '5566778899',
    }).match(
      (result) => {
        expect(result.sent).toBeTruthy()
      },
      (error) => {
        console.log(error)
        assert.fail('should not be an error')
      }
    )
  })

  it('should create a new otp when one does exist', async () => {
    sendChallenge({
      userId: USER_BOB_HAS_EXPIRED_OTP,
      phoneNumber: '44445555',
    }).match(
      (result) => {
        expect(result.sent).toBeTruthy()
      },
      () => {
        assert.fail('should not be an error')
      }
    )
  })

  it('should error if OTP does not exist', async () => {
    verifyChallenge({
      userId: USER_HARRY_DOES_NOT_HAVE_OTP,
      otpSubmission: '111',
    }).match(
      () => {
        assert(false, 'should not be a result')
      },
      (error) => {
        if ('code' in error) {
          expect(error.code).toEqual(ERRORS.NOT_FOUND.code)
        } else {
          assert(false, 'should have code')
        }
      }
    )
  })
})
