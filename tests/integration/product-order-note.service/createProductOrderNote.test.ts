import { describe, expect, it } from 'vitest'
import { createProductOrderNote } from 'services/product-order-note.service'
import { ProductOrderNoteStatus } from '@prisma/client'

describe('createProductOrderNote', () => {
  it('should create a note', async () => {
    const res = await createProductOrderNote({
      productOrderId: '3785e936-4cf6-493f-848f-1c52c47371b1',
      title: 'First note',
      message: 'First message',
      status: ProductOrderNoteStatus.CANCELLED,
      userId: '6599e51f50c57b82204a4cc9',
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    } else {
      expect(res.value).toHaveProperty('id')
      expect(res.value).toHaveProperty('title', 'First note')
      expect(res.value).toHaveProperty('message', 'First message')
      expect(res.value).toHaveProperty(
        'status',
        ProductOrderNoteStatus.CANCELLED
      )
      expect(res.value).toHaveProperty('createdAt')
      expect(res.value).toHaveProperty('updatedAt')
      expect(res.value).toHaveProperty('userId', '6599e51f50c57b82204a4cc9')
      expect(res.value).toHaveProperty(
        'productOrderId',
        '3785e936-4cf6-493f-848f-1c52c47371b1'
      )
    }
  })

  it('should create a note with GENERAL status by default', async () => {
    const res = await createProductOrderNote({
      productOrderId: '3785e936-4cf6-493f-848f-1c52c47371b1',
      title: 'Lorem ipsum',
      message:
        'Aenean nibh elit, ullamcorper vitae leo id, tristique vulputate felis',
      userId: '6599e51f50c57b82204a4cc9',
    })

    if (res.isErr()) {
      throw new Error(JSON.stringify(res.error))
    } else {
      expect(res.value).toHaveProperty('id')
      expect(res.value).toHaveProperty('title', 'Lorem ipsum')
      expect(res.value).toHaveProperty(
        'message',
        'Aenean nibh elit, ullamcorper vitae leo id, tristique vulputate felis'
      )
      expect(res.value).toHaveProperty('status', ProductOrderNoteStatus.GENERAL)
      expect(res.value).toHaveProperty('createdAt')
      expect(res.value).toHaveProperty('updatedAt')
      expect(res.value).toHaveProperty('userId', '6599e51f50c57b82204a4cc9')
      expect(res.value).toHaveProperty(
        'productOrderId',
        '3785e936-4cf6-493f-848f-1c52c47371b1'
      )
    }
  })
})
