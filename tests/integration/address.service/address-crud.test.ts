import { beforeAll, describe, expect, it } from 'vitest'
import { resetAddresses } from '../db/address-reset'
import {
  createAddress,
  deleteAddressById,
  getAddressesByUserId,
  updateAddressById,
} from 'services/address.service'

describe('address.service', () => {
  beforeAll(async () => {
    await resetAddresses()
  })

  describe('getAddressesByUserId', () => {
    it('should return an array of addresses by user ID', async () => {
      const res = await getAddressesByUserId({
        page: 1,
        pageSize: 2,
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })
      const { items, count } = res._unsafeUnwrap()
      expect(items.length).toBe(2)
      expect(count).toBe(2)
    })
    it('should return filtered results correctly', async () => {
      const res = await getAddressesByUserId({
        page: 1,
        pageSize: 2,
        query: 'Hello',
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })
      const { items, count } = res._unsafeUnwrap()
      expect(items.length).toBe(1)
      expect(count).toBe(1)
    })
    it('should return paginated results correctly', async () => {
      const res = await getAddressesByUserId({
        page: 1,
        pageSize: 1,
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })
      const { items, count } = res._unsafeUnwrap()
      expect(items.length).toBe(1)
      expect(count).toBe(2)
    })
  })

  describe('createAddress', () => {
    it('should add an entry in the database', async () => {
      const res = await createAddress({
        address: '3 Fiddy St',
        city: 'Auckland',
        suburb: 'Takapuna',
        postCode: '0616',
        isDefault: false,
        country: 'NZ',
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })

      const created = res._unsafeUnwrap()
      expect(created).toHaveProperty('id')
      expect(created).toHaveProperty('address', '3 Fiddy St')
      expect(created).toHaveProperty('city', 'Auckland')
      expect(created).toHaveProperty('suburb', 'Takapuna')
      expect(created).toHaveProperty('postCode', '0616')
      expect(created).toHaveProperty('isDefault', false)
      expect(created).toHaveProperty('country', 'NZ')
      expect(created).toHaveProperty(
        'userId',
        '64511261-1a28-4a4f-b9ed-ff7bee40d5d2'
      )
    })
    it("should set isDefault to false for user's other addresses when new address is chosen as default", async () => {
      await createAddress({
        address: '4 Head Ave',
        city: 'Rotorua',
        suburb: 'Redwoods',
        postCode: '0101',
        isDefault: true,
        country: 'NZ',
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })

      const getRes = await getAddressesByUserId({
        page: 1,
        pageSize: 2,
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })

      const { items } = getRes._unsafeUnwrap()
      const filtered = items.filter((a) => a.isDefault)
      expect(filtered.length).toBe(1)
      expect(filtered[0]).toHaveProperty('address', '4 Head Ave')
    })
  })

  describe('updateAddressById', () => {
    it('should update address in database', async () => {
      const res = await updateAddressById({
        id: 'address1',
        address: '50 Hello World',
        city: 'Auckland',
        suburb: 'Milford',
        postCode: '1111',
        isDefault: true,
        country: 'NZ',
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })
      const updated = res._unsafeUnwrap()
      expect(updated).toHaveProperty('id', 'address1')
      expect(updated).toHaveProperty('address', '50 Hello World')
    })
    it("should set isDefault to false for user's other addresses when updated address is chosen as default", async () => {
      await updateAddressById({
        id: 'address2',
        address: '66 Ola St',
        city: 'Auckland',
        suburb: 'Milford',
        postCode: '1111',
        isDefault: true,
        country: 'NZ',
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })

      const getRes = await getAddressesByUserId({
        page: 1,
        pageSize: 2,
        userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      })

      const { items } = getRes._unsafeUnwrap()
      const filtered = items.filter((a) => a.isDefault)
      expect(filtered.length).toBe(1)
      expect(filtered[0]).toHaveProperty('id', 'address2')
    })
  })

  describe('deleteAddressById', () => {
    it('should delete the address from the db', async () => {
      const res = await deleteAddressById('address2')
      expect(res._unsafeUnwrap()).toHaveProperty('id', 'address2')
    })
  })
})
