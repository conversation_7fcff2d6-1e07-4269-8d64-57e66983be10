import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Director, User } from '@prisma/client'

import { describe, test, expect } from 'vitest'

import { getBeneficialOwner, getDirector, getUser } from 'test-utils/factory'

import { prisma } from 'utils/prisma'

import {
  createCorporateApplication,
  updateApplicationWithCompanyDetails,
  updateApplicant,
  updateDirectors,
  updateBeneficialOwners,
  deleteDirector,
  deleteBeneficialOwner,
} from 'services/corporate-application.service'
import { ERRORS } from 'utils/error'

describe('corporate application', () => {
  test('create application', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })

    const applicationRes = await createCorporateApplication(user.id)

    expect(applicationRes.isOk()).toBeTruthy()
    expect(applicationRes._unsafeUnwrap()).toHaveProperty('userId', user.id)
  })

  test('update application', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })
    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    const data = {
      address: 'random address',
      country: 'NZ',
      name: 'test co',
    }

    const updateApplicationRes = await updateApplicationWithCompanyDetails({
      id: application.id,
      userId: user.id,
      data,
    })

    expect(updateApplicationRes.isOk()).toBeTruthy()
    const updatedApplication = updateApplicationRes._unsafeUnwrap()
    expect(updatedApplication).toHaveProperty('id', application.id)
    expect(updatedApplication).toHaveProperty('address', data.address)
    expect(updatedApplication).toHaveProperty('country', data.country)
    expect(updatedApplication).toHaveProperty('name', data.name)
  })

  test.skip("failing to update application if you aren't the creator", async () => {
    const user1 = getUser()
    const user2 = getUser()

    await prisma.user.create({ data: user1 })
    await prisma.user.create({ data: user2 })
    const application = await prisma.corporateApplication.create({
      data: {
        userId: user1.id,
      },
    })

    const data = {
      address: 'random address',
      country: 'NZ',
      name: 'test co',
    }

    const updateApplicationRes = await updateApplicationWithCompanyDetails({
      id: application.id,
      userId: user2.id,
      data,
    })

    expect(updateApplicationRes.isErr()).toBeTruthy()
    expect(updateApplicationRes._unsafeUnwrapErr()).toBe('NOT_FOUND')
  })

  test('update applicant', async () => {
    const user = getUser()

    await prisma.user.create({ data: user })

    const data: Partial<User> = {
      firstName: 'Clark',
      lastName: 'Kent',
    }

    const updateApplicantRes = await updateApplicant({ id: user.id, data })

    expect(updateApplicantRes.isOk()).toBeTruthy()
    expect(updateApplicantRes._unsafeUnwrap()).toHaveProperty(
      'firstName',
      data.firstName
    )
    expect(updateApplicantRes._unsafeUnwrap()).toHaveProperty(
      'lastName',
      data.lastName
    )
  })

  // directors
  test('create new directors', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })

    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    const director1 = getDirector()
    const director2 = getDirector()
    const updateDirectorsRes = await updateDirectors({
      userId: user.id,
      corporateApplicationId: application.id,
      directors: [director1, director2],
    })

    expect(updateDirectorsRes.isOk()).toBeTruthy()

    const directors = updateDirectorsRes._unsafeUnwrap()
    expect(
      directors.findIndex((director) => {
        return director.identificationNbr === director1.identificationNbr
      })
    ).not.toBe(-1)
    expect(
      directors.findIndex((director) => {
        return director.identificationNbr === director2.identificationNbr
      })
    ).not.toBe(-1)
  })

  test('create new, update directors, and keeps the order they are sent', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })

    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    const director1 = getDirector()
    const director2 = getDirector()
    const createDirectorsRes = await updateDirectors({
      userId: user.id,
      corporateApplicationId: application.id,
      directors: [director1, director2],
    })

    const createdDirectors = createDirectorsRes
      ._unsafeUnwrap()
      .map<Partial<Director>>((director) => {
        return {
          id: director.id,
          address: 'something',
        }
      })

    const director3 = getDirector()

    const createAndUpdateDirectorsRes = await updateDirectors({
      userId: user.id,
      corporateApplicationId: application.id,
      directors: [director3, ...createdDirectors],
    })

    expect(createAndUpdateDirectorsRes.isOk()).toBeTruthy()

    const directors = createAndUpdateDirectorsRes._unsafeUnwrap()
    expect(
      directors.findIndex((director) => {
        return (
          director.identificationNbr === director1.identificationNbr &&
          director.address === 'something'
        )
      })
    ).not.toBe(-1)
    expect(
      directors.findIndex((director) => {
        return (
          director.identificationNbr === director2.identificationNbr &&
          director.address === 'something'
        )
      })
    ).not.toBe(-1)
    expect(
      directors.findIndex((director) => {
        return director.identificationNbr === director3.identificationNbr
      })
    ).not.toBe(-1)
  })

  test.skip("failing to update directors that isn't connected to your application", async () => {
    const user1 = getUser()
    const user2 = getUser()
    await prisma.user.create({ data: user1 })
    await prisma.user.create({ data: user2 })

    const application1 = await prisma.corporateApplication.create({
      data: {
        userId: user1.id,
      },
    })

    const director1 = getDirector()
    const director2 = getDirector()
    const createDirectorsRes = await updateDirectors({
      userId: user1.id,
      corporateApplicationId: application1.id,
      directors: [director1, director2],
    })

    const createdDirectors = createDirectorsRes
      ._unsafeUnwrap()
      .map<Partial<Director>>((director) => {
        return {
          id: director.id,
          address: 'something',
        }
      })

    const application2 = await prisma.corporateApplication.create({
      data: {
        userId: user1.id,
      },
    })

    const createAndUpdateDirectorsRes = await updateDirectors({
      userId: user2.id,
      corporateApplicationId: application2.id,
      directors: createdDirectors,
    })

    expect(createAndUpdateDirectorsRes.isErr()).toBeTruthy()
    expect(createAndUpdateDirectorsRes._unsafeUnwrapErr()).toBe('NOT_FOUND')
  })

  test('delete director', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })
    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    const director1 = await prisma.director.create({
      data: {
        corporateApplicationId: application.id,
        ...getDirector(),
      },
    })
    const director2 = await prisma.director.create({
      data: {
        corporateApplicationId: application.id,
        ...getDirector(),
      },
    })

    const deleteDirectorRes = await deleteDirector({
      userId: user.id,
      corporateApplicationId: application.id,
      directorId: director1.id,
    })

    expect(deleteDirectorRes.isOk()).toBeTruthy()

    const applicationAfter = await prisma.corporateApplication.findFirst({
      where: {
        id: application.id,
      },
      include: {
        directors: true,
      },
    })

    expect(applicationAfter?.directors.length).toBe(1)
    expect(applicationAfter?.directors[0]).toEqual(director2)
  })

  test('failing to delete director created by someone else', async () => {
    const user1 = getUser()
    await prisma.user.create({ data: user1 })
    const application1 = await prisma.corporateApplication.create({
      data: {
        userId: user1.id,
      },
    })

    const director1 = await prisma.director.create({
      data: {
        corporateApplicationId: application1.id,
        ...getDirector(),
      },
    })
    const director2 = await prisma.director.create({
      data: {
        corporateApplicationId: application1.id,
        ...getDirector(),
      },
    })

    const user2 = getUser()
    await prisma.user.create({ data: user2 })
    const deleteDirectorRes1 = await deleteDirector({
      userId: user2.id,
      corporateApplicationId: application1.id,
      directorId: director1.id,
    })

    expect(deleteDirectorRes1.isErr()).toBeTruthy()

    const application2 = await prisma.corporateApplication.create({
      data: {
        userId: user2.id,
      },
    })

    const deleteDirectorRes2 = await deleteDirector({
      userId: user2.id,
      corporateApplicationId: application2.id,
      directorId: director2.id,
    })
    expect(deleteDirectorRes2.isErr()).toBeTruthy()

    const applicationAfter = await prisma.corporateApplication.findFirst({
      where: {
        id: application1.id,
      },
      include: {
        directors: true,
      },
    })

    expect(applicationAfter?.directors.length).toBe(2)
    expect(applicationAfter?.directors).toContainEqual(director1)
    expect(applicationAfter?.directors).toContainEqual(director2)
  })

  // beneficial owners
  test('create new beneficial owners', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })

    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    const beneficialOwner1 = getBeneficialOwner()
    const beneficialOwner2 = getBeneficialOwner()
    const updateBeneficialOwnersRes = await updateBeneficialOwners({
      userId: user.id,
      corporateApplicationId: application.id,
      beneficialOwners: [beneficialOwner1, beneficialOwner2],
    })

    expect(updateBeneficialOwnersRes.isOk()).toBeTruthy()

    const beneficialOwners = updateBeneficialOwnersRes._unsafeUnwrap()
    expect(
      beneficialOwners.findIndex((beneficialOwner) => {
        return (
          beneficialOwner.identificationNbr ===
          beneficialOwner1.identificationNbr
        )
      })
    ).not.toBe(-1)
    expect(
      beneficialOwners.findIndex((beneficialOwner) => {
        return (
          beneficialOwner.identificationNbr ===
          beneficialOwner2.identificationNbr
        )
      })
    ).not.toBe(-1)
  })

  test('create new and update beneficial owners', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })

    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    const beneficialOwner1 = getBeneficialOwner()
    const beneficialOwner2 = getBeneficialOwner()
    const createBeneficialOwnersRes = await updateBeneficialOwners({
      userId: user.id,
      corporateApplicationId: application.id,
      beneficialOwners: [beneficialOwner1, beneficialOwner2],
    })

    const createdBeneficialOwners = createBeneficialOwnersRes
      ._unsafeUnwrap()
      .map<Partial<BeneficialOwner>>((beneficialOwner) => {
        return {
          id: beneficialOwner.id,
          address: 'something',
        }
      })

    const beneficialOwner3 = getBeneficialOwner()

    const createAndUpdateBeneficialOwnersRes = await updateBeneficialOwners({
      userId: user.id,
      corporateApplicationId: application.id,
      beneficialOwners: [beneficialOwner3, ...createdBeneficialOwners],
    })

    expect(createAndUpdateBeneficialOwnersRes.isOk()).toBeTruthy()

    const beneficialOwners = createAndUpdateBeneficialOwnersRes._unsafeUnwrap()
    expect(
      beneficialOwners.findIndex((beneficialOwner) => {
        return (
          beneficialOwner.identificationNbr ===
            beneficialOwner1.identificationNbr &&
          beneficialOwner.address === 'something'
        )
      })
    ).not.toBe(-1)
    expect(
      beneficialOwners.findIndex((beneficialOwner) => {
        return (
          beneficialOwner.identificationNbr ===
            beneficialOwner2.identificationNbr &&
          beneficialOwner.address === 'something'
        )
      })
    ).not.toBe(-1)
    expect(
      beneficialOwners.findIndex((beneficialOwner) => {
        return (
          beneficialOwner.identificationNbr ===
          beneficialOwner3.identificationNbr
        )
      })
    ).not.toBe(-1)
  })

  test.skip("failing to update directors that isn't connected to your application", async () => {
    const user1 = getUser()
    const user2 = getUser()
    await prisma.user.create({ data: user1 })
    await prisma.user.create({ data: user2 })

    const application1 = await prisma.corporateApplication.create({
      data: {
        userId: user1.id,
      },
    })

    const beneficialOwner1 = getBeneficialOwner()
    const beneficialOwner2 = getBeneficialOwner()
    const createBeneficialOwnersRes = await updateBeneficialOwners({
      userId: user1.id,
      corporateApplicationId: application1.id,
      beneficialOwners: [beneficialOwner1, beneficialOwner2],
    })

    const createdBeneficialOwners = createBeneficialOwnersRes
      ._unsafeUnwrap()
      .map<Partial<BeneficialOwner>>((beneficialOwner) => {
        return {
          id: beneficialOwner.id,
          address: 'something',
        }
      })

    const application2 = await prisma.corporateApplication.create({
      data: {
        userId: user1.id,
      },
    })

    const createAndUpdateBeneficialOwnersRes = await updateBeneficialOwners({
      userId: user2.id,
      corporateApplicationId: application2.id,
      beneficialOwners: createdBeneficialOwners,
    })

    expect(createAndUpdateBeneficialOwnersRes.isErr()).toBeTruthy()
    expect(createAndUpdateBeneficialOwnersRes._unsafeUnwrapErr()).toBe(
      ERRORS.NOT_FOUND
    )
  })
  test('delete beneficial owner', async () => {
    const user = getUser()
    await prisma.user.create({ data: user })
    const application = await prisma.corporateApplication.create({
      data: {
        userId: user.id,
      },
    })

    const beneficialOwner1 = await prisma.beneficialOwner.create({
      data: {
        corporateApplicationId: application.id,
        ...getBeneficialOwner(),
      },
    })
    const beneficialOwner2 = await prisma.beneficialOwner.create({
      data: {
        corporateApplicationId: application.id,
        ...getBeneficialOwner(),
      },
    })

    const deleteBeneficialOwnerRes = await deleteBeneficialOwner({
      userId: user.id,
      corporateApplicationId: application.id,
      beneficialOwnerId: beneficialOwner1.id,
    })

    expect(deleteBeneficialOwnerRes.isOk()).toBeTruthy()

    const applicationAfter = await prisma.corporateApplication.findFirst({
      where: {
        id: application.id,
      },
      include: {
        beneficialOwners: true,
      },
    })

    expect(applicationAfter?.beneficialOwners.length).toBe(1)
    expect(applicationAfter?.beneficialOwners[0]).toEqual(beneficialOwner2)
  })

  test('failing to delete beneficial owner created by someone else', async () => {
    const user1 = getUser()
    await prisma.user.create({ data: user1 })
    const application1 = await prisma.corporateApplication.create({
      data: {
        userId: user1.id,
      },
    })

    const beneficialOwner1 = await prisma.beneficialOwner.create({
      data: {
        corporateApplicationId: application1.id,
        ...getBeneficialOwner(),
      },
    })
    const beneficialOwner2 = await prisma.beneficialOwner.create({
      data: {
        corporateApplicationId: application1.id,
        ...getBeneficialOwner(),
      },
    })

    const user2 = getUser()
    await prisma.user.create({ data: user2 })
    const deleteBeneficialOwnerRes1 = await deleteBeneficialOwner({
      userId: user2.id,
      corporateApplicationId: application1.id,
      beneficialOwnerId: beneficialOwner1.id,
    })

    expect(deleteBeneficialOwnerRes1.isErr()).toBeTruthy()

    const application2 = await prisma.corporateApplication.create({
      data: {
        userId: user2.id,
      },
    })

    const deleteBeneficialOwnerRes2 = await deleteBeneficialOwner({
      userId: user2.id,
      corporateApplicationId: application2.id,
      beneficialOwnerId: beneficialOwner2.id,
    })
    expect(deleteBeneficialOwnerRes2.isErr()).toBeTruthy()

    const applicationAfter = await prisma.corporateApplication.findFirst({
      where: {
        id: application1.id,
      },
      include: {
        beneficialOwners: true,
      },
    })

    expect(applicationAfter?.beneficialOwners.length).toBe(2)
    expect(applicationAfter?.beneficialOwners).toContainEqual(beneficialOwner1)
    expect(applicationAfter?.beneficialOwners).toContainEqual(beneficialOwner2)
  })
})
