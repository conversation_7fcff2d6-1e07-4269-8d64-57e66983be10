import { describe, expect, it, vi, expectTypeOf } from 'vitest'

import { okAsync } from 'neverthrow'

import { CreateProductOrderProps } from 'routes/product.order.route'

function getAddressType() {
  return okAsync('URBAN')
}

vi.mock('services/go-sweet-spot.service', () => {
  return {
    getAddressType: vi.fn().mockImplementation(getAddressType),
  }
})

describe('createOrder', () => {
  it('should create an order', async () => {
    // const orderProps: CreateProductOrderProps = [
    //   {
    //     productCode: 1,
    //     deliveryMethod: {
    //       type: 'PICKUP',
    //     },
    //     quantity: 1,
    //     recipientName: 'Test Tester',
    //     value: 500,
    //     message: 'hello there',
    //     recipientEmail: '<EMAIL>',
    //   },
    // ]

    // const result = await tryCreateProductOrder({
    //   orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
    //   userId: 'ca5d9b37-ce47-4f9c-a82e-5933068afd66',
    //   productOrderItems: orderProps,
    // })

    // expect(result.isOk()).toBeTruthy()
    // const orderNumber = result._unsafeUnwrap().orderNumber
    // expect(orderNumber).not.empty
    // expectTypeOf(orderNumber).toBeString

    // FIXME need to update tests for new flow
    expect(true).toBeTruthy()
  })
})
