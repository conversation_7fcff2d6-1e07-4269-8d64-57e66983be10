import { describe, expect, it, vi } from 'vitest'
import { sendOrderDetail } from 'services/epay-integration.service'
import RequestWithBackoff from 'helpers/request-with-backoff'

describe('sendOrderDetail', () => {
  it('should send order detail to ePay Integration', async () => {
    const requestSpy = vi
      .spyOn(RequestWithBackoff.prototype, 'request')
      .mockResolvedValue({
        status: 200,
        statusText: 'OK',
        headers: {},
        data: {},
        config: {},
      })

    await sendOrderDetail({
      orderNumber: 'order123456',
      userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
      id: 'order-id',
      orderType: 'PRODUCT',
      orderStatus: 'PENDING',
      orderIncrementNumber: 0,
      totalQuantity: 4,
      subTotal: 40000,
      secureDelivery: false,
      loadingFeeTotal: 2380,
      digitalFeeTotal: 300,
      shippingTotal: 750,
      shippingFeeDiscountTotal: 0,
      discountTotal: 1380,
      digitalFeeDiscountTotal: 180,
      loadingFeeDiscountTotal: 1200,
      creditCardFee: null,
      gstAmount: -43,
      orderTotal: 42050,
      lockCode: null,
      lockCodeEmailSent: false,
      createdAt: new Date(),
      submittedAt: null,
      paymentDate: null,
      cancelledByDate: null,
      reportedToEpay: false,
      releasedBy: null,
      organizationId: 'org123',
      paymentMethod: 'BANK_TRANSFER',
      windcaveSessionId: null,
      windcaveSessionConfirmed: false,
      purchaseOrderNumber: null,
      billingAddress: null,
      city: null,
      country: null,
      postCode: null,
      gstNumber: null,
      cancelledById: null,
      scheduledDate: null,
      createdByLiveAgent: false,
      authorizedByName: null,
      authorizedByEmail: null,
      productOrderItems: [
        {
          id: '1',
          productCode: 1,
          deliveryMethod: 'COURIER',
          unitPrice: 10000,
          quantity: 2,
          loadingFeeDiscount: 600,
          loadingFee: 1190,
          discount: 600,
          recipientName: 'Aye',
          recipientEmail: null,
          recipientAddress: null,
          recipientAddressLine2: null,
          recipientSuburb: null,
          recipientCity: null,
          recipientPostCode: null,
          recipientCountry: null,
          externalBatchId: null,
          deliveryBatchId: null,
          deliveryRecipient: false,
          digitalFee: null,
          digitalFeeDiscount: null,
          lockCodeEmailSent: false,
          extraEmbossingLine: null,
          giftStationMessage: null,
          lineNumber: null,
          productOrderId: '',
          productId: '',
          itemFee: null,
          options: null,
          customerReference: null,
          error: null,
        },
        {
          id: '2',
          productCode: 4,
          deliveryMethod: 'EMAIL',
          unitPrice: 10000,
          quantity: 1,
          loadingFee: 595,
          digitalFee: 150,
          loadingFeeDiscount: 300,
          digitalFeeDiscount: 90,
          discount: 390,
          recipientName: 'Aye',
          recipientEmail: null,
          recipientAddress: null,
          recipientAddressLine2: null,
          recipientSuburb: null,
          recipientCity: null,
          recipientPostCode: null,
          recipientCountry: null,
          externalBatchId: null,
          deliveryBatchId: null,
          deliveryRecipient: false,
          lockCodeEmailSent: false,
          extraEmbossingLine: null,
          giftStationMessage: null,
          lineNumber: null,
          productOrderId: '',
          productId: '',
          itemFee: null,
          options: null,
          customerReference: null,
          error: null,
        },
        {
          id: '3',
          productCode: 4,
          deliveryMethod: 'EMAIL',
          unitPrice: 10000,
          quantity: 1,
          loadingFee: 595,
          digitalFee: 150,
          loadingFeeDiscount: 300,
          digitalFeeDiscount: 90,
          discount: 390,
          recipientName: 'Aye',
          recipientEmail: null,
          recipientAddress: null,
          recipientAddressLine2: null,
          recipientSuburb: null,
          recipientCity: null,
          recipientPostCode: null,
          recipientCountry: null,
          externalBatchId: null,
          deliveryBatchId: null,
          deliveryRecipient: false,
          lockCodeEmailSent: false,
          extraEmbossingLine: null,
          giftStationMessage: null,
          lineNumber: null,
          productOrderId: '',
          productId: '',
          itemFee: null,
          options: null,
          customerReference: null,
          error: null,
        },
      ],
    })

    expect(requestSpy).toHaveBeenCalledWith({
      headers: {
        Authorization: expect.stringContaining('Basic'),
      },
      method: 'post',
      url: '/api/portal/newOrder',
      data: expect.objectContaining({
        TotalAmount: expect.arrayContaining([
          {
            due: 0,
            paid: 40000,
            type: 'LoadValue',
            value: 40000,
          },
          {
            discount: [],
            due: 0,
            paid: 750,
            type: 'Freight',
            value: 750,
          },
          {
            discount: [
              {
                _ref: null,
                quantity: 4,
                type: 'org discount',
                value: 300,
              },
            ],
            due: 0,
            paid: 1180,
            type: 'LoadFee',
            value: 2380,
          },
          {
            discount: [
              {
                _ref: null,
                quantity: 2,
                type: 'margin',
                value: 90,
              },
            ],
            due: 0,
            paid: 120,
            type: 'DigitalFee',
            value: 300,
          },
        ]),
        OrderItemArray: expect.arrayContaining([
          expect.objectContaining({
            ProductCode: '1',
            SentToThirdParty: 0,
            UnitAmount: 10000,
            Currency: 'NZD',
            Quantity: 2,
            ItemRef: '',
            DeliveryType: 'COURIER',
            LockCode: '',
            BatchRef: '',
            OrderBatchRef: '',
            ItemCostArray: expect.arrayContaining([
              expect.objectContaining({
                type: 'LoadValue',
                value: 10000,
              }),
              expect.objectContaining({
                type: 'LoadFee',
                value: 1190,
                discount: expect.arrayContaining([
                  expect.objectContaining({
                    type: 'margin',
                    value: 600,
                  }),
                ]),
              }),
              expect.objectContaining({
                type: 'DigitalFee',
                value: 0,
                discount: [],
              }),
            ]),
          }),
          expect.objectContaining({
            ProductCode: '4',
            SentToThirdParty: 0,
            UnitAmount: 10000,
            Currency: 'NZD',
            Quantity: 1,
            ItemRef: '',
            DeliveryType: 'EMAIL',
            LockCode: '',
            BatchRef: '',
            OrderBatchRef: '',
            ItemCostArray: expect.arrayContaining([
              expect.objectContaining({
                type: 'LoadValue',
                value: 10000,
              }),
              expect.objectContaining({
                type: 'LoadFee',
                value: 595,
                discount: expect.arrayContaining([
                  expect.objectContaining({
                    type: 'margin',
                    value: 300,
                  }),
                ]),
              }),
              expect.objectContaining({
                type: 'DigitalFee',
                value: 150,
                discount: expect.arrayContaining([
                  expect.objectContaining({
                    type: 'margin',
                    value: 90,
                  }),
                ]),
              }),
            ]),
          }),
        ]),
      }),
    })
  })
})
