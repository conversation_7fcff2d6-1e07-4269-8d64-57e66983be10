//!! this test is no longer valid as the function as the orderCardsFromSupplier function has been removed

// import { CardType, OrderStatus } from '@prisma/client'
// import { fromPromise, okAsync } from 'neverthrow'
// import { afterAll, beforeEach, describe, expect, it, vi } from 'vitest'
// import { PAYMENT_RECEIVED } from 'helpers/email-templates'
// import * as i2cService from 'services/i2c.service'
// import * as postmarkService from 'services/postmark.service'
// import { centsToDollars } from 'utils/numeric'
// import {
//   resetPartialOrderForRelease,
//   resetProductOrdersForRelease,
// } from '../db/product-order-reset'
// import { prisma } from 'utils/prisma'

// describe.skip('orderCardsFromSupplier', () => {
//   beforeEach(async () => {
//     vi.spyOn(i2cService, 'addCard').mockReturnValue(
//       okAsync({
//         cardType: CardType.PHYSICAL,
//         batchReferenceId: 'batch-123',
//         referenceId: 'mock-123',
//       })
//     )
//   })

//   afterAll(async () => {
//     await prisma.productOrderItem.deleteMany({
//       where: {
//         externalBatchId: 'batch-123',
//       },
//     })
//   })

//   it('should return orderStatus, releaseDate and releasedBy', async () => {
//     await resetProductOrdersForRelease()
//     vi.spyOn(postmarkService, 'sendEmailWithTemplate')

//     const mockDate = new Date(2024, 4, 10)
//     vi.setSystemTime(mockDate)

//     const result = await orderCardsFromSupplier({
//       orderId: 'abb621ee-37d5-4a1a-97a4-0a1c57f43755',
//       disableMocking: true,
//       userId: '6599e4e5193ea79c142c8b7e',
//       paymentDate: new Date(),
//     })

//     vi.useRealTimers()

//     if (result.isErr()) {
//       throw new Error(JSON.stringify(result.error))
//     } else {
//       expect(result?.value).toHaveProperty(
//         'orderStatus',
//         OrderStatus.PROCESSING
//       )
//       expect(result?.value).toHaveProperty('releaseDate', mockDate)
//       expect(result?.value).toHaveProperty('releasedBy', 'Mister Approver')
//     }
//   })

//   it('should order delivery recipients cards first', async () => {
//     await resetProductOrdersForRelease()
//     const addCardSpy = vi
//       .spyOn(i2cService, 'addCard')
//       .mockImplementationOnce(() => {
//         return fromPromise(
//           new Promise((resolve) => setTimeout(resolve, 1000)),
//           () => 'EXTERNAL_API' as const
//         ).andThen(() =>
//           okAsync({
//             cardType: 'PHYSICAL' as CardType,
//             batchReferenceId: 'mock-123456',
//             referenceId: 'mock-crn-456',
//           })
//         )
//       })
//       .mockImplementationOnce(() => {
//         return okAsync({
//           cardType: 'PHYSICAL' as CardType,
//           batchReferenceId: 'mock-123457',
//           referenceId: 'mock-crn-456',
//         })
//       })

//     const result = await orderCardsFromSupplier({
//       orderId: 'abb621ee-37d5-4a1a-97a4-0a1c57f43755',
//       disableMocking: true,
//       userId: '6599e4e5193ea79c142c8b7e',
//       paymentDate: new Date(),
//     })

//     if (result.isErr()) {
//       throw new Error(JSON.stringify(result.error))
//     } else {
//       expect(addCardSpy).toHaveBeenNthCalledWith(1, {
//         orderNumber: '15234',
//         userEmail: '<EMAIL>',
//         productOrderItem: expect.objectContaining({
//           id: '70d7dcf9-212d-47c2-889d-5a55713686d3',
//           deliveryRecipient: true,
//         }),
//         secureDelivery: false,
//         disableMocking: true,
//       })
//       expect(addCardSpy).toHaveBeenNthCalledWith(2, {
//         orderNumber: '15234',
//         userEmail: '<EMAIL>',
//         productOrderItem: expect.objectContaining({
//           id: '70d7dcf9-212d-47c2-889d-5a55713686d2',
//         }),
//         secureDelivery: false,
//         disableMocking: true,
//       })
//     }
//   })

//   it('should not fail when there are no delivery recipients', async () => {
//     await resetProductOrdersForRelease({
//       '70d7dcf9-212d-47c2-889d-5a55713686d3': {
//         deliveryRecipient: false,
//       },
//     })

//     const result = await orderCardsFromSupplier({
//       orderId: 'abb621ee-37d5-4a1a-97a4-0a1c57f43755',
//       disableMocking: true,
//       userId: '6599e4e5193ea79c142c8b7e',
//       paymentDate: new Date(),
//     })

//     if (result.isErr()) {
//       throw new Error(JSON.stringify(result.error))
//     } else {
//       expect(result.isOk()).toBeTruthy()
//     }
//   })

//   it('should send email', async () => {
//     await resetProductOrdersForRelease()
//     const spyOnPostMark = vi.spyOn(postmarkService, 'sendEmailWithTemplate')

//     const result = await orderCardsFromSupplier({
//       orderId: 'abb621ee-37d5-4a1a-97a4-0a1c57f43755',
//       disableMocking: true,
//       userId: '6599e4e5193ea79c142c8b7e',
//       paymentDate: new Date(),
//     })

//     if (result.isErr()) {
//       throw new Error(JSON.stringify(result.error))
//     } else {
//       expect(spyOnPostMark).toHaveBeenCalledWith({
//         email: '<EMAIL>',
//         templateId: PAYMENT_RECEIVED,
//         templateModel: {
//           orderNumber: '15234',
//           firstName: 'Test',
//           orderTotal: centsToDollars(12046),
//         },
//       })
//     }
//   })

//   it('should not call addCard for productOrderItems with batch numbers', async () => {
//     await resetPartialOrderForRelease()

//     const addCardSpy = vi.spyOn(i2cService, 'addCard')

//     const result = await orderCardsFromSupplier({
//       orderId: '4da77f39-1abe-46fe-9d7b-267f39f79042',
//       disableMocking: true,
//       userId: '6599e4e5193ea79c142c8b7e',
//       paymentDate: new Date(),
//     })

//     if (result.isErr()) {
//       throw new Error(JSON.stringify(result.error))
//     } else {
//       expect(addCardSpy).toHaveBeenCalledOnce()
//       expect(addCardSpy).toHaveBeenCalledWith({
//         orderNumber: '00123',
//         userEmail: '<EMAIL>',
//         productOrderItem: expect.objectContaining({
//           id: '1f85d259-84a9-428d-9727-9a84d104fcba',
//         }),
//         disableMocking: true,
//         secureDelivery: false,
//       })
//     }
//   })
// })
