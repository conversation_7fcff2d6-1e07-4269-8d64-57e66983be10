import { describe, expect, it } from 'vitest'
import { getProductOrder } from 'services/product-order.service'
import {
  PENDING_PRODUCT_ORDER_ID,
  PENDING_PRODUCT_ORDER_NUMBER,
} from '../../../prisma/seed-test'
import { ERRORS } from 'utils/error'

describe('test getProductOrder', () => {
  it('should get product order by orderNumber', async () => {
    const result = await getProductOrder({
      orderNumber: PENDING_PRODUCT_ORDER_NUMBER,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(result.value).toHaveProperty(
        'orderNumber',
        PENDING_PRODUCT_ORDER_NUMBER
      )
      expect(result.value).toHaveProperty('id', PENDING_PRODUCT_ORDER_ID)
    }
  })
  it('should get product order by orderId', async () => {
    const result = await getProductOrder({
      orderId: PENDING_PRODUCT_ORDER_ID,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(result.value).toHaveProperty(
        'orderNumber',
        PENDING_PRODUCT_ORDER_NUMBER
      )
      expect(result.value).toHaveProperty('id', PENDING_PRODUCT_ORDER_ID)
    }
  })

  it('should get error when no orderId or orderNumber is provided', async () => {
    const result = await getProductOrder({})
    console.log(result, 'result getProductOrder')
    if (result.isErr()) {
      expect(result.error).toHaveProperty('code', ERRORS.NOT_FOUND.code)
    } else {
      throw new Error('should be an error')
    }
  })

  it('should get error when invalid orderNumber is provided', async () => {
    const INVALID_ORDER_NUMBER = 'INVALID_ORDER_NUMBER'

    const result = await getProductOrder({
      orderNumber: INVALID_ORDER_NUMBER,
    })

    if (result.isErr()) {
      // Expected error
      expect(result.error).toBeDefined()
      expect(result.error).toEqual(ERRORS.NOT_FOUND)
    } else {
      throw new Error('should have received an error')
    }
  })

  it('should get error when invalid orderId is provided', async () => {
    const INVALID_ORDER_ID = 'INVALID_ORDER_ID'

    const result = await getProductOrder({
      orderId: INVALID_ORDER_ID,
    })

    if (result.isErr()) {
      // Expected error
      expect(result.error).toBeDefined()
      expect(result.error).toEqual(ERRORS.NOT_FOUND)
    } else {
      throw new Error('should have received an error')
    }
  })

  it('should get product order when both orderNumber and orderId are provided (orderNumber takes precedence)', async () => {
    const result = await getProductOrder({
      orderNumber: PENDING_PRODUCT_ORDER_NUMBER,
      orderId: PENDING_PRODUCT_ORDER_ID,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(result.value).toHaveProperty(
        'orderNumber',
        PENDING_PRODUCT_ORDER_NUMBER
      )
      expect(result.value).toHaveProperty('id', PENDING_PRODUCT_ORDER_ID)
    }
  })

  it('should get error when orderNumber and orderId refer to different orders', async () => {
    const INVALID_ORDER_NUMBER = 'INVALID_ORDER_NUMBER'

    const result = await getProductOrder({
      orderNumber: INVALID_ORDER_NUMBER,
      orderId: PENDING_PRODUCT_ORDER_ID,
    })

    if (result.isErr()) {
      // Expected error because orderNumber takes precedence and it's invalid
      expect(result.error).toBeDefined()
      expect(result.error).toEqual(ERRORS.NOT_FOUND)
    } else {
      throw new Error('should have received an error')
    }
  })

  it('should get order when orderNumber is valid and orderId invalid because orderNumber takes precedence', async () => {
    const INVALID_ORDER_ID = 'INVALID_ORDER_NUMBER'

    const result = await getProductOrder({
      orderNumber: PENDING_PRODUCT_ORDER_NUMBER,
      orderId: INVALID_ORDER_ID,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(result.value).toHaveProperty(
        'orderNumber',
        PENDING_PRODUCT_ORDER_NUMBER
      )
      expect(result.value).toHaveProperty('id', PENDING_PRODUCT_ORDER_ID)
    }
  })
})
