import { afterAll, beforeAll, describe, expect, it } from 'vitest'
import { OrderStatus, ProductOrderNoteStatus } from '@prisma/client'
import {
  cancelOrderWithNote,
  holdOrderWithNote,
  resumeOrder,
} from 'services/product-order.service'
import { prisma } from 'utils/prisma'

const PRODUCT_ORDER_ID = 'order-review-product-1'

describe('admin order review', () => {
  beforeAll(async () => {
    await prisma.productOrder.create({
      data: {
        id: PRODUCT_ORDER_ID,
        orderStatus: 'SUBMITTED',
        orderNumber: '098776',
        paymentMethod: 'BANK_TRANSFER',
        billingAddress: '5B Luna Way',
        city: 'Auckland',
        country: 'New Zealand',
        postCode: '2014',
        totalQuantity: 2,
        subTotal: 10000,
        secureDelivery: false,
        loadingFeeTotal: 1190,
        digitalFeeTotal: 0,
        shippingTotal: 652,
        discountTotal: 90,
        gstAmount: 98,
        orderTotal: 12046,
        creditCardFee: 294,
        lockCode: null,
        lockCodeEmailSent: false,
        createdAt: '2024-04-10T19:57:17.901Z',
        reportedToEpay: false,
        organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
        userId: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
      },
    })
  })

  afterAll(async () => {
    await prisma.productOrder.delete({
      where: { id: PRODUCT_ORDER_ID },
    })
  })

  describe('cancelOrderWithNote', () => {
    it('should set order status to CANCELLED, cancelledById and cancelledByDate and create a ProductOrderNote', async () => {
      const res = await cancelOrderWithNote({
        orderId: PRODUCT_ORDER_ID,
        noteMessage: 'Cancelling order',
        noteTitle: 'Cancel',
        userId: '6599e51f50c57b82204a4cc9',
      })

      if (res.isErr()) {
        throw new Error(JSON.stringify(res.error))
      } else {
        const order = await prisma.productOrder.findUnique({
          where: { id: PRODUCT_ORDER_ID },
        })
        expect(order).toHaveProperty('orderStatus', OrderStatus.CANCELLED)
        expect(order).toHaveProperty(
          'cancelledById',
          '6599e51f50c57b82204a4cc9'
        )
        expect(order).toHaveProperty('cancelledByDate')

        const note = await prisma.productOrderNote.findFirst({
          where: {
            productOrderId: PRODUCT_ORDER_ID,
            status: ProductOrderNoteStatus.CANCELLED,
          },
        })
        expect(note).toHaveProperty('title', 'Cancel')
        expect(note).toHaveProperty('message', 'Cancelling order')
        expect(note).toHaveProperty('status', ProductOrderNoteStatus.CANCELLED)
      }
    })
  })

  describe('holdOrderWithNote', () => {
    it('should set order status to CANCELLED and create note', async () => {
      const res = await holdOrderWithNote({
        orderId: PRODUCT_ORDER_ID,
        noteMessage: 'Holding order',
        noteTitle: 'HODL',
        userId: '6599e51f50c57b82204a4cc9',
      })

      if (res.isErr()) {
        throw new Error(JSON.stringify(res.error))
      } else {
        const order = await prisma.productOrder.findUnique({
          where: { id: PRODUCT_ORDER_ID },
        })
        expect(order).toHaveProperty('orderStatus', OrderStatus.ON_HOLD)

        const note = await prisma.productOrderNote.findFirst({
          where: {
            productOrderId: PRODUCT_ORDER_ID,
            status: ProductOrderNoteStatus.ON_HOLD,
          },
        })
        expect(note).toHaveProperty('title', 'HODL')
        expect(note).toHaveProperty('message', 'Holding order')
        expect(note).toHaveProperty('status', ProductOrderNoteStatus.ON_HOLD)
      }
    })
  })

  describe('resumeOrder', () => {
    it('should set order status to SUBMITTED', async () => {
      const res = await resumeOrder(PRODUCT_ORDER_ID)
      if (res.isErr()) {
        throw new Error(JSON.stringify(res.error))
      } else {
        expect(res.value).toHaveProperty('orderStatus', OrderStatus.SUBMITTED)
      }
    })
  })
})
