import { beforeEach, describe, expect, it } from 'vitest'
import { CreateProductOrderProps } from 'routes/product.order.route'
import {
  calculateAndCompleteProductOrder,
  createProductOrderItemsFromOrderItems,
} from 'services/product-order/checkout-order.service'
import { prisma } from 'utils/prisma'
import { createEmptyProductOrder } from 'data/product-order.data'

const sampleOrder = {
  orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
  userId: '64511261-1a28-4a4f-b9ed-ff7bee40d5d2',
  productOrderItems: [
    {
      productCode: 3,
      recipientName: 'Black Card',
      quantity: 1,
      value: 75,
      deliveryMethod: {
        type: 'COURIER',
        address: '123123',
        suburb: 'asd',
        city: 'asd',
        postcode: 'asd',
      },
      message: '',
    },
    {
      productCode: 4,
      recipientName: 'Hello World',
      quantity: 1,
      value: 500,
      deliveryMethod: {
        type: 'EMAIL',
        email: '<EMAIL>',
      },
      recipientEmail: '',
      message: '',
    },
  ] as CreateProductOrderProps,
}

describe('createProductOrder', () => {
  beforeEach(async () => {
    await prisma.discount.deleteMany({
      where: {
        organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      },
    })
  })

  it('should return an orderNumber', async () => {
    const completedProductOrder = await createEmptyProductOrder({
      orgId: sampleOrder.orgId,
      userId: sampleOrder.userId,
    }).andThen(({ id }) => {
      return createProductOrderItemsFromOrderItems({
        orgId: sampleOrder.orgId,
        productOrderId: id,
        orderItems: sampleOrder.productOrderItems,
      }).andThen(() => {
        return calculateAndCompleteProductOrder({
          orgId: sampleOrder.orgId,
          productOrderId: id,
        })
      })
    })

    if (completedProductOrder.isErr()) {
      throw new Error('Failed checkout order')
    }

    const productOrder = completedProductOrder.value

    expect(productOrder.id).toBe(productOrder.id)
    expect(productOrder.orderNumber).toBe(productOrder.orderNumber)
    expect(productOrder.organizationId).toBe(sampleOrder.orgId)
    expect(productOrder.userId).toBe(sampleOrder.userId)
    expect(productOrder.totalQuantity).toBe(2)
    expect(productOrder.subTotal).toBe(57500)
    expect(productOrder.secureDelivery).toBe(false)
    expect(productOrder.loadingFeeTotal).toBe(1190)
    expect(productOrder.digitalFeeTotal).toBe(150)
    expect(productOrder.shippingTotal).toBe(750)
    expect(productOrder.discountTotal).toBe(90)
    expect(productOrder.digitalFeeDiscountTotal).toBe(0)
    expect(productOrder.loadingFeeDiscountTotal).toBe(90)
    expect(productOrder.shippingFeeDiscountTotal).toBe(0)
    expect(productOrder.creditCardFee).toBe(1488)
    expect(productOrder.gstAmount).toBe(106)
    expect(productOrder.orderTotal).toBe(60988)
  })

  it('should delete the product order and product order items when it fails', async () => {
    const emptyProductOrder = await createEmptyProductOrder({
      orgId: sampleOrder.orgId,
      userId: sampleOrder.userId,
    })

    if (emptyProductOrder.isErr()) {
      throw new Error('Failed at createEmptyProductOrder')
    }

    const { id } = emptyProductOrder.value

    const failedCreateOrderitems = await createProductOrderItemsFromOrderItems({
      orgId: sampleOrder.orgId,
      productOrderId: id,
      orderItems: sampleOrder.productOrderItems,
    }).andThen(() => {
      return createProductOrderItemsFromOrderItems({
        orgId: sampleOrder.orgId,
        productOrderId: id,
        orderItems: [
          {
            productCode: 1000, //This should fail
            recipientName: 'Black Card',
            quantity: 1,
            value: 75,
            deliveryMethod: {
              type: 'COURIER',
              address: '123123',
              suburb: 'asd',
              city: 'asd',
              postcode: 'asd',
            },
            message: '',
          },
        ],
      })
    })

    if (failedCreateOrderitems.isOk()) {
      throw new Error('This should throw error')
    }

    const productOrder = await prisma.productOrder.findFirst({
      where: { id },
    })

    expect(productOrder).toBeNull()

    const productOrderItems = await prisma.productOrderItem.findMany({
      where: { productOrderId: id },
    })

    expect(productOrderItems).toHaveLength(0)
  })

  it('should calculate fees and discounts correctly', async () => {
    await prisma.discount.create({
      data: {
        id: 'discount-test',
        loadingFee: 300,
        digitalFee: 90,
        organizationId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      },
    })

    const completedProductOrder = await createEmptyProductOrder({
      orgId: sampleOrder.orgId,
      userId: sampleOrder.userId,
    }).andThen(({ id }) => {
      return createProductOrderItemsFromOrderItems({
        orgId: sampleOrder.orgId,
        productOrderId: id,
        orderItems: sampleOrder.productOrderItems,
      }).andThen(() => {
        return calculateAndCompleteProductOrder({
          orgId: sampleOrder.orgId,
          productOrderId: id,
        })
      })
    })

    if (completedProductOrder.isErr()) {
      throw new Error('Failed checkout order')
    }

    expect(completedProductOrder.value).toHaveProperty('orderNumber')

    const productOrder = await prisma.productOrder.findFirst({
      where: {
        orderNumber: completedProductOrder.value.orderNumber,
      },
      include: {
        productOrderItems: true,
      },
    })

    expect(productOrder?.discountTotal).toBe(690) // (300 - Physical) + (300 + 90 -> Virtual)

    // VIRTUAL
    expect(productOrder?.productOrderItems[0].digitalFee).toBe(150)
    expect(productOrder?.productOrderItems[0].digitalFeeDiscount).toBe(90)
    expect(productOrder?.productOrderItems[0].loadingFee).toBe(595)
    expect(productOrder?.productOrderItems[0].loadingFeeDiscount).toBe(300)
    expect(productOrder?.productOrderItems[0].discount).toBe(390)

    //PHYSICAL
    expect(productOrder?.productOrderItems[1].digitalFee).toBe(null)
    expect(productOrder?.productOrderItems[1].digitalFeeDiscount).toBe(null)
    expect(productOrder?.productOrderItems[1].loadingFee).toBe(595)
    expect(productOrder?.productOrderItems[1].loadingFeeDiscount).toBe(300)
    expect(productOrder?.productOrderItems[1].discount).toBe(300)
  })
})
