import { StockCardOrder } from 'routes/stock.route'
import {
  SHIPPING_PRICE,
  tryCreateStockCardOrder,
} from 'services/product-order.service'
import { centsToDollars } from 'utils/numeric'
import { prisma } from 'utils/prisma'
import { describe, expect, it } from 'vitest'
import {
  NEXT_CHAPTER_ORG_ID,
  PRODUCT_ID_WOOHOO_NEW_CARD,
  TEST_GLOBAL_DISCOUNT,
  USER_ID_SEEDED_123,
} from '../../../prisma/seed-test'

describe('tryCreateStockCardOrder', () => {
  it('should create a stock card order successfully', async () => {
    const findProduct = await prisma.product.findUnique({
      where: { id: PRODUCT_ID_WOOHOO_NEW_CARD },
    })

    if (!findProduct) {
      throw new Error('Product not found')
    }

    const stockCardOrderItems: StockCardOrder = [
      {
        productCode: findProduct?.productCode, // Assuming this is the product code for 'Woohooo new card'
        quantity: 2,
        resolution: 'LOW',
        deliveryMethod: {
          type: 'COURIER',
          address: '123 Test Street',
          suburb: 'Test Suburb',
          city: 'Test City',
          postcode: '1234',
        },
      },
    ]

    const result = await tryCreateStockCardOrder({
      userId: USER_ID_SEEDED_123,
      stockCardOrderItems,
      orgId: NEXT_CHAPTER_ORG_ID,
    })

    expect(result.isOk()).toBe(true)

    if (result.isOk()) {
      const { orderNumber } = result.value

      // Fetch the created order from the database
      const createdOrder = await prisma.productOrder.findUnique({
        where: { orderNumber },
        include: { productOrderItems: true },
      })

      expect(createdOrder).not.toBeNull()
      expect(createdOrder?.orderType).toBe('STOCK')
      expect(createdOrder?.orderStatus).toBe('PENDING')
      expect(createdOrder?.userId).toBe(USER_ID_SEEDED_123)
      expect(createdOrder?.organizationId).toBe(NEXT_CHAPTER_ORG_ID)

      // Check product order items
      expect(createdOrder?.productOrderItems).toHaveLength(1)
      const orderItem = createdOrder?.productOrderItems[0]
      expect(orderItem?.productCode).toBe(5)
      expect(orderItem?.quantity).toBe(2)
      expect(orderItem?.unitPrice).toBe(0)
      expect(orderItem?.options).toEqual({
        resolution: 'LOW',
        externalCardDesignId: expect.any(String),
      })

      // Verify totals
      const product = await prisma.product.findUnique({
        where: { id: PRODUCT_ID_WOOHOO_NEW_CARD },
      })
      const expectedItemFee = product?.option1Fee ?? 0
      const expectedSubTotal = expectedItemFee * 2
      const expectedDiscount = 50 * 2 // GLOBAL_DISCOUNT.LOW_RES * quantity
      const expectedShippingTotal = 750 // Assuming URBAN shipping

      expect(createdOrder?.subTotal).toBe(expectedSubTotal)
      expect(createdOrder?.shippingTotal).toBe(expectedShippingTotal)
      expect(createdOrder?.orderTotal).toBe(
        expectedSubTotal + expectedShippingTotal - expectedDiscount
      )

      // Check that gstAmount is an integer
      expect(Number.isInteger(createdOrder?.gstAmount)).toBe(true)
    }
  })
  it('should return an error for invalid product code', async () => {
    const stockCardOrderItems: StockCardOrder = [
      {
        productCode: 999, // Invalid product code
        quantity: 2,
        resolution: 'LOW',
        deliveryMethod: {
          type: 'COURIER',
          address: '123 Test Street',
          suburb: 'Test Suburb',
          city: 'Test City',
          postcode: '1234',
        },
      },
    ]

    const result = await tryCreateStockCardOrder({
      userId: USER_ID_SEEDED_123,
      stockCardOrderItems,
      orgId: NEXT_CHAPTER_ORG_ID,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error).toEqual({
        errorType: 'INVALID_PRODUCT_ORDER',
        data: 'Invalid product codes for stock cards',
      })
    }
  })

  it('should calculate correct totals for multiple items', async () => {
    const findProduct = await prisma.product.findUnique({
      where: { id: PRODUCT_ID_WOOHOO_NEW_CARD },
    })

    if (!findProduct) {
      throw new Error('Product not found')
    }

    const stockCardOrderItems: StockCardOrder = [
      {
        productCode: findProduct?.productCode, // Assuming this is the product code for 'Woohooo new card'
        quantity: 2,
        resolution: 'LOW',
        deliveryMethod: {
          type: 'COURIER',
          address: '123 Test Street',
          suburb: 'Test Suburb',
          city: 'Test City',
          postcode: '1234',
        },
      },
      {
        productCode: findProduct?.productCode, // Assuming this is the product code for 'Woohooo new card'
        quantity: 1,
        resolution: 'HIGH',
        deliveryMethod: {
          type: 'COURIER',
          address: '123 Test Street',
          suburb: 'Test Suburb',
          city: 'Test City',
          postcode: '1234',
        },
      },
    ]

    const result = await tryCreateStockCardOrder({
      userId: USER_ID_SEEDED_123,
      stockCardOrderItems,
      orgId: NEXT_CHAPTER_ORG_ID,
    })

    expect(result.isOk()).toBe(true)

    if (result.isOk()) {
      const { orderNumber } = result.value

      const createdOrder = await prisma.productOrder.findUnique({
        where: { orderNumber },
        include: { productOrderItems: true },
      })

      expect(createdOrder).not.toBeNull()
      expect(createdOrder?.productOrderItems).toHaveLength(2)

      const product = await prisma.product.findUnique({
        where: { id: PRODUCT_ID_WOOHOO_NEW_CARD },
      })
      const lowResItemFee = product?.option1Fee ?? 0
      const highResItemFee = product?.option2Fee ?? 0
      const expectedSubTotal = lowResItemFee * 2 + highResItemFee * 1
      const expectedDiscount =
        TEST_GLOBAL_DISCOUNT.urbanShipping +
        TEST_GLOBAL_DISCOUNT.lowResStockCard * 2 +
        TEST_GLOBAL_DISCOUNT.highResStockCard * 1
      const expectedShippingTotal = SHIPPING_PRICE.URBAN

      // Debug values
      console.log('Debug values:')
      console.log('lowResItemFee:', lowResItemFee)
      console.log('highResItemFee:', highResItemFee)
      console.log('expectedSubTotal:', expectedSubTotal)
      console.log('TEST_GLOBAL_DISCOUNT:', TEST_GLOBAL_DISCOUNT)
      console.log('expectedDiscount:', expectedDiscount)
      console.log('expectedShippingTotal:', expectedShippingTotal)
      console.log('SHIPPING_PRICE.URBAN:', SHIPPING_PRICE.URBAN)

      expect(createdOrder?.subTotal).toBe(expectedSubTotal)
      expect(createdOrder?.shippingTotal).toBe(expectedShippingTotal)
      expect(centsToDollars(createdOrder?.orderTotal ?? 0)).toBeCloseTo(
        centsToDollars(
          expectedSubTotal + expectedShippingTotal - expectedDiscount
        )
      )
    }
  })
})
