import { describe, expect, it, vi } from 'vitest'
import { submitProductOrder } from 'services/product-order.service'
import * as windcaveService from 'services/windcave.service'
import { OrderStatus } from '@prisma/client'
import { prisma } from 'utils/prisma'

describe('submitProductOrder', () => {
  it.skip('should create windcave session and return redirect URL when paymentMethod is "CREDIT_CARD"', async () => {
    const spyOnCreateWindcaveSession = vi.spyOn(
      windcaveService,
      'createWindcaveSession'
    )

    const result = await submitProductOrder({
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      orderNumber: '14899',
      purchaseOrderNumber: null,
      paymentMethod: 'CREDIT_CARD',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postcode: '2014',
      secureDelivery: false,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(spyOnCreateWindcaveSession).toHaveBeenCalled()
      expect(result?.value).toHaveProperty('redirect')
      expect(result?.value).toHaveProperty('orderStatus', OrderStatus.SUBMITTED)
    }
  })

  it.skip('should NOT return redirect URL when paymentMethod is "BANK_TRANSFER"', async () => {
    const spyOnCreateWindcaveSession = vi.spyOn(
      windcaveService,
      'createWindcaveSession'
    )

    const result = await submitProductOrder({
      orgId: '6c16a991-e43b-41bd-9e6a-aq8120cb311e5',
      orderNumber: '15000',
      purchaseOrderNumber: null,
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postcode: '2014',
      secureDelivery: false,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(spyOnCreateWindcaveSession).not.toHaveBeenCalled()
      expect(result?.value).toHaveProperty('redirect', undefined)
      expect(result?.value).toHaveProperty('orderStatus', OrderStatus.SUBMITTED)
    }
  })

  it('should subtract the creditCardFee from the total and set it to 0 when payment method is BANK_TRANSFER', async () => {
    await submitProductOrder({
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      orderNumber: '15000',
      purchaseOrderNumber: null,
      paymentMethod: 'BANK_TRANSFER',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postcode: '2014',
      secureDelivery: false,
    })

    const productOrder = await prisma.productOrder.findFirst({
      where: {
        orderNumber: '15000',
      },
    })

    expect(productOrder?.creditCardFee).toBe(0)
    expect(productOrder?.orderTotal).toBe(5000)
  })

  it('should NOT update the orderTotal and creditCardFee when payment method is CREDIT_CARD', async () => {
    await submitProductOrder({
      orgId: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
      orderNumber: '14899',
      purchaseOrderNumber: null,
      paymentMethod: 'CREDIT_CARD',
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postcode: '2014',
      secureDelivery: false,
    })

    const productOrder = await prisma.productOrder.findFirst({
      where: {
        orderNumber: '14899',
      },
    })

    expect(productOrder?.creditCardFee).toBe(294)
    expect(productOrder?.orderTotal).toBe(12046)
  })
})
