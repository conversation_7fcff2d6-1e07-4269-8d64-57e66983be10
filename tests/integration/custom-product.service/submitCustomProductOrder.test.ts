import { OrderStatus, PaymentMethod } from '@prisma/client'
import { beforeAll, describe, expect, it, vi } from 'vitest'
import { submitCustomProductOrder } from 'services/custom-product.service'
import * as windcaveService from 'services/windcave.service'
import {
  seedCustomProductOrderForSubmit,
  CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS,
} from '../db/custom-product-order'
import { prisma } from 'utils/prisma'
import { seedOrgFloatFund } from '../../../prisma/seed-float-funds'
import { NEXT_CHAPTER_ORG_ID } from '../../../prisma/seed-test'
import { ERRORS } from 'utils/error'

describe('submitCustomProductOrder', () => {
  beforeAll(async () => {
    await seedCustomProductOrderForSubmit()
  })

  it('should return error when orderNumber does not exist', async () => {
    const result = await submitCustomProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: 'ABC1234',
      purchaseOrderNumber: null,
      paymentMethod: PaymentMethod.BANK_TRANSFER,
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      isLiveAgent: false,
    })

    if (result.isErr()) {
      expect(result.error.code).toBe(ERRORS.NOT_FOUND.code)
    } else {
      throw new Error('should be an error')
    }
  })

  it('should create windcave session and return redirect URL when paymentMethod is "CREDIT_CARD"', async () => {
    const spyOnCreateWindcaveSession = vi.spyOn(
      windcaveService,
      'createWindcaveSession'
    )

    const result = await submitCustomProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: '15000',
      purchaseOrderNumber: null,
      paymentMethod: PaymentMethod.CREDIT_CARD,
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      isLiveAgent: false,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(spyOnCreateWindcaveSession).toHaveBeenCalled()
      expect(result?.value).toHaveProperty('redirect')
      expect(result?.value).toHaveProperty('orderStatus', OrderStatus.SUBMITTED)
    }
  })

  it('should NOT return redirect URL when paymentMethod is "BANK_TRANSFER"', async () => {
    const spyOnCreateWindcaveSession = vi.spyOn(
      windcaveService,
      'createWindcaveSession'
    )

    const result = await submitCustomProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: 'LOGO111',
      purchaseOrderNumber: null,
      paymentMethod: PaymentMethod.BANK_TRANSFER,
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      isLiveAgent: false,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    } else {
      expect(spyOnCreateWindcaveSession).not.toHaveBeenCalled()
      expect(result?.value).toHaveProperty('redirect', undefined)
      expect(result?.value).toHaveProperty('orderStatus', OrderStatus.SUBMITTED)
    }
  })

  it('should subtract the creditCardFee from the total and set it to 0 when payment method is BANK_TRANSFER', async () => {
    await submitCustomProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: 'LOGO123',
      purchaseOrderNumber: null,
      paymentMethod: PaymentMethod.BANK_TRANSFER,
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      isLiveAgent: false,
    })

    const customProductOrder = await prisma.customProductOrder.findFirst({
      where: {
        orderNumber: 'LOGO123',
      },
    })

    expect(customProductOrder?.creditCardFee).toBe(0)
    expect(customProductOrder?.orderTotal).toBe(5000)
  })

  it('should NOT update the orderTotal and creditCardFee when payment method is CREDIT_CARD', async () => {
    await submitCustomProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: 'LOGO456',
      purchaseOrderNumber: null,
      paymentMethod: PaymentMethod.CREDIT_CARD,
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      isLiveAgent: false,
    })

    const customProductOrder = await prisma.customProductOrder.findFirst({
      where: {
        orderNumber: 'LOGO456',
      },
    })

    expect(customProductOrder?.creditCardFee).toBe(125)
    expect(customProductOrder?.orderTotal).toBe(5125)
  })

  it('should release order when float funds transaction is created', async () => {
    await seedOrgFloatFund(
      prisma,
      NEXT_CHAPTER_ORG_ID,
      '1234567890',
      'test',
      42000
    )

    const result = await submitCustomProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS.orderNumber,
      purchaseOrderNumber: null,
      paymentMethod: PaymentMethod.FLOAT_FUNDS,
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postCode: '2014',
      isLiveAgent: false,
    })

    if (result.isErr()) {
      throw new Error(result.error.message)
    }

    const floatFundsTransaction = await prisma.floatFundsTransaction.findFirst({
      where: { customProductOrderId: CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS.id },
    })

    const customProductOrder = await prisma.customProductOrder.findFirst({
      where: { id: CUSTOM_PRODUCT_ORDER_WITH_FLOAT_FUNDS.id },
    })

    expect(floatFundsTransaction).not.toBeNull()
    expect(result?.value).toHaveProperty('orderStatus', OrderStatus.PROCESSING)
    expect(customProductOrder).toHaveProperty(
      'orderStatus',
      OrderStatus.PROCESSING
    )
  })
})
