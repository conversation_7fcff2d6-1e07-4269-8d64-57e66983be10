import { beforeAll, describe, expect, it } from 'vitest'

import request from 'supertest'
import {
  CARD_DESIGN_URL,
  NEXT_CHAPTER_ORG_ID,
  USER_ID_SEEDED_123,
} from '../../../../prisma/seed-test'
import { getTestServer } from '../../../helpers/test-server'

import { DesignType } from '@prisma/client'
import { CREATE_STOCK_PRODUCT_CODE } from 'services/custom-product.service'
import { generateRandomNumbers } from 'utils/numeric'
import { prisma } from 'utils/prisma'

describe('/stock/product/create', () => {
  const server = getTestServer({
    userId: USER_ID_SEEDED_123,
    orgId: NEXT_CHAPTER_ORG_ID,
    role: 'ORG_ADMIN',
  })

  type NewProductResponse = {
    orderNumber: string
  }

  beforeAll(async () => {
    const customProductItem = await prisma.customProductOrderItem.create({
      data: {
        productCode: CREATE_STOCK_PRODUCT_CODE,
        discount: 0,
        unitPrice: 15000,
        designType: DesignType.STOCK,
      },
    })

    if (!customProductItem) {
      throw new Error('Failed to create custom product item')
    }
  })

  it('should create a new stock card product and order', async () => {
    const response = await request(server)
      .post('/stock/product/create')
      .send({
        designUrl: CARD_DESIGN_URL,
        logoUrl: 'https://example.com/logo.png',
        name: 'Test Product',
        logoFileName: `test_product_${generateRandomNumbers(5)}.png`,
        termsAndConditionsVersion: '1.0',
      })

    const body = JSON.parse(response.text) as NewProductResponse

    expect(response.status).toBe(200)
    expect(body.orderNumber).toBeDefined()
  })
})
