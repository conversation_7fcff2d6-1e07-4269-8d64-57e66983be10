import { describe, it, expect, beforeAll } from 'vitest'

import request from 'supertest'
import { getTestServer } from '../../../helpers/test-server'
import {
  NEXT_CHAPTER_ORG_ID,
  PRODUCT_ID_WOOHOO_NEW_CARD,
  USER_ID_SEEDED_123,
} from '../../../../prisma/seed-test'
import {
  orderCardsFromSupplier,
  submitProductOrder,
  tryCreateStockCardOrder,
} from 'services/product-order.service'
import { StockCardOrder } from 'routes/stock.route'
import { generateRandomNumbers } from 'utils/numeric'

describe('corporate-application/:id/organization-type/other', () => {
  const server = getTestServer({
    userId: USER_ID_SEEDED_123,
    orgId: NEXT_CHAPTER_ORG_ID,
    role: 'ORG_ADMIN',
  })

  type NewOrderResponse = {
    orderNumber: string
    orderStatus: string
    id: string
  }

  let stockOrderNumber = ''
  let stockOrderId = ''
  beforeAll(async () => {
    const stockCardOrderItems: StockCardOrder = [
      {
        productCode: 5, // Assuming this is the product code for 'Woohooo new card'
        quantity: 2,
        resolution: 'LOW',
        deliveryMethod: {
          type: 'COURIER',
          address: '123 Test Street',
          suburb: 'Test Suburb',
          city: 'Test City',
          postcode: '1234',
        },
      },
    ]

    const orderResult = await tryCreateStockCardOrder({
      userId: USER_ID_SEEDED_123,
      stockCardOrderItems,
      orgId: NEXT_CHAPTER_ORG_ID,
    })

    if (orderResult.isOk()) {
      stockOrderNumber = orderResult.value.orderNumber
      stockOrderId = orderResult.value.id
    }

    await submitProductOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      orderNumber: stockOrderNumber,
      billingAddress: '5B Luna Way',
      city: 'Auckland',
      country: 'New Zealand',
      postcode: '2014',
      purchaseOrderNumber: null,
      lockCode: '',
      paymentMethod: 'BANK_TRANSFER',
      secureDelivery: false,
    })

    // submit order and create cards
    await orderCardsFromSupplier({
      orderId: stockOrderId,
      disableMocking: false,
      userId: USER_ID_SEEDED_123,
      paymentDate: new Date(),
    })

    if (orderResult.isOk()) {
      console.log(orderResult, 'sumbitted order')
    } else {
      console.log(orderResult.error, 'error submitting order')
    }
  })

  it('should create a funding order into pending status', async () => {
    const response = await request(server)
      .post(`/funding/order/create`)
      .send({
        lockCodeType: 'no_lock_code',
        cards: [
          {
            productId: PRODUCT_ID_WOOHOO_NEW_CARD,
            crn: generateRandomNumbers(15),
            unitPriceInDollars: 100,
          },
        ],
      })

    const body = JSON.parse(response.text) as NewOrderResponse

    console.log(body, 'body')
    expect(response.status).toBe(200)
    expect(body.orderStatus).toBe('PENDING')
  })
})
