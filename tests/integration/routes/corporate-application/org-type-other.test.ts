import { describe, it, expect } from 'vitest'

import request from 'supertest'
import { getTestServer } from '../../../helpers/test-server'
import {
  CORP_APPLICATION_ID_456_EMPTY,
  USER_ID_SEEDED_123,
} from '../../../../prisma/seed-test'

describe('corporate-application/:id/organization-type/other', () => {
  const server = getTestServer({
    userId: USER_ID_SEEDED_123,
    role: 'ORG_ADMIN',
  })
  const route = '/corporate-application'

  it('should submit a corporate application', async () => {
    const response = await request(server)
      .post(`${route}/${CORP_APPLICATION_ID_456_EMPTY}/organization-type/other`)
      .send({
        name: 'Example Organization', 
        nzbn: '123456789',
        tradingName: 'Example Trading Name',
        firstName: 'John',
        lastName: 'Doe',
        phoneNbr: '************',
      })

    console.log(response.body)

    expect(response.status).toBe(200)
  })
})
