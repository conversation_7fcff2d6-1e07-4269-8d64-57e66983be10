// TODO: src/services/epay-crn-update.service doesnt exist anymore

// import { err, ok } from 'neverthrow'
// import * as crnUpdateService from  'src/services/epay-crn-update.service'
// import * as epaySoapService from 'utils/soap'
// import * as i2cService from 'services/i2c.service'
// import * as postmarkService from 'services/postmark.service'
// import { describe, it, expect, beforeEach, vi, assert } from 'vitest'
// import { resetCrnUpdateForOrder } from '../db/product-order-reset'
// import { ErrorResponse } from 'utils/error'
// import { AxiosResponse } from 'axios'

// const orderIdForPendingOrder = '3785e936-4cf6-493f-848f-1c52c47371b1'

// describe.skip('retrieveAndProcessCrns', () => {
//   beforeEach(() => {
//     vi.restoreAllMocks()
//   })
//   it('should retrieve and process crns all the way through', async () => {
//     await resetCrnUpdateForOrder(orderIdForPendingOrder)
//     const mockCrns: string[] = ['CRN123', 'CRN456']
//     const spyOnRetrieveCardReferenceNumber = vi
//       .spyOn(epaySoapService, 'retrieveCardReferenceNumber')
//       .mockResolvedValue(ok(mockCrns))

//     const spyOnActivateCard = vi
//       .spyOn(i2cService, 'activateCard')
//       .mockResolvedValue(ok({ crn: 'blah', message: 'pretend' }))

//     const spyOnSetCode = vi
//       .spyOn(i2cService, 'setAccessCode')
//       .mockResolvedValue(ok('pretend'))

//     const spyOnBlocking = vi
//       .spyOn(i2cService, 'blockCard')
//       .mockResolvedValue(ok('pretend'))

//     const spyOnPostMark = vi
//       .spyOn(postmarkService, 'sendEmailWithTemplate')
//       .mockResolvedValue(ok({ data: {}, status: 200 } as AxiosResponse))

//     const result = await crnUpdateService.retrieveAndProcessCrns()

//     if (result.isErr()) {
//       throw new Error('should not be an error')
//     } else {
//       expect(spyOnRetrieveCardReferenceNumber).toHaveBeenCalled()
//       expect(spyOnActivateCard).toHaveBeenCalled()
//       expect(spyOnSetCode).toHaveBeenCalled()
//       expect(spyOnBlocking).toHaveBeenCalled()
//       expect(spyOnPostMark).toHaveBeenCalled()
//       expect(result.value.processedCards).equals(mockCrns.length)
//     }
//   })

//   it('should log error when activate card fails', async () => {
//     await resetCrnUpdateForOrder(orderIdForPendingOrder)
//     const mockCrns: string[] = ['CRN123', 'CRN456']
//     const spyOnRetrieveCardReferenceNumber = vi
//       .spyOn(epaySoapService, 'retrieveCardReferenceNumber')
//       .mockResolvedValue(ok(mockCrns))
//     const spyOnActivateCard = vi
//       .spyOn(i2cService, 'activateCard')
//       .mockResolvedValue(err({ code: 'ERR', message: 'activate card failed' }))
//     const spyOnPostMark = vi
//       .spyOn(postmarkService, 'sendEmailWithTemplate')
//       .mockResolvedValue(ok({ data: {}, status: 200 } as AxiosResponse))

//     const result = await crnUpdateService.retrieveAndProcessCrns()
//     expect(spyOnRetrieveCardReferenceNumber).toHaveBeenCalled()
//     expect(spyOnActivateCard).toHaveBeenCalled()
//     expect(spyOnPostMark).not.toHaveBeenCalled()
//     if (result.isErr()) {
//       assert(
//         (result.error as ErrorResponse[]).find(
//           (e) => 'message' in e && e.message === 'activate card failed'
//         )
//       )
//     } else {
//       throw new Error('should be an error')
//     }
//   })
// })
