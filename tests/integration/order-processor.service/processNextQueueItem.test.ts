import { DeliveryMethod, OrderStatus } from '@prisma/client'
import axios from 'axios'
import { fromPromise } from 'neverthrow'
import { orderCardsFromSupplierByOrderId } from 'services/product-order/order-cards.service'
import { processNextQueueItem } from 'services/product-order/order-processor.service'
import { processScheduledOrder } from 'services/product-order/process-scheduled-order.service'
import { prisma } from 'utils/prisma'
import { beforeAll, describe, expect, it, vi } from 'vitest'

describe('processNextQueueItem', () => {
  beforeAll(async () => {
    vi.spyOn(axios, 'post').mockImplementation(() => {
      return Promise.resolve({ status: 200, data: {} })
    })
  })

  it('should process the next queue item with physical cards', async () => {
    const releaseRes = await releasePhysicalProductOrder()
    if (releaseRes.isErr()) {
      console.log(releaseRes.error)
      throw new Error('Shouldnt error')
    }

    const processRes = await processNextQueueItem({
      orderAgeInMinutes: 0,
      useRen: false,
    })

    if (processRes.isErr()) {
      throw new Error('Shouldnt error')
    }

    console.log(processRes.value)

    const productOrder = await prisma.productOrder.findUniqueOrThrow({
      where: {
        id: releaseRes.value.id,
      },
      include: {
        productOrderItems: {
          include: {
            cardItems: true,
          },
        },
      },
    })
    expect(processRes.value).toBeTruthy()
    expect(productOrder).toHaveProperty('orderStatus', OrderStatus.CONFIRMED)
    expect(productOrder.productOrderItems[0].cardItems.length).toEqual(
      productOrder.productOrderItems[0].quantity
    )
    expect(productOrder.productOrderItems[1].cardItems.length).toEqual(
      productOrder.productOrderItems[1].quantity
    )
  })

  it('should process the next queue item with virtual cards', async () => {
    const scheduledDate = new Date()
    const releaseRes = await releaseVirtualProductOrder(scheduledDate)
    if (releaseRes.isErr()) {
      throw new Error('Shouldnt error')
    }

    const processRes = await processNextQueueItem({
      orderAgeInMinutes: 0,
      useRen: false,
    })

    if (processRes.isErr()) {
      throw new Error('Shouldnt error')
    }

    const productOrder = await prisma.productOrder.findUniqueOrThrow({
      where: {
        id: releaseRes.value.id,
      },
      include: {
        productOrderItems: {
          include: {
            cardItems: true,
          },
        },
      },
    })
    expect(processRes.value).toBeTruthy()
    expect(productOrder).toHaveProperty('orderStatus', OrderStatus.COMPLETED)
    expect(productOrder.productOrderItems[0].cardItems.length).toEqual(1)
    expect(productOrder.productOrderItems[1].cardItems.length).toEqual(1)
  })
})

async function createProductOrderWithItems() {
  return prisma.$transaction(async (tx) => {
    const prezzyRed = await tx.product.findFirstOrThrow({
      where: {
        name: 'Prezzy Red',
      },
    })

    const productOrder = await tx.productOrder.create({
      data: {
        paymentMethod: 'BANK_TRANSFER',
        orderStatus: OrderStatus.RELEASING,
        user: {
          connect: {
            id: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
          },
        },
        organization: {
          connect: {
            id: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
          },
        },
        productOrderItems: {
          create: [
            {
              productId: prezzyRed.id,
              productCode: prezzyRed.productCode,
              recipientName: 'John One',
              quantity: 1,
              unitPrice: 500,
              deliveryMethod: DeliveryMethod.COURIER,
              recipientEmail: '<EMAIL>',
            },
            {
              productId: prezzyRed.id,
              productCode: prezzyRed.productCode,
              recipientName: 'John Two',
              quantity: 1,
              unitPrice: 500,
              deliveryMethod: DeliveryMethod.COURIER,
              recipientEmail: '<EMAIL>',
            },
          ],
        },
      },
    })

    return tx.productOrder.update({
      where: { id: productOrder.id },
      data: { orderNumber: productOrder.orderIncrementNumber.toString() },
    })
  })
}

function releasePhysicalProductOrder() {
  return fromPromise(createProductOrderWithItems(), () => {
    throw new Error('Shouldnt error')
  }).andThen((productOrder) => {
    return orderCardsFromSupplierByOrderId({
      orderId: productOrder.id,
      paymentDate: new Date(),
      useRen: false,
      userId: '6599e51f50c57b82204a4cc9',
    }).map(() => productOrder)
  })
}

async function createProductOrderWithVirtualItems(scheduledDate: Date) {
  return prisma.$transaction(async (tx) => {
    const prezzyVirtual = await tx.product.findFirstOrThrow({
      where: {
        name: 'Prezzy Virtual',
      },
    })

    const productOrder = await tx.productOrder.create({
      data: {
        paymentMethod: 'BANK_TRANSFER',
        orderStatus: OrderStatus.RELEASING,
        scheduledDate,
        user: {
          connect: {
            id: '********-1a28-4a4f-b9ed-ff7bee40d5d2',
          },
        },
        organization: {
          connect: {
            id: '6c16a991-e43b-41bd-9e6a-a8120cb311e5',
          },
        },
        productOrderItems: {
          create: [
            {
              productId: prezzyVirtual.id,
              productCode: prezzyVirtual.productCode,
              recipientName: 'John One',
              quantity: 1,
              unitPrice: 500,
              deliveryMethod: DeliveryMethod.EMAIL,
              recipientEmail: '<EMAIL>',
            },
            {
              productId: prezzyVirtual.id,
              productCode: prezzyVirtual.productCode,
              recipientName: 'John Two',
              quantity: 1,
              unitPrice: 500,
              deliveryMethod: DeliveryMethod.EMAIL,
              recipientEmail: '<EMAIL>',
            },
          ],
        },
      },
    })

    return tx.productOrder.update({
      where: { id: productOrder.id },
      data: { orderNumber: productOrder.orderIncrementNumber.toString() },
    })
  })
}

function releaseVirtualProductOrder(date: Date) {
  return fromPromise(createProductOrderWithVirtualItems(date), () => {
    throw new Error('Shouldnt error')
  }).andThen((productOrder) => {
    return processScheduledOrder({
      orderId: productOrder.id,
      paymentDate: new Date(),
      userId: '6599e51f50c57b82204a4cc9',
      isWindcavePayment: false,
    }).map(() => productOrder)
  })
}
