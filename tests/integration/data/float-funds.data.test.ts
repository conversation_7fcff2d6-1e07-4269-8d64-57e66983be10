import {
  createFloatFundsTransaction,
  upsertFloatFundOrg,
  getFloatFundsTransactions,
  getOrgFloatFundsBalance,
  createFloatFundsOrder,
  getUnprocessedFloatFundsOrder,
  getFloatFundsTransactionById,
} from 'data/float-funds.data'
import { beforeAll, describe, expect, it } from 'vitest'

import {
  NEXT_CHAPTER_ORG_ID,
  USER_ID_SEEDED_123,
  TEST_ORG_1_ID,
} from '../../../prisma/seed-test'
import { FloatFundsTransactionType, PaymentMethod } from '@prisma/client'
import { errorCodes } from 'types/simple-error'
import { prisma } from 'utils/prisma'

describe('createFloatFundsTransaction', () => {
  beforeAll(async () => {
    await upsertFloatFundOrg({
      orgId: NEXT_CHAPTER_ORG_ID,
      updatedByUserId: USER_ID_SEEDED_123,
    })
  })

  it('should create transaction"', async () => {
    const productOrder = await createFloatFundsOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      amount: 1009,
      userId: USER_ID_SEEDED_123,
      billingAddress: '123 Main St',
      city: 'Anytown',
      country: 'NZ',
      postCode: '12345',
      paymentMethod: PaymentMethod.BANK_TRANSFER,
    })

    if (productOrder.isErr()) {
      console.log(
        productOrder.error,
        'should create productOrder first...ERROR'
      )
      throw new Error('should not be an error')
    }

    const result = await createFloatFundsTransaction({
      orgId: NEXT_CHAPTER_ORG_ID,
      amount: 1009,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: USER_ID_SEEDED_123,
      description: 'this is a description',
      note: 'this is a note',
      orderId: productOrder.value.id,
      orderNumber: productOrder.value.orderNumber!,
    })

    if (result.isErr()) {
      console.log(result.error, 'should create transaction...ERROR')
      throw new Error('should not be an error')
    } else {
      const actual = result?.value
      expect(actual).toHaveProperty('orgId', NEXT_CHAPTER_ORG_ID)
      expect(actual).toHaveProperty('amount', 1009)
      expect(actual).toHaveProperty(
        'transactionType',
        FloatFundsTransactionType.CREDIT
      )
      expect(actual).toHaveProperty('description', 'this is a description')
      expect(actual).toHaveProperty('note', 'this is a note')
      expect(actual).toHaveProperty('orderId', productOrder.value.id)
      expect(actual).toHaveProperty(
        'orderNumber',
        productOrder.value.orderNumber
      )
      expect(actual).toHaveProperty('createdBy', USER_ID_SEEDED_123)
    }
  })

  it('should prevent balance going below zero', async () => {
    const result = await createFloatFundsTransaction({
      orgId: NEXT_CHAPTER_ORG_ID,
      amount: 50000, // Amount larger than current balance
      transactionType: FloatFundsTransactionType.DEBIT,
      createdByUserId: USER_ID_SEEDED_123,
      description: 'Attempt to withdraw more than balance',
    })

    expect(result.isErr()).toBe(true)

    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.db.BAD_INPUT)
      expect(result.error.message).toContain('Insufficient funds')
    }
  })
})

describe('getFloatFundsTransactions', () => {
  it('should get page 1 of transactions', async () => {
    const result = await getFloatFundsTransactions({
      orgId: TEST_ORG_1_ID,
      page: 1,
      pageSize: 10,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const { transactions, totalCount, page, pageSize } = result.value

    expect(page).toBe(1)
    expect(pageSize).toBe(10)
    expect(totalCount).toBe(30) // Total number of seeded transactions
    expect(transactions).toHaveLength(10)

    // Check first transaction (most recent)
    expect(transactions[0]).toMatchObject({
      amount: 5000,
      balanceAfterTransaction: 42000,
      transactionType: FloatFundsTransactionType.CREDIT,
      description: 'Large business deposit',
      orderNumber: '200030',
    })
  })

  it('should filter by transaction type DEPOSIT', async () => {
    const result = await getFloatFundsTransactions({
      orgId: TEST_ORG_1_ID,
      page: 1,
      pageSize: 20,
      transactionType: FloatFundsTransactionType.CREDIT,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const { transactions, totalCount } = result.value

    expect(totalCount).toBe(16) // Number of DEPOSIT transactions in seed data
    expect(
      transactions.every(
        (t) => t.transactionType === FloatFundsTransactionType.CREDIT
      )
    ).toBe(true)

    // Verify some specific deposits
    expect(transactions).toContainEqual(
      expect.objectContaining({
        amount: 5000,
        description: 'Large business deposit',
        orderNumber: '200030',
      })
    )
    expect(transactions).toContainEqual(
      expect.objectContaining({
        amount: 8000,
        description: 'Weekly float top-up',
        orderNumber: '200026',
      })
    )
  })

  it('should filter by transaction type WITHDRAW', async () => {
    const result = await getFloatFundsTransactions({
      orgId: TEST_ORG_1_ID,
      page: 1,
      pageSize: 20,
      transactionType: FloatFundsTransactionType.DEBIT,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const { transactions, totalCount } = result.value

    expect(totalCount).toBe(14) // Number of WITHDRAW transactions in seed data
    expect(
      transactions.every(
        (t) => t.transactionType === FloatFundsTransactionType.DEBIT
      )
    ).toBe(true)

    // Verify some specific withdrawals
    expect(transactions).toContainEqual(
      expect.objectContaining({
        amount: 3000,
        description: 'Monthly withdrawal',
        orderNumber: '200027',
      })
    )
  })

  it('should filter by order number', async () => {
    const result = await getFloatFundsTransactions({
      orgId: TEST_ORG_1_ID,
      page: 1,
      pageSize: 10,
      orderNumber: '200030',
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const { transactions, totalCount } = result.value

    expect(totalCount).toBe(1)
    expect(transactions).toHaveLength(1)
    expect(transactions[0]).toMatchObject({
      amount: 5000,
      balanceAfterTransaction: 42000,
      transactionType: FloatFundsTransactionType.CREDIT,
      description: 'Large business deposit',
      orderNumber: '200030',
    })
  })

  it('should get last page of transactions', async () => {
    const result = await getFloatFundsTransactions({
      orgId: TEST_ORG_1_ID,
      page: 3,
      pageSize: 10,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const { transactions, totalCount, page, pageSize } = result.value

    expect(page).toBe(3)
    expect(pageSize).toBe(10)
    expect(totalCount).toBe(30)
    expect(transactions).toHaveLength(10)

    // Check last transaction (oldest)
    expect(transactions[transactions.length - 1]).toMatchObject({
      amount: 1500,
      balanceAfterTransaction: 1210,
      transactionType: FloatFundsTransactionType.CREDIT,
      description: 'Initial deposit',
      orderNumber: '200001',
    })
  })
})

describe('getOrgFloatFundsBalance', () => {
  beforeAll(async () => {
    await upsertFloatFundOrg({
      orgId: TEST_ORG_1_ID,
      updatedByUserId: USER_ID_SEEDED_123,
    })
  })

  it('should return balance for an existing org float fund', async () => {
    const result = await getOrgFloatFundsBalance(TEST_ORG_1_ID)

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const balance = result.value
    expect(balance).toHaveProperty('currentBalance')
    expect(balance).toHaveProperty('lastUpdatedDate')
    expect(balance).toHaveProperty('isActive')
    expect(balance.currentBalance).toBe(42000) // From seed data seed
    expect(balance.isActive).toBe(true)
  })

  it('should return zero balance for non-existent org', async () => {
    const result = await getOrgFloatFundsBalance('non-existent-org-id')

    expect(result.isErr()).toBe(true)
  })
})

describe('createFloatFundsOrder', () => {
  it('should create a float funds order successfully', async () => {
    const result = await createFloatFundsOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      userId: USER_ID_SEEDED_123,
      amount: 1000,
      billingAddress: '123 Test St',
      city: 'Test City',
      country: 'NZ',
      postCode: '1234',
      paymentMethod: PaymentMethod.BANK_TRANSFER,
      purchaseOrderNumber: 'PO123',
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const order = result.value
    expect(order).toHaveProperty('orderNumber')
    expect(order).toHaveProperty('orderStatus', 'SUBMITTED')
    expect(order).toHaveProperty('id')
  })
})

describe('getUnprocessedFloatFundsOrder', () => {
  let createdOrderId: string

  beforeAll(async () => {
    // Create an order first
    const result = await createFloatFundsOrder({
      orgId: NEXT_CHAPTER_ORG_ID,
      userId: USER_ID_SEEDED_123,
      amount: 1000,
      billingAddress: '123 Test St',
      city: 'Test City',
      country: 'NZ',
      postCode: '1234',
      paymentMethod: PaymentMethod.BANK_TRANSFER,
    })

    if (result.isErr()) {
      throw new Error('Failed to create test order')
    }

    createdOrderId = result.value.id
  })

  it('should return null for non-existent order', async () => {
    const result = await getUnprocessedFloatFundsOrder({
      orderId: 'non-existent-id',
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    expect(result.value).toBeNull()
  })

  it('should return null for order with wrong status', async () => {
    const result = await getUnprocessedFloatFundsOrder({
      orderId: createdOrderId,
    })

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    expect(result.value).toBeNull()
  })
})

describe.only('getFloatFundsTransactionById', () => {
  it('should return transaction by id', async () => {
    const txData = {
      orgId: NEXT_CHAPTER_ORG_ID,
      amount: 1000,
      transactionType: FloatFundsTransactionType.CREDIT,
      customerCode: 'TEST-123',
      balanceAfterTransaction: 1000,
      floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
      transactionDate: new Date(),
      description: 'Test transaction',
      note: 'Test note',
    }

    const createdTx = await prisma.floatFundsTransaction.create({
      data: {
        ...txData,
        createdByUser: {
          connect: { id: USER_ID_SEEDED_123 },
        },
      },
    })
    const result = await getFloatFundsTransactionById(createdTx.id)

    if (result.isErr()) {
      throw new Error('should not be an error')
    }

    const transaction = result.value
    expect(transaction).toHaveProperty('id', createdTx.id)
    expect(transaction).toHaveProperty('amount', txData.amount)
    expect(transaction).toHaveProperty(
      'transactionType',
      txData.floatFundsTransactionType
    )
    expect(transaction).toHaveProperty('description', txData.description)
    expect(transaction).toHaveProperty('note', txData.note)
    expect(transaction).toHaveProperty(
      'transactionDate',
      txData.transactionDate
    )
    expect(transaction).toHaveProperty('createdBy', 'Mr Not Proper')
  })

  it('should return error for non-existent transaction', async () => {
    const result = await getFloatFundsTransactionById(999999)

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.db.ITEM_NOT_FOUND)
      expect(result.error.message).toBe('Transaction [999999] not found')
    }
  })
})
