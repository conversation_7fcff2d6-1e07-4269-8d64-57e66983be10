export const productOrderRes = {
  id: 'test-windcaveSessionId',
  state: 'complete',
  type: 'purchase',
  amount: '50.00',
  currency: 'NZD',
  currencyNumeric: 554,
  merchantReference: 'test',
  methods: ['card'],
  expires: '2024-02-18T23:50:28Z',
  callbackUrls: {
    approved: 'http://localhost:3000/order-history/create-card/invoice/15000',
    declined: 'http://localhost:3000/settings',
    cancelled: 'http://localhost:3000/settings',
  },
  notificationUrl: 'http://localhost:4567/notification/product/order/payment',
  storeCard: false,
  clientType: 'internet',
  links: [],
  transactions: [],
}

export const customProductOrderRes = {
  ...productOrderRes,
  callbackUrls: {
    approved: 'http://localhost:3000/order-history/create-card/invoice/LOGO098',
  },
  metaData: ['CUSTOM'],
}
