import { describe, it, expect, vi } from 'vitest'
import {
  getBalance,
} from '../../../../src/services/float-funds.service'
import * as userService from '../../../../src/services/user.service'
import * as floatFundsData from '../../../../src/data/float-funds.data'
import { Organization, UserRole } from '@prisma/client'
import { ResultAsync } from 'neverthrow'
import { errorCodes } from '../../../../src/types/simple-error'

describe('getBalance', () => {
  it('should return error when float funds account is not active', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getOrgFloatFundsBalanceSpy = vi.spyOn(
      floatFundsData,
      'getOrgFloatFundsBalance'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'test-user-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          organization: {
            id: 'test-org-id',
          } as any,
          role: UserRole.ORG_ADMIN,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    getOrgFloatFundsBalanceSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          currentBalance: 1000,
          lastUpdatedDate: new Date(),
          isActive: false,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await getBalance({
      requstedByUserId: 'test-user-id',
      orgId: 'test-org-id',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.db.BAD_INPUT)
      expect(result.error.message).toBe('Float funds account is not active')
    }
  })

  it('should return balance when float funds account is active', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getOrgFloatFundsBalanceSpy = vi.spyOn(
      floatFundsData,
      'getOrgFloatFundsBalance'
    )
    const mockDate = new Date()

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'test-user-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          organization: {
            id: 'test-org-id',
          } as any,
          role: UserRole.ORG_ADMIN,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    getOrgFloatFundsBalanceSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          currentBalance: 1000,
          lastUpdatedDate: mockDate,
          isActive: true,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await getBalance({
      requstedByUserId: 'test-user-id',
      orgId: 'test-org-id',
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value).toEqual({
        currentBalance: 1000,
        lastUpdatedDate: mockDate,
        isActive: true,
      })
    }
  })

  it('should return error when user is not authorized', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getOrgFloatFundsBalanceSpy = vi.spyOn(
      floatFundsData,
      'getOrgFloatFundsBalance'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'test-user-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          organization: {
            id: 'different-org-id',
          } as any,
          role: UserRole.ORG_ADMIN,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await getBalance({
      requstedByUserId: 'test-user-id',
      orgId: 'test-org-id',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe(
        'Only EPAY_ADMIN can view other organization balances'
      )
    }
    expect(getOrgFloatFundsBalanceSpy).not.toHaveBeenCalled()
  })
})

