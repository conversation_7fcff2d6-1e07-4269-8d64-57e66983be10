import { describe, it, expect, vi } from 'vitest'
import { getTransactions } from '../../../../src/services/float-funds.service'
import * as userService from '../../../../src/services/user.service'
import * as floatFundsData from '../../../../src/data/float-funds.data'
import {
  FloatFundsTransactionType,
  Organization,
  UserRole,
} from '@prisma/client'
import { ResultAsync } from 'neverthrow'
import { errorCodes } from '../../../../src/types/simple-error'

describe('getTransactions', () => {
  it('should return transactions when user is EPAY_ADMIN', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionsSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactions'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'admin-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.EPAY_ADMIN,
          organization: {
            id: 'any-org-id',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const mockTransactions = {
      transactions: [
        {
          id: 1,
          amount: 1000,
          balanceAfterTransaction: 1000,
          transactionType: FloatFundsTransactionType.CREDIT,
          orderNumber: '123',
          description: 'Test deposit',
          note: 'Test note',
          transactionDate: new Date(),
          createdBy: 'user-1',
        },
      ],
      page: 1,
      pageSize: 10,
      totalCount: 1,
    }

    getFloatFundsTransactionsSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockTransactions), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await getTransactions({
      requstedByUserId: 'admin-id',
      orgId: 'not-the-same-org-id',
      page: 1,
      pageSize: 10,
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value).toEqual(mockTransactions)
    }
  })

  it('should return transactions when user belongs to the organization', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionsSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactions'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-id',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: {
            id: 'test-org-id',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const mockTransactions = {
      transactions: [],
      page: 1,
      pageSize: 10,
      totalCount: 0,
    }

    getFloatFundsTransactionsSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockTransactions), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await getTransactions({
      requstedByUserId: 'user-id',
      orgId: 'test-org-id',
      page: 1,
      pageSize: 10,
    })

    expect(result.isOk()).toBe(true)
  })

  it('should return error when user tries to access different organization', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionsSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactions'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: {
            id: 'org-1',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await getTransactions({
      requstedByUserId: 'user-id',
      orgId: 'different-org-id',
      page: 1,
      pageSize: 10,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe(
        'Only EPAY_ADMIN can view other organization transactions'
      )
    }
    expect(getFloatFundsTransactionsSpy).not.toHaveBeenCalled()
  })

  it('should handle optional filters when authorized', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionsSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactions'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'admin-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.EPAY_ADMIN,
          organization: null,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const startDate = new Date('2023-01-01')
    const endDate = new Date('2023-12-31')

    const result = await getTransactions({
      requstedByUserId: 'admin-id',
      orgId: 'test-org-id',
      startDate,
      endDate,
      transactionType: FloatFundsTransactionType.CREDIT,
      page: 1,
      pageSize: 10,
      orderNumber: '123',
    })

    expect(getFloatFundsTransactionsSpy).toHaveBeenCalledWith({
      orgId: 'test-org-id',
      startDate,
      endDate,
      transactionType: FloatFundsTransactionType.CREDIT,
      page: 1,
      pageSize: 10,
      orderNumber: '123',
    })
  })

  it('should return error when user is not found', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionsSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactions'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(null), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await getTransactions({
      requstedByUserId: 'non-existent-id',
      orgId: 'test-org-id',
      page: 1,
      pageSize: 10,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe('User not found')
    }
    expect(getFloatFundsTransactionsSpy).not.toHaveBeenCalled()
  })
})
