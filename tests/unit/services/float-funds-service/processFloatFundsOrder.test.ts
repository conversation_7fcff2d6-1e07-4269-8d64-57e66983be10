import { beforeEach, describe, it, expect, vi } from 'vitest'
import { processFloatFundsOrder } from 'services/float-funds.service'
import { getUserById } from 'services/user.service'
import { epayUpdateProductOrderStatusByOrder } from 'epay-data/epay-product-order.data'
import {
  FloatFundsTransactionType,
  OrderStatus,
  PaymentMethod,
  ProductOrder,
  UserRole,
} from '@prisma/client'
import { ResultAsync, okAsync } from 'neverthrow'
import { errorCodes } from 'types/simple-error'
import * as floatFundsData from '../../../../src/data/float-funds.data'
import * as userService from '../../../../src/services/user.service'
import * as epayProductOrderData from '../../../../src/epay-data/epay-product-order.data'
import { initLogger } from 'utils/logger'

describe('processFloatFundsOrder', () => {
  const mockOrderId = 'order-123'
  const mockPaymentDate = new Date()
  const mockUserId = 'user-123'
  const mockOrderNumber = 'ORDER-001'
  const mockOrgId = 'org-123'
  const mockOrderTotal = 1000
  const mockUser = {
    id: 'test-user-id',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    organization: {
      id: mockOrgId,
    } as any,
    role: UserRole.EPAY_ADMIN,
  }

  const mockTransaction = {
    id: 1,
    orgId: mockOrgId,
    amount: 1000,
    balanceAfterTransaction: 1000,
    transactionType: FloatFundsTransactionType.CREDIT,
    orderNumber: '123',
    orderId: 'order-123',
    description: 'Test deposit',
    note: 'Test note',
    transactionDate: new Date(),
    createdBy: 'user-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    customerCode: 'test-customer',
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
  }

  const productOrder = {
    id: mockOrderId,
    orderNumber: mockOrderNumber,
    organizationId: mockOrgId,
    userId: mockUserId,
    orderTotal: mockOrderTotal,
    orderType: 'FLOAT_FUNDS',
    orderStatus: OrderStatus.PENDING,
    orderIncrementNumber: 1,
    paymentMethod: PaymentMethod.BANK_TRANSFER,
  } as ProductOrder

  const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
  const getUnprocessedFloatFundsOrderSpy = vi.spyOn(
    floatFundsData,
    'getUnprocessedFloatFundsOrder'
  )
  const createFloatFundsTransactionSpy = vi.spyOn(
    floatFundsData,
    'createFloatFundsTransaction'
  )
  const epayUpdateProductOrderStatusByOrderSpy = vi.spyOn(
    epayProductOrderData,
    'epayUpdateProductOrderStatusByOrder'
  )

  initLogger('tests')

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should successfully process a float funds order', async () => {
    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockUser), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    getUnprocessedFloatFundsOrderSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(productOrder), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    createFloatFundsTransactionSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockTransaction), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    epayUpdateProductOrderStatusByOrderSpy.mockImplementation(() =>
      okAsync(productOrder as any)
    )

    const result = await processFloatFundsOrder({
      orderId: mockOrderId,
      paymentDate: mockPaymentDate,
      userId: mockUserId,
    })

    expect(result.isOk()).toBe(true)
    expect(getUserById).toHaveBeenCalledWith({ id: mockUserId })
    expect(getUnprocessedFloatFundsOrderSpy).toHaveBeenCalledWith({
      orderId: mockOrderId,
    })
    expect(createFloatFundsTransactionSpy).toHaveBeenCalledWith({
      orgId: mockOrgId,
      amount: mockOrderTotal,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: mockUserId,
      orderNumber: mockOrderNumber,
      orderId: mockOrderId,
      description: 'Customer top-up',
    })
    expect(epayUpdateProductOrderStatusByOrder).toHaveBeenCalledWith({
      orderStatus: OrderStatus.COMPLETED,
      releasedAt: expect.any(Date),
      orderNumber: mockOrderNumber,
      paymentDate: mockPaymentDate,
      releasedBy: 'John Doe',
    })
  })

  it('should return unauthorized error if user is not EPAY_ADMIN', async () => {
    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({ ...mockUser, role: UserRole.ORG_ADMIN }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await processFloatFundsOrder({
      orderId: mockOrderId,
      paymentDate: mockPaymentDate,
      userId: mockUserId,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe(
        'Only EPAY_ADMIN can process float funds order'
      )
    }
    expect(getUnprocessedFloatFundsOrderSpy).not.toHaveBeenCalled()
  })

  it('should return error if order not found', async () => {
    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockUser), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    getUnprocessedFloatFundsOrderSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(null), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await processFloatFundsOrder({
      orderId: mockOrderId,
      paymentDate: mockPaymentDate,
      userId: mockUserId,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.db.ITEM_NOT_FOUND)
    }
    expect(createFloatFundsTransactionSpy).not.toHaveBeenCalled()
  })

  it('should update order status to ON_HOLD if transaction creation fails', async () => {
    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockUser), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    getUnprocessedFloatFundsOrderSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(productOrder), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    createFloatFundsTransactionSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.reject(), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    epayUpdateProductOrderStatusByOrderSpy.mockImplementation(() =>
      okAsync({} as any)
    )

    const result = await processFloatFundsOrder({
      orderId: mockOrderId,
      paymentDate: mockPaymentDate,
      userId: mockUserId,
    })

    if (result.isErr()) {
      expect(result.isErr()).toBe(true)
      expect(getUserByIdSpy).toHaveBeenCalledOnce()
      expect(getUnprocessedFloatFundsOrderSpy).toHaveBeenCalledOnce()
      expect(epayUpdateProductOrderStatusByOrder).toHaveBeenCalledWith({
        orderStatus: OrderStatus.ON_HOLD,
        releasedAt: expect.any(Date),
        orderNumber: mockOrderNumber,
        paymentDate: mockPaymentDate,
        releasedBy: 'John Doe',
      })
    } else {
      throw new Error('should error')
    }
  })
})
