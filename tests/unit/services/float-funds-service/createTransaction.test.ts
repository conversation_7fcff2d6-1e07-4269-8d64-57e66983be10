import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createTransaction } from '../../../../src/services/float-funds.service'
import * as floatFundsData from '../../../../src/data/float-funds.data'
import * as userService from '../../../../src/services/user.service'
import { FloatFundsTransactionType, UserRole } from '@prisma/client'
import { ResultAsync } from 'neverthrow'
import { errorCodes } from '../../../../src/types/simple-error'

describe('createTransaction', () => {
  const mockTransaction = {
    id: 1,
    orgId: 'test-org-id',
    amount: 1000,
    balanceAfterTransaction: 1000,
    transactionType: FloatFundsTransactionType.CREDIT,
    orderNumber: '123',
    orderId: 'order-123',
    description: 'Test deposit',
    note: 'Test note',
    transactionDate: new Date(),
    createdBy: 'user-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    customerCode: 'test-customer',
    floatFundsTransactionType: FloatFundsTransactionType.CREDIT,
  }

  beforeEach(() => {
    vi.resetAllMocks()
  })

  it('should create a transaction successfully as EPAY_ADMIN', async () => {
    const createFloatFundsTransactionSpy = vi.spyOn(
      floatFundsData,
      'createFloatFundsTransaction'
    )
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-1',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.EPAY_ADMIN,
          organization: { id: 'different-org-id' } as any,
        }),
        () => ({
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Error',
        })
      )
    )

    createFloatFundsTransactionSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockTransaction), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await createTransaction({
      orgId: 'test-org-id',
      amount: 1000,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: 'user-1',
      orderNumber: '123',
      orderId: 'order-123',
      description: 'Test deposit',
      note: 'Test note',
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value).toEqual(mockTransaction)
    }

    expect(createFloatFundsTransactionSpy).toHaveBeenCalledWith({
      orgId: 'test-org-id',
      amount: 1000,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: 'user-1',
      orderNumber: '123',
      orderId: 'order-123',
      description: 'Test deposit',
      note: 'Test note',
    })
  })

  it('should create a transaction successfully for user in same organization', async () => {
    const createFloatFundsTransactionSpy = vi.spyOn(
      floatFundsData,
      'createFloatFundsTransaction'
    )
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-1',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: { id: 'test-org-id' } as any,
        }),
        () => ({
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Error',
        })
      )
    )

    createFloatFundsTransactionSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockTransaction), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await createTransaction({
      orgId: 'test-org-id',
      amount: 1000,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: 'user-1',
      description: 'Test deposit',
    })

    expect(result.isOk()).toBe(true)
  })

  it('should reject transaction with amount <= 0', async () => {
    const result = await createTransaction({
      orgId: 'test-org-id',
      amount: 0,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: 'user-1',
      description: 'Test deposit',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.db.BAD_INPUT)
      expect(result.error.message).toBe('Amount must be greater than 0')
    }
  })

  it('should reject transaction from non-EPAY_ADMIN user for different organization', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-1',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: {
            id: 'different-org-id',
          } as any,
        }),
        () => ({
          code: errorCodes.db.UNKNOWN_ERROR,
          message: 'Error',
        })
      )
    )

    const result = await createTransaction({
      orgId: 'test-org-id',
      amount: 1000,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: 'user-1',
      description: 'Test deposit',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe(
        'Only EPAY_ADMIN can create transactions for other organizations'
      )
    }
  })

  it('should reject transaction when user is not found', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(null), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await createTransaction({
      orgId: 'test-org-id',
      amount: 1000,
      transactionType: FloatFundsTransactionType.CREDIT,
      createdByUserId: 'user-1',
      description: 'Test deposit',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe('User not found')
    }
  })
})
