import { describe, it, expect, vi } from 'vitest'
import { getTransactionById } from '../../../../src/services/float-funds.service'
import * as userService from '../../../../src/services/user.service'
import * as floatFundsData from '../../../../src/data/float-funds.data'
import { UserRole } from '@prisma/client'
import { ResultAsync } from 'neverthrow'
import { errorCodes } from '../../../../src/types/simple-error'

describe('getTransactionById', () => {
  it('should return transaction when user is EPAY_ADMIN', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionByIdSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactionById'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'admin-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.EPAY_ADMIN,
          organization: {
            id: 'any-org-id',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const mockTransaction = {
      id: 1,
      amount: 1000,
      balanceAfterTransaction: 1000,
      transactionType: 'CREDIT',
      orderNumber: '123',
      description: 'Test deposit',
      note: 'Test note',
      transactionDate: new Date(),
      bankAccountName: null,
      bankAccountNumber: null,
      createdBy: 'user-1',
    }

    getFloatFundsTransactionByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockTransaction), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await getTransactionById({
      transactionId: 1,
      orgId: 'not-the-same-org-id',
      userId: 'admin-id',
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value).toEqual(mockTransaction)
    }
  })

  it('should return transaction when user belongs to the organization', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionByIdSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactionById'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-id',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: {
            id: 'test-org-id',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const mockTransaction = {
      id: 1,
      amount: 1000,
      balanceAfterTransaction: 1000,
      transactionType: 'CREDIT',
      orderNumber: '123',
      description: 'Test deposit',
      note: 'Test note',
      transactionDate: new Date(),
      bankAccountName: null,
      bankAccountNumber: null,
      createdBy: 'user-1',
    }

    getFloatFundsTransactionByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockTransaction), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await getTransactionById({
      transactionId: 1,
      orgId: 'test-org-id',
      userId: 'user-id',
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value).toEqual(mockTransaction)
    }
  })

  it('should return error when user tries to access different organization', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionByIdSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactionById'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: {
            id: 'org-1',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await getTransactionById({
      transactionId: 1,
      orgId: 'different-org-id',
      userId: 'user-id',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe(
        'Only EPAY_ADMIN can view other organization transactions'
      )
    }
    expect(getFloatFundsTransactionByIdSpy).not.toHaveBeenCalled()
  })

  it('should return error when user is not found', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const getFloatFundsTransactionByIdSpy = vi.spyOn(
      floatFundsData,
      'getFloatFundsTransactionById'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(null), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await getTransactionById({
      transactionId: 1,
      orgId: 'test-org-id',
      userId: 'non-existent-id',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe('User not found')
    }
    expect(getFloatFundsTransactionByIdSpy).not.toHaveBeenCalled()
  })
})
