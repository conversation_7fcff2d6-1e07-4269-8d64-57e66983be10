import { describe, it, expect, vi } from 'vitest'
import { createOrder } from '../../../../src/services/float-funds.service'
import * as userService from '../../../../src/services/user.service'
import * as floatFundsData from '../../../../src/data/float-funds.data'
import { OrderStatus, PaymentMethod, UserRole } from '@prisma/client'
import { ResultAsync } from 'neverthrow'
import { errorCodes } from '../../../../src/types/simple-error'

describe('createOrder', () => {
  it('should create order when user belongs to the organization', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const createFloatFundsOrderSpy = vi.spyOn(
      floatFundsData,
      'createFloatFundsOrder'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-id',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: {
            id: 'test-org-id',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const mockOrder = {
      id: 'order-id',
      orderNumber: '1',
      orderStatus: OrderStatus.PENDING,
    }

    createFloatFundsOrderSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(mockOrder), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await createOrder({
      orgId: 'test-org-id',
      userId: 'user-id',
      amount: 1000,
      billingAddress: '123 Test St',
      city: 'Test City',
      country: 'Test Country',
      postCode: '12345',
      paymentMethod: PaymentMethod.BANK_TRANSFER,
    })

    expect(result.isOk()).toBe(true)
    if (result.isOk()) {
      expect(result.value).toEqual(mockOrder)
    }
  })

  it('should return error when user tries to create order for different organization', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const createFloatFundsOrderSpy = vi.spyOn(
      floatFundsData,
      'createFloatFundsOrder'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'user-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          role: UserRole.ORG_ADMIN,
          organization: {
            id: 'org-1',
          } as any,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await createOrder({
      orgId: 'different-org-id',
      userId: 'user-id',
      amount: 1000,
      billingAddress: '123 Test St',
      city: 'Test City',
      country: 'Test Country',
      postCode: '12345',
      paymentMethod: PaymentMethod.BANK_TRANSFER,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe(
        'Only EPAY_ADMIN can create float funds top up order for other organizations'
      )
    }
    expect(createFloatFundsOrderSpy).not.toHaveBeenCalled()
  })

  it('should return error when user is not found', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const createFloatFundsOrderSpy = vi.spyOn(
      floatFundsData,
      'createFloatFundsOrder'
    )

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(Promise.resolve(null), () => ({
        code: errorCodes.db.UNKNOWN_ERROR,
        message: 'Error',
      }))
    )

    const result = await createOrder({
      orgId: 'test-org-id',
      userId: 'non-existent-id',
      amount: 1000,
      billingAddress: '123 Test St',
      city: 'Test City',
      country: 'Test Country',
      postCode: '12345',
      paymentMethod: PaymentMethod.BANK_TRANSFER,
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe('User not found')
    }
    expect(createFloatFundsOrderSpy).not.toHaveBeenCalled()
  })
})
