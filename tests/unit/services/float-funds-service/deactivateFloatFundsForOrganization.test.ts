
import { describe, it, expect, vi } from 'vitest'
import { deactivateFloatFundsForOrganization } from '../../../../src/services/float-funds.service'
import * as userService from '../../../../src/services/user.service'
import * as floatFundsData from '../../../../src/data/float-funds.data'
import { UserRole } from '@prisma/client'
import { ResultAsync } from 'neverthrow'
import { errorCodes } from '../../../../src/types/simple-error'

describe('deactivateFloatFundsForOrganization', () => {
  it('should return error when user is not EPAY_ADMIN', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const upsertFloatFundOrgSpy = vi.spyOn(floatFundsData, 'upsertFloatFundOrg')
    const deactivateFloatFundOrgSpy = vi.spyOn(floatFundsData, 'deactivateFloatFundOrg')

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'non-admin-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          organization: null,
          role: UserRole.ORG_ADMIN,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await deactivateFloatFundsForOrganization({
      orgId: 'test-org-id',
      updatedByUserId: 'non-admin-id',
    })

    expect(result.isErr()).toBe(true)
    if (result.isErr()) {
      expect(result.error.code).toBe(errorCodes.UNAUTHORIZED)
      expect(result.error.message).toBe(
        'Only EPAY_ADMIN can deactivate float funds'
      )
    }

    expect(upsertFloatFundOrgSpy).not.toHaveBeenCalled()
    expect(deactivateFloatFundOrgSpy).not.toHaveBeenCalled()
  })

  it('should deactivate float funds when user is EPAY_ADMIN', async () => {
    const getUserByIdSpy = vi.spyOn(userService, 'getUserById')
    const upsertFloatFundOrgSpy = vi.spyOn(floatFundsData, 'upsertFloatFundOrg')
    const deactivateFloatFundOrgSpy = vi.spyOn(floatFundsData, 'deactivateFloatFundOrg')

    getUserByIdSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          id: 'admin-id',
          firstName: null,
          lastName: null,
          email: '<EMAIL>',
          organization: null,
          role: UserRole.EPAY_ADMIN,
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    upsertFloatFundOrgSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          orgId: 'test-org-id',
          customerCode: 'TEST-123',
          isActive: true,
          currentBalance: 0,
          lastUpdatedDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          updatedBy: 'admin-id'
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    deactivateFloatFundOrgSpy.mockImplementation(() =>
      ResultAsync.fromPromise(
        Promise.resolve({
          orgId: 'test-org-id',
          customerCode: 'TEST-123',
          isActive: false,
          currentBalance: 0,
          lastUpdatedDate: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          updatedBy: 'admin-id'
        }),
        () => ({ code: errorCodes.db.UNKNOWN_ERROR, message: 'Error' })
      )
    )

    const result = await deactivateFloatFundsForOrganization({
      orgId: 'test-org-id',
      updatedByUserId: 'admin-id',
    })

    expect(result.isOk()).toBe(true)
    expect(upsertFloatFundOrgSpy).toHaveBeenCalledWith({
      orgId: 'test-org-id',
      updatedByUserId: 'admin-id',
    })
    expect(deactivateFloatFundOrgSpy).toHaveBeenCalledWith('test-org-id')
  })
})

