import { afterEach, beforeEach, describe, it, expect, vi } from 'vitest'
import {
  addCardsForProductOrderItemsV2,
  ProductOrderItemProp,
} from 'services/product-order/order-cards.service'
import { fromPromise } from 'neverthrow'
import { initLogger } from 'utils/logger'
import { orderPhysicalCardBatch } from 'services/product-order/physical-card.service'
import { orderVirtualCard } from 'services/product-order/virtual-card.service'

const LOCK_CODE = '5949'

// Track the order of processing
let processingOrder: number[] = []

const processWithDelay = (itemId: number) => {
  return fromPromise(
    new Promise((resolve) => {
      // Simulate processing delay
      setTimeout(() => {
        processingOrder.push(itemId)
        resolve({})
      }, Math.random() * 500) // Randomize delay to simulate API call
    }),
    (error) => error
  )
}

vi.mock('services/product-order/physical-card.service', () => ({
  orderPhysicalCardBatch: vi.fn().mockImplementation(({ productOrderItem }) => {
    return processWithDelay(parseInt(productOrderItem.id))
  }),
}))

vi.mock('services/product-order/virtual-card.service', () => ({
  orderVirtualCard: vi.fn().mockImplementation(({ productOrderItem }) => {
    return processWithDelay(parseInt(productOrderItem.id))
  }),
}))

vi.mock('services/product-order/product-order-util', () => ({
  getLockCode: vi.fn().mockImplementation(() => {
    return LOCK_CODE
  }),
}))

describe('addCardsForProductOrderItemsV2', () => {
  initLogger('addCardsForProductOrderItemsV2')

  beforeEach(() => {
    processingOrder = []
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should process items in batches of 5', async () => {
    // Create 12 items to test multiple batches
    const productOrderItems = Array.from({ length: 12 }, (_, i) => ({
      id: `${i + 1}`,
      quantity: 1,
      productOrderId: '1',
      deliveryMethod: 'EMAIL',
      product: { type: 'CUSTOM' },
    })) as ProductOrderItemProp[]

    const result = await addCardsForProductOrderItemsV2({
      productOrderItems,
      useRen: false,
      orderNumber: '12345',
      userEmail: '<EMAIL>',
      secureDelivery: false,
      lockCodeOption: null,
    })

    if (result.isErr()) {
      throw result.error
    }

    // Verify all items were processed
    expect(orderVirtualCard).toHaveBeenCalledTimes(12)

    // Verify each batch's items were processed together
    const firstBatch = processingOrder.slice(0, 5)
    const secondBatch = processingOrder.slice(5, 10)
    const thirdBatch = processingOrder.slice(10)

    // Verify first batch (items 1-5)
    expect(firstBatch).toEqual(expect.arrayContaining([1, 2, 3, 4, 5]))
    expect(orderVirtualCard).toHaveBeenNthCalledWith(1, {
      productOrderItem: expect.objectContaining({ id: '1' }),
      useRen: false,
      lockCodeOption: LOCK_CODE,
      orderNumber: '12345',
    })

    // Verify second batch (items 6-10)
    expect(secondBatch).toEqual(expect.arrayContaining([6, 7, 8, 9, 10]))
    expect(orderVirtualCard).toHaveBeenNthCalledWith(6, {
      productOrderItem: expect.objectContaining({ id: '6' }),
      useRen: false,
      lockCodeOption: LOCK_CODE,
      orderNumber: '12345',
    })

    // Verify third batch (items 11-12)
    expect(thirdBatch).toEqual(expect.arrayContaining([11, 12]))
    expect(orderVirtualCard).toHaveBeenNthCalledWith(11, {
      productOrderItem: expect.objectContaining({ id: '11' }),
      useRen: false,
      lockCodeOption: LOCK_CODE,
      orderNumber: '12345',
    })
  })

  it('should process mixed card types in batches', async () => {
    const productOrderItems = [
      // First batch
      {
        id: '1',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'COURIER',
        product: { type: 'CUSTOM' },
      },
      {
        id: '2',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'EMAIL',
        product: { type: 'CUSTOM' },
      },
      {
        id: '3',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'COURIER',
        product: { type: 'CUSTOM' },
      },
      {
        id: '4',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'EMAIL',
        product: { type: 'CUSTOM' },
      },
      {
        id: '5',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'COURIER',
        product: { type: 'CUSTOM' },
      },
      // Second batch
      {
        id: '6',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'EMAIL',
        product: { type: 'CUSTOM' },
      },
      {
        id: '7',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'COURIER',
        product: { type: 'CUSTOM' },
      },
      {
        id: '8',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'EMAIL',
        product: { type: 'CUSTOM' },
      },
      {
        id: '9',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'COURIER',
        product: { type: 'CUSTOM' },
      },
      {
        id: '10',
        quantity: 1,
        productOrderId: '1',
        deliveryMethod: 'EMAIL',
        product: { type: 'CUSTOM' },
      },
    ] as ProductOrderItemProp[]

    const result = await addCardsForProductOrderItemsV2({
      productOrderItems,
      useRen: false,
      orderNumber: '12345',
      userEmail: '<EMAIL>',
      secureDelivery: false,
      lockCodeOption: null,
    })

    if (result.isErr()) {
      throw result.error
    }

    // Verify physical cards were processed
    expect(orderPhysicalCardBatch).toHaveBeenCalledTimes(5)
    expect(orderPhysicalCardBatch).toHaveBeenNthCalledWith(1, {
      userEmail: '<EMAIL>',
      productOrderItem: expect.objectContaining({
        id: '1',
        deliveryMethod: 'COURIER',
      }),
      useRen: false,
      secureDelivery: false,
      orderNumber: '12345',
    })

    // Verify virtual cards were processed
    expect(orderVirtualCard).toHaveBeenCalledTimes(5)
    expect(orderVirtualCard).toHaveBeenNthCalledWith(1, {
      useRen: false,
      productOrderItem: expect.objectContaining({
        id: '2',
        deliveryMethod: 'EMAIL',
      }),
      orderNumber: '12345',
      lockCodeOption: LOCK_CODE,
    })
  })
})
