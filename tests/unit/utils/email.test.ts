import { validateEmail } from 'utils/email'
import { describe, expect, it } from 'vitest'

const validEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
]

const invalidEmails = [
  'test@example',
  '<EMAIL>',
  '<EMAIL>.',
  '<EMAIL>>',
  'test@<EMAIL>>',
]

describe('validateEmail', () => {
  // Test valid emails
  validEmails.forEach((email) => {
    it(`should return true for a valid email: ${email}`, () => {
      expect(validateEmail(email)).toBe(true)
    })
  })

  // Test invalid emails
  invalidEmails.forEach((email) => {
    it(`should return false for an invalid email: ${email}`, () => {
      expect(validateEmail(email)).toBe(false)
    })
  })
})
