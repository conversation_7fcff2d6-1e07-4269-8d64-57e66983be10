// tests/helpers/setupIntegrationTests.ts
import { afterAll, beforeAll, vi } from 'vitest'
import { TEST_DATABASE_URL } from './database-test-setup'
import { disableLogging, initLogger } from 'utils/logger'
import { IS_GITHUB_ACTIONS_ENV } from 'utils/config'

beforeAll(async () => {
  initLogger('tests')
  // if (IS_GITHUB_ACTIONS_ENV) {
  //   disableLogging()
  // }

  vi.stubEnv('DATABASE_URL', TEST_DATABASE_URL)
  vi.stubEnv('USE_MOCKING', 'true')
  if (IS_GITHUB_ACTIONS_ENV) {
    vi.stubEnv('ENV', 'dev')
  }
})

afterAll(async () => {
  // add teardown per test here
})
